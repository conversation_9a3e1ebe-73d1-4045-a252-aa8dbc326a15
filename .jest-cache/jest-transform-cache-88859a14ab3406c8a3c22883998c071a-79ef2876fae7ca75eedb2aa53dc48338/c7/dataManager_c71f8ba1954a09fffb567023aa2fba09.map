{"file": "/Users/<USER>/WebstormProjects/goo/src/services/dataManager.ts", "mappings": ";;;AACA,6CAA8C;AAC9C,4CAA4C;AA8B5C;;GAEG;AACH,MAAa,WAAW;IAAxB;QACmB,cAAS,GAAG,wBAAY,CAAC,UAAU,CAAC;QACpC,iBAAY,GAAG,wBAAY,CAAC,gBAAgB,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAmUjF,CAAC;IAjUC;;;;OAIG;IACH,gBAAgB,CAAC,YAA0B;QACzC,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;YAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAEvB,MAAM,UAAU,GAAe;gBAC7B,IAAI,EAAE,YAAY;gBAClB,SAAS,EAAE,GAAG;gBACd,SAAS,EAAE,GAAG,GAAG,IAAI,CAAC,YAAY;aACnC,CAAC;YAEF,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YACtD,OAAO,GAAG,CAAC;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,mBAAU,CAAC,iCAAiC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAClH,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,eAAe,CAAC,GAAW;QACzB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACzC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,UAAU,GAAe,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAElD,4BAA4B;YAC5B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;gBACtC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;gBAC7B,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,wBAAwB;YACxB,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,mBAAmB;QACjB,MAAM,OAAO,GAAmB,EAAE,CAAC;QACnC,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5C,SAAS;YACX,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACzC,IAAI,CAAC,MAAM;oBAAE,SAAS;gBAEtB,MAAM,UAAU,GAAe,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBAElD,4BAA4B;gBAC5B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;oBACtC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACvB,SAAS;gBACX,CAAC;gBAED,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;YAC9D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,wBAAwB;gBACxB,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,qCAAqC;QACrC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;QAE1D,mCAAmC;QACnC,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAC3B,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAC5F,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,kBAAkB,CAAC,GAAW;QAC5B,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,YAAY;QACV,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC1C,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED;;;OAGG;IACH,cAAc;QACZ,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,eAAe,GAAkB,IAAI,CAAC;QAC1C,IAAI,eAAe,GAAkB,IAAI,CAAC;QAE1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5C,SAAS;YACX,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACzC,IAAI,CAAC,MAAM;oBAAE,SAAS;gBAEtB,MAAM,UAAU,GAAe,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBAElD,oBAAoB;gBACpB,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;oBACtC,SAAS;gBACX,CAAC;gBAED,YAAY,EAAE,CAAC;gBACf,cAAc,IAAI,MAAM,CAAC,MAAM,CAAC;gBAEhC,IAAI,eAAe,KAAK,IAAI,IAAI,UAAU,CAAC,SAAS,GAAG,eAAe,EAAE,CAAC;oBACvE,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC;gBACzC,CAAC;gBACD,IAAI,eAAe,KAAK,IAAI,IAAI,UAAU,CAAC,SAAS,GAAG,eAAe,EAAE,CAAC;oBACvE,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC;gBACzC,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,sBAAsB;gBACtB,SAAS;YACX,CAAC;QACH,CAAC;QAED,OAAO;YACL,YAAY;YACZ,cAAc;YACd,WAAW,EAAE,eAAe,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI;YAC/D,WAAW,EAAE,eAAe,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI;SAChE,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,kBAAkB;QAChB,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5C,SAAS;YACX,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACzC,IAAI,CAAC,MAAM;oBAAE,SAAS;gBAEtB,MAAM,UAAU,GAAe,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBAElD,IAAI,GAAG,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;oBAC/B,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,wBAAwB;gBACxB,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1D,OAAO,YAAY,CAAC,MAAM,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACH,UAAU;QACR,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEjD,MAAM,UAAU,GAAe;YAC7B,OAAO,EAAE,KAAK;YACd,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACpC,aAAa;SACd,CAAC;QAEF,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACH,UAAU,CAAC,QAAgB;QACzB,IAAI,CAAC;YACH,MAAM,UAAU,GAAe,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAEpD,iCAAiC;YACjC,IAAI,CAAC,UAAU,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC1E,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;YACnD,CAAC;YAED,IAAI,aAAa,GAAG,CAAC,CAAC;YAEtB,KAAK,MAAM,YAAY,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC;gBACpD,IAAI,CAAC;oBACH,8CAA8C;oBAC9C,MAAM,kBAAkB,GAAG,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;oBACtE,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;oBAC1C,aAAa,EAAE,CAAC;gBAClB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;oBACvD,8BAA8B;gBAChC,CAAC;YACH,CAAC;YAED,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,mBAAU,CAAC,0BAA0B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC3G,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,WAAW,CAAC,YAA0B;QAC5C,MAAM,MAAM,GAAG,YAAY,CAAC,YAAY,CAAC;QACzC,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QACxH,OAAO,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,EAAE,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACK,UAAU,CAAC,GAAW;QAC5B,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC/B,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YACnC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,4BAA4B;QAClD,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACrC,CAAC;IAED;;;OAGG;IACH,YAAY;QACV,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC1C,KAAK,EAAE,CAAC;YACV,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;OAIG;IACK,uBAAuB,CAAC,IAAS;QACvC,OAAO;YACL,GAAG,IAAI;YACP,YAAY,EAAE;gBACZ,GAAG,IAAI,CAAC,YAAY;gBACpB,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;aACjD;YACD,OAAO,EAAE;gBACP,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,QAAa,EAAE,EAAE,CAAC,CAAC;oBAC9D,GAAG,QAAQ;oBACX,QAAQ,EAAE;wBACR,GAAG,QAAQ,CAAC,QAAQ;wBACpB,WAAW,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC;qBACrD;iBACF,CAAC,CAAC;gBACH,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,QAAa,EAAE,EAAE,CAAC,CAAC;oBACpE,GAAG,QAAQ;oBACX,QAAQ,EAAE;wBACR,GAAG,QAAQ,CAAC,QAAQ;wBACpB,WAAW,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC;qBACrD;iBACF,CAAC,CAAC;aACJ;SACF,CAAC;IACJ,CAAC;CACF;AArUD,kCAqUC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/src/services/dataManager.ts"], "sourcesContent": ["import { SearchResult } from '../models/Business';\nimport { CacheError } from '../models/Errors';\nimport { CACHE_CONFIG } from '../constants';\n\n/**\n * Cached data structure\n */\ninterface CachedData {\n  data: SearchResult;\n  timestamp: number;\n  expiresAt: number;\n}\n\n/**\n * Storage information\n */\nexport interface StorageInfo {\n  totalEntries: number;\n  totalSizeBytes: number;\n  oldestEntry: Date | null;\n  newestEntry: Date | null;\n}\n\n/**\n * Export data structure\n */\ninterface ExportData {\n  version: string;\n  exportDate: string;\n  searchResults: SearchResult[];\n}\n\n/**\n * Service for managing data persistence using localStorage\n */\nexport class DataManager {\n  private readonly keyPrefix = CACHE_CONFIG.KEY_PREFIX;\n  private readonly expirationMs = CACHE_CONFIG.EXPIRATION_HOURS * 60 * 60 * 1000;\n\n  /**\n   * Saves a search result to localStorage\n   * @param searchResult - The search result to save\n   * @returns The storage key for the saved data\n   */\n  saveSearchResult(searchResult: SearchResult): string {\n    try {\n      const key = this.generateKey(searchResult);\n      const now = Date.now();\n      \n      const cachedData: CachedData = {\n        data: searchResult,\n        timestamp: now,\n        expiresAt: now + this.expirationMs,\n      };\n\n      localStorage.setItem(key, JSON.stringify(cachedData));\n      return key;\n    } catch (error) {\n      throw new CacheError(`Failed to save search result: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n\n  /**\n   * Retrieves a search result from localStorage\n   * @param key - The storage key\n   * @returns The search result or null if not found/expired\n   */\n  getSearchResult(key: string): SearchResult | null {\n    try {\n      const stored = localStorage.getItem(key);\n      if (!stored) {\n        return null;\n      }\n\n      const cachedData: CachedData = JSON.parse(stored);\n      \n      // Check if data has expired\n      if (Date.now() > cachedData.expiresAt) {\n        localStorage.removeItem(key);\n        return null;\n      }\n\n      return this.deserializeSearchResult(cachedData.data);\n    } catch (error) {\n      // Handle corrupted data\n      localStorage.removeItem(key);\n      return null;\n    }\n  }\n\n  /**\n   * Gets all stored search results (excluding expired ones)\n   * @returns Array of all valid search results\n   */\n  getAllSearchResults(): SearchResult[] {\n    const results: SearchResult[] = [];\n    const keysToRemove: string[] = [];\n\n    for (let i = 0; i < localStorage.length; i++) {\n      const key = localStorage.key(i);\n      if (!key || !key.startsWith(this.keyPrefix)) {\n        continue;\n      }\n\n      try {\n        const stored = localStorage.getItem(key);\n        if (!stored) continue;\n\n        const cachedData: CachedData = JSON.parse(stored);\n        \n        // Check if data has expired\n        if (Date.now() > cachedData.expiresAt) {\n          keysToRemove.push(key);\n          continue;\n        }\n\n        results.push(this.deserializeSearchResult(cachedData.data));\n      } catch (error) {\n        // Handle corrupted data\n        keysToRemove.push(key);\n      }\n    }\n\n    // Clean up expired/corrupted entries\n    keysToRemove.forEach(key => localStorage.removeItem(key));\n\n    // Sort by timestamp (newest first)\n    return results.sort((a, b) => \n      new Date(b.searchParams.timestamp).getTime() - new Date(a.searchParams.timestamp).getTime()\n    );\n  }\n\n  /**\n   * Deletes a specific search result\n   * @param key - The storage key to delete\n   */\n  deleteSearchResult(key: string): void {\n    localStorage.removeItem(key);\n  }\n\n  /**\n   * Clears all business search data from localStorage\n   */\n  clearAllData(): void {\n    const keysToRemove: string[] = [];\n\n    for (let i = 0; i < localStorage.length; i++) {\n      const key = localStorage.key(i);\n      if (key && key.startsWith(this.keyPrefix)) {\n        keysToRemove.push(key);\n      }\n    }\n\n    keysToRemove.forEach(key => localStorage.removeItem(key));\n  }\n\n  /**\n   * Gets information about current storage usage\n   * @returns Storage information\n   */\n  getStorageInfo(): StorageInfo {\n    let totalEntries = 0;\n    let totalSizeBytes = 0;\n    let oldestTimestamp: number | null = null;\n    let newestTimestamp: number | null = null;\n\n    for (let i = 0; i < localStorage.length; i++) {\n      const key = localStorage.key(i);\n      if (!key || !key.startsWith(this.keyPrefix)) {\n        continue;\n      }\n\n      try {\n        const stored = localStorage.getItem(key);\n        if (!stored) continue;\n\n        const cachedData: CachedData = JSON.parse(stored);\n        \n        // Skip expired data\n        if (Date.now() > cachedData.expiresAt) {\n          continue;\n        }\n\n        totalEntries++;\n        totalSizeBytes += stored.length;\n\n        if (oldestTimestamp === null || cachedData.timestamp < oldestTimestamp) {\n          oldestTimestamp = cachedData.timestamp;\n        }\n        if (newestTimestamp === null || cachedData.timestamp > newestTimestamp) {\n          newestTimestamp = cachedData.timestamp;\n        }\n      } catch (error) {\n        // Skip corrupted data\n        continue;\n      }\n    }\n\n    return {\n      totalEntries,\n      totalSizeBytes,\n      oldestEntry: oldestTimestamp ? new Date(oldestTimestamp) : null,\n      newestEntry: newestTimestamp ? new Date(newestTimestamp) : null,\n    };\n  }\n\n  /**\n   * Removes expired data from localStorage\n   * @returns Number of entries removed\n   */\n  cleanupExpiredData(): number {\n    const keysToRemove: string[] = [];\n    const now = Date.now();\n\n    for (let i = 0; i < localStorage.length; i++) {\n      const key = localStorage.key(i);\n      if (!key || !key.startsWith(this.keyPrefix)) {\n        continue;\n      }\n\n      try {\n        const stored = localStorage.getItem(key);\n        if (!stored) continue;\n\n        const cachedData: CachedData = JSON.parse(stored);\n        \n        if (now > cachedData.expiresAt) {\n          keysToRemove.push(key);\n        }\n      } catch (error) {\n        // Remove corrupted data\n        keysToRemove.push(key);\n      }\n    }\n\n    keysToRemove.forEach(key => localStorage.removeItem(key));\n    return keysToRemove.length;\n  }\n\n  /**\n   * Exports all search results as JSON\n   * @returns JSON string containing all search results\n   */\n  exportData(): string {\n    const searchResults = this.getAllSearchResults();\n    \n    const exportData: ExportData = {\n      version: '1.0',\n      exportDate: new Date().toISOString(),\n      searchResults,\n    };\n\n    return JSON.stringify(exportData, null, 2);\n  }\n\n  /**\n   * Imports search results from JSON\n   * @param jsonData - JSON string containing search results\n   * @returns Number of imported results\n   */\n  importData(jsonData: string): number {\n    try {\n      const importData: ExportData = JSON.parse(jsonData);\n      \n      // Validate import data structure\n      if (!importData.searchResults || !Array.isArray(importData.searchResults)) {\n        throw new Error('Invalid import data structure');\n      }\n\n      let importedCount = 0;\n      \n      for (const searchResult of importData.searchResults) {\n        try {\n          // Deserialize the search result before saving\n          const deserializedResult = this.deserializeSearchResult(searchResult);\n          this.saveSearchResult(deserializedResult);\n          importedCount++;\n        } catch (error) {\n          console.warn('Failed to import search result:', error);\n          // Continue with other results\n        }\n      }\n\n      return importedCount;\n    } catch (error) {\n      throw new CacheError(`Failed to import data: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n\n  /**\n   * Generates a unique key for a search result\n   * @param searchResult - The search result\n   * @returns Unique storage key\n   */\n  private generateKey(searchResult: SearchResult): string {\n    const params = searchResult.searchParams;\n    const hash = this.simpleHash(`${params.zipCode}_${params.radius}_${params.businessType}_${params.timestamp.getTime()}`);\n    return `${this.keyPrefix}${hash}`;\n  }\n\n  /**\n   * Simple hash function for generating keys\n   * @param str - String to hash\n   * @returns Hash string\n   */\n  private simpleHash(str: string): string {\n    let hash = 0;\n    for (let i = 0; i < str.length; i++) {\n      const char = str.charCodeAt(i);\n      hash = ((hash << 5) - hash) + char;\n      hash = hash & hash; // Convert to 32-bit integer\n    }\n    return Math.abs(hash).toString(36);\n  }\n\n  /**\n   * Gets the current cache size (number of entries)\n   * @returns Number of cached entries\n   */\n  getCacheSize(): number {\n    let count = 0;\n    for (let i = 0; i < localStorage.length; i++) {\n      const key = localStorage.key(i);\n      if (key && key.startsWith(this.keyPrefix)) {\n        count++;\n      }\n    }\n    return count;\n  }\n\n  /**\n   * Deserializes a search result, converting date strings back to Date objects\n   * @param data - Raw search result data\n   * @returns Properly typed search result\n   */\n  private deserializeSearchResult(data: any): SearchResult {\n    return {\n      ...data,\n      searchParams: {\n        ...data.searchParams,\n        timestamp: new Date(data.searchParams.timestamp),\n      },\n      results: {\n        withWebsites: data.results.withWebsites.map((business: any) => ({\n          ...business,\n          metadata: {\n            ...business.metadata,\n            lastUpdated: new Date(business.metadata.lastUpdated),\n          },\n        })),\n        withoutWebsites: data.results.withoutWebsites.map((business: any) => ({\n          ...business,\n          metadata: {\n            ...business.metadata,\n            lastUpdated: new Date(business.metadata.lastUpdated),\n          },\n        })),\n      },\n    };\n  }\n}\n"], "version": 3}