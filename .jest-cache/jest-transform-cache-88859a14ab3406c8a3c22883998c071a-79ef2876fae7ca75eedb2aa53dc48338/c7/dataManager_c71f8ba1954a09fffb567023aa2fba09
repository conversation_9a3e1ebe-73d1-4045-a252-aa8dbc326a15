f9b0a20ceed989a7112390320d003a18
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataManager = void 0;
const Errors_1 = require("../models/Errors");
const constants_1 = require("../constants");
/**
 * Service for managing data persistence using localStorage
 */
class DataManager {
    constructor() {
        this.keyPrefix = constants_1.CACHE_CONFIG.KEY_PREFIX;
        this.expirationMs = constants_1.CACHE_CONFIG.EXPIRATION_HOURS * 60 * 60 * 1000;
    }
    /**
     * Saves a search result to localStorage
     * @param searchResult - The search result to save
     * @returns The storage key for the saved data
     */
    saveSearchResult(searchResult) {
        try {
            const key = this.generateKey(searchResult);
            const now = Date.now();
            const cachedData = {
                data: searchResult,
                timestamp: now,
                expiresAt: now + this.expirationMs,
            };
            localStorage.setItem(key, JSON.stringify(cachedData));
            return key;
        }
        catch (error) {
            throw new Errors_1.CacheError(`Failed to save search result: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    /**
     * Retrieves a search result from localStorage
     * @param key - The storage key
     * @returns The search result or null if not found/expired
     */
    getSearchResult(key) {
        try {
            const stored = localStorage.getItem(key);
            if (!stored) {
                return null;
            }
            const cachedData = JSON.parse(stored);
            // Check if data has expired
            if (Date.now() > cachedData.expiresAt) {
                localStorage.removeItem(key);
                return null;
            }
            return this.deserializeSearchResult(cachedData.data);
        }
        catch (error) {
            // Handle corrupted data
            localStorage.removeItem(key);
            return null;
        }
    }
    /**
     * Gets all stored search results (excluding expired ones)
     * @returns Array of all valid search results
     */
    getAllSearchResults() {
        const results = [];
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (!key || !key.startsWith(this.keyPrefix)) {
                continue;
            }
            try {
                const stored = localStorage.getItem(key);
                if (!stored)
                    continue;
                const cachedData = JSON.parse(stored);
                // Check if data has expired
                if (Date.now() > cachedData.expiresAt) {
                    keysToRemove.push(key);
                    continue;
                }
                results.push(this.deserializeSearchResult(cachedData.data));
            }
            catch (error) {
                // Handle corrupted data
                keysToRemove.push(key);
            }
        }
        // Clean up expired/corrupted entries
        keysToRemove.forEach(key => localStorage.removeItem(key));
        // Sort by timestamp (newest first)
        return results.sort((a, b) => new Date(b.searchParams.timestamp).getTime() - new Date(a.searchParams.timestamp).getTime());
    }
    /**
     * Deletes a specific search result
     * @param key - The storage key to delete
     */
    deleteSearchResult(key) {
        localStorage.removeItem(key);
    }
    /**
     * Clears all business search data from localStorage
     */
    clearAllData() {
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(this.keyPrefix)) {
                keysToRemove.push(key);
            }
        }
        keysToRemove.forEach(key => localStorage.removeItem(key));
    }
    /**
     * Gets information about current storage usage
     * @returns Storage information
     */
    getStorageInfo() {
        let totalEntries = 0;
        let totalSizeBytes = 0;
        let oldestTimestamp = null;
        let newestTimestamp = null;
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (!key || !key.startsWith(this.keyPrefix)) {
                continue;
            }
            try {
                const stored = localStorage.getItem(key);
                if (!stored)
                    continue;
                const cachedData = JSON.parse(stored);
                // Skip expired data
                if (Date.now() > cachedData.expiresAt) {
                    continue;
                }
                totalEntries++;
                totalSizeBytes += stored.length;
                if (oldestTimestamp === null || cachedData.timestamp < oldestTimestamp) {
                    oldestTimestamp = cachedData.timestamp;
                }
                if (newestTimestamp === null || cachedData.timestamp > newestTimestamp) {
                    newestTimestamp = cachedData.timestamp;
                }
            }
            catch (error) {
                // Skip corrupted data
                continue;
            }
        }
        return {
            totalEntries,
            totalSizeBytes,
            oldestEntry: oldestTimestamp ? new Date(oldestTimestamp) : null,
            newestEntry: newestTimestamp ? new Date(newestTimestamp) : null,
        };
    }
    /**
     * Removes expired data from localStorage
     * @returns Number of entries removed
     */
    cleanupExpiredData() {
        const keysToRemove = [];
        const now = Date.now();
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (!key || !key.startsWith(this.keyPrefix)) {
                continue;
            }
            try {
                const stored = localStorage.getItem(key);
                if (!stored)
                    continue;
                const cachedData = JSON.parse(stored);
                if (now > cachedData.expiresAt) {
                    keysToRemove.push(key);
                }
            }
            catch (error) {
                // Remove corrupted data
                keysToRemove.push(key);
            }
        }
        keysToRemove.forEach(key => localStorage.removeItem(key));
        return keysToRemove.length;
    }
    /**
     * Exports all search results as JSON
     * @returns JSON string containing all search results
     */
    exportData() {
        const searchResults = this.getAllSearchResults();
        const exportData = {
            version: '1.0',
            exportDate: new Date().toISOString(),
            searchResults,
        };
        return JSON.stringify(exportData, null, 2);
    }
    /**
     * Imports search results from JSON
     * @param jsonData - JSON string containing search results
     * @returns Number of imported results
     */
    importData(jsonData) {
        try {
            const importData = JSON.parse(jsonData);
            // Validate import data structure
            if (!importData.searchResults || !Array.isArray(importData.searchResults)) {
                throw new Error('Invalid import data structure');
            }
            let importedCount = 0;
            for (const searchResult of importData.searchResults) {
                try {
                    // Deserialize the search result before saving
                    const deserializedResult = this.deserializeSearchResult(searchResult);
                    this.saveSearchResult(deserializedResult);
                    importedCount++;
                }
                catch (error) {
                    console.warn('Failed to import search result:', error);
                    // Continue with other results
                }
            }
            return importedCount;
        }
        catch (error) {
            throw new Errors_1.CacheError(`Failed to import data: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    /**
     * Generates a unique key for a search result
     * @param searchResult - The search result
     * @returns Unique storage key
     */
    generateKey(searchResult) {
        const params = searchResult.searchParams;
        const hash = this.simpleHash(`${params.zipCode}_${params.radius}_${params.businessType}_${params.timestamp.getTime()}`);
        return `${this.keyPrefix}${hash}`;
    }
    /**
     * Simple hash function for generating keys
     * @param str - String to hash
     * @returns Hash string
     */
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(36);
    }
    /**
     * Gets the current cache size (number of entries)
     * @returns Number of cached entries
     */
    getCacheSize() {
        let count = 0;
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(this.keyPrefix)) {
                count++;
            }
        }
        return count;
    }
    /**
     * Deserializes a search result, converting date strings back to Date objects
     * @param data - Raw search result data
     * @returns Properly typed search result
     */
    deserializeSearchResult(data) {
        return {
            ...data,
            searchParams: {
                ...data.searchParams,
                timestamp: new Date(data.searchParams.timestamp),
            },
            results: {
                withWebsites: data.results.withWebsites.map((business) => ({
                    ...business,
                    metadata: {
                        ...business.metadata,
                        lastUpdated: new Date(business.metadata.lastUpdated),
                    },
                })),
                withoutWebsites: data.results.withoutWebsites.map((business) => ({
                    ...business,
                    metadata: {
                        ...business.metadata,
                        lastUpdated: new Date(business.metadata.lastUpdated),
                    },
                })),
            },
        };
    }
}
exports.DataManager = DataManager;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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