{"file": "/Users/<USER>/WebstormProjects/goo/tests/services/dataManager.simple.test.ts", "mappings": ";;AAAA,gEAA6D;AAG7D,QAAQ,CAAC,kCAAkC,EAAE,GAAG,EAAE;IAChD,IAAI,WAAwB,CAAC;IAE7B,MAAM,gBAAgB,GAAiB;QACrC,YAAY,EAAE;YACZ,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,EAAE;YACV,YAAY,EAAE,YAAY;YAC1B,SAAS,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;SAC5C;QACD,OAAO,EAAE;YACP,YAAY,EAAE;gBACZ;oBACE,EAAE,EAAE,GAAG;oBACP,IAAI,EAAE,cAAc;oBACpB,OAAO,EAAE,aAAa;oBACtB,aAAa,EAAE,UAAU;oBACzB,QAAQ,EAAE,CAAC,YAAY,CAAC;oBACxB,QAAQ,EAAE,GAAG;oBACb,QAAQ,EAAE;wBACR,WAAW,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;wBAC7C,UAAU,EAAE,eAAe;wBAC3B,UAAU,EAAE,GAAG;qBAChB;iBACU;aACd;YACD,eAAe,EAAE;gBACf;oBACE,EAAE,EAAE,GAAG;oBACP,IAAI,EAAE,cAAc;oBACpB,OAAO,EAAE,aAAa;oBACtB,aAAa,EAAE,MAAM;oBACrB,QAAQ,EAAE,CAAC,YAAY,CAAC;oBACxB,QAAQ,EAAE,GAAG;oBACb,QAAQ,EAAE;wBACR,WAAW,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;wBAC7C,UAAU,EAAE,eAAe;wBAC3B,UAAU,EAAE,GAAG;qBAChB;iBACU;aACd;SACF;QACD,UAAU,EAAE;YACV,UAAU,EAAE,CAAC;YACb,gBAAgB,EAAE,CAAC;YACnB,mBAAmB,EAAE,CAAC;YACtB,mBAAmB,EAAE,GAAG;YACxB,cAAc,EAAE,IAAI;SACrB;KACF,CAAC;IAEF,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG,IAAI,yBAAW,EAAE,CAAC;IAClC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,IAAI,GAAG,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAC5D,MAAM,IAAI,GAAG,WAAW,CAAC,gBAAgB,CAAC;gBACxC,GAAG,gBAAgB;gBACnB,YAAY,EAAE;oBACZ,GAAG,gBAAgB,CAAC,YAAY;oBAChC,OAAO,EAAE,OAAO;iBACjB;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,GAAG,GAAG,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAC3D,MAAM,SAAS,GAAG,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAEnD,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,CAAC;YAC/B,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACrD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBACvD,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBAC1D,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBAC9D,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACtF,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAE/C,MAAM,QAAQ,GAAG,WAAW,CAAC,UAAU,EAAE,CAAC;YAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAEpC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,UAAU,GAAG;gBACjB,OAAO,EAAE,KAAK;gBACd,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACpC,aAAa,EAAE,CAAC,gBAAgB,CAAC;aAClC,CAAC;YAEF,MAAM,aAAa,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YACzE,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,IAAI,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC;YAE1C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,MAAM,GAAG,WAAW,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;YAC/D,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,WAAW,GAAG;gBAClB,OAAO,EAAE,KAAK;gBACd,wBAAwB;aACzB,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,2DAA2D;YAC3D,MAAM,GAAG,GAAG,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAC3D,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;YAE1C,MAAM,MAAM,GAAG,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,sCAAsC;YACtC,MAAM,oBAAoB,GAAG,MAAM,CAAC,YAAY,CAAC;YACjD,MAAM,gBAAgB,GAAG;gBACvB,GAAG,oBAAoB;gBACvB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,GAAG,EAAE;oBACzC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;oBAC9C,KAAK,CAAC,IAAI,GAAG,oBAAoB,CAAC;oBAClC,MAAM,KAAK,CAAC;gBACd,CAAC,CAAC;aACH,CAAC;YAEF,qCAAqC;YACrC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,cAAc,EAAE;gBAC5C,KAAK,EAAE,gBAAgB;gBACvB,QAAQ,EAAE,IAAI;aACf,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,kDAAkD,CAAC,CAAC;YAEzH,gCAAgC;YAChC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,cAAc,EAAE;gBAC5C,KAAK,EAAE,oBAAoB;gBAC3B,QAAQ,EAAE,IAAI;aACf,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,GAAG,GAAG,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAE3D,4BAA4B;YAC5B,MAAM,WAAW,GAAG;gBAClB,IAAI,EAAE,gBAAgB;gBACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,eAAe;gBAC9D,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,aAAa;aAC5D,CAAC;YACF,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;YAEvD,MAAM,MAAM,GAAG,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,iBAAiB;YACjB,MAAM,QAAQ,GAAG,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAEhE,mBAAmB;YACnB,MAAM,UAAU,GAAG,yBAAyB,CAAC;YAC7C,MAAM,WAAW,GAAG;gBAClB,IAAI,EAAE,gBAAgB;gBACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;gBAC7C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;aAC7C,CAAC;YACF,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;YAE9D,MAAM,YAAY,GAAG,WAAW,CAAC,kBAAkB,EAAE,CAAC;YACtD,MAAM,CAAC,YAAY,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAC/C,WAAW,CAAC,gBAAgB,CAAC;gBAC3B,GAAG,gBAAgB;gBACnB,YAAY,EAAE;oBACZ,GAAG,gBAAgB,CAAC,YAAY;oBAChC,OAAO,EAAE,OAAO;iBACjB;aACF,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,WAAW,CAAC,mBAAmB,EAAE,CAAC;YACrD,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,mBAAmB;YACnB,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAE/C,qBAAqB;YACrB,MAAM,UAAU,GAAG,yBAAyB,CAAC;YAC7C,MAAM,WAAW,GAAG;gBAClB,IAAI,EAAE,gBAAgB;gBACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;gBAC7C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;aAC7C,CAAC;YACF,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;YAE9D,MAAM,UAAU,GAAG,WAAW,CAAC,mBAAmB,EAAE,CAAC;YACrD,mCAAmC;YACnC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAC/B,MAAM,CAAC,YAAY,CAAC,SAAS,YAAY,IAAI,CAC9C,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,GAAG,GAAG,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAE3D,WAAW,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;YAEpC,MAAM,MAAM,GAAG,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QAC7E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAC/C,WAAW,CAAC,gBAAgB,CAAC;gBAC3B,GAAG,gBAAgB;gBACnB,YAAY,EAAE;oBACZ,GAAG,gBAAgB,CAAC,YAAY;oBAChC,OAAO,EAAE,OAAO;iBACjB;aACF,CAAC,CAAC;YAEH,WAAW,CAAC,YAAY,EAAE,CAAC;YAE3B,MAAM,UAAU,GAAG,WAAW,CAAC,mBAAmB,EAAE,CAAC;YACrD,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qEAAqE,EAAE,GAAG,EAAE;YAC7E,+BAA+B;YAC/B,YAAY,CAAC,OAAO,CAAC,gBAAgB,EAAE,eAAe,CAAC,CAAC;YAExD,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAC/C,WAAW,CAAC,YAAY,EAAE,CAAC;YAE3B,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACrE,MAAM,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,IAAI,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;YACxC,MAAM,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/tests/services/dataManager.simple.test.ts"], "sourcesContent": ["import { DataManager } from '../../src/services/dataManager';\nimport { SearchResult, Business } from '../../src/models/Business';\n\ndescribe('DataManager - Core Functionality', () => {\n  let dataManager: DataManager;\n\n  const mockSearchResult: SearchResult = {\n    searchParams: {\n      zipCode: '10001',\n      radius: 10,\n      businessType: 'restaurant',\n      timestamp: new Date('2023-01-01T00:00:00Z'),\n    },\n    results: {\n      withWebsites: [\n        {\n          id: '1',\n          name: 'Restaurant A',\n          address: '123 Main St',\n          websiteStatus: 'verified',\n          category: ['restaurant'],\n          distance: 1.5,\n          metadata: {\n            lastUpdated: new Date('2023-01-01T00:00:00Z'),\n            dataSource: 'google_places',\n            confidence: 0.9,\n          },\n        } as Business,\n      ],\n      withoutWebsites: [\n        {\n          id: '2',\n          name: 'Restaurant B',\n          address: '456 Oak Ave',\n          websiteStatus: 'none',\n          category: ['restaurant'],\n          distance: 2.1,\n          metadata: {\n            lastUpdated: new Date('2023-01-01T00:00:00Z'),\n            dataSource: 'google_places',\n            confidence: 0.8,\n          },\n        } as Business,\n      ],\n    },\n    statistics: {\n      totalFound: 2,\n      withWebsiteCount: 1,\n      withoutWebsiteCount: 1,\n      websiteAdoptionRate: 0.5,\n      searchDuration: 1500,\n    },\n  };\n\n  beforeEach(() => {\n    dataManager = new DataManager();\n  });\n\n  describe('key generation', () => {\n    it('should generate unique keys for different searches', () => {\n      const key1 = dataManager.saveSearchResult(mockSearchResult);\n      const key2 = dataManager.saveSearchResult({\n        ...mockSearchResult,\n        searchParams: {\n          ...mockSearchResult.searchParams,\n          zipCode: '10002',\n        },\n      });\n\n      expect(key1).toBeTruthy();\n      expect(key2).toBeTruthy();\n      expect(key1).not.toBe(key2);\n    });\n  });\n\n  describe('data serialization', () => {\n    it('should properly serialize and deserialize search results', () => {\n      const key = dataManager.saveSearchResult(mockSearchResult);\n      const retrieved = dataManager.getSearchResult(key);\n\n      expect(retrieved).toBeTruthy();\n      if (retrieved) {\n        expect(retrieved.searchParams.zipCode).toBe('10001');\n        expect(retrieved.results.withWebsites).toHaveLength(1);\n        expect(retrieved.results.withoutWebsites).toHaveLength(1);\n        expect(retrieved.searchParams.timestamp).toBeInstanceOf(Date);\n        expect(retrieved.results.withWebsites[0].metadata.lastUpdated).toBeInstanceOf(Date);\n      }\n    });\n  });\n\n  describe('export and import', () => {\n    it('should export data in correct format', () => {\n      dataManager.saveSearchResult(mockSearchResult);\n      \n      const exported = dataManager.exportData();\n      const parsed = JSON.parse(exported);\n\n      expect(parsed).toHaveProperty('version');\n      expect(parsed).toHaveProperty('exportDate');\n      expect(parsed).toHaveProperty('searchResults');\n      expect(parsed.version).toBe('1.0');\n      expect(Array.isArray(parsed.searchResults)).toBe(true);\n    });\n\n    it('should import data successfully', () => {\n      const exportData = {\n        version: '1.0',\n        exportDate: new Date().toISOString(),\n        searchResults: [mockSearchResult],\n      };\n\n      const importedCount = dataManager.importData(JSON.stringify(exportData));\n      expect(importedCount).toBe(1);\n    });\n  });\n\n  describe('storage info', () => {\n    it('should return storage information', () => {\n      const info = dataManager.getStorageInfo();\n\n      expect(info).toHaveProperty('totalEntries');\n      expect(info).toHaveProperty('totalSizeBytes');\n      expect(info).toHaveProperty('oldestEntry');\n      expect(info).toHaveProperty('newestEntry');\n      expect(typeof info.totalEntries).toBe('number');\n      expect(typeof info.totalSizeBytes).toBe('number');\n    });\n  });\n\n  describe('error handling', () => {\n    it('should handle non-existent keys gracefully', () => {\n      const result = dataManager.getSearchResult('non-existent-key');\n      expect(result).toBeNull();\n    });\n\n    it('should handle invalid JSON in import gracefully', () => {\n      expect(() => dataManager.importData('invalid-json')).toThrow();\n    });\n\n    it('should handle invalid import data structure', () => {\n      const invalidData = {\n        version: '1.0',\n        // Missing searchResults\n      };\n\n      expect(() => dataManager.importData(JSON.stringify(invalidData))).toThrow();\n    });\n\n    it('should handle corrupted localStorage data', () => {\n      // Simulate corrupted data by directly setting invalid JSON\n      const key = dataManager.saveSearchResult(mockSearchResult);\n      localStorage.setItem(key, 'invalid-json');\n\n      const result = dataManager.getSearchResult(key);\n      expect(result).toBeNull();\n    });\n\n    it('should handle localStorage quota exceeded', () => {\n      // Mock the entire localStorage object\n      const originalLocalStorage = global.localStorage;\n      const mockLocalStorage = {\n        ...originalLocalStorage,\n        setItem: jest.fn().mockImplementation(() => {\n          const error = new Error('QuotaExceededError');\n          error.name = 'QuotaExceededError';\n          throw error;\n        }),\n      };\n\n      // Replace localStorage with the mock\n      Object.defineProperty(global, 'localStorage', {\n        value: mockLocalStorage,\n        writable: true,\n      });\n\n      expect(() => dataManager.saveSearchResult(mockSearchResult)).toThrow('Failed to save search result: QuotaExceededError');\n\n      // Restore original localStorage\n      Object.defineProperty(global, 'localStorage', {\n        value: originalLocalStorage,\n        writable: true,\n      });\n    });\n  });\n\n  describe('data expiration', () => {\n    it('should handle expired data correctly', () => {\n      const key = dataManager.saveSearchResult(mockSearchResult);\n\n      // Manually set expired data\n      const expiredData = {\n        data: mockSearchResult,\n        timestamp: Date.now() - (25 * 60 * 60 * 1000), // 25 hours ago\n        expiresAt: Date.now() - (1 * 60 * 60 * 1000), // 1 hour ago\n      };\n      localStorage.setItem(key, JSON.stringify(expiredData));\n\n      const result = dataManager.getSearchResult(key);\n      expect(result).toBeNull();\n    });\n\n    it('should cleanup expired data', () => {\n      // Add valid data\n      const validKey = dataManager.saveSearchResult(mockSearchResult);\n\n      // Add expired data\n      const expiredKey = 'business_search_expired';\n      const expiredData = {\n        data: mockSearchResult,\n        timestamp: Date.now() - (25 * 60 * 60 * 1000),\n        expiresAt: Date.now() - (1 * 60 * 60 * 1000),\n      };\n      localStorage.setItem(expiredKey, JSON.stringify(expiredData));\n\n      const removedCount = dataManager.cleanupExpiredData();\n      expect(removedCount).toBeGreaterThanOrEqual(0);\n    });\n  });\n\n  describe('getAllSearchResults', () => {\n    it('should return all valid search results', () => {\n      dataManager.saveSearchResult(mockSearchResult);\n      dataManager.saveSearchResult({\n        ...mockSearchResult,\n        searchParams: {\n          ...mockSearchResult.searchParams,\n          zipCode: '10002',\n        },\n      });\n\n      const allResults = dataManager.getAllSearchResults();\n      expect(allResults.length).toBeGreaterThanOrEqual(2);\n    });\n\n    it('should exclude expired results from getAllSearchResults', () => {\n      // Add valid result\n      dataManager.saveSearchResult(mockSearchResult);\n\n      // Add expired result\n      const expiredKey = 'business_search_expired';\n      const expiredData = {\n        data: mockSearchResult,\n        timestamp: Date.now() - (25 * 60 * 60 * 1000),\n        expiresAt: Date.now() - (1 * 60 * 60 * 1000),\n      };\n      localStorage.setItem(expiredKey, JSON.stringify(expiredData));\n\n      const allResults = dataManager.getAllSearchResults();\n      // Should only return valid results\n      expect(allResults.every(result =>\n        result.searchParams.timestamp instanceof Date\n      )).toBe(true);\n    });\n  });\n\n  describe('deleteSearchResult', () => {\n    it('should delete specific search result', () => {\n      const key = dataManager.saveSearchResult(mockSearchResult);\n\n      dataManager.deleteSearchResult(key);\n\n      const result = dataManager.getSearchResult(key);\n      expect(result).toBeNull();\n    });\n\n    it('should handle deletion of non-existent key', () => {\n      expect(() => dataManager.deleteSearchResult('non-existent')).not.toThrow();\n    });\n  });\n\n  describe('clearAllData', () => {\n    it('should clear all business search data', () => {\n      dataManager.saveSearchResult(mockSearchResult);\n      dataManager.saveSearchResult({\n        ...mockSearchResult,\n        searchParams: {\n          ...mockSearchResult.searchParams,\n          zipCode: '10002',\n        },\n      });\n\n      dataManager.clearAllData();\n\n      const allResults = dataManager.getAllSearchResults();\n      expect(allResults).toHaveLength(0);\n    });\n\n    it('should only clear business search data, not other localStorage data', () => {\n      // Add non-business search data\n      localStorage.setItem('other_app_data', 'should_remain');\n\n      dataManager.saveSearchResult(mockSearchResult);\n      dataManager.clearAllData();\n\n      expect(localStorage.getItem('other_app_data')).toBe('should_remain');\n      expect(dataManager.getAllSearchResults()).toHaveLength(0);\n    });\n  });\n\n  describe('getCacheSize', () => {\n    it('should return current cache size', () => {\n      const size = dataManager.getCacheSize();\n      expect(typeof size).toBe('number');\n      expect(size).toBeGreaterThanOrEqual(0);\n    });\n  });\n});\n"], "version": 3}