08c2f22abe4f5e938da81571bf062d6a
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const dataManager_1 = require("../../src/services/dataManager");
describe('DataManager - Core Functionality', () => {
    let dataManager;
    const mockSearchResult = {
        searchParams: {
            zipCode: '10001',
            radius: 10,
            businessType: 'restaurant',
            timestamp: new Date('2023-01-01T00:00:00Z'),
        },
        results: {
            withWebsites: [
                {
                    id: '1',
                    name: 'Restaurant A',
                    address: '123 Main St',
                    websiteStatus: 'verified',
                    category: ['restaurant'],
                    distance: 1.5,
                    metadata: {
                        lastUpdated: new Date('2023-01-01T00:00:00Z'),
                        dataSource: 'google_places',
                        confidence: 0.9,
                    },
                },
            ],
            withoutWebsites: [
                {
                    id: '2',
                    name: 'Restaurant B',
                    address: '456 Oak Ave',
                    websiteStatus: 'none',
                    category: ['restaurant'],
                    distance: 2.1,
                    metadata: {
                        lastUpdated: new Date('2023-01-01T00:00:00Z'),
                        dataSource: 'google_places',
                        confidence: 0.8,
                    },
                },
            ],
        },
        statistics: {
            totalFound: 2,
            withWebsiteCount: 1,
            withoutWebsiteCount: 1,
            websiteAdoptionRate: 0.5,
            searchDuration: 1500,
        },
    };
    beforeEach(() => {
        dataManager = new dataManager_1.DataManager();
    });
    describe('key generation', () => {
        it('should generate unique keys for different searches', () => {
            const key1 = dataManager.saveSearchResult(mockSearchResult);
            const key2 = dataManager.saveSearchResult({
                ...mockSearchResult,
                searchParams: {
                    ...mockSearchResult.searchParams,
                    zipCode: '10002',
                },
            });
            expect(key1).toBeTruthy();
            expect(key2).toBeTruthy();
            expect(key1).not.toBe(key2);
        });
    });
    describe('data serialization', () => {
        it('should properly serialize and deserialize search results', () => {
            const key = dataManager.saveSearchResult(mockSearchResult);
            const retrieved = dataManager.getSearchResult(key);
            expect(retrieved).toBeTruthy();
            if (retrieved) {
                expect(retrieved.searchParams.zipCode).toBe('10001');
                expect(retrieved.results.withWebsites).toHaveLength(1);
                expect(retrieved.results.withoutWebsites).toHaveLength(1);
                expect(retrieved.searchParams.timestamp).toBeInstanceOf(Date);
                expect(retrieved.results.withWebsites[0].metadata.lastUpdated).toBeInstanceOf(Date);
            }
        });
    });
    describe('export and import', () => {
        it('should export data in correct format', () => {
            dataManager.saveSearchResult(mockSearchResult);
            const exported = dataManager.exportData();
            const parsed = JSON.parse(exported);
            expect(parsed).toHaveProperty('version');
            expect(parsed).toHaveProperty('exportDate');
            expect(parsed).toHaveProperty('searchResults');
            expect(parsed.version).toBe('1.0');
            expect(Array.isArray(parsed.searchResults)).toBe(true);
        });
        it('should import data successfully', () => {
            const exportData = {
                version: '1.0',
                exportDate: new Date().toISOString(),
                searchResults: [mockSearchResult],
            };
            const importedCount = dataManager.importData(JSON.stringify(exportData));
            expect(importedCount).toBe(1);
        });
    });
    describe('storage info', () => {
        it('should return storage information', () => {
            const info = dataManager.getStorageInfo();
            expect(info).toHaveProperty('totalEntries');
            expect(info).toHaveProperty('totalSizeBytes');
            expect(info).toHaveProperty('oldestEntry');
            expect(info).toHaveProperty('newestEntry');
            expect(typeof info.totalEntries).toBe('number');
            expect(typeof info.totalSizeBytes).toBe('number');
        });
    });
    describe('error handling', () => {
        it('should handle non-existent keys gracefully', () => {
            const result = dataManager.getSearchResult('non-existent-key');
            expect(result).toBeNull();
        });
        it('should handle invalid JSON in import gracefully', () => {
            expect(() => dataManager.importData('invalid-json')).toThrow();
        });
        it('should handle invalid import data structure', () => {
            const invalidData = {
                version: '1.0',
                // Missing searchResults
            };
            expect(() => dataManager.importData(JSON.stringify(invalidData))).toThrow();
        });
        it('should handle corrupted localStorage data', () => {
            // Simulate corrupted data by directly setting invalid JSON
            const key = dataManager.saveSearchResult(mockSearchResult);
            localStorage.setItem(key, 'invalid-json');
            const result = dataManager.getSearchResult(key);
            expect(result).toBeNull();
        });
        it('should handle localStorage quota exceeded', () => {
            // Mock the entire localStorage object
            const originalLocalStorage = global.localStorage;
            const mockLocalStorage = {
                ...originalLocalStorage,
                setItem: jest.fn().mockImplementation(() => {
                    const error = new Error('QuotaExceededError');
                    error.name = 'QuotaExceededError';
                    throw error;
                }),
            };
            // Replace localStorage with the mock
            Object.defineProperty(global, 'localStorage', {
                value: mockLocalStorage,
                writable: true,
            });
            expect(() => dataManager.saveSearchResult(mockSearchResult)).toThrow('Failed to save search result: QuotaExceededError');
            // Restore original localStorage
            Object.defineProperty(global, 'localStorage', {
                value: originalLocalStorage,
                writable: true,
            });
        });
    });
    describe('data expiration', () => {
        it('should handle expired data correctly', () => {
            const key = dataManager.saveSearchResult(mockSearchResult);
            // Manually set expired data
            const expiredData = {
                data: mockSearchResult,
                timestamp: Date.now() - (25 * 60 * 60 * 1000), // 25 hours ago
                expiresAt: Date.now() - (1 * 60 * 60 * 1000), // 1 hour ago
            };
            localStorage.setItem(key, JSON.stringify(expiredData));
            const result = dataManager.getSearchResult(key);
            expect(result).toBeNull();
        });
        it('should cleanup expired data', () => {
            // Add valid data
            const validKey = dataManager.saveSearchResult(mockSearchResult);
            // Add expired data
            const expiredKey = 'business_search_expired';
            const expiredData = {
                data: mockSearchResult,
                timestamp: Date.now() - (25 * 60 * 60 * 1000),
                expiresAt: Date.now() - (1 * 60 * 60 * 1000),
            };
            localStorage.setItem(expiredKey, JSON.stringify(expiredData));
            const removedCount = dataManager.cleanupExpiredData();
            expect(removedCount).toBeGreaterThanOrEqual(0);
        });
    });
    describe('getAllSearchResults', () => {
        it('should return all valid search results', () => {
            dataManager.saveSearchResult(mockSearchResult);
            dataManager.saveSearchResult({
                ...mockSearchResult,
                searchParams: {
                    ...mockSearchResult.searchParams,
                    zipCode: '10002',
                },
            });
            const allResults = dataManager.getAllSearchResults();
            expect(allResults.length).toBeGreaterThanOrEqual(2);
        });
        it('should exclude expired results from getAllSearchResults', () => {
            // Add valid result
            dataManager.saveSearchResult(mockSearchResult);
            // Add expired result
            const expiredKey = 'business_search_expired';
            const expiredData = {
                data: mockSearchResult,
                timestamp: Date.now() - (25 * 60 * 60 * 1000),
                expiresAt: Date.now() - (1 * 60 * 60 * 1000),
            };
            localStorage.setItem(expiredKey, JSON.stringify(expiredData));
            const allResults = dataManager.getAllSearchResults();
            // Should only return valid results
            expect(allResults.every(result => result.searchParams.timestamp instanceof Date)).toBe(true);
        });
    });
    describe('deleteSearchResult', () => {
        it('should delete specific search result', () => {
            const key = dataManager.saveSearchResult(mockSearchResult);
            dataManager.deleteSearchResult(key);
            const result = dataManager.getSearchResult(key);
            expect(result).toBeNull();
        });
        it('should handle deletion of non-existent key', () => {
            expect(() => dataManager.deleteSearchResult('non-existent')).not.toThrow();
        });
    });
    describe('clearAllData', () => {
        it('should clear all business search data', () => {
            dataManager.saveSearchResult(mockSearchResult);
            dataManager.saveSearchResult({
                ...mockSearchResult,
                searchParams: {
                    ...mockSearchResult.searchParams,
                    zipCode: '10002',
                },
            });
            dataManager.clearAllData();
            const allResults = dataManager.getAllSearchResults();
            expect(allResults).toHaveLength(0);
        });
        it('should only clear business search data, not other localStorage data', () => {
            // Add non-business search data
            localStorage.setItem('other_app_data', 'should_remain');
            dataManager.saveSearchResult(mockSearchResult);
            dataManager.clearAllData();
            expect(localStorage.getItem('other_app_data')).toBe('should_remain');
            expect(dataManager.getAllSearchResults()).toHaveLength(0);
        });
    });
    describe('getCacheSize', () => {
        it('should return current cache size', () => {
            const size = dataManager.getCacheSize();
            expect(typeof size).toBe('number');
            expect(size).toBeGreaterThanOrEqual(0);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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