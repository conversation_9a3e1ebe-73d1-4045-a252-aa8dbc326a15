{"version": 3, "names": ["cov_otmiqg058", "actualCoverage", "s", "exports", "calculateDistance", "calculateDistanceKm", "findClosest", "isWithinRadius", "sortByDistance", "coord1", "coord2", "f", "R", "dLat", "toRadians", "latitude", "dLon", "longitude", "a", "Math", "sin", "cos", "c", "atan2", "sqrt", "distance", "round", "degrees", "PI", "distanceInMiles", "target", "coordinates", "length", "b", "closest", "minDistance", "i", "coordinate", "center", "point", "radiusMiles", "map", "coord", "sort"], "sources": ["/Users/<USER>/WebstormProjects/goo/src/utils/distance.ts"], "sourcesContent": ["import { Coordinates } from '../models/Business';\n\n/**\n * Calculates the distance between two coordinates using the Haversine formula\n * @param coord1 - First coordinate\n * @param coord2 - Second coordinate\n * @returns Distance in miles\n */\nexport function calculateDistance(coord1: Coordinates, coord2: Coordinates): number {\n  const R = 3959; // Earth's radius in miles\n  const dLat = toRadians(coord2.latitude - coord1.latitude);\n  const dLon = toRadians(coord2.longitude - coord1.longitude);\n  \n  const a = \n    Math.sin(dLat / 2) * Math.sin(dLat / 2) +\n    Math.cos(toRadians(coord1.latitude)) * Math.cos(toRadians(coord2.latitude)) *\n    Math.sin(dLon / 2) * Math.sin(dLon / 2);\n  \n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n  const distance = R * c;\n  \n  return Math.round(distance * 100) / 100; // Round to 2 decimal places\n}\n\n/**\n * Converts degrees to radians\n * @param degrees - Angle in degrees\n * @returns Angle in radians\n */\nfunction toRadians(degrees: number): number {\n  return degrees * (Math.PI / 180);\n}\n\n/**\n * Calculates the distance between two coordinates in kilometers\n * @param coord1 - First coordinate\n * @param coord2 - Second coordinate\n * @returns Distance in kilometers\n */\nexport function calculateDistanceKm(coord1: Coordinates, coord2: Coordinates): number {\n  const distanceInMiles = calculateDistance(coord1, coord2);\n  return Math.round(distanceInMiles * 1.60934 * 100) / 100; // Convert to km and round\n}\n\n/**\n * Finds the closest coordinate from an array of coordinates\n * @param target - Target coordinate\n * @param coordinates - Array of coordinates to search\n * @returns Closest coordinate and its distance\n */\nexport function findClosest(\n  target: Coordinates,\n  coordinates: Coordinates[]\n): { coordinate: Coordinates; distance: number } | null {\n  if (coordinates.length === 0) {\n    return null;\n  }\n\n  let closest = coordinates[0];\n  let minDistance = calculateDistance(target, closest);\n\n  for (let i = 1; i < coordinates.length; i++) {\n    const distance = calculateDistance(target, coordinates[i]);\n    if (distance < minDistance) {\n      minDistance = distance;\n      closest = coordinates[i];\n    }\n  }\n\n  return { coordinate: closest, distance: minDistance };\n}\n\n/**\n * Checks if a coordinate is within a certain radius of a center point\n * @param center - Center coordinate\n * @param point - Point to check\n * @param radiusMiles - Radius in miles\n * @returns True if point is within radius\n */\nexport function isWithinRadius(\n  center: Coordinates,\n  point: Coordinates,\n  radiusMiles: number\n): boolean {\n  const distance = calculateDistance(center, point);\n  return distance <= radiusMiles;\n}\n\n/**\n * Sorts an array of coordinates by distance from a target point\n * @param target - Target coordinate\n * @param coordinates - Array of coordinates to sort\n * @returns Sorted array with distances\n */\nexport function sortByDistance(\n  target: Coordinates,\n  coordinates: Coordinates[]\n): Array<{ coordinate: Coordinates; distance: number }> {\n  return coordinates\n    .map(coord => ({\n      coordinate: coord,\n      distance: calculateDistance(target, coord)\n    }))\n    .sort((a, b) => a.distance - b.distance);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUQ;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;;;AAFRC,OAAA,CAAAC,iBAAA,GAAAA,iBAAA;AAcC;AAAAJ,aAAA,GAAAE,CAAA;AAiBDC,OAAA,CAAAE,mBAAA,GAAAA,mBAAA;AAGC;AAAAL,aAAA,GAAAE,CAAA;AAQDC,OAAA,CAAAG,WAAA,GAAAA,WAAA;AAoBC;AAAAN,aAAA,GAAAE,CAAA;AASDC,OAAA,CAAAI,cAAA,GAAAA,cAAA;AAOC;AAAAP,aAAA,GAAAE,CAAA;AAQDC,OAAA,CAAAK,cAAA,GAAAA,cAAA;AA5FA;;;;;;AAMA,SAAgBJ,iBAAiBA,CAACK,MAAmB,EAAEC,MAAmB;EAAA;EAAAV,aAAA,GAAAW,CAAA;EACxE,MAAMC,CAAC;EAAA;EAAA,CAAAZ,aAAA,GAAAE,CAAA,OAAG,IAAI,EAAC,CAAC;EAChB,MAAMW,IAAI;EAAA;EAAA,CAAAb,aAAA,GAAAE,CAAA,OAAGY,SAAS,CAACJ,MAAM,CAACK,QAAQ,GAAGN,MAAM,CAACM,QAAQ,CAAC;EACzD,MAAMC,IAAI;EAAA;EAAA,CAAAhB,aAAA,GAAAE,CAAA,OAAGY,SAAS,CAACJ,MAAM,CAACO,SAAS,GAAGR,MAAM,CAACQ,SAAS,CAAC;EAE3D,MAAMC,CAAC;EAAA;EAAA,CAAAlB,aAAA,GAAAE,CAAA,OACLiB,IAAI,CAACC,GAAG,CAACP,IAAI,GAAG,CAAC,CAAC,GAAGM,IAAI,CAACC,GAAG,CAACP,IAAI,GAAG,CAAC,CAAC,GACvCM,IAAI,CAACE,GAAG,CAACP,SAAS,CAACL,MAAM,CAACM,QAAQ,CAAC,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACP,SAAS,CAACJ,MAAM,CAACK,QAAQ,CAAC,CAAC,GAC3EI,IAAI,CAACC,GAAG,CAACJ,IAAI,GAAG,CAAC,CAAC,GAAGG,IAAI,CAACC,GAAG,CAACJ,IAAI,GAAG,CAAC,CAAC;EAEzC,MAAMM,CAAC;EAAA;EAAA,CAAAtB,aAAA,GAAAE,CAAA,QAAG,CAAC,GAAGiB,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACK,IAAI,CAACN,CAAC,CAAC,EAAEC,IAAI,CAACK,IAAI,CAAC,CAAC,GAAGN,CAAC,CAAC,CAAC;EACxD,MAAMO,QAAQ;EAAA;EAAA,CAAAzB,aAAA,GAAAE,CAAA,QAAGU,CAAC,GAAGU,CAAC;EAAC;EAAAtB,aAAA,GAAAE,CAAA;EAEvB,OAAOiB,IAAI,CAACO,KAAK,CAACD,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAC3C;AAEA;;;;;AAKA,SAASX,SAASA,CAACa,OAAe;EAAA;EAAA3B,aAAA,GAAAW,CAAA;EAAAX,aAAA,GAAAE,CAAA;EAChC,OAAOyB,OAAO,IAAIR,IAAI,CAACS,EAAE,GAAG,GAAG,CAAC;AAClC;AAEA;;;;;;AAMA,SAAgBvB,mBAAmBA,CAACI,MAAmB,EAAEC,MAAmB;EAAA;EAAAV,aAAA,GAAAW,CAAA;EAC1E,MAAMkB,eAAe;EAAA;EAAA,CAAA7B,aAAA,GAAAE,CAAA,QAAGE,iBAAiB,CAACK,MAAM,EAAEC,MAAM,CAAC;EAAC;EAAAV,aAAA,GAAAE,CAAA;EAC1D,OAAOiB,IAAI,CAACO,KAAK,CAACG,eAAe,GAAG,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAC5D;AAEA;;;;;;AAMA,SAAgBvB,WAAWA,CACzBwB,MAAmB,EACnBC,WAA0B;EAAA;EAAA/B,aAAA,GAAAW,CAAA;EAAAX,aAAA,GAAAE,CAAA;EAE1B,IAAI6B,WAAW,CAACC,MAAM,KAAK,CAAC,EAAE;IAAA;IAAAhC,aAAA,GAAAiC,CAAA;IAAAjC,aAAA,GAAAE,CAAA;IAC5B,OAAO,IAAI;EACb,CAAC;EAAA;EAAA;IAAAF,aAAA,GAAAiC,CAAA;EAAA;EAED,IAAIC,OAAO;EAAA;EAAA,CAAAlC,aAAA,GAAAE,CAAA,QAAG6B,WAAW,CAAC,CAAC,CAAC;EAC5B,IAAII,WAAW;EAAA;EAAA,CAAAnC,aAAA,GAAAE,CAAA,QAAGE,iBAAiB,CAAC0B,MAAM,EAAEI,OAAO,CAAC;EAAC;EAAAlC,aAAA,GAAAE,CAAA;EAErD,KAAK,IAAIkC,CAAC;EAAA;EAAA,CAAApC,aAAA,GAAAE,CAAA,QAAG,CAAC,GAAEkC,CAAC,GAAGL,WAAW,CAACC,MAAM,EAAEI,CAAC,EAAE,EAAE;IAC3C,MAAMX,QAAQ;IAAA;IAAA,CAAAzB,aAAA,GAAAE,CAAA,QAAGE,iBAAiB,CAAC0B,MAAM,EAAEC,WAAW,CAACK,CAAC,CAAC,CAAC;IAAC;IAAApC,aAAA,GAAAE,CAAA;IAC3D,IAAIuB,QAAQ,GAAGU,WAAW,EAAE;MAAA;MAAAnC,aAAA,GAAAiC,CAAA;MAAAjC,aAAA,GAAAE,CAAA;MAC1BiC,WAAW,GAAGV,QAAQ;MAAC;MAAAzB,aAAA,GAAAE,CAAA;MACvBgC,OAAO,GAAGH,WAAW,CAACK,CAAC,CAAC;IAC1B,CAAC;IAAA;IAAA;MAAApC,aAAA,GAAAiC,CAAA;IAAA;EACH;EAAC;EAAAjC,aAAA,GAAAE,CAAA;EAED,OAAO;IAAEmC,UAAU,EAAEH,OAAO;IAAET,QAAQ,EAAEU;EAAW,CAAE;AACvD;AAEA;;;;;;;AAOA,SAAgB5B,cAAcA,CAC5B+B,MAAmB,EACnBC,KAAkB,EAClBC,WAAmB;EAAA;EAAAxC,aAAA,GAAAW,CAAA;EAEnB,MAAMc,QAAQ;EAAA;EAAA,CAAAzB,aAAA,GAAAE,CAAA,QAAGE,iBAAiB,CAACkC,MAAM,EAAEC,KAAK,CAAC;EAAC;EAAAvC,aAAA,GAAAE,CAAA;EAClD,OAAOuB,QAAQ,IAAIe,WAAW;AAChC;AAEA;;;;;;AAMA,SAAgBhC,cAAcA,CAC5BsB,MAAmB,EACnBC,WAA0B;EAAA;EAAA/B,aAAA,GAAAW,CAAA;EAAAX,aAAA,GAAAE,CAAA;EAE1B,OAAO6B,WAAW,CACfU,GAAG,CAACC,KAAK,IAAK;IAAA;IAAA1C,aAAA,GAAAW,CAAA;IAAAX,aAAA,GAAAE,CAAA;IAAA;MACbmC,UAAU,EAAEK,KAAK;MACjBjB,QAAQ,EAAErB,iBAAiB,CAAC0B,MAAM,EAAEY,KAAK;KAC1C;GAAC,CAAC,CACFC,IAAI,CAAC,CAACzB,CAAC,EAAEe,CAAC,KAAK;IAAA;IAAAjC,aAAA,GAAAW,CAAA;IAAAX,aAAA,GAAAE,CAAA;IAAA,OAAAgB,CAAC,CAACO,QAAQ,GAAGQ,CAAC,CAACR,QAAQ;EAAR,CAAQ,CAAC;AAC5C", "ignoreList": []}