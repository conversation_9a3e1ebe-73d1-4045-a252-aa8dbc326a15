ef1a3578502aa7ddc3090bf60b27513d
"use strict";

/* istanbul ignore next */
function cov_otmiqg058() {
  var path = "/Users/<USER>/WebstormProjects/goo/src/utils/distance.ts";
  var hash = "7b14809878e1b0be95f4e5b7372eb6c4329a6c1e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/WebstormProjects/goo/src/utils/distance.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 3,
          column: 46
        }
      },
      "2": {
        start: {
          line: 4,
          column: 0
        },
        end: {
          line: 4,
          column: 50
        }
      },
      "3": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 34
        }
      },
      "4": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 40
        }
      },
      "5": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 7,
          column: 40
        }
      },
      "6": {
        start: {
          line: 15,
          column: 14
        },
        end: {
          line: 15,
          column: 18
        }
      },
      "7": {
        start: {
          line: 16,
          column: 17
        },
        end: {
          line: 16,
          column: 61
        }
      },
      "8": {
        start: {
          line: 17,
          column: 17
        },
        end: {
          line: 17,
          column: 63
        }
      },
      "9": {
        start: {
          line: 18,
          column: 14
        },
        end: {
          line: 20,
          column: 51
        }
      },
      "10": {
        start: {
          line: 21,
          column: 14
        },
        end: {
          line: 21,
          column: 60
        }
      },
      "11": {
        start: {
          line: 22,
          column: 21
        },
        end: {
          line: 22,
          column: 26
        }
      },
      "12": {
        start: {
          line: 23,
          column: 4
        },
        end: {
          line: 23,
          column: 44
        }
      },
      "13": {
        start: {
          line: 31,
          column: 4
        },
        end: {
          line: 31,
          column: 37
        }
      },
      "14": {
        start: {
          line: 40,
          column: 28
        },
        end: {
          line: 40,
          column: 61
        }
      },
      "15": {
        start: {
          line: 41,
          column: 4
        },
        end: {
          line: 41,
          column: 61
        }
      },
      "16": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 52,
          column: 5
        }
      },
      "17": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 51,
          column: 20
        }
      },
      "18": {
        start: {
          line: 53,
          column: 18
        },
        end: {
          line: 53,
          column: 32
        }
      },
      "19": {
        start: {
          line: 54,
          column: 22
        },
        end: {
          line: 54,
          column: 56
        }
      },
      "20": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 61,
          column: 5
        }
      },
      "21": {
        start: {
          line: 55,
          column: 17
        },
        end: {
          line: 55,
          column: 18
        }
      },
      "22": {
        start: {
          line: 56,
          column: 25
        },
        end: {
          line: 56,
          column: 66
        }
      },
      "23": {
        start: {
          line: 57,
          column: 8
        },
        end: {
          line: 60,
          column: 9
        }
      },
      "24": {
        start: {
          line: 58,
          column: 12
        },
        end: {
          line: 58,
          column: 35
        }
      },
      "25": {
        start: {
          line: 59,
          column: 12
        },
        end: {
          line: 59,
          column: 37
        }
      },
      "26": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 62,
          column: 58
        }
      },
      "27": {
        start: {
          line: 72,
          column: 21
        },
        end: {
          line: 72,
          column: 53
        }
      },
      "28": {
        start: {
          line: 73,
          column: 4
        },
        end: {
          line: 73,
          column: 35
        }
      },
      "29": {
        start: {
          line: 82,
          column: 4
        },
        end: {
          line: 87,
          column: 49
        }
      },
      "30": {
        start: {
          line: 83,
          column: 23
        },
        end: {
          line: 86,
          column: 5
        }
      },
      "31": {
        start: {
          line: 87,
          column: 24
        },
        end: {
          line: 87,
          column: 47
        }
      }
    },
    fnMap: {
      "0": {
        name: "calculateDistance",
        decl: {
          start: {
            line: 14,
            column: 9
          },
          end: {
            line: 14,
            column: 26
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 24,
            column: 1
          }
        },
        line: 14
      },
      "1": {
        name: "toRadians",
        decl: {
          start: {
            line: 30,
            column: 9
          },
          end: {
            line: 30,
            column: 18
          }
        },
        loc: {
          start: {
            line: 30,
            column: 28
          },
          end: {
            line: 32,
            column: 1
          }
        },
        line: 30
      },
      "2": {
        name: "calculateDistanceKm",
        decl: {
          start: {
            line: 39,
            column: 9
          },
          end: {
            line: 39,
            column: 28
          }
        },
        loc: {
          start: {
            line: 39,
            column: 45
          },
          end: {
            line: 42,
            column: 1
          }
        },
        line: 39
      },
      "3": {
        name: "findClosest",
        decl: {
          start: {
            line: 49,
            column: 9
          },
          end: {
            line: 49,
            column: 20
          }
        },
        loc: {
          start: {
            line: 49,
            column: 42
          },
          end: {
            line: 63,
            column: 1
          }
        },
        line: 49
      },
      "4": {
        name: "isWithinRadius",
        decl: {
          start: {
            line: 71,
            column: 9
          },
          end: {
            line: 71,
            column: 23
          }
        },
        loc: {
          start: {
            line: 71,
            column: 52
          },
          end: {
            line: 74,
            column: 1
          }
        },
        line: 71
      },
      "5": {
        name: "sortByDistance",
        decl: {
          start: {
            line: 81,
            column: 9
          },
          end: {
            line: 81,
            column: 23
          }
        },
        loc: {
          start: {
            line: 81,
            column: 45
          },
          end: {
            line: 88,
            column: 1
          }
        },
        line: 81
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 83,
            column: 13
          },
          end: {
            line: 83,
            column: 14
          }
        },
        loc: {
          start: {
            line: 83,
            column: 23
          },
          end: {
            line: 86,
            column: 5
          }
        },
        line: 83
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 87,
            column: 14
          },
          end: {
            line: 87,
            column: 15
          }
        },
        loc: {
          start: {
            line: 87,
            column: 24
          },
          end: {
            line: 87,
            column: 47
          }
        },
        line: 87
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 50,
            column: 4
          },
          end: {
            line: 52,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 4
          },
          end: {
            line: 52,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "1": {
        loc: {
          start: {
            line: 57,
            column: 8
          },
          end: {
            line: 60,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 57,
            column: 8
          },
          end: {
            line: 60,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 57
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/WebstormProjects/goo/src/utils/distance.ts",
      mappings: ";;AAQA,8CAcC;AAiBD,kDAGC;AAQD,kCAoBC;AASD,wCAOC;AAQD,wCAUC;AAtGD;;;;;GAKG;AACH,SAAgB,iBAAiB,CAAC,MAAmB,EAAE,MAAmB;IACxE,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,0BAA0B;IAC1C,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1D,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;IAE5D,MAAM,CAAC,GACL,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;QACvC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC3E,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;IAE1C,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzD,MAAM,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;IAEvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,4BAA4B;AACvE,CAAC;AAED;;;;GAIG;AACH,SAAS,SAAS,CAAC,OAAe;IAChC,OAAO,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;AACnC,CAAC;AAED;;;;;GAKG;AACH,SAAgB,mBAAmB,CAAC,MAAmB,EAAE,MAAmB;IAC1E,MAAM,eAAe,GAAG,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC1D,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,0BAA0B;AACtF,CAAC;AAED;;;;;GAKG;AACH,SAAgB,WAAW,CACzB,MAAmB,EACnB,WAA0B;IAE1B,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;IAC7B,IAAI,WAAW,GAAG,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAErD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5C,MAAM,QAAQ,GAAG,iBAAiB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,IAAI,QAAQ,GAAG,WAAW,EAAE,CAAC;YAC3B,WAAW,GAAG,QAAQ,CAAC;YACvB,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC;AACxD,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,cAAc,CAC5B,MAAmB,EACnB,KAAkB,EAClB,WAAmB;IAEnB,MAAM,QAAQ,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAClD,OAAO,QAAQ,IAAI,WAAW,CAAC;AACjC,CAAC;AAED;;;;;GAKG;AACH,SAAgB,cAAc,CAC5B,MAAmB,EACnB,WAA0B;IAE1B,OAAO,WAAW;SACf,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACb,UAAU,EAAE,KAAK;QACjB,QAAQ,EAAE,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC;KAC3C,CAAC,CAAC;SACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC7C,CAAC",
      names: [],
      sources: ["/Users/<USER>/WebstormProjects/goo/src/utils/distance.ts"],
      sourcesContent: ["import { Coordinates } from '../models/Business';\n\n/**\n * Calculates the distance between two coordinates using the Haversine formula\n * @param coord1 - First coordinate\n * @param coord2 - Second coordinate\n * @returns Distance in miles\n */\nexport function calculateDistance(coord1: Coordinates, coord2: Coordinates): number {\n  const R = 3959; // Earth's radius in miles\n  const dLat = toRadians(coord2.latitude - coord1.latitude);\n  const dLon = toRadians(coord2.longitude - coord1.longitude);\n  \n  const a = \n    Math.sin(dLat / 2) * Math.sin(dLat / 2) +\n    Math.cos(toRadians(coord1.latitude)) * Math.cos(toRadians(coord2.latitude)) *\n    Math.sin(dLon / 2) * Math.sin(dLon / 2);\n  \n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n  const distance = R * c;\n  \n  return Math.round(distance * 100) / 100; // Round to 2 decimal places\n}\n\n/**\n * Converts degrees to radians\n * @param degrees - Angle in degrees\n * @returns Angle in radians\n */\nfunction toRadians(degrees: number): number {\n  return degrees * (Math.PI / 180);\n}\n\n/**\n * Calculates the distance between two coordinates in kilometers\n * @param coord1 - First coordinate\n * @param coord2 - Second coordinate\n * @returns Distance in kilometers\n */\nexport function calculateDistanceKm(coord1: Coordinates, coord2: Coordinates): number {\n  const distanceInMiles = calculateDistance(coord1, coord2);\n  return Math.round(distanceInMiles * 1.60934 * 100) / 100; // Convert to km and round\n}\n\n/**\n * Finds the closest coordinate from an array of coordinates\n * @param target - Target coordinate\n * @param coordinates - Array of coordinates to search\n * @returns Closest coordinate and its distance\n */\nexport function findClosest(\n  target: Coordinates,\n  coordinates: Coordinates[]\n): { coordinate: Coordinates; distance: number } | null {\n  if (coordinates.length === 0) {\n    return null;\n  }\n\n  let closest = coordinates[0];\n  let minDistance = calculateDistance(target, closest);\n\n  for (let i = 1; i < coordinates.length; i++) {\n    const distance = calculateDistance(target, coordinates[i]);\n    if (distance < minDistance) {\n      minDistance = distance;\n      closest = coordinates[i];\n    }\n  }\n\n  return { coordinate: closest, distance: minDistance };\n}\n\n/**\n * Checks if a coordinate is within a certain radius of a center point\n * @param center - Center coordinate\n * @param point - Point to check\n * @param radiusMiles - Radius in miles\n * @returns True if point is within radius\n */\nexport function isWithinRadius(\n  center: Coordinates,\n  point: Coordinates,\n  radiusMiles: number\n): boolean {\n  const distance = calculateDistance(center, point);\n  return distance <= radiusMiles;\n}\n\n/**\n * Sorts an array of coordinates by distance from a target point\n * @param target - Target coordinate\n * @param coordinates - Array of coordinates to sort\n * @returns Sorted array with distances\n */\nexport function sortByDistance(\n  target: Coordinates,\n  coordinates: Coordinates[]\n): Array<{ coordinate: Coordinates; distance: number }> {\n  return coordinates\n    .map(coord => ({\n      coordinate: coord,\n      distance: calculateDistance(target, coord)\n    }))\n    .sort((a, b) => a.distance - b.distance);\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "7b14809878e1b0be95f4e5b7372eb6c4329a6c1e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_otmiqg058 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_otmiqg058();
cov_otmiqg058().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_otmiqg058().s[1]++;
exports.calculateDistance = calculateDistance;
/* istanbul ignore next */
cov_otmiqg058().s[2]++;
exports.calculateDistanceKm = calculateDistanceKm;
/* istanbul ignore next */
cov_otmiqg058().s[3]++;
exports.findClosest = findClosest;
/* istanbul ignore next */
cov_otmiqg058().s[4]++;
exports.isWithinRadius = isWithinRadius;
/* istanbul ignore next */
cov_otmiqg058().s[5]++;
exports.sortByDistance = sortByDistance;
/**
 * Calculates the distance between two coordinates using the Haversine formula
 * @param coord1 - First coordinate
 * @param coord2 - Second coordinate
 * @returns Distance in miles
 */
function calculateDistance(coord1, coord2) {
  /* istanbul ignore next */
  cov_otmiqg058().f[0]++;
  const R =
  /* istanbul ignore next */
  (cov_otmiqg058().s[6]++, 3959); // Earth's radius in miles
  const dLat =
  /* istanbul ignore next */
  (cov_otmiqg058().s[7]++, toRadians(coord2.latitude - coord1.latitude));
  const dLon =
  /* istanbul ignore next */
  (cov_otmiqg058().s[8]++, toRadians(coord2.longitude - coord1.longitude));
  const a =
  /* istanbul ignore next */
  (cov_otmiqg058().s[9]++, Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(toRadians(coord1.latitude)) * Math.cos(toRadians(coord2.latitude)) * Math.sin(dLon / 2) * Math.sin(dLon / 2));
  const c =
  /* istanbul ignore next */
  (cov_otmiqg058().s[10]++, 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)));
  const distance =
  /* istanbul ignore next */
  (cov_otmiqg058().s[11]++, R * c);
  /* istanbul ignore next */
  cov_otmiqg058().s[12]++;
  return Math.round(distance * 100) / 100; // Round to 2 decimal places
}
/**
 * Converts degrees to radians
 * @param degrees - Angle in degrees
 * @returns Angle in radians
 */
function toRadians(degrees) {
  /* istanbul ignore next */
  cov_otmiqg058().f[1]++;
  cov_otmiqg058().s[13]++;
  return degrees * (Math.PI / 180);
}
/**
 * Calculates the distance between two coordinates in kilometers
 * @param coord1 - First coordinate
 * @param coord2 - Second coordinate
 * @returns Distance in kilometers
 */
function calculateDistanceKm(coord1, coord2) {
  /* istanbul ignore next */
  cov_otmiqg058().f[2]++;
  const distanceInMiles =
  /* istanbul ignore next */
  (cov_otmiqg058().s[14]++, calculateDistance(coord1, coord2));
  /* istanbul ignore next */
  cov_otmiqg058().s[15]++;
  return Math.round(distanceInMiles * 1.60934 * 100) / 100; // Convert to km and round
}
/**
 * Finds the closest coordinate from an array of coordinates
 * @param target - Target coordinate
 * @param coordinates - Array of coordinates to search
 * @returns Closest coordinate and its distance
 */
function findClosest(target, coordinates) {
  /* istanbul ignore next */
  cov_otmiqg058().f[3]++;
  cov_otmiqg058().s[16]++;
  if (coordinates.length === 0) {
    /* istanbul ignore next */
    cov_otmiqg058().b[0][0]++;
    cov_otmiqg058().s[17]++;
    return null;
  } else
  /* istanbul ignore next */
  {
    cov_otmiqg058().b[0][1]++;
  }
  let closest =
  /* istanbul ignore next */
  (cov_otmiqg058().s[18]++, coordinates[0]);
  let minDistance =
  /* istanbul ignore next */
  (cov_otmiqg058().s[19]++, calculateDistance(target, closest));
  /* istanbul ignore next */
  cov_otmiqg058().s[20]++;
  for (let i =
  /* istanbul ignore next */
  (cov_otmiqg058().s[21]++, 1); i < coordinates.length; i++) {
    const distance =
    /* istanbul ignore next */
    (cov_otmiqg058().s[22]++, calculateDistance(target, coordinates[i]));
    /* istanbul ignore next */
    cov_otmiqg058().s[23]++;
    if (distance < minDistance) {
      /* istanbul ignore next */
      cov_otmiqg058().b[1][0]++;
      cov_otmiqg058().s[24]++;
      minDistance = distance;
      /* istanbul ignore next */
      cov_otmiqg058().s[25]++;
      closest = coordinates[i];
    } else
    /* istanbul ignore next */
    {
      cov_otmiqg058().b[1][1]++;
    }
  }
  /* istanbul ignore next */
  cov_otmiqg058().s[26]++;
  return {
    coordinate: closest,
    distance: minDistance
  };
}
/**
 * Checks if a coordinate is within a certain radius of a center point
 * @param center - Center coordinate
 * @param point - Point to check
 * @param radiusMiles - Radius in miles
 * @returns True if point is within radius
 */
function isWithinRadius(center, point, radiusMiles) {
  /* istanbul ignore next */
  cov_otmiqg058().f[4]++;
  const distance =
  /* istanbul ignore next */
  (cov_otmiqg058().s[27]++, calculateDistance(center, point));
  /* istanbul ignore next */
  cov_otmiqg058().s[28]++;
  return distance <= radiusMiles;
}
/**
 * Sorts an array of coordinates by distance from a target point
 * @param target - Target coordinate
 * @param coordinates - Array of coordinates to sort
 * @returns Sorted array with distances
 */
function sortByDistance(target, coordinates) {
  /* istanbul ignore next */
  cov_otmiqg058().f[5]++;
  cov_otmiqg058().s[29]++;
  return coordinates.map(coord => {
    /* istanbul ignore next */
    cov_otmiqg058().f[6]++;
    cov_otmiqg058().s[30]++;
    return {
      coordinate: coord,
      distance: calculateDistance(target, coord)
    };
  }).sort((a, b) => {
    /* istanbul ignore next */
    cov_otmiqg058().f[7]++;
    cov_otmiqg058().s[31]++;
    return a.distance - b.distance;
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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