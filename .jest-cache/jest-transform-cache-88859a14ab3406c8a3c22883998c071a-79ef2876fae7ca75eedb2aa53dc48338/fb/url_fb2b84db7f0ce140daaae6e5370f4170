1243db2e9b18e2b1d675ebcc5ae08b72
"use strict";

/**
 * URL utility functions for normalizing, validating, and cleaning URLs
 */
/* istanbul ignore next */
function cov_1l6gh6qwai() {
  var path = "/Users/<USER>/WebstormProjects/goo/src/utils/url.ts";
  var hash = "5f6faa52f4d5efc4d8677a249d41e7b93a7b4603";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/WebstormProjects/goo/src/utils/url.ts",
    statementMap: {
      "0": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 36
        }
      },
      "2": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 7,
          column: 32
        }
      },
      "3": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 8,
          column: 38
        }
      },
      "4": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 34
        }
      },
      "5": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 28
        }
      },
      "6": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 46
        }
      },
      "7": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 58
        }
      },
      "8": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 21,
          column: 5
        }
      },
      "9": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 20,
          column: 18
        }
      },
      "10": {
        start: {
          line: 22,
          column: 21
        },
        end: {
          line: 22,
          column: 31
        }
      },
      "11": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 26,
          column: 5
        }
      },
      "12": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 45
        }
      },
      "13": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 45,
          column: 5
        }
      },
      "14": {
        start: {
          line: 29,
          column: 23
        },
        end: {
          line: 29,
          column: 42
        }
      },
      "15": {
        start: {
          line: 30,
          column: 8
        },
        end: {
          line: 38,
          column: 9
        }
      },
      "16": {
        start: {
          line: 32,
          column: 12
        },
        end: {
          line: 32,
          column: 62
        }
      },
      "17": {
        start: {
          line: 34,
          column: 13
        },
        end: {
          line: 38,
          column: 9
        }
      },
      "18": {
        start: {
          line: 36,
          column: 12
        },
        end: {
          line: 36,
          column: 59
        }
      },
      "19": {
        start: {
          line: 37,
          column: 12
        },
        end: {
          line: 37,
          column: 43
        }
      },
      "20": {
        start: {
          line: 42,
          column: 8
        },
        end: {
          line: 44,
          column: 9
        }
      },
      "21": {
        start: {
          line: 43,
          column: 12
        },
        end: {
          line: 43,
          column: 49
        }
      },
      "22": {
        start: {
          line: 46,
          column: 4
        },
        end: {
          line: 46,
          column: 22
        }
      },
      "23": {
        start: {
          line: 54,
          column: 4
        },
        end: {
          line: 56,
          column: 5
        }
      },
      "24": {
        start: {
          line: 55,
          column: 8
        },
        end: {
          line: 55,
          column: 21
        }
      },
      "25": {
        start: {
          line: 57,
          column: 4
        },
        end: {
          line: 63,
          column: 5
        }
      },
      "26": {
        start: {
          line: 58,
          column: 23
        },
        end: {
          line: 58,
          column: 35
        }
      },
      "27": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 59,
          column: 75
        }
      },
      "28": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 62,
          column: 21
        }
      },
      "29": {
        start: {
          line: 71,
          column: 4
        },
        end: {
          line: 73,
          column: 5
        }
      },
      "30": {
        start: {
          line: 72,
          column: 8
        },
        end: {
          line: 72,
          column: 18
        }
      },
      "31": {
        start: {
          line: 74,
          column: 4
        },
        end: {
          line: 80,
          column: 5
        }
      },
      "32": {
        start: {
          line: 75,
          column: 23
        },
        end: {
          line: 75,
          column: 35
        }
      },
      "33": {
        start: {
          line: 76,
          column: 8
        },
        end: {
          line: 76,
          column: 31
        }
      },
      "34": {
        start: {
          line: 79,
          column: 8
        },
        end: {
          line: 79,
          column: 18
        }
      },
      "35": {
        start: {
          line: 88,
          column: 4
        },
        end: {
          line: 90,
          column: 5
        }
      },
      "36": {
        start: {
          line: 89,
          column: 8
        },
        end: {
          line: 89,
          column: 18
        }
      },
      "37": {
        start: {
          line: 91,
          column: 20
        },
        end: {
          line: 91,
          column: 30
        }
      },
      "38": {
        start: {
          line: 92,
          column: 4
        },
        end: {
          line: 94,
          column: 5
        }
      },
      "39": {
        start: {
          line: 93,
          column: 8
        },
        end: {
          line: 93,
          column: 23
        }
      },
      "40": {
        start: {
          line: 95,
          column: 4
        },
        end: {
          line: 95,
          column: 32
        }
      },
      "41": {
        start: {
          line: 103,
          column: 4
        },
        end: {
          line: 105,
          column: 5
        }
      },
      "42": {
        start: {
          line: 104,
          column: 8
        },
        end: {
          line: 104,
          column: 18
        }
      },
      "43": {
        start: {
          line: 106,
          column: 4
        },
        end: {
          line: 142,
          column: 5
        }
      },
      "44": {
        start: {
          line: 107,
          column: 23
        },
        end: {
          line: 107,
          column: 35
        }
      },
      "45": {
        start: {
          line: 108,
          column: 23
        },
        end: {
          line: 108,
          column: 57
        }
      },
      "46": {
        start: {
          line: 110,
          column: 31
        },
        end: {
          line: 125,
          column: 9
        }
      },
      "47": {
        start: {
          line: 127,
          column: 8
        },
        end: {
          line: 129,
          column: 11
        }
      },
      "48": {
        start: {
          line: 128,
          column: 12
        },
        end: {
          line: 128,
          column: 33
        }
      },
      "49": {
        start: {
          line: 131,
          column: 8
        },
        end: {
          line: 131,
          column: 42
        }
      },
      "50": {
        start: {
          line: 133,
          column: 8
        },
        end: {
          line: 135,
          column: 9
        }
      },
      "51": {
        start: {
          line: 134,
          column: 12
        },
        end: {
          line: 134,
          column: 56
        }
      },
      "52": {
        start: {
          line: 137,
          column: 8
        },
        end: {
          line: 137,
          column: 33
        }
      },
      "53": {
        start: {
          line: 141,
          column: 8
        },
        end: {
          line: 141,
          column: 19
        }
      },
      "54": {
        start: {
          line: 151,
          column: 4
        },
        end: {
          line: 162,
          column: 5
        }
      },
      "55": {
        start: {
          line: 152,
          column: 26
        },
        end: {
          line: 157,
          column: 9
        }
      },
      "56": {
        start: {
          line: 153,
          column: 27
        },
        end: {
          line: 153,
          column: 53
        }
      },
      "57": {
        start: {
          line: 155,
          column: 29
        },
        end: {
          line: 155,
          column: 66
        }
      },
      "58": {
        start: {
          line: 156,
          column: 12
        },
        end: {
          line: 156,
          column: 81
        }
      },
      "59": {
        start: {
          line: 158,
          column: 8
        },
        end: {
          line: 158,
          column: 51
        }
      },
      "60": {
        start: {
          line: 161,
          column: 8
        },
        end: {
          line: 161,
          column: 21
        }
      },
      "61": {
        start: {
          line: 170,
          column: 4
        },
        end: {
          line: 172,
          column: 5
        }
      },
      "62": {
        start: {
          line: 171,
          column: 8
        },
        end: {
          line: 171,
          column: 20
        }
      },
      "63": {
        start: {
          line: 173,
          column: 23
        },
        end: {
          line: 173,
          column: 40
        }
      },
      "64": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 174,
          column: 54
        }
      }
    },
    fnMap: {
      "0": {
        name: "normalizeUrl",
        decl: {
          start: {
            line: 18,
            column: 9
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 27
          },
          end: {
            line: 47,
            column: 1
          }
        },
        line: 18
      },
      "1": {
        name: "isValidUrl",
        decl: {
          start: {
            line: 53,
            column: 9
          },
          end: {
            line: 53,
            column: 19
          }
        },
        loc: {
          start: {
            line: 53,
            column: 25
          },
          end: {
            line: 64,
            column: 1
          }
        },
        line: 53
      },
      "2": {
        name: "extractDomain",
        decl: {
          start: {
            line: 70,
            column: 9
          },
          end: {
            line: 70,
            column: 22
          }
        },
        loc: {
          start: {
            line: 70,
            column: 28
          },
          end: {
            line: 81,
            column: 1
          }
        },
        line: 70
      },
      "3": {
        name: "addProtocol",
        decl: {
          start: {
            line: 87,
            column: 9
          },
          end: {
            line: 87,
            column: 20
          }
        },
        loc: {
          start: {
            line: 87,
            column: 26
          },
          end: {
            line: 96,
            column: 1
          }
        },
        line: 87
      },
      "4": {
        name: "cleanUrl",
        decl: {
          start: {
            line: 102,
            column: 9
          },
          end: {
            line: 102,
            column: 17
          }
        },
        loc: {
          start: {
            line: 102,
            column: 23
          },
          end: {
            line: 143,
            column: 1
          }
        },
        line: 102
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 127,
            column: 31
          },
          end: {
            line: 127,
            column: 32
          }
        },
        loc: {
          start: {
            line: 127,
            column: 40
          },
          end: {
            line: 129,
            column: 9
          }
        },
        line: 127
      },
      "6": {
        name: "areUrlsEquivalent",
        decl: {
          start: {
            line: 150,
            column: 9
          },
          end: {
            line: 150,
            column: 26
          }
        },
        loc: {
          start: {
            line: 150,
            column: 39
          },
          end: {
            line: 163,
            column: 1
          }
        },
        line: 150
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 152,
            column: 26
          },
          end: {
            line: 152,
            column: 27
          }
        },
        loc: {
          start: {
            line: 152,
            column: 35
          },
          end: {
            line: 157,
            column: 9
          }
        },
        line: 152
      },
      "8": {
        name: "validateAndNormalizeUrl",
        decl: {
          start: {
            line: 169,
            column: 9
          },
          end: {
            line: 169,
            column: 32
          }
        },
        loc: {
          start: {
            line: 169,
            column: 38
          },
          end: {
            line: 175,
            column: 1
          }
        },
        line: 169
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 19,
            column: 4
          },
          end: {
            line: 21,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 4
          },
          end: {
            line: 21,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "1": {
        loc: {
          start: {
            line: 19,
            column: 8
          },
          end: {
            line: 19,
            column: 27
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 8
          },
          end: {
            line: 19,
            column: 12
          }
        }, {
          start: {
            line: 19,
            column: 16
          },
          end: {
            line: 19,
            column: 27
          }
        }],
        line: 19
      },
      "2": {
        loc: {
          start: {
            line: 24,
            column: 4
          },
          end: {
            line: 26,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 24,
            column: 4
          },
          end: {
            line: 26,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 24
      },
      "3": {
        loc: {
          start: {
            line: 24,
            column: 8
          },
          end: {
            line: 24,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 8
          },
          end: {
            line: 24,
            column: 41
          }
        }, {
          start: {
            line: 24,
            column: 45
          },
          end: {
            line: 24,
            column: 79
          }
        }],
        line: 24
      },
      "4": {
        loc: {
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 38,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 38,
            column: 9
          }
        }, {
          start: {
            line: 34,
            column: 13
          },
          end: {
            line: 38,
            column: 9
          }
        }],
        line: 30
      },
      "5": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 35
          }
        }, {
          start: {
            line: 30,
            column: 39
          },
          end: {
            line: 30,
            column: 59
          }
        }, {
          start: {
            line: 30,
            column: 63
          },
          end: {
            line: 30,
            column: 81
          }
        }],
        line: 30
      },
      "6": {
        loc: {
          start: {
            line: 34,
            column: 13
          },
          end: {
            line: 38,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 13
          },
          end: {
            line: 38,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "7": {
        loc: {
          start: {
            line: 34,
            column: 17
          },
          end: {
            line: 34,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 34,
            column: 17
          },
          end: {
            line: 34,
            column: 46
          }
        }, {
          start: {
            line: 34,
            column: 50
          },
          end: {
            line: 34,
            column: 73
          }
        }],
        line: 34
      },
      "8": {
        loc: {
          start: {
            line: 42,
            column: 8
          },
          end: {
            line: 44,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 8
          },
          end: {
            line: 44,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "9": {
        loc: {
          start: {
            line: 42,
            column: 12
          },
          end: {
            line: 42,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 12
          },
          end: {
            line: 42,
            column: 36
          }
        }, {
          start: {
            line: 42,
            column: 40
          },
          end: {
            line: 42,
            column: 61
          }
        }],
        line: 42
      },
      "10": {
        loc: {
          start: {
            line: 54,
            column: 4
          },
          end: {
            line: 56,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 54,
            column: 4
          },
          end: {
            line: 56,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 54
      },
      "11": {
        loc: {
          start: {
            line: 54,
            column: 8
          },
          end: {
            line: 54,
            column: 27
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 54,
            column: 8
          },
          end: {
            line: 54,
            column: 12
          }
        }, {
          start: {
            line: 54,
            column: 16
          },
          end: {
            line: 54,
            column: 27
          }
        }],
        line: 54
      },
      "12": {
        loc: {
          start: {
            line: 59,
            column: 15
          },
          end: {
            line: 59,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 59,
            column: 15
          },
          end: {
            line: 59,
            column: 42
          }
        }, {
          start: {
            line: 59,
            column: 46
          },
          end: {
            line: 59,
            column: 74
          }
        }],
        line: 59
      },
      "13": {
        loc: {
          start: {
            line: 71,
            column: 4
          },
          end: {
            line: 73,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 71,
            column: 4
          },
          end: {
            line: 73,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 71
      },
      "14": {
        loc: {
          start: {
            line: 71,
            column: 8
          },
          end: {
            line: 71,
            column: 27
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 71,
            column: 8
          },
          end: {
            line: 71,
            column: 12
          }
        }, {
          start: {
            line: 71,
            column: 16
          },
          end: {
            line: 71,
            column: 27
          }
        }],
        line: 71
      },
      "15": {
        loc: {
          start: {
            line: 88,
            column: 4
          },
          end: {
            line: 90,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 88,
            column: 4
          },
          end: {
            line: 90,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 88
      },
      "16": {
        loc: {
          start: {
            line: 88,
            column: 8
          },
          end: {
            line: 88,
            column: 27
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 88,
            column: 8
          },
          end: {
            line: 88,
            column: 12
          }
        }, {
          start: {
            line: 88,
            column: 16
          },
          end: {
            line: 88,
            column: 27
          }
        }],
        line: 88
      },
      "17": {
        loc: {
          start: {
            line: 92,
            column: 4
          },
          end: {
            line: 94,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 92,
            column: 4
          },
          end: {
            line: 94,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 92
      },
      "18": {
        loc: {
          start: {
            line: 92,
            column: 8
          },
          end: {
            line: 92,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 92,
            column: 8
          },
          end: {
            line: 92,
            column: 37
          }
        }, {
          start: {
            line: 92,
            column: 41
          },
          end: {
            line: 92,
            column: 71
          }
        }],
        line: 92
      },
      "19": {
        loc: {
          start: {
            line: 103,
            column: 4
          },
          end: {
            line: 105,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 103,
            column: 4
          },
          end: {
            line: 105,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 103
      },
      "20": {
        loc: {
          start: {
            line: 103,
            column: 8
          },
          end: {
            line: 103,
            column: 27
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 103,
            column: 8
          },
          end: {
            line: 103,
            column: 12
          }
        }, {
          start: {
            line: 103,
            column: 16
          },
          end: {
            line: 103,
            column: 27
          }
        }],
        line: 103
      },
      "21": {
        loc: {
          start: {
            line: 133,
            column: 8
          },
          end: {
            line: 135,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 133,
            column: 8
          },
          end: {
            line: 135,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 133
      },
      "22": {
        loc: {
          start: {
            line: 133,
            column: 12
          },
          end: {
            line: 133,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 133,
            column: 12
          },
          end: {
            line: 133,
            column: 35
          }
        }, {
          start: {
            line: 133,
            column: 39
          },
          end: {
            line: 133,
            column: 59
          }
        }, {
          start: {
            line: 133,
            column: 63
          },
          end: {
            line: 133,
            column: 81
          }
        }],
        line: 133
      },
      "23": {
        loc: {
          start: {
            line: 170,
            column: 4
          },
          end: {
            line: 172,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 170,
            column: 4
          },
          end: {
            line: 172,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 170
      },
      "24": {
        loc: {
          start: {
            line: 170,
            column: 8
          },
          end: {
            line: 170,
            column: 27
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 170,
            column: 8
          },
          end: {
            line: 170,
            column: 12
          }
        }, {
          start: {
            line: 170,
            column: 16
          },
          end: {
            line: 170,
            column: 27
          }
        }],
        line: 170
      },
      "25": {
        loc: {
          start: {
            line: 174,
            column: 11
          },
          end: {
            line: 174,
            column: 53
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 174,
            column: 36
          },
          end: {
            line: 174,
            column: 46
          }
        }, {
          start: {
            line: 174,
            column: 49
          },
          end: {
            line: 174,
            column: 53
          }
        }],
        line: 174
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/WebstormProjects/goo/src/utils/url.ts",
      mappings: ";AAAA;;GAEG;;AAOH,oCA+BC;AAOD,gCAWC;AAOD,sCAWC;AAOD,kCAYC;AAOD,4BA8CC;AAQD,8CAaC;AAOD,0DAOC;AAnLD;;;;GAIG;AACH,SAAgB,YAAY,CAAC,GAAW;IACtC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;QACxB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,IAAI,UAAU,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;IAE5B,0BAA0B;IAC1B,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;QAC5E,UAAU,GAAG,WAAW,UAAU,EAAE,CAAC;IACvC,CAAC;IAED,+CAA+C;IAC/C,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;QACnC,IAAI,MAAM,CAAC,QAAQ,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,IAAI,MAAM,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;YAC1E,0DAA0D;YAC1D,UAAU,GAAG,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,IAAI,EAAE,CAAC;QACpD,CAAC;aAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,QAAQ,KAAK,GAAG,EAAE,CAAC;YACpE,4CAA4C;YAC5C,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/C,UAAU,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QACjC,CAAC;IACH,CAAC;IAAC,MAAM,CAAC;QACP,+DAA+D;QAC/D,IAAI,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtD,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;;;GAIG;AACH,SAAgB,UAAU,CAAC,GAAW;IACpC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;QACxB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5B,OAAO,MAAM,CAAC,QAAQ,KAAK,OAAO,IAAI,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAAC;IACrE,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,aAAa,CAAC,GAAW;IACvC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;QACxB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5B,OAAO,MAAM,CAAC,QAAQ,CAAC;IACzB,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,WAAW,CAAC,GAAW;IACrC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;QACxB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;IAE3B,IAAI,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;QACpE,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,OAAO,WAAW,OAAO,EAAE,CAAC;AAC9B,CAAC;AAED;;;;GAIG;AACH,SAAgB,QAAQ,CAAC,GAAW;IAClC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;QACxB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5B,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAElD,wCAAwC;QACxC,MAAM,cAAc,GAAG;YACrB,YAAY;YACZ,YAAY;YACZ,cAAc;YACd,UAAU;YACV,aAAa;YACb,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,OAAO;YACP,SAAS;YACT,KAAK;YACL,KAAK;YACL,QAAQ;YACR,QAAQ;SACT,CAAC;QAEF,6BAA6B;QAC7B,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC7B,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,cAAc;QACd,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAElC,yEAAyE;QACzE,IAAI,MAAM,CAAC,QAAQ,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,IAAI,MAAM,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;YAC1E,OAAO,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,IAAI,EAAE,CAAC;QAC9C,CAAC;QAED,sDAAsD;QACtD,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC;IAC3B,CAAC;IAAC,MAAM,CAAC;QACP,wCAAwC;QACxC,OAAO,GAAG,CAAC;IACb,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,SAAgB,iBAAiB,CAAC,IAAY,EAAE,IAAY;IAC1D,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,CAAC,GAAW,EAAE,EAAE;YAChC,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1C,mCAAmC;YACnC,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YACvD,OAAO,GAAG,QAAQ,GAAG,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QACvE,CAAC,CAAC;QAEF,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,uBAAuB,CAAC,GAAW;IACjD,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,UAAU,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;IACrC,OAAO,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;AACpD,CAAC",
      names: [],
      sources: ["/Users/<USER>/WebstormProjects/goo/src/utils/url.ts"],
      sourcesContent: ["/**\n * URL utility functions for normalizing, validating, and cleaning URLs\n */\n\n/**\n * Normalizes a URL by adding protocol and removing trailing slash\n * @param url - The URL to normalize\n * @returns Normalized URL\n */\nexport function normalizeUrl(url: string): string {\n  if (!url || !url.trim()) {\n    return '';\n  }\n\n  let normalized = url.trim();\n\n  // Add protocol if missing\n  if (!normalized.startsWith('http://') && !normalized.startsWith('https://')) {\n    normalized = `https://${normalized}`;\n  }\n\n  // Remove trailing slash (except for root path)\n  try {\n    const urlObj = new URL(normalized);\n    if (urlObj.pathname === '/' && urlObj.search === '' && urlObj.hash === '') {\n      // For root path with no query/hash, remove trailing slash\n      normalized = `${urlObj.protocol}//${urlObj.host}`;\n    } else if (urlObj.pathname.endsWith('/') && urlObj.pathname !== '/') {\n      // For non-root paths, remove trailing slash\n      urlObj.pathname = urlObj.pathname.slice(0, -1);\n      normalized = urlObj.toString();\n    }\n  } catch {\n    // If URL parsing fails, just remove trailing slash from string\n    if (normalized.endsWith('/') && normalized.length > 8) {\n      normalized = normalized.slice(0, -1);\n    }\n  }\n\n  return normalized;\n}\n\n/**\n * Validates if a string is a valid HTTP/HTTPS URL\n * @param url - The URL to validate\n * @returns True if valid, false otherwise\n */\nexport function isValidUrl(url: string): boolean {\n  if (!url || !url.trim()) {\n    return false;\n  }\n\n  try {\n    const urlObj = new URL(url);\n    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Extracts the domain from a URL\n * @param url - The URL to extract domain from\n * @returns Domain name or empty string if invalid\n */\nexport function extractDomain(url: string): string {\n  if (!url || !url.trim()) {\n    return '';\n  }\n\n  try {\n    const urlObj = new URL(url);\n    return urlObj.hostname;\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Adds HTTPS protocol to a URL if no protocol is present\n * @param url - The URL to add protocol to\n * @returns URL with protocol\n */\nexport function addProtocol(url: string): string {\n  if (!url || !url.trim()) {\n    return '';\n  }\n\n  const trimmed = url.trim();\n  \n  if (trimmed.startsWith('http://') || trimmed.startsWith('https://')) {\n    return trimmed;\n  }\n\n  return `https://${trimmed}`;\n}\n\n/**\n * Removes tracking parameters and cleans up URL\n * @param url - The URL to clean\n * @returns Cleaned URL\n */\nexport function cleanUrl(url: string): string {\n  if (!url || !url.trim()) {\n    return '';\n  }\n\n  try {\n    const urlObj = new URL(url);\n    const params = new URLSearchParams(urlObj.search);\n\n    // List of tracking parameters to remove\n    const trackingParams = [\n      'utm_source',\n      'utm_medium',\n      'utm_campaign',\n      'utm_term',\n      'utm_content',\n      'fbclid',\n      'gclid',\n      'gclsrc',\n      'dclid',\n      'msclkid',\n      '_ga',\n      '_gl',\n      'mc_cid',\n      'mc_eid'\n    ];\n\n    // Remove tracking parameters\n    trackingParams.forEach(param => {\n      params.delete(param);\n    });\n\n    // Rebuild URL\n    urlObj.search = params.toString();\n\n    // Remove trailing slash for root path only if no query parameters remain\n    if (urlObj.pathname === '/' && urlObj.search === '' && urlObj.hash === '') {\n      return `${urlObj.protocol}//${urlObj.host}`;\n    }\n\n    // For root path with query parameters, keep the slash\n    return urlObj.toString();\n  } catch {\n    // If URL parsing fails, return original\n    return url;\n  }\n}\n\n/**\n * Checks if two URLs point to the same resource (ignoring protocol and www)\n * @param url1 - First URL\n * @param url2 - Second URL\n * @returns True if URLs are equivalent\n */\nexport function areUrlsEquivalent(url1: string, url2: string): boolean {\n  try {\n    const normalize = (url: string) => {\n      const urlObj = new URL(normalizeUrl(url));\n      // Remove www prefix for comparison\n      const hostname = urlObj.hostname.replace(/^www\\./, '');\n      return `${hostname}${urlObj.pathname}${urlObj.search}${urlObj.hash}`;\n    };\n\n    return normalize(url1) === normalize(url2);\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Validates and normalizes a website URL\n * @param url - The URL to validate and normalize\n * @returns Normalized URL or null if invalid\n */\nexport function validateAndNormalizeUrl(url: string): string | null {\n  if (!url || !url.trim()) {\n    return null;\n  }\n\n  const normalized = normalizeUrl(url);\n  return isValidUrl(normalized) ? normalized : null;\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5f6faa52f4d5efc4d8677a249d41e7b93a7b4603"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1l6gh6qwai = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1l6gh6qwai();
cov_1l6gh6qwai().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1l6gh6qwai().s[1]++;
exports.normalizeUrl = normalizeUrl;
/* istanbul ignore next */
cov_1l6gh6qwai().s[2]++;
exports.isValidUrl = isValidUrl;
/* istanbul ignore next */
cov_1l6gh6qwai().s[3]++;
exports.extractDomain = extractDomain;
/* istanbul ignore next */
cov_1l6gh6qwai().s[4]++;
exports.addProtocol = addProtocol;
/* istanbul ignore next */
cov_1l6gh6qwai().s[5]++;
exports.cleanUrl = cleanUrl;
/* istanbul ignore next */
cov_1l6gh6qwai().s[6]++;
exports.areUrlsEquivalent = areUrlsEquivalent;
/* istanbul ignore next */
cov_1l6gh6qwai().s[7]++;
exports.validateAndNormalizeUrl = validateAndNormalizeUrl;
/**
 * Normalizes a URL by adding protocol and removing trailing slash
 * @param url - The URL to normalize
 * @returns Normalized URL
 */
function normalizeUrl(url) {
  /* istanbul ignore next */
  cov_1l6gh6qwai().f[0]++;
  cov_1l6gh6qwai().s[8]++;
  if (
  /* istanbul ignore next */
  (cov_1l6gh6qwai().b[1][0]++, !url) ||
  /* istanbul ignore next */
  (cov_1l6gh6qwai().b[1][1]++, !url.trim())) {
    /* istanbul ignore next */
    cov_1l6gh6qwai().b[0][0]++;
    cov_1l6gh6qwai().s[9]++;
    return '';
  } else
  /* istanbul ignore next */
  {
    cov_1l6gh6qwai().b[0][1]++;
  }
  let normalized =
  /* istanbul ignore next */
  (cov_1l6gh6qwai().s[10]++, url.trim());
  // Add protocol if missing
  /* istanbul ignore next */
  cov_1l6gh6qwai().s[11]++;
  if (
  /* istanbul ignore next */
  (cov_1l6gh6qwai().b[3][0]++, !normalized.startsWith('http://')) &&
  /* istanbul ignore next */
  (cov_1l6gh6qwai().b[3][1]++, !normalized.startsWith('https://'))) {
    /* istanbul ignore next */
    cov_1l6gh6qwai().b[2][0]++;
    cov_1l6gh6qwai().s[12]++;
    normalized = `https://${normalized}`;
  } else
  /* istanbul ignore next */
  {
    cov_1l6gh6qwai().b[2][1]++;
  }
  // Remove trailing slash (except for root path)
  cov_1l6gh6qwai().s[13]++;
  try {
    const urlObj =
    /* istanbul ignore next */
    (cov_1l6gh6qwai().s[14]++, new URL(normalized));
    /* istanbul ignore next */
    cov_1l6gh6qwai().s[15]++;
    if (
    /* istanbul ignore next */
    (cov_1l6gh6qwai().b[5][0]++, urlObj.pathname === '/') &&
    /* istanbul ignore next */
    (cov_1l6gh6qwai().b[5][1]++, urlObj.search === '') &&
    /* istanbul ignore next */
    (cov_1l6gh6qwai().b[5][2]++, urlObj.hash === '')) {
      /* istanbul ignore next */
      cov_1l6gh6qwai().b[4][0]++;
      cov_1l6gh6qwai().s[16]++;
      // For root path with no query/hash, remove trailing slash
      normalized = `${urlObj.protocol}//${urlObj.host}`;
    } else {
      /* istanbul ignore next */
      cov_1l6gh6qwai().b[4][1]++;
      cov_1l6gh6qwai().s[17]++;
      if (
      /* istanbul ignore next */
      (cov_1l6gh6qwai().b[7][0]++, urlObj.pathname.endsWith('/')) &&
      /* istanbul ignore next */
      (cov_1l6gh6qwai().b[7][1]++, urlObj.pathname !== '/')) {
        /* istanbul ignore next */
        cov_1l6gh6qwai().b[6][0]++;
        cov_1l6gh6qwai().s[18]++;
        // For non-root paths, remove trailing slash
        urlObj.pathname = urlObj.pathname.slice(0, -1);
        /* istanbul ignore next */
        cov_1l6gh6qwai().s[19]++;
        normalized = urlObj.toString();
      } else
      /* istanbul ignore next */
      {
        cov_1l6gh6qwai().b[6][1]++;
      }
    }
  } catch {
    /* istanbul ignore next */
    cov_1l6gh6qwai().s[20]++;
    // If URL parsing fails, just remove trailing slash from string
    if (
    /* istanbul ignore next */
    (cov_1l6gh6qwai().b[9][0]++, normalized.endsWith('/')) &&
    /* istanbul ignore next */
    (cov_1l6gh6qwai().b[9][1]++, normalized.length > 8)) {
      /* istanbul ignore next */
      cov_1l6gh6qwai().b[8][0]++;
      cov_1l6gh6qwai().s[21]++;
      normalized = normalized.slice(0, -1);
    } else
    /* istanbul ignore next */
    {
      cov_1l6gh6qwai().b[8][1]++;
    }
  }
  /* istanbul ignore next */
  cov_1l6gh6qwai().s[22]++;
  return normalized;
}
/**
 * Validates if a string is a valid HTTP/HTTPS URL
 * @param url - The URL to validate
 * @returns True if valid, false otherwise
 */
function isValidUrl(url) {
  /* istanbul ignore next */
  cov_1l6gh6qwai().f[1]++;
  cov_1l6gh6qwai().s[23]++;
  if (
  /* istanbul ignore next */
  (cov_1l6gh6qwai().b[11][0]++, !url) ||
  /* istanbul ignore next */
  (cov_1l6gh6qwai().b[11][1]++, !url.trim())) {
    /* istanbul ignore next */
    cov_1l6gh6qwai().b[10][0]++;
    cov_1l6gh6qwai().s[24]++;
    return false;
  } else
  /* istanbul ignore next */
  {
    cov_1l6gh6qwai().b[10][1]++;
  }
  cov_1l6gh6qwai().s[25]++;
  try {
    const urlObj =
    /* istanbul ignore next */
    (cov_1l6gh6qwai().s[26]++, new URL(url));
    /* istanbul ignore next */
    cov_1l6gh6qwai().s[27]++;
    return /* istanbul ignore next */(cov_1l6gh6qwai().b[12][0]++, urlObj.protocol === 'http:') ||
    /* istanbul ignore next */
    (cov_1l6gh6qwai().b[12][1]++, urlObj.protocol === 'https:');
  } catch {
    /* istanbul ignore next */
    cov_1l6gh6qwai().s[28]++;
    return false;
  }
}
/**
 * Extracts the domain from a URL
 * @param url - The URL to extract domain from
 * @returns Domain name or empty string if invalid
 */
function extractDomain(url) {
  /* istanbul ignore next */
  cov_1l6gh6qwai().f[2]++;
  cov_1l6gh6qwai().s[29]++;
  if (
  /* istanbul ignore next */
  (cov_1l6gh6qwai().b[14][0]++, !url) ||
  /* istanbul ignore next */
  (cov_1l6gh6qwai().b[14][1]++, !url.trim())) {
    /* istanbul ignore next */
    cov_1l6gh6qwai().b[13][0]++;
    cov_1l6gh6qwai().s[30]++;
    return '';
  } else
  /* istanbul ignore next */
  {
    cov_1l6gh6qwai().b[13][1]++;
  }
  cov_1l6gh6qwai().s[31]++;
  try {
    const urlObj =
    /* istanbul ignore next */
    (cov_1l6gh6qwai().s[32]++, new URL(url));
    /* istanbul ignore next */
    cov_1l6gh6qwai().s[33]++;
    return urlObj.hostname;
  } catch {
    /* istanbul ignore next */
    cov_1l6gh6qwai().s[34]++;
    return '';
  }
}
/**
 * Adds HTTPS protocol to a URL if no protocol is present
 * @param url - The URL to add protocol to
 * @returns URL with protocol
 */
function addProtocol(url) {
  /* istanbul ignore next */
  cov_1l6gh6qwai().f[3]++;
  cov_1l6gh6qwai().s[35]++;
  if (
  /* istanbul ignore next */
  (cov_1l6gh6qwai().b[16][0]++, !url) ||
  /* istanbul ignore next */
  (cov_1l6gh6qwai().b[16][1]++, !url.trim())) {
    /* istanbul ignore next */
    cov_1l6gh6qwai().b[15][0]++;
    cov_1l6gh6qwai().s[36]++;
    return '';
  } else
  /* istanbul ignore next */
  {
    cov_1l6gh6qwai().b[15][1]++;
  }
  const trimmed =
  /* istanbul ignore next */
  (cov_1l6gh6qwai().s[37]++, url.trim());
  /* istanbul ignore next */
  cov_1l6gh6qwai().s[38]++;
  if (
  /* istanbul ignore next */
  (cov_1l6gh6qwai().b[18][0]++, trimmed.startsWith('http://')) ||
  /* istanbul ignore next */
  (cov_1l6gh6qwai().b[18][1]++, trimmed.startsWith('https://'))) {
    /* istanbul ignore next */
    cov_1l6gh6qwai().b[17][0]++;
    cov_1l6gh6qwai().s[39]++;
    return trimmed;
  } else
  /* istanbul ignore next */
  {
    cov_1l6gh6qwai().b[17][1]++;
  }
  cov_1l6gh6qwai().s[40]++;
  return `https://${trimmed}`;
}
/**
 * Removes tracking parameters and cleans up URL
 * @param url - The URL to clean
 * @returns Cleaned URL
 */
function cleanUrl(url) {
  /* istanbul ignore next */
  cov_1l6gh6qwai().f[4]++;
  cov_1l6gh6qwai().s[41]++;
  if (
  /* istanbul ignore next */
  (cov_1l6gh6qwai().b[20][0]++, !url) ||
  /* istanbul ignore next */
  (cov_1l6gh6qwai().b[20][1]++, !url.trim())) {
    /* istanbul ignore next */
    cov_1l6gh6qwai().b[19][0]++;
    cov_1l6gh6qwai().s[42]++;
    return '';
  } else
  /* istanbul ignore next */
  {
    cov_1l6gh6qwai().b[19][1]++;
  }
  cov_1l6gh6qwai().s[43]++;
  try {
    const urlObj =
    /* istanbul ignore next */
    (cov_1l6gh6qwai().s[44]++, new URL(url));
    const params =
    /* istanbul ignore next */
    (cov_1l6gh6qwai().s[45]++, new URLSearchParams(urlObj.search));
    // List of tracking parameters to remove
    const trackingParams =
    /* istanbul ignore next */
    (cov_1l6gh6qwai().s[46]++, ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content', 'fbclid', 'gclid', 'gclsrc', 'dclid', 'msclkid', '_ga', '_gl', 'mc_cid', 'mc_eid']);
    // Remove tracking parameters
    /* istanbul ignore next */
    cov_1l6gh6qwai().s[47]++;
    trackingParams.forEach(param => {
      /* istanbul ignore next */
      cov_1l6gh6qwai().f[5]++;
      cov_1l6gh6qwai().s[48]++;
      params.delete(param);
    });
    // Rebuild URL
    /* istanbul ignore next */
    cov_1l6gh6qwai().s[49]++;
    urlObj.search = params.toString();
    // Remove trailing slash for root path only if no query parameters remain
    /* istanbul ignore next */
    cov_1l6gh6qwai().s[50]++;
    if (
    /* istanbul ignore next */
    (cov_1l6gh6qwai().b[22][0]++, urlObj.pathname === '/') &&
    /* istanbul ignore next */
    (cov_1l6gh6qwai().b[22][1]++, urlObj.search === '') &&
    /* istanbul ignore next */
    (cov_1l6gh6qwai().b[22][2]++, urlObj.hash === '')) {
      /* istanbul ignore next */
      cov_1l6gh6qwai().b[21][0]++;
      cov_1l6gh6qwai().s[51]++;
      return `${urlObj.protocol}//${urlObj.host}`;
    } else
    /* istanbul ignore next */
    {
      cov_1l6gh6qwai().b[21][1]++;
    }
    // For root path with query parameters, keep the slash
    cov_1l6gh6qwai().s[52]++;
    return urlObj.toString();
  } catch {
    /* istanbul ignore next */
    cov_1l6gh6qwai().s[53]++;
    // If URL parsing fails, return original
    return url;
  }
}
/**
 * Checks if two URLs point to the same resource (ignoring protocol and www)
 * @param url1 - First URL
 * @param url2 - Second URL
 * @returns True if URLs are equivalent
 */
function areUrlsEquivalent(url1, url2) {
  /* istanbul ignore next */
  cov_1l6gh6qwai().f[6]++;
  cov_1l6gh6qwai().s[54]++;
  try {
    /* istanbul ignore next */
    cov_1l6gh6qwai().s[55]++;
    const normalize = url => {
      /* istanbul ignore next */
      cov_1l6gh6qwai().f[7]++;
      const urlObj =
      /* istanbul ignore next */
      (cov_1l6gh6qwai().s[56]++, new URL(normalizeUrl(url)));
      // Remove www prefix for comparison
      const hostname =
      /* istanbul ignore next */
      (cov_1l6gh6qwai().s[57]++, urlObj.hostname.replace(/^www\./, ''));
      /* istanbul ignore next */
      cov_1l6gh6qwai().s[58]++;
      return `${hostname}${urlObj.pathname}${urlObj.search}${urlObj.hash}`;
    };
    /* istanbul ignore next */
    cov_1l6gh6qwai().s[59]++;
    return normalize(url1) === normalize(url2);
  } catch {
    /* istanbul ignore next */
    cov_1l6gh6qwai().s[60]++;
    return false;
  }
}
/**
 * Validates and normalizes a website URL
 * @param url - The URL to validate and normalize
 * @returns Normalized URL or null if invalid
 */
function validateAndNormalizeUrl(url) {
  /* istanbul ignore next */
  cov_1l6gh6qwai().f[8]++;
  cov_1l6gh6qwai().s[61]++;
  if (
  /* istanbul ignore next */
  (cov_1l6gh6qwai().b[24][0]++, !url) ||
  /* istanbul ignore next */
  (cov_1l6gh6qwai().b[24][1]++, !url.trim())) {
    /* istanbul ignore next */
    cov_1l6gh6qwai().b[23][0]++;
    cov_1l6gh6qwai().s[62]++;
    return null;
  } else
  /* istanbul ignore next */
  {
    cov_1l6gh6qwai().b[23][1]++;
  }
  const normalized =
  /* istanbul ignore next */
  (cov_1l6gh6qwai().s[63]++, normalizeUrl(url));
  /* istanbul ignore next */
  cov_1l6gh6qwai().s[64]++;
  return isValidUrl(normalized) ?
  /* istanbul ignore next */
  (cov_1l6gh6qwai().b[25][0]++, normalized) :
  /* istanbul ignore next */
  (cov_1l6gh6qwai().b[25][1]++, null);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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