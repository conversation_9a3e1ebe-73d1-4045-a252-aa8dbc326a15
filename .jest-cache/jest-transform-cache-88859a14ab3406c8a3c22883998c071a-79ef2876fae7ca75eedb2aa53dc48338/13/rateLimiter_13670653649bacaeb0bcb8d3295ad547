a97e604b12dd263c67ab13d60d19e2ba
"use strict";

/* istanbul ignore next */
function cov_221z0t0bq0() {
  var path = "/Users/<USER>/WebstormProjects/goo/src/utils/rateLimiter.ts";
  var hash = "009f2b837d89b9c7d6a65320568f1d8d60fd39f9";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/WebstormProjects/goo/src/utils/rateLimiter.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 3,
          column: 40
        }
      },
      "2": {
        start: {
          line: 4,
          column: 0
        },
        end: {
          line: 4,
          column: 52
        }
      },
      "3": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 56
        }
      },
      "4": {
        start: {
          line: 6,
          column: 17
        },
        end: {
          line: 6,
          column: 44
        }
      },
      "5": {
        start: {
          line: 18,
          column: 8
        },
        end: {
          line: 18,
          column: 47
        }
      },
      "6": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 37
        }
      },
      "7": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 22,
          column: 9
        }
      },
      "8": {
        start: {
          line: 21,
          column: 12
        },
        end: {
          line: 21,
          column: 82
        }
      },
      "9": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 33
        }
      },
      "10": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 24,
          column: 37
        }
      },
      "11": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 53
        }
      },
      "12": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 32,
          column: 28
        }
      },
      "13": {
        start: {
          line: 33,
          column: 8
        },
        end: {
          line: 36,
          column: 9
        }
      },
      "14": {
        start: {
          line: 34,
          column: 12
        },
        end: {
          line: 34,
          column: 29
        }
      },
      "15": {
        start: {
          line: 35,
          column: 12
        },
        end: {
          line: 35,
          column: 24
        }
      },
      "16": {
        start: {
          line: 37,
          column: 8
        },
        end: {
          line: 37,
          column: 21
        }
      },
      "17": {
        start: {
          line: 46,
          column: 26
        },
        end: {
          line: 46,
          column: 36
        }
      },
      "18": {
        start: {
          line: 47,
          column: 8
        },
        end: {
          line: 61,
          column: 11
        }
      },
      "19": {
        start: {
          line: 48,
          column: 31
        },
        end: {
          line: 59,
          column: 13
        }
      },
      "20": {
        start: {
          line: 49,
          column: 16
        },
        end: {
          line: 52,
          column: 17
        }
      },
      "21": {
        start: {
          line: 50,
          column: 20
        },
        end: {
          line: 50,
          column: 30
        }
      },
      "22": {
        start: {
          line: 51,
          column: 20
        },
        end: {
          line: 51,
          column: 27
        }
      },
      "23": {
        start: {
          line: 53,
          column: 16
        },
        end: {
          line: 56,
          column: 17
        }
      },
      "24": {
        start: {
          line: 54,
          column: 20
        },
        end: {
          line: 54,
          column: 87
        }
      },
      "25": {
        start: {
          line: 55,
          column: 20
        },
        end: {
          line: 55,
          column: 27
        }
      },
      "26": {
        start: {
          line: 58,
          column: 16
        },
        end: {
          line: 58,
          column: 78
        }
      },
      "27": {
        start: {
          line: 60,
          column: 12
        },
        end: {
          line: 60,
          column: 25
        }
      },
      "28": {
        start: {
          line: 68,
          column: 8
        },
        end: {
          line: 68,
          column: 28
        }
      },
      "29": {
        start: {
          line: 69,
          column: 8
        },
        end: {
          line: 69,
          column: 39
        }
      },
      "30": {
        start: {
          line: 75,
          column: 8
        },
        end: {
          line: 75,
          column: 38
        }
      },
      "31": {
        start: {
          line: 76,
          column: 8
        },
        end: {
          line: 76,
          column: 37
        }
      },
      "32": {
        start: {
          line: 83,
          column: 8
        },
        end: {
          line: 83,
          column: 28
        }
      },
      "33": {
        start: {
          line: 84,
          column: 8
        },
        end: {
          line: 86,
          column: 9
        }
      },
      "34": {
        start: {
          line: 85,
          column: 12
        },
        end: {
          line: 85,
          column: 21
        }
      },
      "35": {
        start: {
          line: 87,
          column: 36
        },
        end: {
          line: 87,
          column: 64
        }
      },
      "36": {
        start: {
          line: 88,
          column: 33
        },
        end: {
          line: 88,
          column: 98
        }
      },
      "37": {
        start: {
          line: 89,
          column: 8
        },
        end: {
          line: 89,
          column: 45
        }
      },
      "38": {
        start: {
          line: 95,
          column: 20
        },
        end: {
          line: 95,
          column: 30
        }
      },
      "39": {
        start: {
          line: 96,
          column: 27
        },
        end: {
          line: 96,
          column: 48
        }
      },
      "40": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 101,
          column: 9
        }
      },
      "41": {
        start: {
          line: 98,
          column: 32
        },
        end: {
          line: 98,
          column: 76
        }
      },
      "42": {
        start: {
          line: 99,
          column: 12
        },
        end: {
          line: 99,
          column: 79
        }
      },
      "43": {
        start: {
          line: 100,
          column: 12
        },
        end: {
          line: 100,
          column: 71
        }
      },
      "44": {
        start: {
          line: 104,
          column: 0
        },
        end: {
          line: 104,
          column: 56
        }
      },
      "45": {
        start: {
          line: 108,
          column: 24
        },
        end: {
          line: 108,
          column: 28
        }
      },
      "46": {
        start: {
          line: 116,
          column: 4
        },
        end: {
          line: 118,
          column: 5
        }
      },
      "47": {
        start: {
          line: 117,
          column: 8
        },
        end: {
          line: 117,
          column: 84
        }
      },
      "48": {
        start: {
          line: 119,
          column: 4
        },
        end: {
          line: 119,
          column: 29
        }
      },
      "49": {
        start: {
          line: 125,
          column: 4
        },
        end: {
          line: 125,
          column: 29
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 17,
            column: 4
          },
          end: {
            line: 17,
            column: 5
          }
        },
        loc: {
          start: {
            line: 17,
            column: 45
          },
          end: {
            line: 26,
            column: 5
          }
        },
        line: 17
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 31,
            column: 4
          },
          end: {
            line: 31,
            column: 5
          }
        },
        loc: {
          start: {
            line: 31,
            column: 17
          },
          end: {
            line: 38,
            column: 5
          }
        },
        line: 31
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 45,
            column: 4
          },
          end: {
            line: 45,
            column: 5
          }
        },
        loc: {
          start: {
            line: 45,
            column: 41
          },
          end: {
            line: 62,
            column: 5
          }
        },
        line: 45
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 47,
            column: 27
          },
          end: {
            line: 47,
            column: 28
          }
        },
        loc: {
          start: {
            line: 47,
            column: 48
          },
          end: {
            line: 61,
            column: 9
          }
        },
        line: 47
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 48,
            column: 31
          },
          end: {
            line: 48,
            column: 32
          }
        },
        loc: {
          start: {
            line: 48,
            column: 37
          },
          end: {
            line: 59,
            column: 13
          }
        },
        line: 48
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 67,
            column: 4
          },
          end: {
            line: 67,
            column: 5
          }
        },
        loc: {
          start: {
            line: 67,
            column: 25
          },
          end: {
            line: 70,
            column: 5
          }
        },
        line: 67
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 74,
            column: 4
          },
          end: {
            line: 74,
            column: 5
          }
        },
        loc: {
          start: {
            line: 74,
            column: 12
          },
          end: {
            line: 77,
            column: 5
          }
        },
        line: 74
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 82,
            column: 4
          },
          end: {
            line: 82,
            column: 5
          }
        },
        loc: {
          start: {
            line: 82,
            column: 28
          },
          end: {
            line: 90,
            column: 5
          }
        },
        line: 82
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 94,
            column: 4
          },
          end: {
            line: 94,
            column: 5
          }
        },
        loc: {
          start: {
            line: 94,
            column: 19
          },
          end: {
            line: 102,
            column: 5
          }
        },
        line: 94
      },
      "9": {
        name: "getGlobalRateLimiter",
        decl: {
          start: {
            line: 115,
            column: 9
          },
          end: {
            line: 115,
            column: 29
          }
        },
        loc: {
          start: {
            line: 115,
            column: 69
          },
          end: {
            line: 120,
            column: 1
          }
        },
        line: 115
      },
      "10": {
        name: "resetGlobalRateLimiter",
        decl: {
          start: {
            line: 124,
            column: 9
          },
          end: {
            line: 124,
            column: 31
          }
        },
        loc: {
          start: {
            line: 124,
            column: 34
          },
          end: {
            line: 126,
            column: 1
          }
        },
        line: 124
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 22,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 20,
            column: 8
          },
          end: {
            line: 22,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 20
      },
      "1": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 20,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 20,
            column: 32
          }
        }, {
          start: {
            line: 20,
            column: 36
          },
          end: {
            line: 20,
            column: 51
          }
        }],
        line: 20
      },
      "2": {
        loc: {
          start: {
            line: 33,
            column: 8
          },
          end: {
            line: 36,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 8
          },
          end: {
            line: 36,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "3": {
        loc: {
          start: {
            line: 45,
            column: 23
          },
          end: {
            line: 45,
            column: 39
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 45,
            column: 35
          },
          end: {
            line: 45,
            column: 39
          }
        }],
        line: 45
      },
      "4": {
        loc: {
          start: {
            line: 49,
            column: 16
          },
          end: {
            line: 52,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 49,
            column: 16
          },
          end: {
            line: 52,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 49
      },
      "5": {
        loc: {
          start: {
            line: 53,
            column: 16
          },
          end: {
            line: 56,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 53,
            column: 16
          },
          end: {
            line: 56,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 53
      },
      "6": {
        loc: {
          start: {
            line: 84,
            column: 8
          },
          end: {
            line: 86,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 84,
            column: 8
          },
          end: {
            line: 86,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 84
      },
      "7": {
        loc: {
          start: {
            line: 97,
            column: 8
          },
          end: {
            line: 101,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 97,
            column: 8
          },
          end: {
            line: 101,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 97
      },
      "8": {
        loc: {
          start: {
            line: 115,
            column: 30
          },
          end: {
            line: 115,
            column: 50
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 115,
            column: 48
          },
          end: {
            line: 115,
            column: 50
          }
        }],
        line: 115
      },
      "9": {
        loc: {
          start: {
            line: 115,
            column: 52
          },
          end: {
            line: 115,
            column: 67
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 115,
            column: 65
          },
          end: {
            line: 115,
            column: 67
          }
        }],
        line: 115
      },
      "10": {
        loc: {
          start: {
            line: 116,
            column: 4
          },
          end: {
            line: 118,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 116,
            column: 4
          },
          end: {
            line: 118,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 116
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0],
      "9": [0],
      "10": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/WebstormProjects/goo/src/utils/rateLimiter.ts",
      mappings: ";;;AAqIA,oDAQC;AAKD,wDAEC;AApJD,6CAAkD;AAElD;;;GAGG;AACH,MAAa,sBAAsB;IAKjC;;;;OAIG;IACH,YACmB,eAAuB,EACvB,UAAkB;QADlB,oBAAe,GAAf,eAAe,CAAQ;QACvB,eAAU,GAAV,UAAU,CAAQ;QAEnC,IAAI,eAAe,IAAI,CAAC,IAAI,UAAU,IAAI,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,eAAe,CAAC,CAAC,eAAe;IAC/D,CAAC;IAED;;;OAGG;IACI,UAAU;QACf,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;YACjB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,YAAY,CAAC,YAAoB,IAAI;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,UAAU,GAAG,GAAG,EAAE;gBACtB,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;oBACtB,OAAO,EAAE,CAAC;oBACV,OAAO;gBACT,CAAC;gBAED,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,IAAI,SAAS,EAAE,CAAC;oBACxC,MAAM,CAAC,IAAI,uBAAc,CAAC,6BAA6B,CAAC,CAAC,CAAC;oBAC1D,OAAO;gBACT,CAAC;gBAED,kCAAkC;gBAClC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAChE,CAAC,CAAC;YAEF,UAAU,EAAE,CAAC;QACf,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACvB,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,KAAK;QACV,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,qBAAqB;QAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACrB,OAAO,CAAC,CAAC;QACX,CAAC;QAED,MAAM,mBAAmB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC;QACzD,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;QAE3F,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,YAAY;QAClB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;QAEzC,IAAI,UAAU,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;YACjE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC;YACnE,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;CACF;AAlHD,wDAkHC;AAED;;GAEG;AACH,IAAI,iBAAiB,GAAkC,IAAI,CAAC;AAE5D;;;;;GAKG;AACH,SAAgB,oBAAoB,CAClC,kBAA0B,EAAE,EAC5B,aAAqB,EAAE;IAEvB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvB,iBAAiB,GAAG,IAAI,sBAAsB,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;IAC9E,CAAC;IACD,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED;;GAEG;AACH,SAAgB,sBAAsB;IACpC,iBAAiB,GAAG,IAAI,CAAC;AAC3B,CAAC",
      names: [],
      sources: ["/Users/<USER>/WebstormProjects/goo/src/utils/rateLimiter.ts"],
      sourcesContent: ["import { RateLimitError } from '../models/Errors';\n\n/**\n * Token bucket rate limiter implementation\n * Allows burst requests up to bucket size, then limits to refill rate\n */\nexport class TokenBucketRateLimiter {\n  private tokens: number;\n  private lastRefill: number;\n  private readonly refillInterval: number; // milliseconds per token\n\n  /**\n   * Creates a new token bucket rate limiter\n   * @param tokensPerSecond - Number of tokens to add per second\n   * @param bucketSize - Maximum number of tokens in bucket\n   */\n  constructor(\n    private readonly tokensPerSecond: number,\n    private readonly bucketSize: number\n  ) {\n    if (tokensPerSecond <= 0 || bucketSize <= 0) {\n      throw new Error('Tokens per second and bucket size must be positive');\n    }\n\n    this.tokens = bucketSize;\n    this.lastRefill = Date.now();\n    this.refillInterval = 1000 / tokensPerSecond; // ms per token\n  }\n\n  /**\n   * Attempts to consume a token from the bucket\n   * @returns True if token was consumed, false if bucket is empty\n   */\n  public tryConsume(): boolean {\n    this.refillTokens();\n\n    if (this.tokens >= 1) {\n      this.tokens -= 1;\n      return true;\n    }\n\n    return false;\n  }\n\n  /**\n   * Waits for a token to become available\n   * @param timeoutMs - Maximum time to wait in milliseconds\n   * @returns Promise that resolves when token is available\n   * @throws RateLimitError if timeout is reached\n   */\n  public async waitForToken(timeoutMs: number = 5000): Promise<void> {\n    const startTime = Date.now();\n\n    return new Promise((resolve, reject) => {\n      const checkToken = () => {\n        if (this.tryConsume()) {\n          resolve();\n          return;\n        }\n\n        if (Date.now() - startTime >= timeoutMs) {\n          reject(new RateLimitError('Rate limit timeout exceeded'));\n          return;\n        }\n\n        // Check again after a short delay\n        setTimeout(checkToken, Math.min(this.refillInterval / 2, 50));\n      };\n\n      checkToken();\n    });\n  }\n\n  /**\n   * Gets the number of available tokens\n   * @returns Number of available tokens\n   */\n  public getAvailableTokens(): number {\n    this.refillTokens();\n    return Math.floor(this.tokens);\n  }\n\n  /**\n   * Resets the bucket to full capacity\n   */\n  public reset(): void {\n    this.tokens = this.bucketSize;\n    this.lastRefill = Date.now();\n  }\n\n  /**\n   * Gets the time until next token is available\n   * @returns Milliseconds until next token, or 0 if tokens are available\n   */\n  public getTimeUntilNextToken(): number {\n    this.refillTokens();\n\n    if (this.tokens >= 1) {\n      return 0;\n    }\n\n    const timeSinceLastRefill = Date.now() - this.lastRefill;\n    const timeForNextToken = this.refillInterval - (timeSinceLastRefill % this.refillInterval);\n    \n    return Math.max(0, timeForNextToken);\n  }\n\n  /**\n   * Refills tokens based on elapsed time\n   */\n  private refillTokens(): void {\n    const now = Date.now();\n    const timePassed = now - this.lastRefill;\n\n    if (timePassed >= this.refillInterval) {\n      const tokensToAdd = Math.floor(timePassed / this.refillInterval);\n      this.tokens = Math.min(this.bucketSize, this.tokens + tokensToAdd);\n      this.lastRefill = now - (timePassed % this.refillInterval);\n    }\n  }\n}\n\n/**\n * Global rate limiter instance for API requests\n */\nlet globalRateLimiter: TokenBucketRateLimiter | null = null;\n\n/**\n * Gets or creates the global rate limiter instance\n * @param tokensPerSecond - Tokens per second (default from config)\n * @param bucketSize - Bucket size (default from config)\n * @returns Global rate limiter instance\n */\nexport function getGlobalRateLimiter(\n  tokensPerSecond: number = 10,\n  bucketSize: number = 20\n): TokenBucketRateLimiter {\n  if (!globalRateLimiter) {\n    globalRateLimiter = new TokenBucketRateLimiter(tokensPerSecond, bucketSize);\n  }\n  return globalRateLimiter;\n}\n\n/**\n * Resets the global rate limiter\n */\nexport function resetGlobalRateLimiter(): void {\n  globalRateLimiter = null;\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "009f2b837d89b9c7d6a65320568f1d8d60fd39f9"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_221z0t0bq0 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_221z0t0bq0();
cov_221z0t0bq0().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_221z0t0bq0().s[1]++;
exports.TokenBucketRateLimiter = void 0;
/* istanbul ignore next */
cov_221z0t0bq0().s[2]++;
exports.getGlobalRateLimiter = getGlobalRateLimiter;
/* istanbul ignore next */
cov_221z0t0bq0().s[3]++;
exports.resetGlobalRateLimiter = resetGlobalRateLimiter;
const Errors_1 =
/* istanbul ignore next */
(cov_221z0t0bq0().s[4]++, require("../models/Errors"));
/**
 * Token bucket rate limiter implementation
 * Allows burst requests up to bucket size, then limits to refill rate
 */
class TokenBucketRateLimiter {
  /**
   * Creates a new token bucket rate limiter
   * @param tokensPerSecond - Number of tokens to add per second
   * @param bucketSize - Maximum number of tokens in bucket
   */
  constructor(tokensPerSecond, bucketSize) {
    /* istanbul ignore next */
    cov_221z0t0bq0().f[0]++;
    cov_221z0t0bq0().s[5]++;
    this.tokensPerSecond = tokensPerSecond;
    /* istanbul ignore next */
    cov_221z0t0bq0().s[6]++;
    this.bucketSize = bucketSize;
    /* istanbul ignore next */
    cov_221z0t0bq0().s[7]++;
    if (
    /* istanbul ignore next */
    (cov_221z0t0bq0().b[1][0]++, tokensPerSecond <= 0) ||
    /* istanbul ignore next */
    (cov_221z0t0bq0().b[1][1]++, bucketSize <= 0)) {
      /* istanbul ignore next */
      cov_221z0t0bq0().b[0][0]++;
      cov_221z0t0bq0().s[8]++;
      throw new Error('Tokens per second and bucket size must be positive');
    } else
    /* istanbul ignore next */
    {
      cov_221z0t0bq0().b[0][1]++;
    }
    cov_221z0t0bq0().s[9]++;
    this.tokens = bucketSize;
    /* istanbul ignore next */
    cov_221z0t0bq0().s[10]++;
    this.lastRefill = Date.now();
    /* istanbul ignore next */
    cov_221z0t0bq0().s[11]++;
    this.refillInterval = 1000 / tokensPerSecond; // ms per token
  }
  /**
   * Attempts to consume a token from the bucket
   * @returns True if token was consumed, false if bucket is empty
   */
  tryConsume() {
    /* istanbul ignore next */
    cov_221z0t0bq0().f[1]++;
    cov_221z0t0bq0().s[12]++;
    this.refillTokens();
    /* istanbul ignore next */
    cov_221z0t0bq0().s[13]++;
    if (this.tokens >= 1) {
      /* istanbul ignore next */
      cov_221z0t0bq0().b[2][0]++;
      cov_221z0t0bq0().s[14]++;
      this.tokens -= 1;
      /* istanbul ignore next */
      cov_221z0t0bq0().s[15]++;
      return true;
    } else
    /* istanbul ignore next */
    {
      cov_221z0t0bq0().b[2][1]++;
    }
    cov_221z0t0bq0().s[16]++;
    return false;
  }
  /**
   * Waits for a token to become available
   * @param timeoutMs - Maximum time to wait in milliseconds
   * @returns Promise that resolves when token is available
   * @throws RateLimitError if timeout is reached
   */
  async waitForToken(timeoutMs =
  /* istanbul ignore next */
  (cov_221z0t0bq0().b[3][0]++, 5000)) {
    /* istanbul ignore next */
    cov_221z0t0bq0().f[2]++;
    const startTime =
    /* istanbul ignore next */
    (cov_221z0t0bq0().s[17]++, Date.now());
    /* istanbul ignore next */
    cov_221z0t0bq0().s[18]++;
    return new Promise((resolve, reject) => {
      /* istanbul ignore next */
      cov_221z0t0bq0().f[3]++;
      cov_221z0t0bq0().s[19]++;
      const checkToken = () => {
        /* istanbul ignore next */
        cov_221z0t0bq0().f[4]++;
        cov_221z0t0bq0().s[20]++;
        if (this.tryConsume()) {
          /* istanbul ignore next */
          cov_221z0t0bq0().b[4][0]++;
          cov_221z0t0bq0().s[21]++;
          resolve();
          /* istanbul ignore next */
          cov_221z0t0bq0().s[22]++;
          return;
        } else
        /* istanbul ignore next */
        {
          cov_221z0t0bq0().b[4][1]++;
        }
        cov_221z0t0bq0().s[23]++;
        if (Date.now() - startTime >= timeoutMs) {
          /* istanbul ignore next */
          cov_221z0t0bq0().b[5][0]++;
          cov_221z0t0bq0().s[24]++;
          reject(new Errors_1.RateLimitError('Rate limit timeout exceeded'));
          /* istanbul ignore next */
          cov_221z0t0bq0().s[25]++;
          return;
        } else
        /* istanbul ignore next */
        {
          cov_221z0t0bq0().b[5][1]++;
        }
        // Check again after a short delay
        cov_221z0t0bq0().s[26]++;
        setTimeout(checkToken, Math.min(this.refillInterval / 2, 50));
      };
      /* istanbul ignore next */
      cov_221z0t0bq0().s[27]++;
      checkToken();
    });
  }
  /**
   * Gets the number of available tokens
   * @returns Number of available tokens
   */
  getAvailableTokens() {
    /* istanbul ignore next */
    cov_221z0t0bq0().f[5]++;
    cov_221z0t0bq0().s[28]++;
    this.refillTokens();
    /* istanbul ignore next */
    cov_221z0t0bq0().s[29]++;
    return Math.floor(this.tokens);
  }
  /**
   * Resets the bucket to full capacity
   */
  reset() {
    /* istanbul ignore next */
    cov_221z0t0bq0().f[6]++;
    cov_221z0t0bq0().s[30]++;
    this.tokens = this.bucketSize;
    /* istanbul ignore next */
    cov_221z0t0bq0().s[31]++;
    this.lastRefill = Date.now();
  }
  /**
   * Gets the time until next token is available
   * @returns Milliseconds until next token, or 0 if tokens are available
   */
  getTimeUntilNextToken() {
    /* istanbul ignore next */
    cov_221z0t0bq0().f[7]++;
    cov_221z0t0bq0().s[32]++;
    this.refillTokens();
    /* istanbul ignore next */
    cov_221z0t0bq0().s[33]++;
    if (this.tokens >= 1) {
      /* istanbul ignore next */
      cov_221z0t0bq0().b[6][0]++;
      cov_221z0t0bq0().s[34]++;
      return 0;
    } else
    /* istanbul ignore next */
    {
      cov_221z0t0bq0().b[6][1]++;
    }
    const timeSinceLastRefill =
    /* istanbul ignore next */
    (cov_221z0t0bq0().s[35]++, Date.now() - this.lastRefill);
    const timeForNextToken =
    /* istanbul ignore next */
    (cov_221z0t0bq0().s[36]++, this.refillInterval - timeSinceLastRefill % this.refillInterval);
    /* istanbul ignore next */
    cov_221z0t0bq0().s[37]++;
    return Math.max(0, timeForNextToken);
  }
  /**
   * Refills tokens based on elapsed time
   */
  refillTokens() {
    /* istanbul ignore next */
    cov_221z0t0bq0().f[8]++;
    const now =
    /* istanbul ignore next */
    (cov_221z0t0bq0().s[38]++, Date.now());
    const timePassed =
    /* istanbul ignore next */
    (cov_221z0t0bq0().s[39]++, now - this.lastRefill);
    /* istanbul ignore next */
    cov_221z0t0bq0().s[40]++;
    if (timePassed >= this.refillInterval) {
      /* istanbul ignore next */
      cov_221z0t0bq0().b[7][0]++;
      const tokensToAdd =
      /* istanbul ignore next */
      (cov_221z0t0bq0().s[41]++, Math.floor(timePassed / this.refillInterval));
      /* istanbul ignore next */
      cov_221z0t0bq0().s[42]++;
      this.tokens = Math.min(this.bucketSize, this.tokens + tokensToAdd);
      /* istanbul ignore next */
      cov_221z0t0bq0().s[43]++;
      this.lastRefill = now - timePassed % this.refillInterval;
    } else
    /* istanbul ignore next */
    {
      cov_221z0t0bq0().b[7][1]++;
    }
  }
}
/* istanbul ignore next */
cov_221z0t0bq0().s[44]++;
exports.TokenBucketRateLimiter = TokenBucketRateLimiter;
/**
 * Global rate limiter instance for API requests
 */
let globalRateLimiter =
/* istanbul ignore next */
(cov_221z0t0bq0().s[45]++, null);
/**
 * Gets or creates the global rate limiter instance
 * @param tokensPerSecond - Tokens per second (default from config)
 * @param bucketSize - Bucket size (default from config)
 * @returns Global rate limiter instance
 */
function getGlobalRateLimiter(tokensPerSecond =
/* istanbul ignore next */
(cov_221z0t0bq0().b[8][0]++, 10), bucketSize =
/* istanbul ignore next */
(cov_221z0t0bq0().b[9][0]++, 20)) {
  /* istanbul ignore next */
  cov_221z0t0bq0().f[9]++;
  cov_221z0t0bq0().s[46]++;
  if (!globalRateLimiter) {
    /* istanbul ignore next */
    cov_221z0t0bq0().b[10][0]++;
    cov_221z0t0bq0().s[47]++;
    globalRateLimiter = new TokenBucketRateLimiter(tokensPerSecond, bucketSize);
  } else
  /* istanbul ignore next */
  {
    cov_221z0t0bq0().b[10][1]++;
  }
  cov_221z0t0bq0().s[48]++;
  return globalRateLimiter;
}
/**
 * Resets the global rate limiter
 */
function resetGlobalRateLimiter() {
  /* istanbul ignore next */
  cov_221z0t0bq0().f[10]++;
  cov_221z0t0bq0().s[49]++;
  globalRateLimiter = null;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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