{"version": 3, "names": ["cov_221z0t0bq0", "actualCoverage", "s", "exports", "getGlobalRateLimiter", "resetGlobalRateLimiter", "Errors_1", "require", "TokenBucketRateLimiter", "constructor", "tokensPerSecond", "bucketSize", "f", "b", "Error", "tokens", "lastRefill", "Date", "now", "refillInterval", "tryConsume", "refillTokens", "waitForToken", "timeoutMs", "startTime", "Promise", "resolve", "reject", "checkToken", "RateLimitError", "setTimeout", "Math", "min", "getAvailableTokens", "floor", "reset", "getTimeUntilNextToken", "timeSinceLastRefill", "timeForNextToken", "max", "timePassed", "tokensToAdd", "globalRateLimiter"], "sources": ["/Users/<USER>/WebstormProjects/goo/src/utils/rateLimiter.ts"], "sourcesContent": ["import { RateLimitError } from '../models/Errors';\n\n/**\n * Token bucket rate limiter implementation\n * Allows burst requests up to bucket size, then limits to refill rate\n */\nexport class TokenBucketRateLimiter {\n  private tokens: number;\n  private lastRefill: number;\n  private readonly refillInterval: number; // milliseconds per token\n\n  /**\n   * Creates a new token bucket rate limiter\n   * @param tokensPerSecond - Number of tokens to add per second\n   * @param bucketSize - Maximum number of tokens in bucket\n   */\n  constructor(\n    private readonly tokensPerSecond: number,\n    private readonly bucketSize: number\n  ) {\n    if (tokensPerSecond <= 0 || bucketSize <= 0) {\n      throw new Error('Tokens per second and bucket size must be positive');\n    }\n\n    this.tokens = bucketSize;\n    this.lastRefill = Date.now();\n    this.refillInterval = 1000 / tokensPerSecond; // ms per token\n  }\n\n  /**\n   * Attempts to consume a token from the bucket\n   * @returns True if token was consumed, false if bucket is empty\n   */\n  public tryConsume(): boolean {\n    this.refillTokens();\n\n    if (this.tokens >= 1) {\n      this.tokens -= 1;\n      return true;\n    }\n\n    return false;\n  }\n\n  /**\n   * Waits for a token to become available\n   * @param timeoutMs - Maximum time to wait in milliseconds\n   * @returns Promise that resolves when token is available\n   * @throws RateLimitError if timeout is reached\n   */\n  public async waitForToken(timeoutMs: number = 5000): Promise<void> {\n    const startTime = Date.now();\n\n    return new Promise((resolve, reject) => {\n      const checkToken = () => {\n        if (this.tryConsume()) {\n          resolve();\n          return;\n        }\n\n        if (Date.now() - startTime >= timeoutMs) {\n          reject(new RateLimitError('Rate limit timeout exceeded'));\n          return;\n        }\n\n        // Check again after a short delay\n        setTimeout(checkToken, Math.min(this.refillInterval / 2, 50));\n      };\n\n      checkToken();\n    });\n  }\n\n  /**\n   * Gets the number of available tokens\n   * @returns Number of available tokens\n   */\n  public getAvailableTokens(): number {\n    this.refillTokens();\n    return Math.floor(this.tokens);\n  }\n\n  /**\n   * Resets the bucket to full capacity\n   */\n  public reset(): void {\n    this.tokens = this.bucketSize;\n    this.lastRefill = Date.now();\n  }\n\n  /**\n   * Gets the time until next token is available\n   * @returns Milliseconds until next token, or 0 if tokens are available\n   */\n  public getTimeUntilNextToken(): number {\n    this.refillTokens();\n\n    if (this.tokens >= 1) {\n      return 0;\n    }\n\n    const timeSinceLastRefill = Date.now() - this.lastRefill;\n    const timeForNextToken = this.refillInterval - (timeSinceLastRefill % this.refillInterval);\n    \n    return Math.max(0, timeForNextToken);\n  }\n\n  /**\n   * Refills tokens based on elapsed time\n   */\n  private refillTokens(): void {\n    const now = Date.now();\n    const timePassed = now - this.lastRefill;\n\n    if (timePassed >= this.refillInterval) {\n      const tokensToAdd = Math.floor(timePassed / this.refillInterval);\n      this.tokens = Math.min(this.bucketSize, this.tokens + tokensToAdd);\n      this.lastRefill = now - (timePassed % this.refillInterval);\n    }\n  }\n}\n\n/**\n * Global rate limiter instance for API requests\n */\nlet globalRateLimiter: TokenBucketRateLimiter | null = null;\n\n/**\n * Gets or creates the global rate limiter instance\n * @param tokensPerSecond - Tokens per second (default from config)\n * @param bucketSize - Bucket size (default from config)\n * @returns Global rate limiter instance\n */\nexport function getGlobalRateLimiter(\n  tokensPerSecond: number = 10,\n  bucketSize: number = 20\n): TokenBucketRateLimiter {\n  if (!globalRateLimiter) {\n    globalRateLimiter = new TokenBucketRateLimiter(tokensPerSecond, bucketSize);\n  }\n  return globalRateLimiter;\n}\n\n/**\n * Resets the global rate limiter\n */\nexport function resetGlobalRateLimiter(): void {\n  globalRateLimiter = null;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeK;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;;;;AAsHLC,OAAA,CAAAC,oBAAA,GAAAA,oBAAA;AAQC;AAAAJ,cAAA,GAAAE,CAAA;AAKDC,OAAA,CAAAE,sBAAA,GAAAA,sBAAA;AAlJA,MAAAC,QAAA;AAAA;AAAA,CAAAN,cAAA,GAAAE,CAAA,OAAAK,OAAA;AAEA;;;;AAIA,MAAaC,sBAAsB;EAKjC;;;;;EAKAC,YACmBC,eAAuB,EACvBC,UAAkB;IAAA;IAAAX,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAE,CAAA;IADlB,KAAAQ,eAAe,GAAfA,eAAe;IAAQ;IAAAV,cAAA,GAAAE,CAAA;IACvB,KAAAS,UAAU,GAAVA,UAAU;IAAQ;IAAAX,cAAA,GAAAE,CAAA;IAEnC;IAAI;IAAA,CAAAF,cAAA,GAAAa,CAAA,UAAAH,eAAe,IAAI,CAAC;IAAA;IAAA,CAAAV,cAAA,GAAAa,CAAA,UAAIF,UAAU,IAAI,CAAC,GAAE;MAAA;MAAAX,cAAA,GAAAa,CAAA;MAAAb,cAAA,GAAAE,CAAA;MAC3C,MAAM,IAAIY,KAAK,CAAC,oDAAoD,CAAC;IACvE,CAAC;IAAA;IAAA;MAAAd,cAAA,GAAAa,CAAA;IAAA;IAAAb,cAAA,GAAAE,CAAA;IAED,IAAI,CAACa,MAAM,GAAGJ,UAAU;IAAC;IAAAX,cAAA,GAAAE,CAAA;IACzB,IAAI,CAACc,UAAU,GAAGC,IAAI,CAACC,GAAG,EAAE;IAAC;IAAAlB,cAAA,GAAAE,CAAA;IAC7B,IAAI,CAACiB,cAAc,GAAG,IAAI,GAAGT,eAAe,CAAC,CAAC;EAChD;EAEA;;;;EAIOU,UAAUA,CAAA;IAAA;IAAApB,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAE,CAAA;IACf,IAAI,CAACmB,YAAY,EAAE;IAAC;IAAArB,cAAA,GAAAE,CAAA;IAEpB,IAAI,IAAI,CAACa,MAAM,IAAI,CAAC,EAAE;MAAA;MAAAf,cAAA,GAAAa,CAAA;MAAAb,cAAA,GAAAE,CAAA;MACpB,IAAI,CAACa,MAAM,IAAI,CAAC;MAAC;MAAAf,cAAA,GAAAE,CAAA;MACjB,OAAO,IAAI;IACb,CAAC;IAAA;IAAA;MAAAF,cAAA,GAAAa,CAAA;IAAA;IAAAb,cAAA,GAAAE,CAAA;IAED,OAAO,KAAK;EACd;EAEA;;;;;;EAMO,MAAMoB,YAAYA,CAACC,SAAA;EAAA;EAAA,CAAAvB,cAAA,GAAAa,CAAA,UAAoB,IAAI;IAAA;IAAAb,cAAA,GAAAY,CAAA;IAChD,MAAMY,SAAS;IAAA;IAAA,CAAAxB,cAAA,GAAAE,CAAA,QAAGe,IAAI,CAACC,GAAG,EAAE;IAAC;IAAAlB,cAAA,GAAAE,CAAA;IAE7B,OAAO,IAAIuB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MAAA;MAAA3B,cAAA,GAAAY,CAAA;MAAAZ,cAAA,GAAAE,CAAA;MACrC,MAAM0B,UAAU,GAAGA,CAAA,KAAK;QAAA;QAAA5B,cAAA,GAAAY,CAAA;QAAAZ,cAAA,GAAAE,CAAA;QACtB,IAAI,IAAI,CAACkB,UAAU,EAAE,EAAE;UAAA;UAAApB,cAAA,GAAAa,CAAA;UAAAb,cAAA,GAAAE,CAAA;UACrBwB,OAAO,EAAE;UAAC;UAAA1B,cAAA,GAAAE,CAAA;UACV;QACF,CAAC;QAAA;QAAA;UAAAF,cAAA,GAAAa,CAAA;QAAA;QAAAb,cAAA,GAAAE,CAAA;QAED,IAAIe,IAAI,CAACC,GAAG,EAAE,GAAGM,SAAS,IAAID,SAAS,EAAE;UAAA;UAAAvB,cAAA,GAAAa,CAAA;UAAAb,cAAA,GAAAE,CAAA;UACvCyB,MAAM,CAAC,IAAIrB,QAAA,CAAAuB,cAAc,CAAC,6BAA6B,CAAC,CAAC;UAAC;UAAA7B,cAAA,GAAAE,CAAA;UAC1D;QACF,CAAC;QAAA;QAAA;UAAAF,cAAA,GAAAa,CAAA;QAAA;QAED;QAAAb,cAAA,GAAAE,CAAA;QACA4B,UAAU,CAACF,UAAU,EAAEG,IAAI,CAACC,GAAG,CAAC,IAAI,CAACb,cAAc,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;MAC/D,CAAC;MAAC;MAAAnB,cAAA,GAAAE,CAAA;MAEF0B,UAAU,EAAE;IACd,CAAC,CAAC;EACJ;EAEA;;;;EAIOK,kBAAkBA,CAAA;IAAA;IAAAjC,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAE,CAAA;IACvB,IAAI,CAACmB,YAAY,EAAE;IAAC;IAAArB,cAAA,GAAAE,CAAA;IACpB,OAAO6B,IAAI,CAACG,KAAK,CAAC,IAAI,CAACnB,MAAM,CAAC;EAChC;EAEA;;;EAGOoB,KAAKA,CAAA;IAAA;IAAAnC,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAE,CAAA;IACV,IAAI,CAACa,MAAM,GAAG,IAAI,CAACJ,UAAU;IAAC;IAAAX,cAAA,GAAAE,CAAA;IAC9B,IAAI,CAACc,UAAU,GAAGC,IAAI,CAACC,GAAG,EAAE;EAC9B;EAEA;;;;EAIOkB,qBAAqBA,CAAA;IAAA;IAAApC,cAAA,GAAAY,CAAA;IAAAZ,cAAA,GAAAE,CAAA;IAC1B,IAAI,CAACmB,YAAY,EAAE;IAAC;IAAArB,cAAA,GAAAE,CAAA;IAEpB,IAAI,IAAI,CAACa,MAAM,IAAI,CAAC,EAAE;MAAA;MAAAf,cAAA,GAAAa,CAAA;MAAAb,cAAA,GAAAE,CAAA;MACpB,OAAO,CAAC;IACV,CAAC;IAAA;IAAA;MAAAF,cAAA,GAAAa,CAAA;IAAA;IAED,MAAMwB,mBAAmB;IAAA;IAAA,CAAArC,cAAA,GAAAE,CAAA,QAAGe,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACF,UAAU;IACxD,MAAMsB,gBAAgB;IAAA;IAAA,CAAAtC,cAAA,GAAAE,CAAA,QAAG,IAAI,CAACiB,cAAc,GAAIkB,mBAAmB,GAAG,IAAI,CAAClB,cAAe;IAAC;IAAAnB,cAAA,GAAAE,CAAA;IAE3F,OAAO6B,IAAI,CAACQ,GAAG,CAAC,CAAC,EAAED,gBAAgB,CAAC;EACtC;EAEA;;;EAGQjB,YAAYA,CAAA;IAAA;IAAArB,cAAA,GAAAY,CAAA;IAClB,MAAMM,GAAG;IAAA;IAAA,CAAAlB,cAAA,GAAAE,CAAA,QAAGe,IAAI,CAACC,GAAG,EAAE;IACtB,MAAMsB,UAAU;IAAA;IAAA,CAAAxC,cAAA,GAAAE,CAAA,QAAGgB,GAAG,GAAG,IAAI,CAACF,UAAU;IAAC;IAAAhB,cAAA,GAAAE,CAAA;IAEzC,IAAIsC,UAAU,IAAI,IAAI,CAACrB,cAAc,EAAE;MAAA;MAAAnB,cAAA,GAAAa,CAAA;MACrC,MAAM4B,WAAW;MAAA;MAAA,CAAAzC,cAAA,GAAAE,CAAA,QAAG6B,IAAI,CAACG,KAAK,CAACM,UAAU,GAAG,IAAI,CAACrB,cAAc,CAAC;MAAC;MAAAnB,cAAA,GAAAE,CAAA;MACjE,IAAI,CAACa,MAAM,GAAGgB,IAAI,CAACC,GAAG,CAAC,IAAI,CAACrB,UAAU,EAAE,IAAI,CAACI,MAAM,GAAG0B,WAAW,CAAC;MAAC;MAAAzC,cAAA,GAAAE,CAAA;MACnE,IAAI,CAACc,UAAU,GAAGE,GAAG,GAAIsB,UAAU,GAAG,IAAI,CAACrB,cAAe;IAC5D,CAAC;IAAA;IAAA;MAAAnB,cAAA,GAAAa,CAAA;IAAA;EACH;;AACD;AAAAb,cAAA,GAAAE,CAAA;AAlHDC,OAAA,CAAAK,sBAAA,GAAAA,sBAAA;AAoHA;;;AAGA,IAAIkC,iBAAiB;AAAA;AAAA,CAAA1C,cAAA,GAAAE,CAAA,QAAkC,IAAI;AAE3D;;;;;;AAMA,SAAgBE,oBAAoBA,CAClCM,eAAA;AAAA;AAAA,CAAAV,cAAA,GAAAa,CAAA,UAA0B,EAAE,GAC5BF,UAAA;AAAA;AAAA,CAAAX,cAAA,GAAAa,CAAA,UAAqB,EAAE;EAAA;EAAAb,cAAA,GAAAY,CAAA;EAAAZ,cAAA,GAAAE,CAAA;EAEvB,IAAI,CAACwC,iBAAiB,EAAE;IAAA;IAAA1C,cAAA,GAAAa,CAAA;IAAAb,cAAA,GAAAE,CAAA;IACtBwC,iBAAiB,GAAG,IAAIlC,sBAAsB,CAACE,eAAe,EAAEC,UAAU,CAAC;EAC7E,CAAC;EAAA;EAAA;IAAAX,cAAA,GAAAa,CAAA;EAAA;EAAAb,cAAA,GAAAE,CAAA;EACD,OAAOwC,iBAAiB;AAC1B;AAEA;;;AAGA,SAAgBrC,sBAAsBA,CAAA;EAAA;EAAAL,cAAA,GAAAY,CAAA;EAAAZ,cAAA,GAAAE,CAAA;EACpCwC,iBAAiB,GAAG,IAAI;AAC1B", "ignoreList": []}