{"version": 3, "names": ["cov_gfdwisfzv", "actualCoverage", "s", "Errors_1", "require", "constants_1", "HttpClient", "constructor", "timeout", "b", "API_CONFIG", "REQUEST_TIMEOUT", "maxRetries", "MAX_RETRIES", "f", "requestInterceptors", "responseInterceptors", "defaultTimeout", "addRequestInterceptor", "interceptor", "push", "addResponseInterceptor", "get", "url", "config", "response", "request", "method", "data", "post", "body", "JSON", "stringify", "undefined", "headers", "head", "status", "statusText", "ok", "finalConfig", "retries", "lastError", "attempt", "makeRequest", "responseData", "error", "ApiError", "isRetryableError", "RateLimitError", "delay", "calculateRetryDelay", "sleep", "NetworkError", "Headers", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "fetch", "signal", "clearTimeout", "processResponse", "Error", "name", "message", "String", "retryAfter", "retryAfterSeconds", "parseInt", "errorText", "text", "catch", "contentType", "includes", "json", "statusCode", "baseDelay", "RETRY_DELAY_BASE", "Math", "pow", "ms", "Promise", "resolve", "exports"], "sources": ["/Users/<USER>/WebstormProjects/goo/src/services/httpClient.ts"], "sourcesContent": ["import { ApiError, NetworkError, RateLimitError } from '../models/Errors';\nimport { API_CONFIG } from '../constants';\n\n/**\n * HTTP request configuration\n */\nexport interface RequestConfig {\n  method?: string;\n  headers?: Record<string, string>;\n  body?: string;\n  timeout?: number;\n  retries?: number;\n}\n\n/**\n * HTTP response interface\n */\nexport interface HttpResponse<T = any> {\n  data: T;\n  status: number;\n  statusText: string;\n  headers: Headers;\n  ok: boolean;\n}\n\n/**\n * Request interceptor function type\n */\nexport type RequestInterceptor = (config: RequestConfig) => RequestConfig;\n\n/**\n * Response interceptor function type\n */\nexport type ResponseInterceptor = <T>(response: T) => T;\n\n/**\n * HTTP client with retry logic, interceptors, and error handling\n */\nexport class HttpClient {\n  private requestInterceptors: RequestInterceptor[] = [];\n  private responseInterceptors: ResponseInterceptor[] = [];\n  private readonly defaultTimeout: number;\n  private readonly maxRetries: number;\n\n  constructor(timeout: number = API_CONFIG.REQUEST_TIMEOUT, maxRetries: number = API_CONFIG.MAX_RETRIES) {\n    this.defaultTimeout = timeout;\n    this.maxRetries = maxRetries;\n  }\n\n  /**\n   * Adds a request interceptor\n   * @param interceptor - Function to modify request config\n   */\n  addRequestInterceptor(interceptor: RequestInterceptor): void {\n    this.requestInterceptors.push(interceptor);\n  }\n\n  /**\n   * Adds a response interceptor\n   * @param interceptor - Function to modify response data\n   */\n  addResponseInterceptor(interceptor: ResponseInterceptor): void {\n    this.responseInterceptors.push(interceptor);\n  }\n\n  /**\n   * Makes a GET request\n   * @param url - Request URL\n   * @param config - Request configuration\n   * @returns Promise resolving to response data\n   */\n  async get<T = any>(url: string, config: Partial<RequestConfig> = {}): Promise<T> {\n    const response = await this.request<T>(url, {\n      ...config,\n      method: 'GET',\n    });\n    return response.data;\n  }\n\n  /**\n   * Makes a POST request\n   * @param url - Request URL\n   * @param data - Request body data\n   * @param config - Request configuration\n   * @returns Promise resolving to response data\n   */\n  async post<T = any>(url: string, data?: any, config: Partial<RequestConfig> = {}): Promise<T> {\n    const response = await this.request<T>(url, {\n      ...config,\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined,\n      headers: {\n        'Content-Type': 'application/json',\n        ...config.headers,\n      },\n    });\n    return response.data;\n  }\n\n  /**\n   * Makes a HEAD request\n   * @param url - Request URL\n   * @param config - Request configuration\n   * @returns Promise resolving to response (without body)\n   */\n  async head(url: string, config: Partial<RequestConfig> = {}): Promise<Omit<HttpResponse, 'data'>> {\n    const response = await this.request(url, {\n      ...config,\n      method: 'HEAD',\n    });\n\n    return {\n      status: response.status,\n      statusText: response.statusText,\n      headers: response.headers,\n      ok: response.ok,\n    };\n  }\n\n  /**\n   * Makes an HTTP request with retry logic\n   * @param url - Request URL\n   * @param config - Request configuration\n   * @returns Promise resolving to HTTP response\n   */\n  private async request<T = any>(url: string, config: RequestConfig): Promise<HttpResponse<T>> {\n    let finalConfig = { ...config };\n\n    // Apply request interceptors\n    for (const interceptor of this.requestInterceptors) {\n      finalConfig = interceptor(finalConfig);\n    }\n\n    const timeout = finalConfig.timeout || this.defaultTimeout;\n    const maxRetries = finalConfig.retries !== undefined ? finalConfig.retries : this.maxRetries;\n\n    let lastError: Error | null = null;\n\n    for (let attempt = 0; attempt <= maxRetries; attempt++) {\n      try {\n        const response = await this.makeRequest<T>(url, finalConfig, timeout);\n\n        // Apply response interceptors\n        let responseData = response.data;\n        for (const interceptor of this.responseInterceptors) {\n          responseData = interceptor(responseData);\n        }\n\n        return {\n          ...response,\n          data: responseData,\n        };\n      } catch (error) {\n        lastError = error as Error;\n\n        // Don't retry on certain errors\n        if (error instanceof ApiError && !this.isRetryableError(error)) {\n          throw error;\n        }\n\n        if (error instanceof RateLimitError) {\n          throw error;\n        }\n\n        // Don't retry on the last attempt\n        if (attempt >= maxRetries) {\n          break;\n        }\n\n        // Wait before retrying (exponential backoff) - but only if we're going to retry\n        if (attempt < maxRetries) {\n          const delay = this.calculateRetryDelay(attempt);\n          await this.sleep(delay);\n        }\n      }\n    }\n\n    // If we get here, all retries failed\n    throw lastError || new NetworkError('All retry attempts failed');\n  }\n\n  /**\n   * Makes the actual HTTP request\n   * @param url - Request URL\n   * @param config - Request configuration\n   * @param timeout - Request timeout\n   * @returns Promise resolving to HTTP response\n   */\n  private async makeRequest<T>(url: string, config: RequestConfig, timeout: number): Promise<HttpResponse<T>> {\n    try {\n      const headers = new Headers({\n        'User-Agent': 'BusinessSearchApp/1.0',\n        ...config.headers,\n      });\n\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), timeout);\n\n      try {\n        const response = await fetch(url, {\n          method: config.method || 'GET',\n          headers,\n          body: config.body,\n          signal: controller.signal,\n        });\n\n        clearTimeout(timeoutId);\n        return await this.processResponse<T>(response, config);\n      } catch (error) {\n        clearTimeout(timeoutId);\n        throw error;\n      }\n    } catch (error) {\n      if (error instanceof ApiError || error instanceof RateLimitError) {\n        throw error;\n      }\n\n      if (error instanceof Error) {\n        if (error.name === 'AbortError') {\n          throw new NetworkError('Request timed out', error);\n        }\n        throw new NetworkError(`Network error: ${error.message}`, error);\n      }\n\n      throw new NetworkError(`Unknown network error: ${String(error)}`);\n    }\n  }\n\n  /**\n   * Processes the HTTP response\n   * @param response - The fetch response\n   * @param config - Request configuration\n   * @returns Promise resolving to HTTP response\n   */\n  private async processResponse<T>(response: Response, config: RequestConfig): Promise<HttpResponse<T>> {\n    // Handle rate limiting\n    if (response.status === 429) {\n      const retryAfter = response.headers.get('Retry-After');\n      const retryAfterSeconds = retryAfter ? parseInt(retryAfter, 10) : undefined;\n      throw new RateLimitError('Rate limit exceeded', retryAfterSeconds);\n    }\n\n    // Handle other HTTP errors\n    if (!response.ok) {\n      const errorText = await response.text().catch(() => 'Unknown error');\n      throw new ApiError(\n        `HTTP ${response.status}: ${response.statusText} - ${errorText}`,\n        response.status\n      );\n    }\n\n    // Parse response data\n    let data: T;\n    const contentType = response.headers.get('content-type');\n\n    if (config.method === 'HEAD') {\n      data = null as any;\n    } else if (contentType && contentType.includes('application/json')) {\n      try {\n        data = await response.json();\n      } catch (error) {\n        throw new NetworkError('JSON parsing failed', error instanceof Error ? error : undefined);\n      }\n    } else {\n      try {\n        data = await response.text() as any;\n      } catch (error) {\n        throw new NetworkError('Text parsing failed', error instanceof Error ? error : undefined);\n      }\n    }\n\n    return {\n      data,\n      status: response.status,\n      statusText: response.statusText,\n      headers: response.headers,\n      ok: response.ok,\n    };\n  }\n\n  /**\n   * Determines if an error should be retried\n   * @param error - Error to check\n   * @returns True if error should be retried\n   */\n  private isRetryableError(error: ApiError): boolean {\n    // Retry on 5xx server errors and 429 rate limit errors\n    return (error.statusCode >= 500 && error.statusCode < 600) || error.statusCode === 429;\n  }\n\n  /**\n   * Calculates retry delay using exponential backoff\n   * @param attempt - The current attempt number (0-based)\n   * @returns Delay in milliseconds\n   */\n  private calculateRetryDelay(attempt: number): number {\n    const baseDelay = API_CONFIG.RETRY_DELAY_BASE;\n    return baseDelay * Math.pow(2, attempt);\n  }\n\n  /**\n   * Sleeps for the specified duration\n   * @param ms - Duration in milliseconds\n   * @returns Promise that resolves after the delay\n   */\n  private sleep(ms: number): Promise<void> {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAiDE;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;;;;AAjDF,MAAAC,QAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAC,WAAA;AAAA;AAAA,CAAAL,aAAA,GAAAE,CAAA,OAAAE,OAAA;AAkCA;;;AAGA,MAAaE,UAAU;EAMrBC,YAAYC,OAAA;EAAA;EAAA,CAAAR,aAAA,GAAAS,CAAA,UAAkBJ,WAAA,CAAAK,UAAU,CAACC,eAAe,GAAEC,UAAA;EAAA;EAAA,CAAAZ,aAAA,GAAAS,CAAA,UAAqBJ,WAAA,CAAAK,UAAU,CAACG,WAAW;IAAA;IAAAb,aAAA,GAAAc,CAAA;IAAAd,aAAA,GAAAE,CAAA;IAL7F,KAAAa,mBAAmB,GAAyB,EAAE;IAAC;IAAAf,aAAA,GAAAE,CAAA;IAC/C,KAAAc,oBAAoB,GAA0B,EAAE;IAAC;IAAAhB,aAAA,GAAAE,CAAA;IAKvD,IAAI,CAACe,cAAc,GAAGT,OAAO;IAAC;IAAAR,aAAA,GAAAE,CAAA;IAC9B,IAAI,CAACU,UAAU,GAAGA,UAAU;EAC9B;EAEA;;;;EAIAM,qBAAqBA,CAACC,WAA+B;IAAA;IAAAnB,aAAA,GAAAc,CAAA;IAAAd,aAAA,GAAAE,CAAA;IACnD,IAAI,CAACa,mBAAmB,CAACK,IAAI,CAACD,WAAW,CAAC;EAC5C;EAEA;;;;EAIAE,sBAAsBA,CAACF,WAAgC;IAAA;IAAAnB,aAAA,GAAAc,CAAA;IAAAd,aAAA,GAAAE,CAAA;IACrD,IAAI,CAACc,oBAAoB,CAACI,IAAI,CAACD,WAAW,CAAC;EAC7C;EAEA;;;;;;EAMA,MAAMG,GAAGA,CAAUC,GAAW,EAAEC,MAAA;EAAA;EAAA,CAAAxB,aAAA,GAAAS,CAAA,UAAiC,EAAE;IAAA;IAAAT,aAAA,GAAAc,CAAA;IACjE,MAAMW,QAAQ;IAAA;IAAA,CAAAzB,aAAA,GAAAE,CAAA,QAAG,MAAM,IAAI,CAACwB,OAAO,CAAIH,GAAG,EAAE;MAC1C,GAAGC,MAAM;MACTG,MAAM,EAAE;KACT,CAAC;IAAC;IAAA3B,aAAA,GAAAE,CAAA;IACH,OAAOuB,QAAQ,CAACG,IAAI;EACtB;EAEA;;;;;;;EAOA,MAAMC,IAAIA,CAAUN,GAAW,EAAEK,IAAU,EAAEJ,MAAA;EAAA;EAAA,CAAAxB,aAAA,GAAAS,CAAA,UAAiC,EAAE;IAAA;IAAAT,aAAA,GAAAc,CAAA;IAC9E,MAAMW,QAAQ;IAAA;IAAA,CAAAzB,aAAA,GAAAE,CAAA,QAAG,MAAM,IAAI,CAACwB,OAAO,CAAIH,GAAG,EAAE;MAC1C,GAAGC,MAAM;MACTG,MAAM,EAAE,MAAM;MACdG,IAAI,EAAEF,IAAI;MAAA;MAAA,CAAA5B,aAAA,GAAAS,CAAA,UAAGsB,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC;MAAA;MAAA,CAAA5B,aAAA,GAAAS,CAAA,UAAGwB,SAAS;MAC7CC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,GAAGV,MAAM,CAACU;;KAEb,CAAC;IAAC;IAAAlC,aAAA,GAAAE,CAAA;IACH,OAAOuB,QAAQ,CAACG,IAAI;EACtB;EAEA;;;;;;EAMA,MAAMO,IAAIA,CAACZ,GAAW,EAAEC,MAAA;EAAA;EAAA,CAAAxB,aAAA,GAAAS,CAAA,UAAiC,EAAE;IAAA;IAAAT,aAAA,GAAAc,CAAA;IACzD,MAAMW,QAAQ;IAAA;IAAA,CAAAzB,aAAA,GAAAE,CAAA,QAAG,MAAM,IAAI,CAACwB,OAAO,CAACH,GAAG,EAAE;MACvC,GAAGC,MAAM;MACTG,MAAM,EAAE;KACT,CAAC;IAAC;IAAA3B,aAAA,GAAAE,CAAA;IAEH,OAAO;MACLkC,MAAM,EAAEX,QAAQ,CAACW,MAAM;MACvBC,UAAU,EAAEZ,QAAQ,CAACY,UAAU;MAC/BH,OAAO,EAAET,QAAQ,CAACS,OAAO;MACzBI,EAAE,EAAEb,QAAQ,CAACa;KACd;EACH;EAEA;;;;;;EAMQ,MAAMZ,OAAOA,CAAUH,GAAW,EAAEC,MAAqB;IAAA;IAAAxB,aAAA,GAAAc,CAAA;IAC/D,IAAIyB,WAAW;IAAA;IAAA,CAAAvC,aAAA,GAAAE,CAAA,QAAG;MAAE,GAAGsB;IAAM,CAAE;IAE/B;IAAA;IAAAxB,aAAA,GAAAE,CAAA;IACA,KAAK,MAAMiB,WAAW,IAAI,IAAI,CAACJ,mBAAmB,EAAE;MAAA;MAAAf,aAAA,GAAAE,CAAA;MAClDqC,WAAW,GAAGpB,WAAW,CAACoB,WAAW,CAAC;IACxC;IAEA,MAAM/B,OAAO;IAAA;IAAA,CAAAR,aAAA,GAAAE,CAAA;IAAG;IAAA,CAAAF,aAAA,GAAAS,CAAA,UAAA8B,WAAW,CAAC/B,OAAO;IAAA;IAAA,CAAAR,aAAA,GAAAS,CAAA,UAAI,IAAI,CAACQ,cAAc;IAC1D,MAAML,UAAU;IAAA;IAAA,CAAAZ,aAAA,GAAAE,CAAA,QAAGqC,WAAW,CAACC,OAAO,KAAKP,SAAS;IAAA;IAAA,CAAAjC,aAAA,GAAAS,CAAA,UAAG8B,WAAW,CAACC,OAAO;IAAA;IAAA,CAAAxC,aAAA,GAAAS,CAAA,UAAG,IAAI,CAACG,UAAU;IAE5F,IAAI6B,SAAS;IAAA;IAAA,CAAAzC,aAAA,GAAAE,CAAA,QAAiB,IAAI;IAAC;IAAAF,aAAA,GAAAE,CAAA;IAEnC,KAAK,IAAIwC,OAAO;IAAA;IAAA,CAAA1C,aAAA,GAAAE,CAAA,QAAG,CAAC,GAAEwC,OAAO,IAAI9B,UAAU,EAAE8B,OAAO,EAAE,EAAE;MAAA;MAAA1C,aAAA,GAAAE,CAAA;MACtD,IAAI;QACF,MAAMuB,QAAQ;QAAA;QAAA,CAAAzB,aAAA,GAAAE,CAAA,QAAG,MAAM,IAAI,CAACyC,WAAW,CAAIpB,GAAG,EAAEgB,WAAW,EAAE/B,OAAO,CAAC;QAErE;QACA,IAAIoC,YAAY;QAAA;QAAA,CAAA5C,aAAA,GAAAE,CAAA,QAAGuB,QAAQ,CAACG,IAAI;QAAC;QAAA5B,aAAA,GAAAE,CAAA;QACjC,KAAK,MAAMiB,WAAW,IAAI,IAAI,CAACH,oBAAoB,EAAE;UAAA;UAAAhB,aAAA,GAAAE,CAAA;UACnD0C,YAAY,GAAGzB,WAAW,CAACyB,YAAY,CAAC;QAC1C;QAAC;QAAA5C,aAAA,GAAAE,CAAA;QAED,OAAO;UACL,GAAGuB,QAAQ;UACXG,IAAI,EAAEgB;SACP;MACH,CAAC,CAAC,OAAOC,KAAK,EAAE;QAAA;QAAA7C,aAAA,GAAAE,CAAA;QACduC,SAAS,GAAGI,KAAc;QAE1B;QAAA;QAAA7C,aAAA,GAAAE,CAAA;QACA;QAAI;QAAA,CAAAF,aAAA,GAAAS,CAAA,UAAAoC,KAAK,YAAY1C,QAAA,CAAA2C,QAAQ;QAAA;QAAA,CAAA9C,aAAA,GAAAS,CAAA,UAAI,CAAC,IAAI,CAACsC,gBAAgB,CAACF,KAAK,CAAC,GAAE;UAAA;UAAA7C,aAAA,GAAAS,CAAA;UAAAT,aAAA,GAAAE,CAAA;UAC9D,MAAM2C,KAAK;QACb,CAAC;QAAA;QAAA;UAAA7C,aAAA,GAAAS,CAAA;QAAA;QAAAT,aAAA,GAAAE,CAAA;QAED,IAAI2C,KAAK,YAAY1C,QAAA,CAAA6C,cAAc,EAAE;UAAA;UAAAhD,aAAA,GAAAS,CAAA;UAAAT,aAAA,GAAAE,CAAA;UACnC,MAAM2C,KAAK;QACb,CAAC;QAAA;QAAA;UAAA7C,aAAA,GAAAS,CAAA;QAAA;QAED;QAAAT,aAAA,GAAAE,CAAA;QACA,IAAIwC,OAAO,IAAI9B,UAAU,EAAE;UAAA;UAAAZ,aAAA,GAAAS,CAAA;UAAAT,aAAA,GAAAE,CAAA;UACzB;QACF,CAAC;QAAA;QAAA;UAAAF,aAAA,GAAAS,CAAA;QAAA;QAED;QAAAT,aAAA,GAAAE,CAAA;QACA,IAAIwC,OAAO,GAAG9B,UAAU,EAAE;UAAA;UAAAZ,aAAA,GAAAS,CAAA;UACxB,MAAMwC,KAAK;UAAA;UAAA,CAAAjD,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACgD,mBAAmB,CAACR,OAAO,CAAC;UAAC;UAAA1C,aAAA,GAAAE,CAAA;UAChD,MAAM,IAAI,CAACiD,KAAK,CAACF,KAAK,CAAC;QACzB,CAAC;QAAA;QAAA;UAAAjD,aAAA,GAAAS,CAAA;QAAA;MACH;IACF;IAEA;IAAA;IAAAT,aAAA,GAAAE,CAAA;IACA,MAAM,2BAAAF,aAAA,GAAAS,CAAA,WAAAgC,SAAS;IAAA;IAAA,CAAAzC,aAAA,GAAAS,CAAA,WAAI,IAAIN,QAAA,CAAAiD,YAAY,CAAC,2BAA2B,CAAC;EAClE;EAEA;;;;;;;EAOQ,MAAMT,WAAWA,CAAIpB,GAAW,EAAEC,MAAqB,EAAEhB,OAAe;IAAA;IAAAR,aAAA,GAAAc,CAAA;IAAAd,aAAA,GAAAE,CAAA;IAC9E,IAAI;MACF,MAAMgC,OAAO;MAAA;MAAA,CAAAlC,aAAA,GAAAE,CAAA,QAAG,IAAImD,OAAO,CAAC;QAC1B,YAAY,EAAE,uBAAuB;QACrC,GAAG7B,MAAM,CAACU;OACX,CAAC;MAEF,MAAMoB,UAAU;MAAA;MAAA,CAAAtD,aAAA,GAAAE,CAAA,QAAG,IAAIqD,eAAe,EAAE;MACxC,MAAMC,SAAS;MAAA;MAAA,CAAAxD,aAAA,GAAAE,CAAA,QAAGuD,UAAU,CAAC,MAAM;QAAA;QAAAzD,aAAA,GAAAc,CAAA;QAAAd,aAAA,GAAAE,CAAA;QAAA,OAAAoD,UAAU,CAACI,KAAK,EAAE;MAAF,CAAE,EAAElD,OAAO,CAAC;MAAC;MAAAR,aAAA,GAAAE,CAAA;MAEhE,IAAI;QACF,MAAMuB,QAAQ;QAAA;QAAA,CAAAzB,aAAA,GAAAE,CAAA,QAAG,MAAMyD,KAAK,CAACpC,GAAG,EAAE;UAChCI,MAAM;UAAE;UAAA,CAAA3B,aAAA,GAAAS,CAAA,WAAAe,MAAM,CAACG,MAAM;UAAA;UAAA,CAAA3B,aAAA,GAAAS,CAAA,WAAI,KAAK;UAC9ByB,OAAO;UACPJ,IAAI,EAAEN,MAAM,CAACM,IAAI;UACjB8B,MAAM,EAAEN,UAAU,CAACM;SACpB,CAAC;QAAC;QAAA5D,aAAA,GAAAE,CAAA;QAEH2D,YAAY,CAACL,SAAS,CAAC;QAAC;QAAAxD,aAAA,GAAAE,CAAA;QACxB,OAAO,MAAM,IAAI,CAAC4D,eAAe,CAAIrC,QAAQ,EAAED,MAAM,CAAC;MACxD,CAAC,CAAC,OAAOqB,KAAK,EAAE;QAAA;QAAA7C,aAAA,GAAAE,CAAA;QACd2D,YAAY,CAACL,SAAS,CAAC;QAAC;QAAAxD,aAAA,GAAAE,CAAA;QACxB,MAAM2C,KAAK;MACb;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MAAA;MAAA7C,aAAA,GAAAE,CAAA;MACd;MAAI;MAAA,CAAAF,aAAA,GAAAS,CAAA,WAAAoC,KAAK,YAAY1C,QAAA,CAAA2C,QAAQ;MAAA;MAAA,CAAA9C,aAAA,GAAAS,CAAA,WAAIoC,KAAK,YAAY1C,QAAA,CAAA6C,cAAc,GAAE;QAAA;QAAAhD,aAAA,GAAAS,CAAA;QAAAT,aAAA,GAAAE,CAAA;QAChE,MAAM2C,KAAK;MACb,CAAC;MAAA;MAAA;QAAA7C,aAAA,GAAAS,CAAA;MAAA;MAAAT,aAAA,GAAAE,CAAA;MAED,IAAI2C,KAAK,YAAYkB,KAAK,EAAE;QAAA;QAAA/D,aAAA,GAAAS,CAAA;QAAAT,aAAA,GAAAE,CAAA;QAC1B,IAAI2C,KAAK,CAACmB,IAAI,KAAK,YAAY,EAAE;UAAA;UAAAhE,aAAA,GAAAS,CAAA;UAAAT,aAAA,GAAAE,CAAA;UAC/B,MAAM,IAAIC,QAAA,CAAAiD,YAAY,CAAC,mBAAmB,EAAEP,KAAK,CAAC;QACpD,CAAC;QAAA;QAAA;UAAA7C,aAAA,GAAAS,CAAA;QAAA;QAAAT,aAAA,GAAAE,CAAA;QACD,MAAM,IAAIC,QAAA,CAAAiD,YAAY,CAAC,kBAAkBP,KAAK,CAACoB,OAAO,EAAE,EAAEpB,KAAK,CAAC;MAClE,CAAC;MAAA;MAAA;QAAA7C,aAAA,GAAAS,CAAA;MAAA;MAAAT,aAAA,GAAAE,CAAA;MAED,MAAM,IAAIC,QAAA,CAAAiD,YAAY,CAAC,0BAA0Bc,MAAM,CAACrB,KAAK,CAAC,EAAE,CAAC;IACnE;EACF;EAEA;;;;;;EAMQ,MAAMiB,eAAeA,CAAIrC,QAAkB,EAAED,MAAqB;IAAA;IAAAxB,aAAA,GAAAc,CAAA;IAAAd,aAAA,GAAAE,CAAA;IACxE;IACA,IAAIuB,QAAQ,CAACW,MAAM,KAAK,GAAG,EAAE;MAAA;MAAApC,aAAA,GAAAS,CAAA;MAC3B,MAAM0D,UAAU;MAAA;MAAA,CAAAnE,aAAA,GAAAE,CAAA,QAAGuB,QAAQ,CAACS,OAAO,CAACZ,GAAG,CAAC,aAAa,CAAC;MACtD,MAAM8C,iBAAiB;MAAA;MAAA,CAAApE,aAAA,GAAAE,CAAA,QAAGiE,UAAU;MAAA;MAAA,CAAAnE,aAAA,GAAAS,CAAA,WAAG4D,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAAA;MAAA,CAAAnE,aAAA,GAAAS,CAAA,WAAGwB,SAAS;MAAC;MAAAjC,aAAA,GAAAE,CAAA;MAC5E,MAAM,IAAIC,QAAA,CAAA6C,cAAc,CAAC,qBAAqB,EAAEoB,iBAAiB,CAAC;IACpE,CAAC;IAAA;IAAA;MAAApE,aAAA,GAAAS,CAAA;IAAA;IAED;IAAAT,aAAA,GAAAE,CAAA;IACA,IAAI,CAACuB,QAAQ,CAACa,EAAE,EAAE;MAAA;MAAAtC,aAAA,GAAAS,CAAA;MAChB,MAAM6D,SAAS;MAAA;MAAA,CAAAtE,aAAA,GAAAE,CAAA,QAAG,MAAMuB,QAAQ,CAAC8C,IAAI,EAAE,CAACC,KAAK,CAAC,MAAM;QAAA;QAAAxE,aAAA,GAAAc,CAAA;QAAAd,aAAA,GAAAE,CAAA;QAAA,sBAAe;MAAf,CAAe,CAAC;MAAC;MAAAF,aAAA,GAAAE,CAAA;MACrE,MAAM,IAAIC,QAAA,CAAA2C,QAAQ,CAChB,QAAQrB,QAAQ,CAACW,MAAM,KAAKX,QAAQ,CAACY,UAAU,MAAMiC,SAAS,EAAE,EAChE7C,QAAQ,CAACW,MAAM,CAChB;IACH,CAAC;IAAA;IAAA;MAAApC,aAAA,GAAAS,CAAA;IAAA;IAED;IACA,IAAImB,IAAO;IACX,MAAM6C,WAAW;IAAA;IAAA,CAAAzE,aAAA,GAAAE,CAAA,QAAGuB,QAAQ,CAACS,OAAO,CAACZ,GAAG,CAAC,cAAc,CAAC;IAAC;IAAAtB,aAAA,GAAAE,CAAA;IAEzD,IAAIsB,MAAM,CAACG,MAAM,KAAK,MAAM,EAAE;MAAA;MAAA3B,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAE,CAAA;MAC5B0B,IAAI,GAAG,IAAW;IACpB,CAAC,MAAM;MAAA;MAAA5B,aAAA,GAAAS,CAAA;MAAAT,aAAA,GAAAE,CAAA;MAAA;MAAI;MAAA,CAAAF,aAAA,GAAAS,CAAA,WAAAgE,WAAW;MAAA;MAAA,CAAAzE,aAAA,GAAAS,CAAA,WAAIgE,WAAW,CAACC,QAAQ,CAAC,kBAAkB,CAAC,GAAE;QAAA;QAAA1E,aAAA,GAAAS,CAAA;QAAAT,aAAA,GAAAE,CAAA;QAClE,IAAI;UAAA;UAAAF,aAAA,GAAAE,CAAA;UACF0B,IAAI,GAAG,MAAMH,QAAQ,CAACkD,IAAI,EAAE;QAC9B,CAAC,CAAC,OAAO9B,KAAK,EAAE;UAAA;UAAA7C,aAAA,GAAAE,CAAA;UACd,MAAM,IAAIC,QAAA,CAAAiD,YAAY,CAAC,qBAAqB,EAAEP,KAAK,YAAYkB,KAAK;UAAA;UAAA,CAAA/D,aAAA,GAAAS,CAAA,WAAGoC,KAAK;UAAA;UAAA,CAAA7C,aAAA,GAAAS,CAAA,WAAGwB,SAAS,EAAC;QAC3F;MACF,CAAC,MAAM;QAAA;QAAAjC,aAAA,GAAAS,CAAA;QAAAT,aAAA,GAAAE,CAAA;QACL,IAAI;UAAA;UAAAF,aAAA,GAAAE,CAAA;UACF0B,IAAI,GAAG,MAAMH,QAAQ,CAAC8C,IAAI,EAAS;QACrC,CAAC,CAAC,OAAO1B,KAAK,EAAE;UAAA;UAAA7C,aAAA,GAAAE,CAAA;UACd,MAAM,IAAIC,QAAA,CAAAiD,YAAY,CAAC,qBAAqB,EAAEP,KAAK,YAAYkB,KAAK;UAAA;UAAA,CAAA/D,aAAA,GAAAS,CAAA,WAAGoC,KAAK;UAAA;UAAA,CAAA7C,aAAA,GAAAS,CAAA,WAAGwB,SAAS,EAAC;QAC3F;MACF;IAAA;IAAC;IAAAjC,aAAA,GAAAE,CAAA;IAED,OAAO;MACL0B,IAAI;MACJQ,MAAM,EAAEX,QAAQ,CAACW,MAAM;MACvBC,UAAU,EAAEZ,QAAQ,CAACY,UAAU;MAC/BH,OAAO,EAAET,QAAQ,CAACS,OAAO;MACzBI,EAAE,EAAEb,QAAQ,CAACa;KACd;EACH;EAEA;;;;;EAKQS,gBAAgBA,CAACF,KAAe;IAAA;IAAA7C,aAAA,GAAAc,CAAA;IAAAd,aAAA,GAAAE,CAAA;IACtC;IACA,OAAQ,2BAAAF,aAAA,GAAAS,CAAA,WAAAoC,KAAK,CAAC+B,UAAU,IAAI,GAAG;IAAA;IAAA,CAAA5E,aAAA,GAAAS,CAAA,WAAIoC,KAAK,CAAC+B,UAAU,GAAG,GAAG;IAAA;IAAA,CAAA5E,aAAA,GAAAS,CAAA,WAAKoC,KAAK,CAAC+B,UAAU,KAAK,GAAG;EACxF;EAEA;;;;;EAKQ1B,mBAAmBA,CAACR,OAAe;IAAA;IAAA1C,aAAA,GAAAc,CAAA;IACzC,MAAM+D,SAAS;IAAA;IAAA,CAAA7E,aAAA,GAAAE,CAAA,QAAGG,WAAA,CAAAK,UAAU,CAACoE,gBAAgB;IAAC;IAAA9E,aAAA,GAAAE,CAAA;IAC9C,OAAO2E,SAAS,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEtC,OAAO,CAAC;EACzC;EAEA;;;;;EAKQS,KAAKA,CAAC8B,EAAU;IAAA;IAAAjF,aAAA,GAAAc,CAAA;IAAAd,aAAA,GAAAE,CAAA;IACtB,OAAO,IAAIgF,OAAO,CAACC,OAAO,IAAI;MAAA;MAAAnF,aAAA,GAAAc,CAAA;MAAAd,aAAA,GAAAE,CAAA;MAAA,OAAAuD,UAAU,CAAC0B,OAAO,EAAEF,EAAE,CAAC;IAAD,CAAC,CAAC;EACxD;;AACD;AAAAjF,aAAA,GAAAE,CAAA;AA9QDkF,OAAA,CAAA9E,UAAA,GAAAA,UAAA", "ignoreList": []}