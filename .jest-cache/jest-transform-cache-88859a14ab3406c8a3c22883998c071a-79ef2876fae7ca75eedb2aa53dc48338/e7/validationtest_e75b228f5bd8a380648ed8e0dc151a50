73128a4966b16fb06f51a4b6e64ea287
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const Errors_1 = require("../../src/models/Errors");
const validation_1 = require("../../src/utils/validation");
describe('Validation Utils', () => {
    describe('validateZipCode', () => {
        it('should accept valid 5-digit zip codes', () => {
            expect(() => (0, validation_1.validateZipCode)('90210')).not.toThrow(); // Beverly Hills, CA
            expect(() => (0, validation_1.validateZipCode)('10001')).not.toThrow(); // New York, NY
            expect(() => (0, validation_1.validateZipCode)('60601')).not.toThrow(); // Chicago, IL
            expect(() => (0, validation_1.validateZipCode)('94102')).not.toThrow(); // San Francisco, CA
        });
        it('should accept valid 9-digit zip codes', () => {
            expect(() => (0, validation_1.validateZipCode)('90210-1234')).not.toThrow(); // ZIP+4 format
            expect(() => (0, validation_1.validateZipCode)('10001-0001')).not.toThrow(); // ZIP+4 format
            expect(() => (0, validation_1.validateZipCode)('60601-5555')).not.toThrow(); // ZIP+4 format
        });
        it('should reject invalid zip code formats', () => {
            expect(() => (0, validation_1.validateZipCode)('1234')).toThrow(Errors_1.ValidationError); // Too short
            expect(() => (0, validation_1.validateZipCode)('123456')).toThrow(Errors_1.ValidationError); // Too long
            expect(() => (0, validation_1.validateZipCode)('12345-123')).toThrow(Errors_1.ValidationError); // Invalid ZIP+4
            expect(() => (0, validation_1.validateZipCode)('12345-12345')).toThrow(Errors_1.ValidationError); // Invalid ZIP+4
            expect(() => (0, validation_1.validateZipCode)('abcde')).toThrow(Errors_1.ValidationError); // Letters
            expect(() => (0, validation_1.validateZipCode)('')).toThrow(Errors_1.ValidationError); // Empty
            expect(() => (0, validation_1.validateZipCode)('99999')).toThrow(Errors_1.ValidationError); // Non-existent zip
        });
        it('should throw ValidationError with correct message and field', () => {
            try {
                (0, validation_1.validateZipCode)('invalid');
            }
            catch (error) {
                expect(error).toBeInstanceOf(Errors_1.ValidationError);
                expect(error.field).toBe('zipCode');
                expect(error.message).toContain('Invalid zip code format');
            }
        });
    });
    describe('validateRadius', () => {
        it('should accept valid radius values', () => {
            expect(() => (0, validation_1.validateRadius)(1)).not.toThrow();
            expect(() => (0, validation_1.validateRadius)(25)).not.toThrow();
            expect(() => (0, validation_1.validateRadius)(50)).not.toThrow();
        });
        it('should reject radius values outside valid range', () => {
            expect(() => (0, validation_1.validateRadius)(0)).toThrow(Errors_1.ValidationError);
            expect(() => (0, validation_1.validateRadius)(51)).toThrow(Errors_1.ValidationError);
            expect(() => (0, validation_1.validateRadius)(-5)).toThrow(Errors_1.ValidationError);
        });
        it('should reject non-integer radius values', () => {
            expect(() => (0, validation_1.validateRadius)(1.5)).toThrow(Errors_1.ValidationError);
            expect(() => (0, validation_1.validateRadius)(25.7)).toThrow(Errors_1.ValidationError);
        });
        it('should throw ValidationError with correct field', () => {
            try {
                (0, validation_1.validateRadius)(0);
            }
            catch (error) {
                expect(error).toBeInstanceOf(Errors_1.ValidationError);
                expect(error.field).toBe('radius');
            }
        });
    });
    describe('validateBusinessType', () => {
        it('should accept valid business types', () => {
            expect(() => (0, validation_1.validateBusinessType)('restaurant')).not.toThrow();
            expect(() => (0, validation_1.validateBusinessType)('gas_station')).not.toThrow();
            expect(() => (0, validation_1.validateBusinessType)('hospital')).not.toThrow();
        });
        it('should reject invalid business types', () => {
            expect(() => (0, validation_1.validateBusinessType)('invalid_type')).toThrow(Errors_1.ValidationError);
            expect(() => (0, validation_1.validateBusinessType)('')).toThrow(Errors_1.ValidationError);
            expect(() => (0, validation_1.validateBusinessType)('RESTAURANT')).toThrow(Errors_1.ValidationError);
        });
        it('should throw ValidationError with correct field', () => {
            try {
                (0, validation_1.validateBusinessType)('invalid');
            }
            catch (error) {
                expect(error).toBeInstanceOf(Errors_1.ValidationError);
                expect(error.field).toBe('businessType');
            }
        });
    });
    describe('validateUrl', () => {
        it('should accept valid URLs', () => {
            expect(() => (0, validation_1.validateUrl)('https://example.com')).not.toThrow();
            expect(() => (0, validation_1.validateUrl)('http://test.org')).not.toThrow();
            expect(() => (0, validation_1.validateUrl)('https://subdomain.example.com/path')).not.toThrow();
        });
        it('should reject invalid URLs', () => {
            expect(() => (0, validation_1.validateUrl)('example.com')).toThrow(Errors_1.ValidationError);
            expect(() => (0, validation_1.validateUrl)('ftp://example.com')).toThrow(Errors_1.ValidationError);
            expect(() => (0, validation_1.validateUrl)('')).toThrow(Errors_1.ValidationError);
            expect(() => (0, validation_1.validateUrl)('not-a-url')).toThrow(Errors_1.ValidationError);
        });
        it('should throw ValidationError with correct field', () => {
            try {
                (0, validation_1.validateUrl)('invalid');
            }
            catch (error) {
                expect(error).toBeInstanceOf(Errors_1.ValidationError);
                expect(error.field).toBe('url');
            }
        });
    });
    describe('validateSearchParams', () => {
        const validParams = {
            zipCode: '90210',
            radius: 10,
            businessType: 'restaurant'
        };
        it('should accept valid search parameters', () => {
            expect(() => (0, validation_1.validateSearchParams)(validParams)).not.toThrow();
        });
        it('should reject invalid zip code in search params', () => {
            const invalidParams = { ...validParams, zipCode: 'invalid' };
            expect(() => (0, validation_1.validateSearchParams)(invalidParams)).toThrow(Errors_1.ValidationError);
        });
        it('should reject invalid radius in search params', () => {
            const invalidParams = { ...validParams, radius: 0 };
            expect(() => (0, validation_1.validateSearchParams)(invalidParams)).toThrow(Errors_1.ValidationError);
        });
        it('should reject invalid business type in search params', () => {
            const invalidParams = { ...validParams, businessType: 'invalid' };
            expect(() => (0, validation_1.validateSearchParams)(invalidParams)).toThrow(Errors_1.ValidationError);
        });
        it('should validate each parameter individually', () => {
            expect(() => (0, validation_1.validateSearchParams)({
                zipCode: '90210',
                radius: 10,
                businessType: 'restaurant'
            })).not.toThrow();
            expect(() => (0, validation_1.validateSearchParams)({
                zipCode: 'invalid',
                radius: 10,
                businessType: 'restaurant'
            })).toThrow();
            expect(() => (0, validation_1.validateSearchParams)({
                zipCode: '90210',
                radius: 0,
                businessType: 'restaurant'
            })).toThrow();
            expect(() => (0, validation_1.validateSearchParams)({
                zipCode: '90210',
                radius: 10,
                businessType: 'invalid'
            })).toThrow();
        });
    });
    describe('validateRequired', () => {
        it('should pass for non-empty values', () => {
            expect(() => (0, validation_1.validateRequired)('value', 'field')).not.toThrow();
            expect(() => (0, validation_1.validateRequired)(123, 'number')).not.toThrow();
            expect(() => (0, validation_1.validateRequired)(true, 'boolean')).not.toThrow();
            expect(() => (0, validation_1.validateRequired)([], 'array')).not.toThrow();
            expect(() => (0, validation_1.validateRequired)({}, 'object')).not.toThrow();
        });
        it('should throw for empty or null values', () => {
            expect(() => (0, validation_1.validateRequired)('', 'field')).toThrow(Errors_1.ValidationError);
            expect(() => (0, validation_1.validateRequired)(null, 'field')).toThrow(Errors_1.ValidationError);
            expect(() => (0, validation_1.validateRequired)(undefined, 'field')).toThrow(Errors_1.ValidationError);
        });
        it('should include field name in error message', () => {
            try {
                (0, validation_1.validateRequired)('', 'testField');
            }
            catch (error) {
                expect(error).toBeInstanceOf(Errors_1.ValidationError);
                expect(error.message).toContain('testField');
            }
        });
    });
    describe('validateRange', () => {
        it('should pass for values within range', () => {
            expect(() => (0, validation_1.validateRange)(5, 1, 10, 'value')).not.toThrow();
            expect(() => (0, validation_1.validateRange)(1, 1, 10, 'value')).not.toThrow(); // Min boundary
            expect(() => (0, validation_1.validateRange)(10, 1, 10, 'value')).not.toThrow(); // Max boundary
        });
        it('should throw for values outside range', () => {
            expect(() => (0, validation_1.validateRange)(0, 1, 10, 'value')).toThrow(Errors_1.ValidationError);
            expect(() => (0, validation_1.validateRange)(11, 1, 10, 'value')).toThrow(Errors_1.ValidationError);
            expect(() => (0, validation_1.validateRange)(-5, 1, 10, 'value')).toThrow(Errors_1.ValidationError);
        });
        it('should include field name and range in error message', () => {
            try {
                (0, validation_1.validateRange)(15, 1, 10, 'testValue');
            }
            catch (error) {
                expect(error).toBeInstanceOf(Errors_1.ValidationError);
                expect(error.message).toContain('testValue');
                expect(error.message).toContain('1');
                expect(error.message).toContain('10');
            }
        });
        it('should handle edge cases', () => {
            expect(() => (0, validation_1.validateRange)(0, 0, 0, 'zero')).not.toThrow();
            expect(() => (0, validation_1.validateRange)(-5, -10, -1, 'negative')).not.toThrow();
            expect(() => (0, validation_1.validateRange)(1.5, 1, 2, 'decimal')).not.toThrow();
        });
    });
    describe('validatePhone', () => {
        it('should pass for valid US phone numbers', () => {
            expect(() => (0, validation_1.validatePhone)('(*************')).not.toThrow();
            expect(() => (0, validation_1.validatePhone)('************')).not.toThrow();
            expect(() => (0, validation_1.validatePhone)('5551234567')).not.toThrow();
            expect(() => (0, validation_1.validatePhone)('****** 123 4567')).not.toThrow();
            expect(() => (0, validation_1.validatePhone)('+15551234567')).not.toThrow();
        });
        it('should pass for valid international phone numbers', () => {
            expect(() => (0, validation_1.validatePhone)('+44 20 7946 0958')).not.toThrow(); // UK
            expect(() => (0, validation_1.validatePhone)('+33 1 42 86 83 26')).not.toThrow(); // France
            expect(() => (0, validation_1.validatePhone)('+81 3 1234 5678')).not.toThrow(); // Japan
        });
        it('should pass for empty phone number', () => {
            expect(() => (0, validation_1.validatePhone)('')).not.toThrow();
        });
        it('should throw for invalid phone numbers', () => {
            expect(() => (0, validation_1.validatePhone)('invalid-phone')).toThrow('Invalid phone number format');
            expect(() => (0, validation_1.validatePhone)('123')).toThrow('Invalid phone number format');
            expect(() => (0, validation_1.validatePhone)('abc-def-ghij')).toThrow('Invalid phone number format');
            expect(() => (0, validation_1.validatePhone)('555-555-555')).toThrow('Invalid phone number format'); // Too short
        });
        it('should throw ValidationError with correct field', () => {
            try {
                (0, validation_1.validatePhone)('invalid');
            }
            catch (error) {
                expect(error).toBeInstanceOf(Errors_1.ValidationError);
                expect(error.field).toBe('phone');
            }
        });
    });
    describe('validateEmail', () => {
        it('should pass for valid email addresses', () => {
            expect(() => (0, validation_1.validateEmail)('<EMAIL>')).not.toThrow();
            expect(() => (0, validation_1.validateEmail)('<EMAIL>')).not.toThrow();
            expect(() => (0, validation_1.validateEmail)('<EMAIL>')).not.toThrow();
            expect(() => (0, validation_1.validateEmail)('<EMAIL>')).not.toThrow();
            expect(() => (0, validation_1.validateEmail)('<EMAIL>')).not.toThrow();
        });
        it('should pass for empty email', () => {
            expect(() => (0, validation_1.validateEmail)('')).not.toThrow();
        });
        it('should throw for invalid email addresses', () => {
            expect(() => (0, validation_1.validateEmail)('invalid-email')).toThrow('Invalid email format');
            expect(() => (0, validation_1.validateEmail)('@example.com')).toThrow('Invalid email format');
            expect(() => (0, validation_1.validateEmail)('test@')).toThrow('Invalid email format');
            expect(() => (0, validation_1.validateEmail)('test.example.com')).toThrow('Invalid email format');
            expect(() => (0, validation_1.validateEmail)('test@example')).toThrow('Invalid email format'); // No TLD
            expect(() => (0, validation_1.validateEmail)('<EMAIL>')).toThrow('Invalid email format'); // Double dots
        });
        it('should throw ValidationError with correct field', () => {
            try {
                (0, validation_1.validateEmail)('invalid');
            }
            catch (error) {
                expect(error).toBeInstanceOf(Errors_1.ValidationError);
                expect(error.field).toBe('email');
            }
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************