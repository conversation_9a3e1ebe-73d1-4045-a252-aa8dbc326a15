f5bf3dff9eaec41811baf95bd23e81d5
"use strict";

/* istanbul ignore next */
function cov_gfdwisfzv() {
  var path = "/Users/<USER>/WebstormProjects/goo/src/services/httpClient.ts";
  var hash = "468e499a671c766d7252b1d42a131fac3e1fde3e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/WebstormProjects/goo/src/services/httpClient.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 3,
          column: 28
        }
      },
      "2": {
        start: {
          line: 4,
          column: 17
        },
        end: {
          line: 4,
          column: 44
        }
      },
      "3": {
        start: {
          line: 5,
          column: 20
        },
        end: {
          line: 5,
          column: 43
        }
      },
      "4": {
        start: {
          line: 11,
          column: 8
        },
        end: {
          line: 11,
          column: 38
        }
      },
      "5": {
        start: {
          line: 12,
          column: 8
        },
        end: {
          line: 12,
          column: 39
        }
      },
      "6": {
        start: {
          line: 13,
          column: 8
        },
        end: {
          line: 13,
          column: 38
        }
      },
      "7": {
        start: {
          line: 14,
          column: 8
        },
        end: {
          line: 14,
          column: 37
        }
      },
      "8": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 21,
          column: 51
        }
      },
      "9": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 28,
          column: 52
        }
      },
      "10": {
        start: {
          line: 37,
          column: 25
        },
        end: {
          line: 40,
          column: 10
        }
      },
      "11": {
        start: {
          line: 41,
          column: 8
        },
        end: {
          line: 41,
          column: 29
        }
      },
      "12": {
        start: {
          line: 51,
          column: 25
        },
        end: {
          line: 59,
          column: 10
        }
      },
      "13": {
        start: {
          line: 60,
          column: 8
        },
        end: {
          line: 60,
          column: 29
        }
      },
      "14": {
        start: {
          line: 69,
          column: 25
        },
        end: {
          line: 72,
          column: 10
        }
      },
      "15": {
        start: {
          line: 73,
          column: 8
        },
        end: {
          line: 78,
          column: 10
        }
      },
      "16": {
        start: {
          line: 87,
          column: 26
        },
        end: {
          line: 87,
          column: 39
        }
      },
      "17": {
        start: {
          line: 89,
          column: 8
        },
        end: {
          line: 91,
          column: 9
        }
      },
      "18": {
        start: {
          line: 90,
          column: 12
        },
        end: {
          line: 90,
          column: 51
        }
      },
      "19": {
        start: {
          line: 92,
          column: 24
        },
        end: {
          line: 92,
          column: 66
        }
      },
      "20": {
        start: {
          line: 93,
          column: 27
        },
        end: {
          line: 93,
          column: 100
        }
      },
      "21": {
        start: {
          line: 94,
          column: 24
        },
        end: {
          line: 94,
          column: 28
        }
      },
      "22": {
        start: {
          line: 95,
          column: 8
        },
        end: {
          line: 127,
          column: 9
        }
      },
      "23": {
        start: {
          line: 95,
          column: 27
        },
        end: {
          line: 95,
          column: 28
        }
      },
      "24": {
        start: {
          line: 96,
          column: 12
        },
        end: {
          line: 126,
          column: 13
        }
      },
      "25": {
        start: {
          line: 97,
          column: 33
        },
        end: {
          line: 97,
          column: 82
        }
      },
      "26": {
        start: {
          line: 99,
          column: 35
        },
        end: {
          line: 99,
          column: 48
        }
      },
      "27": {
        start: {
          line: 100,
          column: 16
        },
        end: {
          line: 102,
          column: 17
        }
      },
      "28": {
        start: {
          line: 101,
          column: 20
        },
        end: {
          line: 101,
          column: 61
        }
      },
      "29": {
        start: {
          line: 103,
          column: 16
        },
        end: {
          line: 106,
          column: 18
        }
      },
      "30": {
        start: {
          line: 109,
          column: 16
        },
        end: {
          line: 109,
          column: 34
        }
      },
      "31": {
        start: {
          line: 111,
          column: 16
        },
        end: {
          line: 113,
          column: 17
        }
      },
      "32": {
        start: {
          line: 112,
          column: 20
        },
        end: {
          line: 112,
          column: 32
        }
      },
      "33": {
        start: {
          line: 114,
          column: 16
        },
        end: {
          line: 116,
          column: 17
        }
      },
      "34": {
        start: {
          line: 115,
          column: 20
        },
        end: {
          line: 115,
          column: 32
        }
      },
      "35": {
        start: {
          line: 118,
          column: 16
        },
        end: {
          line: 120,
          column: 17
        }
      },
      "36": {
        start: {
          line: 119,
          column: 20
        },
        end: {
          line: 119,
          column: 26
        }
      },
      "37": {
        start: {
          line: 122,
          column: 16
        },
        end: {
          line: 125,
          column: 17
        }
      },
      "38": {
        start: {
          line: 123,
          column: 34
        },
        end: {
          line: 123,
          column: 67
        }
      },
      "39": {
        start: {
          line: 124,
          column: 20
        },
        end: {
          line: 124,
          column: 44
        }
      },
      "40": {
        start: {
          line: 129,
          column: 8
        },
        end: {
          line: 129,
          column: 82
        }
      },
      "41": {
        start: {
          line: 139,
          column: 8
        },
        end: {
          line: 172,
          column: 9
        }
      },
      "42": {
        start: {
          line: 140,
          column: 28
        },
        end: {
          line: 143,
          column: 14
        }
      },
      "43": {
        start: {
          line: 144,
          column: 31
        },
        end: {
          line: 144,
          column: 52
        }
      },
      "44": {
        start: {
          line: 145,
          column: 30
        },
        end: {
          line: 145,
          column: 75
        }
      },
      "45": {
        start: {
          line: 145,
          column: 47
        },
        end: {
          line: 145,
          column: 65
        }
      },
      "46": {
        start: {
          line: 146,
          column: 12
        },
        end: {
          line: 159,
          column: 13
        }
      },
      "47": {
        start: {
          line: 147,
          column: 33
        },
        end: {
          line: 152,
          column: 18
        }
      },
      "48": {
        start: {
          line: 153,
          column: 16
        },
        end: {
          line: 153,
          column: 40
        }
      },
      "49": {
        start: {
          line: 154,
          column: 16
        },
        end: {
          line: 154,
          column: 68
        }
      },
      "50": {
        start: {
          line: 157,
          column: 16
        },
        end: {
          line: 157,
          column: 40
        }
      },
      "51": {
        start: {
          line: 158,
          column: 16
        },
        end: {
          line: 158,
          column: 28
        }
      },
      "52": {
        start: {
          line: 162,
          column: 12
        },
        end: {
          line: 164,
          column: 13
        }
      },
      "53": {
        start: {
          line: 163,
          column: 16
        },
        end: {
          line: 163,
          column: 28
        }
      },
      "54": {
        start: {
          line: 165,
          column: 12
        },
        end: {
          line: 170,
          column: 13
        }
      },
      "55": {
        start: {
          line: 166,
          column: 16
        },
        end: {
          line: 168,
          column: 17
        }
      },
      "56": {
        start: {
          line: 167,
          column: 20
        },
        end: {
          line: 167,
          column: 80
        }
      },
      "57": {
        start: {
          line: 169,
          column: 16
        },
        end: {
          line: 169,
          column: 90
        }
      },
      "58": {
        start: {
          line: 171,
          column: 12
        },
        end: {
          line: 171,
          column: 87
        }
      },
      "59": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 186,
          column: 9
        }
      },
      "60": {
        start: {
          line: 183,
          column: 31
        },
        end: {
          line: 183,
          column: 66
        }
      },
      "61": {
        start: {
          line: 184,
          column: 38
        },
        end: {
          line: 184,
          column: 87
        }
      },
      "62": {
        start: {
          line: 185,
          column: 12
        },
        end: {
          line: 185,
          column: 88
        }
      },
      "63": {
        start: {
          line: 188,
          column: 8
        },
        end: {
          line: 191,
          column: 9
        }
      },
      "64": {
        start: {
          line: 189,
          column: 30
        },
        end: {
          line: 189,
          column: 80
        }
      },
      "65": {
        start: {
          line: 189,
          column: 64
        },
        end: {
          line: 189,
          column: 79
        }
      },
      "66": {
        start: {
          line: 190,
          column: 12
        },
        end: {
          line: 190,
          column: 123
        }
      },
      "67": {
        start: {
          line: 194,
          column: 28
        },
        end: {
          line: 194,
          column: 64
        }
      },
      "68": {
        start: {
          line: 195,
          column: 8
        },
        end: {
          line: 213,
          column: 9
        }
      },
      "69": {
        start: {
          line: 196,
          column: 12
        },
        end: {
          line: 196,
          column: 24
        }
      },
      "70": {
        start: {
          line: 198,
          column: 13
        },
        end: {
          line: 213,
          column: 9
        }
      },
      "71": {
        start: {
          line: 199,
          column: 12
        },
        end: {
          line: 204,
          column: 13
        }
      },
      "72": {
        start: {
          line: 200,
          column: 16
        },
        end: {
          line: 200,
          column: 45
        }
      },
      "73": {
        start: {
          line: 203,
          column: 16
        },
        end: {
          line: 203,
          column: 115
        }
      },
      "74": {
        start: {
          line: 207,
          column: 12
        },
        end: {
          line: 212,
          column: 13
        }
      },
      "75": {
        start: {
          line: 208,
          column: 16
        },
        end: {
          line: 208,
          column: 45
        }
      },
      "76": {
        start: {
          line: 211,
          column: 16
        },
        end: {
          line: 211,
          column: 115
        }
      },
      "77": {
        start: {
          line: 214,
          column: 8
        },
        end: {
          line: 220,
          column: 10
        }
      },
      "78": {
        start: {
          line: 229,
          column: 8
        },
        end: {
          line: 229,
          column: 95
        }
      },
      "79": {
        start: {
          line: 237,
          column: 26
        },
        end: {
          line: 237,
          column: 65
        }
      },
      "80": {
        start: {
          line: 238,
          column: 8
        },
        end: {
          line: 238,
          column: 48
        }
      },
      "81": {
        start: {
          line: 246,
          column: 8
        },
        end: {
          line: 246,
          column: 63
        }
      },
      "82": {
        start: {
          line: 246,
          column: 38
        },
        end: {
          line: 246,
          column: 61
        }
      },
      "83": {
        start: {
          line: 249,
          column: 0
        },
        end: {
          line: 249,
          column: 32
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 10,
            column: 4
          },
          end: {
            line: 10,
            column: 5
          }
        },
        loc: {
          start: {
            line: 10,
            column: 115
          },
          end: {
            line: 15,
            column: 5
          }
        },
        line: 10
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 20,
            column: 4
          },
          end: {
            line: 20,
            column: 5
          }
        },
        loc: {
          start: {
            line: 20,
            column: 39
          },
          end: {
            line: 22,
            column: 5
          }
        },
        line: 20
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 27,
            column: 4
          },
          end: {
            line: 27,
            column: 5
          }
        },
        loc: {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 29,
            column: 5
          }
        },
        line: 27
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 36,
            column: 4
          },
          end: {
            line: 36,
            column: 5
          }
        },
        loc: {
          start: {
            line: 36,
            column: 32
          },
          end: {
            line: 42,
            column: 5
          }
        },
        line: 36
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 50,
            column: 4
          },
          end: {
            line: 50,
            column: 5
          }
        },
        loc: {
          start: {
            line: 50,
            column: 39
          },
          end: {
            line: 61,
            column: 5
          }
        },
        line: 50
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 68,
            column: 4
          },
          end: {
            line: 68,
            column: 5
          }
        },
        loc: {
          start: {
            line: 68,
            column: 33
          },
          end: {
            line: 79,
            column: 5
          }
        },
        line: 68
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 86,
            column: 4
          },
          end: {
            line: 86,
            column: 5
          }
        },
        loc: {
          start: {
            line: 86,
            column: 31
          },
          end: {
            line: 130,
            column: 5
          }
        },
        line: 86
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 138,
            column: 4
          },
          end: {
            line: 138,
            column: 5
          }
        },
        loc: {
          start: {
            line: 138,
            column: 44
          },
          end: {
            line: 173,
            column: 5
          }
        },
        line: 138
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 145,
            column: 41
          },
          end: {
            line: 145,
            column: 42
          }
        },
        loc: {
          start: {
            line: 145,
            column: 47
          },
          end: {
            line: 145,
            column: 65
          }
        },
        line: 145
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 180,
            column: 4
          },
          end: {
            line: 180,
            column: 5
          }
        },
        loc: {
          start: {
            line: 180,
            column: 44
          },
          end: {
            line: 221,
            column: 5
          }
        },
        line: 180
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 189,
            column: 58
          },
          end: {
            line: 189,
            column: 59
          }
        },
        loc: {
          start: {
            line: 189,
            column: 64
          },
          end: {
            line: 189,
            column: 79
          }
        },
        line: 189
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 227,
            column: 4
          },
          end: {
            line: 227,
            column: 5
          }
        },
        loc: {
          start: {
            line: 227,
            column: 28
          },
          end: {
            line: 230,
            column: 5
          }
        },
        line: 227
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 236,
            column: 4
          },
          end: {
            line: 236,
            column: 5
          }
        },
        loc: {
          start: {
            line: 236,
            column: 33
          },
          end: {
            line: 239,
            column: 5
          }
        },
        line: 236
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 245,
            column: 4
          },
          end: {
            line: 245,
            column: 5
          }
        },
        loc: {
          start: {
            line: 245,
            column: 14
          },
          end: {
            line: 247,
            column: 5
          }
        },
        line: 245
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 246,
            column: 27
          },
          end: {
            line: 246,
            column: 28
          }
        },
        loc: {
          start: {
            line: 246,
            column: 38
          },
          end: {
            line: 246,
            column: 61
          }
        },
        line: 246
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 10,
            column: 16
          },
          end: {
            line: 10,
            column: 64
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 10,
            column: 26
          },
          end: {
            line: 10,
            column: 64
          }
        }],
        line: 10
      },
      "1": {
        loc: {
          start: {
            line: 10,
            column: 66
          },
          end: {
            line: 10,
            column: 113
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 10,
            column: 79
          },
          end: {
            line: 10,
            column: 113
          }
        }],
        line: 10
      },
      "2": {
        loc: {
          start: {
            line: 36,
            column: 19
          },
          end: {
            line: 36,
            column: 30
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 36,
            column: 28
          },
          end: {
            line: 36,
            column: 30
          }
        }],
        line: 36
      },
      "3": {
        loc: {
          start: {
            line: 50,
            column: 26
          },
          end: {
            line: 50,
            column: 37
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 50,
            column: 35
          },
          end: {
            line: 50,
            column: 37
          }
        }],
        line: 50
      },
      "4": {
        loc: {
          start: {
            line: 54,
            column: 18
          },
          end: {
            line: 54,
            column: 57
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 54,
            column: 25
          },
          end: {
            line: 54,
            column: 45
          }
        }, {
          start: {
            line: 54,
            column: 48
          },
          end: {
            line: 54,
            column: 57
          }
        }],
        line: 54
      },
      "5": {
        loc: {
          start: {
            line: 68,
            column: 20
          },
          end: {
            line: 68,
            column: 31
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 68,
            column: 29
          },
          end: {
            line: 68,
            column: 31
          }
        }],
        line: 68
      },
      "6": {
        loc: {
          start: {
            line: 92,
            column: 24
          },
          end: {
            line: 92,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 92,
            column: 24
          },
          end: {
            line: 92,
            column: 43
          }
        }, {
          start: {
            line: 92,
            column: 47
          },
          end: {
            line: 92,
            column: 66
          }
        }],
        line: 92
      },
      "7": {
        loc: {
          start: {
            line: 93,
            column: 27
          },
          end: {
            line: 93,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 93,
            column: 63
          },
          end: {
            line: 93,
            column: 82
          }
        }, {
          start: {
            line: 93,
            column: 85
          },
          end: {
            line: 93,
            column: 100
          }
        }],
        line: 93
      },
      "8": {
        loc: {
          start: {
            line: 111,
            column: 16
          },
          end: {
            line: 113,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 111,
            column: 16
          },
          end: {
            line: 113,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 111
      },
      "9": {
        loc: {
          start: {
            line: 111,
            column: 20
          },
          end: {
            line: 111,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 111,
            column: 20
          },
          end: {
            line: 111,
            column: 54
          }
        }, {
          start: {
            line: 111,
            column: 58
          },
          end: {
            line: 111,
            column: 87
          }
        }],
        line: 111
      },
      "10": {
        loc: {
          start: {
            line: 114,
            column: 16
          },
          end: {
            line: 116,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 114,
            column: 16
          },
          end: {
            line: 116,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 114
      },
      "11": {
        loc: {
          start: {
            line: 118,
            column: 16
          },
          end: {
            line: 120,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 118,
            column: 16
          },
          end: {
            line: 120,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 118
      },
      "12": {
        loc: {
          start: {
            line: 122,
            column: 16
          },
          end: {
            line: 125,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 122,
            column: 16
          },
          end: {
            line: 125,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 122
      },
      "13": {
        loc: {
          start: {
            line: 129,
            column: 14
          },
          end: {
            line: 129,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 129,
            column: 14
          },
          end: {
            line: 129,
            column: 23
          }
        }, {
          start: {
            line: 129,
            column: 27
          },
          end: {
            line: 129,
            column: 81
          }
        }],
        line: 129
      },
      "14": {
        loc: {
          start: {
            line: 148,
            column: 28
          },
          end: {
            line: 148,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 148,
            column: 28
          },
          end: {
            line: 148,
            column: 41
          }
        }, {
          start: {
            line: 148,
            column: 45
          },
          end: {
            line: 148,
            column: 50
          }
        }],
        line: 148
      },
      "15": {
        loc: {
          start: {
            line: 162,
            column: 12
          },
          end: {
            line: 164,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 162,
            column: 12
          },
          end: {
            line: 164,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 162
      },
      "16": {
        loc: {
          start: {
            line: 162,
            column: 16
          },
          end: {
            line: 162,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 162,
            column: 16
          },
          end: {
            line: 162,
            column: 50
          }
        }, {
          start: {
            line: 162,
            column: 54
          },
          end: {
            line: 162,
            column: 94
          }
        }],
        line: 162
      },
      "17": {
        loc: {
          start: {
            line: 165,
            column: 12
          },
          end: {
            line: 170,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 165,
            column: 12
          },
          end: {
            line: 170,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 165
      },
      "18": {
        loc: {
          start: {
            line: 166,
            column: 16
          },
          end: {
            line: 168,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 166,
            column: 16
          },
          end: {
            line: 168,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 166
      },
      "19": {
        loc: {
          start: {
            line: 182,
            column: 8
          },
          end: {
            line: 186,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 182,
            column: 8
          },
          end: {
            line: 186,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 182
      },
      "20": {
        loc: {
          start: {
            line: 184,
            column: 38
          },
          end: {
            line: 184,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 184,
            column: 51
          },
          end: {
            line: 184,
            column: 75
          }
        }, {
          start: {
            line: 184,
            column: 78
          },
          end: {
            line: 184,
            column: 87
          }
        }],
        line: 184
      },
      "21": {
        loc: {
          start: {
            line: 188,
            column: 8
          },
          end: {
            line: 191,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 188,
            column: 8
          },
          end: {
            line: 191,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 188
      },
      "22": {
        loc: {
          start: {
            line: 195,
            column: 8
          },
          end: {
            line: 213,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 195,
            column: 8
          },
          end: {
            line: 213,
            column: 9
          }
        }, {
          start: {
            line: 198,
            column: 13
          },
          end: {
            line: 213,
            column: 9
          }
        }],
        line: 195
      },
      "23": {
        loc: {
          start: {
            line: 198,
            column: 13
          },
          end: {
            line: 213,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 198,
            column: 13
          },
          end: {
            line: 213,
            column: 9
          }
        }, {
          start: {
            line: 206,
            column: 13
          },
          end: {
            line: 213,
            column: 9
          }
        }],
        line: 198
      },
      "24": {
        loc: {
          start: {
            line: 198,
            column: 17
          },
          end: {
            line: 198,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 198,
            column: 17
          },
          end: {
            line: 198,
            column: 28
          }
        }, {
          start: {
            line: 198,
            column: 32
          },
          end: {
            line: 198,
            column: 72
          }
        }],
        line: 198
      },
      "25": {
        loc: {
          start: {
            line: 203,
            column: 71
          },
          end: {
            line: 203,
            column: 113
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 203,
            column: 96
          },
          end: {
            line: 203,
            column: 101
          }
        }, {
          start: {
            line: 203,
            column: 104
          },
          end: {
            line: 203,
            column: 113
          }
        }],
        line: 203
      },
      "26": {
        loc: {
          start: {
            line: 211,
            column: 71
          },
          end: {
            line: 211,
            column: 113
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 211,
            column: 96
          },
          end: {
            line: 211,
            column: 101
          }
        }, {
          start: {
            line: 211,
            column: 104
          },
          end: {
            line: 211,
            column: 113
          }
        }],
        line: 211
      },
      "27": {
        loc: {
          start: {
            line: 229,
            column: 15
          },
          end: {
            line: 229,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 229,
            column: 16
          },
          end: {
            line: 229,
            column: 39
          }
        }, {
          start: {
            line: 229,
            column: 43
          },
          end: {
            line: 229,
            column: 65
          }
        }, {
          start: {
            line: 229,
            column: 70
          },
          end: {
            line: 229,
            column: 94
          }
        }],
        line: 229
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0],
      "3": [0],
      "4": [0, 0],
      "5": [0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/WebstormProjects/goo/src/services/httpClient.ts",
      mappings: ";;;AAAA,6CAA0E;AAC1E,4CAA0C;AAkC1C;;GAEG;AACH,MAAa,UAAU;IAMrB,YAAY,UAAkB,sBAAU,CAAC,eAAe,EAAE,aAAqB,sBAAU,CAAC,WAAW;QAL7F,wBAAmB,GAAyB,EAAE,CAAC;QAC/C,yBAAoB,GAA0B,EAAE,CAAC;QAKvD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACH,qBAAqB,CAAC,WAA+B;QACnD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACH,sBAAsB,CAAC,WAAgC;QACrD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,GAAG,CAAU,GAAW,EAAE,SAAiC,EAAE;QACjE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAI,GAAG,EAAE;YAC1C,GAAG,MAAM;YACT,MAAM,EAAE,KAAK;SACd,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,IAAI,CAAU,GAAW,EAAE,IAAU,EAAE,SAAiC,EAAE;QAC9E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAI,GAAG,EAAE;YAC1C,GAAG,MAAM;YACT,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;YAC7C,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,GAAG,MAAM,CAAC,OAAO;aAClB;SACF,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,IAAI,CAAC,GAAW,EAAE,SAAiC,EAAE;QACzD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YACvC,GAAG,MAAM;YACT,MAAM,EAAE,MAAM;SACf,CAAC,CAAC;QAEH,OAAO;YACL,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;YAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,EAAE,EAAE,QAAQ,CAAC,EAAE;SAChB,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,OAAO,CAAU,GAAW,EAAE,MAAqB;QAC/D,IAAI,WAAW,GAAG,EAAE,GAAG,MAAM,EAAE,CAAC;QAEhC,6BAA6B;QAC7B,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACnD,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,CAAC;QACzC,CAAC;QAED,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC;QAC3D,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;QAE7F,IAAI,SAAS,GAAiB,IAAI,CAAC;QAEnC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;YACvD,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAI,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;gBAEtE,8BAA8B;gBAC9B,IAAI,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC;gBACjC,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBACpD,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC;gBAC3C,CAAC;gBAED,OAAO;oBACL,GAAG,QAAQ;oBACX,IAAI,EAAE,YAAY;iBACnB,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAc,CAAC;gBAE3B,gCAAgC;gBAChC,IAAI,KAAK,YAAY,iBAAQ,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC/D,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,IAAI,KAAK,YAAY,uBAAc,EAAE,CAAC;oBACpC,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,kCAAkC;gBAClC,IAAI,OAAO,IAAI,UAAU,EAAE,CAAC;oBAC1B,MAAM;gBACR,CAAC;gBAED,gFAAgF;gBAChF,IAAI,OAAO,GAAG,UAAU,EAAE,CAAC;oBACzB,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;oBAChD,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC1B,CAAC;YACH,CAAC;QACH,CAAC;QAED,qCAAqC;QACrC,MAAM,SAAS,IAAI,IAAI,qBAAY,CAAC,2BAA2B,CAAC,CAAC;IACnE,CAAC;IAED;;;;;;OAMG;IACK,KAAK,CAAC,WAAW,CAAI,GAAW,EAAE,MAAqB,EAAE,OAAe;QAC9E,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC;gBAC1B,YAAY,EAAE,uBAAuB;gBACrC,GAAG,MAAM,CAAC,OAAO;aAClB,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;YACzC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC;YAEhE,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;oBAChC,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,KAAK;oBAC9B,OAAO;oBACP,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,MAAM,EAAE,UAAU,CAAC,MAAM;iBAC1B,CAAC,CAAC;gBAEH,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,OAAO,MAAM,IAAI,CAAC,eAAe,CAAI,QAAQ,EAAE,MAAM,CAAC,CAAC;YACzD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,iBAAQ,IAAI,KAAK,YAAY,uBAAc,EAAE,CAAC;gBACjE,MAAM,KAAK,CAAC;YACd,CAAC;YAED,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;oBAChC,MAAM,IAAI,qBAAY,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;gBACrD,CAAC;gBACD,MAAM,IAAI,qBAAY,CAAC,kBAAkB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;YACnE,CAAC;YAED,MAAM,IAAI,qBAAY,CAAC,0BAA0B,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,eAAe,CAAI,QAAkB,EAAE,MAAqB;QACxE,uBAAuB;QACvB,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;YAC5B,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YACvD,MAAM,iBAAiB,GAAG,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAC5E,MAAM,IAAI,uBAAc,CAAC,qBAAqB,EAAE,iBAAiB,CAAC,CAAC;QACrE,CAAC;QAED,2BAA2B;QAC3B,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,eAAe,CAAC,CAAC;YACrE,MAAM,IAAI,iBAAQ,CAChB,QAAQ,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,UAAU,MAAM,SAAS,EAAE,EAChE,QAAQ,CAAC,MAAM,CAChB,CAAC;QACJ,CAAC;QAED,sBAAsB;QACtB,IAAI,IAAO,CAAC;QACZ,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAEzD,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YAC7B,IAAI,GAAG,IAAW,CAAC;QACrB,CAAC;aAAM,IAAI,WAAW,IAAI,WAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACnE,IAAI,CAAC;gBACH,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC/B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,IAAI,qBAAY,CAAC,qBAAqB,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YAC5F,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC;gBACH,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;YACtC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,IAAI,qBAAY,CAAC,qBAAqB,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YAC5F,CAAC;QACH,CAAC;QAED,OAAO;YACL,IAAI;YACJ,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;YAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,EAAE,EAAE,QAAQ,CAAC,EAAE;SAChB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACK,gBAAgB,CAAC,KAAe;QACtC,uDAAuD;QACvD,OAAO,CAAC,KAAK,CAAC,UAAU,IAAI,GAAG,IAAI,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,CAAC;IACzF,CAAC;IAED;;;;OAIG;IACK,mBAAmB,CAAC,OAAe;QACzC,MAAM,SAAS,GAAG,sBAAU,CAAC,gBAAgB,CAAC;QAC9C,OAAO,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;AA9QD,gCA8QC",
      names: [],
      sources: ["/Users/<USER>/WebstormProjects/goo/src/services/httpClient.ts"],
      sourcesContent: ["import { ApiError, NetworkError, RateLimitError } from '../models/Errors';\nimport { API_CONFIG } from '../constants';\n\n/**\n * HTTP request configuration\n */\nexport interface RequestConfig {\n  method?: string;\n  headers?: Record<string, string>;\n  body?: string;\n  timeout?: number;\n  retries?: number;\n}\n\n/**\n * HTTP response interface\n */\nexport interface HttpResponse<T = any> {\n  data: T;\n  status: number;\n  statusText: string;\n  headers: Headers;\n  ok: boolean;\n}\n\n/**\n * Request interceptor function type\n */\nexport type RequestInterceptor = (config: RequestConfig) => RequestConfig;\n\n/**\n * Response interceptor function type\n */\nexport type ResponseInterceptor = <T>(response: T) => T;\n\n/**\n * HTTP client with retry logic, interceptors, and error handling\n */\nexport class HttpClient {\n  private requestInterceptors: RequestInterceptor[] = [];\n  private responseInterceptors: ResponseInterceptor[] = [];\n  private readonly defaultTimeout: number;\n  private readonly maxRetries: number;\n\n  constructor(timeout: number = API_CONFIG.REQUEST_TIMEOUT, maxRetries: number = API_CONFIG.MAX_RETRIES) {\n    this.defaultTimeout = timeout;\n    this.maxRetries = maxRetries;\n  }\n\n  /**\n   * Adds a request interceptor\n   * @param interceptor - Function to modify request config\n   */\n  addRequestInterceptor(interceptor: RequestInterceptor): void {\n    this.requestInterceptors.push(interceptor);\n  }\n\n  /**\n   * Adds a response interceptor\n   * @param interceptor - Function to modify response data\n   */\n  addResponseInterceptor(interceptor: ResponseInterceptor): void {\n    this.responseInterceptors.push(interceptor);\n  }\n\n  /**\n   * Makes a GET request\n   * @param url - Request URL\n   * @param config - Request configuration\n   * @returns Promise resolving to response data\n   */\n  async get<T = any>(url: string, config: Partial<RequestConfig> = {}): Promise<T> {\n    const response = await this.request<T>(url, {\n      ...config,\n      method: 'GET',\n    });\n    return response.data;\n  }\n\n  /**\n   * Makes a POST request\n   * @param url - Request URL\n   * @param data - Request body data\n   * @param config - Request configuration\n   * @returns Promise resolving to response data\n   */\n  async post<T = any>(url: string, data?: any, config: Partial<RequestConfig> = {}): Promise<T> {\n    const response = await this.request<T>(url, {\n      ...config,\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined,\n      headers: {\n        'Content-Type': 'application/json',\n        ...config.headers,\n      },\n    });\n    return response.data;\n  }\n\n  /**\n   * Makes a HEAD request\n   * @param url - Request URL\n   * @param config - Request configuration\n   * @returns Promise resolving to response (without body)\n   */\n  async head(url: string, config: Partial<RequestConfig> = {}): Promise<Omit<HttpResponse, 'data'>> {\n    const response = await this.request(url, {\n      ...config,\n      method: 'HEAD',\n    });\n\n    return {\n      status: response.status,\n      statusText: response.statusText,\n      headers: response.headers,\n      ok: response.ok,\n    };\n  }\n\n  /**\n   * Makes an HTTP request with retry logic\n   * @param url - Request URL\n   * @param config - Request configuration\n   * @returns Promise resolving to HTTP response\n   */\n  private async request<T = any>(url: string, config: RequestConfig): Promise<HttpResponse<T>> {\n    let finalConfig = { ...config };\n\n    // Apply request interceptors\n    for (const interceptor of this.requestInterceptors) {\n      finalConfig = interceptor(finalConfig);\n    }\n\n    const timeout = finalConfig.timeout || this.defaultTimeout;\n    const maxRetries = finalConfig.retries !== undefined ? finalConfig.retries : this.maxRetries;\n\n    let lastError: Error | null = null;\n\n    for (let attempt = 0; attempt <= maxRetries; attempt++) {\n      try {\n        const response = await this.makeRequest<T>(url, finalConfig, timeout);\n\n        // Apply response interceptors\n        let responseData = response.data;\n        for (const interceptor of this.responseInterceptors) {\n          responseData = interceptor(responseData);\n        }\n\n        return {\n          ...response,\n          data: responseData,\n        };\n      } catch (error) {\n        lastError = error as Error;\n\n        // Don't retry on certain errors\n        if (error instanceof ApiError && !this.isRetryableError(error)) {\n          throw error;\n        }\n\n        if (error instanceof RateLimitError) {\n          throw error;\n        }\n\n        // Don't retry on the last attempt\n        if (attempt >= maxRetries) {\n          break;\n        }\n\n        // Wait before retrying (exponential backoff) - but only if we're going to retry\n        if (attempt < maxRetries) {\n          const delay = this.calculateRetryDelay(attempt);\n          await this.sleep(delay);\n        }\n      }\n    }\n\n    // If we get here, all retries failed\n    throw lastError || new NetworkError('All retry attempts failed');\n  }\n\n  /**\n   * Makes the actual HTTP request\n   * @param url - Request URL\n   * @param config - Request configuration\n   * @param timeout - Request timeout\n   * @returns Promise resolving to HTTP response\n   */\n  private async makeRequest<T>(url: string, config: RequestConfig, timeout: number): Promise<HttpResponse<T>> {\n    try {\n      const headers = new Headers({\n        'User-Agent': 'BusinessSearchApp/1.0',\n        ...config.headers,\n      });\n\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), timeout);\n\n      try {\n        const response = await fetch(url, {\n          method: config.method || 'GET',\n          headers,\n          body: config.body,\n          signal: controller.signal,\n        });\n\n        clearTimeout(timeoutId);\n        return await this.processResponse<T>(response, config);\n      } catch (error) {\n        clearTimeout(timeoutId);\n        throw error;\n      }\n    } catch (error) {\n      if (error instanceof ApiError || error instanceof RateLimitError) {\n        throw error;\n      }\n\n      if (error instanceof Error) {\n        if (error.name === 'AbortError') {\n          throw new NetworkError('Request timed out', error);\n        }\n        throw new NetworkError(`Network error: ${error.message}`, error);\n      }\n\n      throw new NetworkError(`Unknown network error: ${String(error)}`);\n    }\n  }\n\n  /**\n   * Processes the HTTP response\n   * @param response - The fetch response\n   * @param config - Request configuration\n   * @returns Promise resolving to HTTP response\n   */\n  private async processResponse<T>(response: Response, config: RequestConfig): Promise<HttpResponse<T>> {\n    // Handle rate limiting\n    if (response.status === 429) {\n      const retryAfter = response.headers.get('Retry-After');\n      const retryAfterSeconds = retryAfter ? parseInt(retryAfter, 10) : undefined;\n      throw new RateLimitError('Rate limit exceeded', retryAfterSeconds);\n    }\n\n    // Handle other HTTP errors\n    if (!response.ok) {\n      const errorText = await response.text().catch(() => 'Unknown error');\n      throw new ApiError(\n        `HTTP ${response.status}: ${response.statusText} - ${errorText}`,\n        response.status\n      );\n    }\n\n    // Parse response data\n    let data: T;\n    const contentType = response.headers.get('content-type');\n\n    if (config.method === 'HEAD') {\n      data = null as any;\n    } else if (contentType && contentType.includes('application/json')) {\n      try {\n        data = await response.json();\n      } catch (error) {\n        throw new NetworkError('JSON parsing failed', error instanceof Error ? error : undefined);\n      }\n    } else {\n      try {\n        data = await response.text() as any;\n      } catch (error) {\n        throw new NetworkError('Text parsing failed', error instanceof Error ? error : undefined);\n      }\n    }\n\n    return {\n      data,\n      status: response.status,\n      statusText: response.statusText,\n      headers: response.headers,\n      ok: response.ok,\n    };\n  }\n\n  /**\n   * Determines if an error should be retried\n   * @param error - Error to check\n   * @returns True if error should be retried\n   */\n  private isRetryableError(error: ApiError): boolean {\n    // Retry on 5xx server errors and 429 rate limit errors\n    return (error.statusCode >= 500 && error.statusCode < 600) || error.statusCode === 429;\n  }\n\n  /**\n   * Calculates retry delay using exponential backoff\n   * @param attempt - The current attempt number (0-based)\n   * @returns Delay in milliseconds\n   */\n  private calculateRetryDelay(attempt: number): number {\n    const baseDelay = API_CONFIG.RETRY_DELAY_BASE;\n    return baseDelay * Math.pow(2, attempt);\n  }\n\n  /**\n   * Sleeps for the specified duration\n   * @param ms - Duration in milliseconds\n   * @returns Promise that resolves after the delay\n   */\n  private sleep(ms: number): Promise<void> {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "468e499a671c766d7252b1d42a131fac3e1fde3e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_gfdwisfzv = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_gfdwisfzv();
cov_gfdwisfzv().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_gfdwisfzv().s[1]++;
exports.HttpClient = void 0;
const Errors_1 =
/* istanbul ignore next */
(cov_gfdwisfzv().s[2]++, require("../models/Errors"));
const constants_1 =
/* istanbul ignore next */
(cov_gfdwisfzv().s[3]++, require("../constants"));
/**
 * HTTP client with retry logic, interceptors, and error handling
 */
class HttpClient {
  constructor(timeout =
  /* istanbul ignore next */
  (cov_gfdwisfzv().b[0][0]++, constants_1.API_CONFIG.REQUEST_TIMEOUT), maxRetries =
  /* istanbul ignore next */
  (cov_gfdwisfzv().b[1][0]++, constants_1.API_CONFIG.MAX_RETRIES)) {
    /* istanbul ignore next */
    cov_gfdwisfzv().f[0]++;
    cov_gfdwisfzv().s[4]++;
    this.requestInterceptors = [];
    /* istanbul ignore next */
    cov_gfdwisfzv().s[5]++;
    this.responseInterceptors = [];
    /* istanbul ignore next */
    cov_gfdwisfzv().s[6]++;
    this.defaultTimeout = timeout;
    /* istanbul ignore next */
    cov_gfdwisfzv().s[7]++;
    this.maxRetries = maxRetries;
  }
  /**
   * Adds a request interceptor
   * @param interceptor - Function to modify request config
   */
  addRequestInterceptor(interceptor) {
    /* istanbul ignore next */
    cov_gfdwisfzv().f[1]++;
    cov_gfdwisfzv().s[8]++;
    this.requestInterceptors.push(interceptor);
  }
  /**
   * Adds a response interceptor
   * @param interceptor - Function to modify response data
   */
  addResponseInterceptor(interceptor) {
    /* istanbul ignore next */
    cov_gfdwisfzv().f[2]++;
    cov_gfdwisfzv().s[9]++;
    this.responseInterceptors.push(interceptor);
  }
  /**
   * Makes a GET request
   * @param url - Request URL
   * @param config - Request configuration
   * @returns Promise resolving to response data
   */
  async get(url, config =
  /* istanbul ignore next */
  (cov_gfdwisfzv().b[2][0]++, {})) {
    /* istanbul ignore next */
    cov_gfdwisfzv().f[3]++;
    const response =
    /* istanbul ignore next */
    (cov_gfdwisfzv().s[10]++, await this.request(url, {
      ...config,
      method: 'GET'
    }));
    /* istanbul ignore next */
    cov_gfdwisfzv().s[11]++;
    return response.data;
  }
  /**
   * Makes a POST request
   * @param url - Request URL
   * @param data - Request body data
   * @param config - Request configuration
   * @returns Promise resolving to response data
   */
  async post(url, data, config =
  /* istanbul ignore next */
  (cov_gfdwisfzv().b[3][0]++, {})) {
    /* istanbul ignore next */
    cov_gfdwisfzv().f[4]++;
    const response =
    /* istanbul ignore next */
    (cov_gfdwisfzv().s[12]++, await this.request(url, {
      ...config,
      method: 'POST',
      body: data ?
      /* istanbul ignore next */
      (cov_gfdwisfzv().b[4][0]++, JSON.stringify(data)) :
      /* istanbul ignore next */
      (cov_gfdwisfzv().b[4][1]++, undefined),
      headers: {
        'Content-Type': 'application/json',
        ...config.headers
      }
    }));
    /* istanbul ignore next */
    cov_gfdwisfzv().s[13]++;
    return response.data;
  }
  /**
   * Makes a HEAD request
   * @param url - Request URL
   * @param config - Request configuration
   * @returns Promise resolving to response (without body)
   */
  async head(url, config =
  /* istanbul ignore next */
  (cov_gfdwisfzv().b[5][0]++, {})) {
    /* istanbul ignore next */
    cov_gfdwisfzv().f[5]++;
    const response =
    /* istanbul ignore next */
    (cov_gfdwisfzv().s[14]++, await this.request(url, {
      ...config,
      method: 'HEAD'
    }));
    /* istanbul ignore next */
    cov_gfdwisfzv().s[15]++;
    return {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      ok: response.ok
    };
  }
  /**
   * Makes an HTTP request with retry logic
   * @param url - Request URL
   * @param config - Request configuration
   * @returns Promise resolving to HTTP response
   */
  async request(url, config) {
    /* istanbul ignore next */
    cov_gfdwisfzv().f[6]++;
    let finalConfig =
    /* istanbul ignore next */
    (cov_gfdwisfzv().s[16]++, {
      ...config
    });
    // Apply request interceptors
    /* istanbul ignore next */
    cov_gfdwisfzv().s[17]++;
    for (const interceptor of this.requestInterceptors) {
      /* istanbul ignore next */
      cov_gfdwisfzv().s[18]++;
      finalConfig = interceptor(finalConfig);
    }
    const timeout =
    /* istanbul ignore next */
    (cov_gfdwisfzv().s[19]++,
    /* istanbul ignore next */
    (cov_gfdwisfzv().b[6][0]++, finalConfig.timeout) ||
    /* istanbul ignore next */
    (cov_gfdwisfzv().b[6][1]++, this.defaultTimeout));
    const maxRetries =
    /* istanbul ignore next */
    (cov_gfdwisfzv().s[20]++, finalConfig.retries !== undefined ?
    /* istanbul ignore next */
    (cov_gfdwisfzv().b[7][0]++, finalConfig.retries) :
    /* istanbul ignore next */
    (cov_gfdwisfzv().b[7][1]++, this.maxRetries));
    let lastError =
    /* istanbul ignore next */
    (cov_gfdwisfzv().s[21]++, null);
    /* istanbul ignore next */
    cov_gfdwisfzv().s[22]++;
    for (let attempt =
    /* istanbul ignore next */
    (cov_gfdwisfzv().s[23]++, 0); attempt <= maxRetries; attempt++) {
      /* istanbul ignore next */
      cov_gfdwisfzv().s[24]++;
      try {
        const response =
        /* istanbul ignore next */
        (cov_gfdwisfzv().s[25]++, await this.makeRequest(url, finalConfig, timeout));
        // Apply response interceptors
        let responseData =
        /* istanbul ignore next */
        (cov_gfdwisfzv().s[26]++, response.data);
        /* istanbul ignore next */
        cov_gfdwisfzv().s[27]++;
        for (const interceptor of this.responseInterceptors) {
          /* istanbul ignore next */
          cov_gfdwisfzv().s[28]++;
          responseData = interceptor(responseData);
        }
        /* istanbul ignore next */
        cov_gfdwisfzv().s[29]++;
        return {
          ...response,
          data: responseData
        };
      } catch (error) {
        /* istanbul ignore next */
        cov_gfdwisfzv().s[30]++;
        lastError = error;
        // Don't retry on certain errors
        /* istanbul ignore next */
        cov_gfdwisfzv().s[31]++;
        if (
        /* istanbul ignore next */
        (cov_gfdwisfzv().b[9][0]++, error instanceof Errors_1.ApiError) &&
        /* istanbul ignore next */
        (cov_gfdwisfzv().b[9][1]++, !this.isRetryableError(error))) {
          /* istanbul ignore next */
          cov_gfdwisfzv().b[8][0]++;
          cov_gfdwisfzv().s[32]++;
          throw error;
        } else
        /* istanbul ignore next */
        {
          cov_gfdwisfzv().b[8][1]++;
        }
        cov_gfdwisfzv().s[33]++;
        if (error instanceof Errors_1.RateLimitError) {
          /* istanbul ignore next */
          cov_gfdwisfzv().b[10][0]++;
          cov_gfdwisfzv().s[34]++;
          throw error;
        } else
        /* istanbul ignore next */
        {
          cov_gfdwisfzv().b[10][1]++;
        }
        // Don't retry on the last attempt
        cov_gfdwisfzv().s[35]++;
        if (attempt >= maxRetries) {
          /* istanbul ignore next */
          cov_gfdwisfzv().b[11][0]++;
          cov_gfdwisfzv().s[36]++;
          break;
        } else
        /* istanbul ignore next */
        {
          cov_gfdwisfzv().b[11][1]++;
        }
        // Wait before retrying (exponential backoff) - but only if we're going to retry
        cov_gfdwisfzv().s[37]++;
        if (attempt < maxRetries) {
          /* istanbul ignore next */
          cov_gfdwisfzv().b[12][0]++;
          const delay =
          /* istanbul ignore next */
          (cov_gfdwisfzv().s[38]++, this.calculateRetryDelay(attempt));
          /* istanbul ignore next */
          cov_gfdwisfzv().s[39]++;
          await this.sleep(delay);
        } else
        /* istanbul ignore next */
        {
          cov_gfdwisfzv().b[12][1]++;
        }
      }
    }
    // If we get here, all retries failed
    /* istanbul ignore next */
    cov_gfdwisfzv().s[40]++;
    throw /* istanbul ignore next */(cov_gfdwisfzv().b[13][0]++, lastError) ||
    /* istanbul ignore next */
    (cov_gfdwisfzv().b[13][1]++, new Errors_1.NetworkError('All retry attempts failed'));
  }
  /**
   * Makes the actual HTTP request
   * @param url - Request URL
   * @param config - Request configuration
   * @param timeout - Request timeout
   * @returns Promise resolving to HTTP response
   */
  async makeRequest(url, config, timeout) {
    /* istanbul ignore next */
    cov_gfdwisfzv().f[7]++;
    cov_gfdwisfzv().s[41]++;
    try {
      const headers =
      /* istanbul ignore next */
      (cov_gfdwisfzv().s[42]++, new Headers({
        'User-Agent': 'BusinessSearchApp/1.0',
        ...config.headers
      }));
      const controller =
      /* istanbul ignore next */
      (cov_gfdwisfzv().s[43]++, new AbortController());
      const timeoutId =
      /* istanbul ignore next */
      (cov_gfdwisfzv().s[44]++, setTimeout(() => {
        /* istanbul ignore next */
        cov_gfdwisfzv().f[8]++;
        cov_gfdwisfzv().s[45]++;
        return controller.abort();
      }, timeout));
      /* istanbul ignore next */
      cov_gfdwisfzv().s[46]++;
      try {
        const response =
        /* istanbul ignore next */
        (cov_gfdwisfzv().s[47]++, await fetch(url, {
          method:
          /* istanbul ignore next */
          (cov_gfdwisfzv().b[14][0]++, config.method) ||
          /* istanbul ignore next */
          (cov_gfdwisfzv().b[14][1]++, 'GET'),
          headers,
          body: config.body,
          signal: controller.signal
        }));
        /* istanbul ignore next */
        cov_gfdwisfzv().s[48]++;
        clearTimeout(timeoutId);
        /* istanbul ignore next */
        cov_gfdwisfzv().s[49]++;
        return await this.processResponse(response, config);
      } catch (error) {
        /* istanbul ignore next */
        cov_gfdwisfzv().s[50]++;
        clearTimeout(timeoutId);
        /* istanbul ignore next */
        cov_gfdwisfzv().s[51]++;
        throw error;
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_gfdwisfzv().s[52]++;
      if (
      /* istanbul ignore next */
      (cov_gfdwisfzv().b[16][0]++, error instanceof Errors_1.ApiError) ||
      /* istanbul ignore next */
      (cov_gfdwisfzv().b[16][1]++, error instanceof Errors_1.RateLimitError)) {
        /* istanbul ignore next */
        cov_gfdwisfzv().b[15][0]++;
        cov_gfdwisfzv().s[53]++;
        throw error;
      } else
      /* istanbul ignore next */
      {
        cov_gfdwisfzv().b[15][1]++;
      }
      cov_gfdwisfzv().s[54]++;
      if (error instanceof Error) {
        /* istanbul ignore next */
        cov_gfdwisfzv().b[17][0]++;
        cov_gfdwisfzv().s[55]++;
        if (error.name === 'AbortError') {
          /* istanbul ignore next */
          cov_gfdwisfzv().b[18][0]++;
          cov_gfdwisfzv().s[56]++;
          throw new Errors_1.NetworkError('Request timed out', error);
        } else
        /* istanbul ignore next */
        {
          cov_gfdwisfzv().b[18][1]++;
        }
        cov_gfdwisfzv().s[57]++;
        throw new Errors_1.NetworkError(`Network error: ${error.message}`, error);
      } else
      /* istanbul ignore next */
      {
        cov_gfdwisfzv().b[17][1]++;
      }
      cov_gfdwisfzv().s[58]++;
      throw new Errors_1.NetworkError(`Unknown network error: ${String(error)}`);
    }
  }
  /**
   * Processes the HTTP response
   * @param response - The fetch response
   * @param config - Request configuration
   * @returns Promise resolving to HTTP response
   */
  async processResponse(response, config) {
    /* istanbul ignore next */
    cov_gfdwisfzv().f[9]++;
    cov_gfdwisfzv().s[59]++;
    // Handle rate limiting
    if (response.status === 429) {
      /* istanbul ignore next */
      cov_gfdwisfzv().b[19][0]++;
      const retryAfter =
      /* istanbul ignore next */
      (cov_gfdwisfzv().s[60]++, response.headers.get('Retry-After'));
      const retryAfterSeconds =
      /* istanbul ignore next */
      (cov_gfdwisfzv().s[61]++, retryAfter ?
      /* istanbul ignore next */
      (cov_gfdwisfzv().b[20][0]++, parseInt(retryAfter, 10)) :
      /* istanbul ignore next */
      (cov_gfdwisfzv().b[20][1]++, undefined));
      /* istanbul ignore next */
      cov_gfdwisfzv().s[62]++;
      throw new Errors_1.RateLimitError('Rate limit exceeded', retryAfterSeconds);
    } else
    /* istanbul ignore next */
    {
      cov_gfdwisfzv().b[19][1]++;
    }
    // Handle other HTTP errors
    cov_gfdwisfzv().s[63]++;
    if (!response.ok) {
      /* istanbul ignore next */
      cov_gfdwisfzv().b[21][0]++;
      const errorText =
      /* istanbul ignore next */
      (cov_gfdwisfzv().s[64]++, await response.text().catch(() => {
        /* istanbul ignore next */
        cov_gfdwisfzv().f[10]++;
        cov_gfdwisfzv().s[65]++;
        return 'Unknown error';
      }));
      /* istanbul ignore next */
      cov_gfdwisfzv().s[66]++;
      throw new Errors_1.ApiError(`HTTP ${response.status}: ${response.statusText} - ${errorText}`, response.status);
    } else
    /* istanbul ignore next */
    {
      cov_gfdwisfzv().b[21][1]++;
    }
    // Parse response data
    let data;
    const contentType =
    /* istanbul ignore next */
    (cov_gfdwisfzv().s[67]++, response.headers.get('content-type'));
    /* istanbul ignore next */
    cov_gfdwisfzv().s[68]++;
    if (config.method === 'HEAD') {
      /* istanbul ignore next */
      cov_gfdwisfzv().b[22][0]++;
      cov_gfdwisfzv().s[69]++;
      data = null;
    } else {
      /* istanbul ignore next */
      cov_gfdwisfzv().b[22][1]++;
      cov_gfdwisfzv().s[70]++;
      if (
      /* istanbul ignore next */
      (cov_gfdwisfzv().b[24][0]++, contentType) &&
      /* istanbul ignore next */
      (cov_gfdwisfzv().b[24][1]++, contentType.includes('application/json'))) {
        /* istanbul ignore next */
        cov_gfdwisfzv().b[23][0]++;
        cov_gfdwisfzv().s[71]++;
        try {
          /* istanbul ignore next */
          cov_gfdwisfzv().s[72]++;
          data = await response.json();
        } catch (error) {
          /* istanbul ignore next */
          cov_gfdwisfzv().s[73]++;
          throw new Errors_1.NetworkError('JSON parsing failed', error instanceof Error ?
          /* istanbul ignore next */
          (cov_gfdwisfzv().b[25][0]++, error) :
          /* istanbul ignore next */
          (cov_gfdwisfzv().b[25][1]++, undefined));
        }
      } else {
        /* istanbul ignore next */
        cov_gfdwisfzv().b[23][1]++;
        cov_gfdwisfzv().s[74]++;
        try {
          /* istanbul ignore next */
          cov_gfdwisfzv().s[75]++;
          data = await response.text();
        } catch (error) {
          /* istanbul ignore next */
          cov_gfdwisfzv().s[76]++;
          throw new Errors_1.NetworkError('Text parsing failed', error instanceof Error ?
          /* istanbul ignore next */
          (cov_gfdwisfzv().b[26][0]++, error) :
          /* istanbul ignore next */
          (cov_gfdwisfzv().b[26][1]++, undefined));
        }
      }
    }
    /* istanbul ignore next */
    cov_gfdwisfzv().s[77]++;
    return {
      data,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      ok: response.ok
    };
  }
  /**
   * Determines if an error should be retried
   * @param error - Error to check
   * @returns True if error should be retried
   */
  isRetryableError(error) {
    /* istanbul ignore next */
    cov_gfdwisfzv().f[11]++;
    cov_gfdwisfzv().s[78]++;
    // Retry on 5xx server errors and 429 rate limit errors
    return /* istanbul ignore next */(cov_gfdwisfzv().b[27][0]++, error.statusCode >= 500) &&
    /* istanbul ignore next */
    (cov_gfdwisfzv().b[27][1]++, error.statusCode < 600) ||
    /* istanbul ignore next */
    (cov_gfdwisfzv().b[27][2]++, error.statusCode === 429);
  }
  /**
   * Calculates retry delay using exponential backoff
   * @param attempt - The current attempt number (0-based)
   * @returns Delay in milliseconds
   */
  calculateRetryDelay(attempt) {
    /* istanbul ignore next */
    cov_gfdwisfzv().f[12]++;
    const baseDelay =
    /* istanbul ignore next */
    (cov_gfdwisfzv().s[79]++, constants_1.API_CONFIG.RETRY_DELAY_BASE);
    /* istanbul ignore next */
    cov_gfdwisfzv().s[80]++;
    return baseDelay * Math.pow(2, attempt);
  }
  /**
   * Sleeps for the specified duration
   * @param ms - Duration in milliseconds
   * @returns Promise that resolves after the delay
   */
  sleep(ms) {
    /* istanbul ignore next */
    cov_gfdwisfzv().f[13]++;
    cov_gfdwisfzv().s[81]++;
    return new Promise(resolve => {
      /* istanbul ignore next */
      cov_gfdwisfzv().f[14]++;
      cov_gfdwisfzv().s[82]++;
      return setTimeout(resolve, ms);
    });
  }
}
/* istanbul ignore next */
cov_gfdwisfzv().s[83]++;
exports.HttpClient = HttpClient;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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