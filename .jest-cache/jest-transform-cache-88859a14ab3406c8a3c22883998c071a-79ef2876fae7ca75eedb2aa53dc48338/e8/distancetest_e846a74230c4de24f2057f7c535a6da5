0aab3b7df6c603df48322eb524398e64
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const distance_1 = require("../../src/utils/distance");
describe('Distance Utils', () => {
    const newYork = { latitude: 40.7128, longitude: -74.0060 };
    const losAngeles = { latitude: 34.0522, longitude: -118.2437 };
    const chicago = { latitude: 41.8781, longitude: -87.6298 };
    const nearby = { latitude: 40.7589, longitude: -73.9851 }; // Times Square
    describe('calculateDistance', () => {
        it('should calculate distance between two coordinates', () => {
            const distance = (0, distance_1.calculateDistance)(newYork, losAngeles);
            // Distance between NYC and LA is approximately 2445 miles
            expect(distance).toBeGreaterThan(2400);
            expect(distance).toBeLessThan(2500);
        });
        it('should return 0 for identical coordinates', () => {
            const distance = (0, distance_1.calculateDistance)(newYork, newYork);
            expect(distance).toBe(0);
        });
        it('should calculate short distances accurately', () => {
            const distance = (0, distance_1.calculateDistance)(newYork, nearby);
            // Distance from NYC to Times Square is about 4-5 miles
            expect(distance).toBeGreaterThan(3);
            expect(distance).toBeLessThan(6);
        });
        it('should return positive distances regardless of coordinate order', () => {
            const distance1 = (0, distance_1.calculateDistance)(newYork, losAngeles);
            const distance2 = (0, distance_1.calculateDistance)(losAngeles, newYork);
            expect(distance1).toBe(distance2);
            expect(distance1).toBeGreaterThan(0);
        });
        it('should handle coordinates with decimal precision', () => {
            const coord1 = { latitude: 40.712800, longitude: -74.006000 };
            const coord2 = { latitude: 40.712801, longitude: -74.006001 };
            const distance = (0, distance_1.calculateDistance)(coord1, coord2);
            expect(distance).toBeGreaterThanOrEqual(0);
            expect(distance).toBeLessThan(0.01); // Very small distance
        });
    });
    describe('calculateDistanceKm', () => {
        it('should calculate distance in kilometers', () => {
            const distanceMiles = (0, distance_1.calculateDistance)(newYork, losAngeles);
            const distanceKm = (0, distance_1.calculateDistanceKm)(newYork, losAngeles);
            // 1 mile ≈ 1.60934 km
            expect(distanceKm).toBeCloseTo(distanceMiles * 1.60934, 1);
        });
        it('should return 0 for identical coordinates', () => {
            const distance = (0, distance_1.calculateDistanceKm)(newYork, newYork);
            expect(distance).toBe(0);
        });
    });
    describe('findClosest', () => {
        const coordinates = [losAngeles, chicago, nearby];
        it('should find the closest coordinate', () => {
            const result = (0, distance_1.findClosest)(newYork, coordinates);
            expect(result).not.toBeNull();
            expect(result.coordinate).toEqual(nearby);
            expect(result.distance).toBeLessThan(10); // Should be less than 10 miles
        });
        it('should return null for empty array', () => {
            const result = (0, distance_1.findClosest)(newYork, []);
            expect(result).toBeNull();
        });
        it('should return the only coordinate if array has one element', () => {
            const result = (0, distance_1.findClosest)(newYork, [chicago]);
            expect(result).not.toBeNull();
            expect(result.coordinate).toEqual(chicago);
        });
        it('should calculate correct distances', () => {
            const result = (0, distance_1.findClosest)(newYork, coordinates);
            const manualDistance = (0, distance_1.calculateDistance)(newYork, nearby);
            expect(result.distance).toBe(manualDistance);
        });
    });
    describe('isWithinRadius', () => {
        it('should return true for points within radius', () => {
            const isWithin = (0, distance_1.isWithinRadius)(newYork, nearby, 10);
            expect(isWithin).toBe(true);
        });
        it('should return false for points outside radius', () => {
            const isWithin = (0, distance_1.isWithinRadius)(newYork, losAngeles, 100);
            expect(isWithin).toBe(false);
        });
        it('should return true for identical coordinates', () => {
            const isWithin = (0, distance_1.isWithinRadius)(newYork, newYork, 1);
            expect(isWithin).toBe(true);
        });
        it('should handle edge cases at radius boundary', () => {
            const distance = (0, distance_1.calculateDistance)(newYork, nearby);
            // Should be true when radius equals distance
            expect((0, distance_1.isWithinRadius)(newYork, nearby, distance)).toBe(true);
            // Should be false when radius is slightly less than distance
            expect((0, distance_1.isWithinRadius)(newYork, nearby, distance - 0.01)).toBe(false);
        });
    });
    describe('sortByDistance', () => {
        const coordinates = [losAngeles, chicago, nearby];
        it('should sort coordinates by distance from target', () => {
            const sorted = (0, distance_1.sortByDistance)(newYork, coordinates);
            expect(sorted).toHaveLength(3);
            // Should be sorted by distance (closest first)
            expect(sorted[0].distance).toBeLessThanOrEqual(sorted[1].distance);
            expect(sorted[1].distance).toBeLessThanOrEqual(sorted[2].distance);
            // Closest should be Times Square (nearby)
            expect(sorted[0].coordinate).toEqual(nearby);
        });
        it('should include correct distances', () => {
            const sorted = (0, distance_1.sortByDistance)(newYork, coordinates);
            sorted.forEach(item => {
                const expectedDistance = (0, distance_1.calculateDistance)(newYork, item.coordinate);
                expect(item.distance).toBe(expectedDistance);
            });
        });
        it('should handle empty array', () => {
            const sorted = (0, distance_1.sortByDistance)(newYork, []);
            expect(sorted).toHaveLength(0);
        });
        it('should handle single coordinate', () => {
            const sorted = (0, distance_1.sortByDistance)(newYork, [chicago]);
            expect(sorted).toHaveLength(1);
            expect(sorted[0].coordinate).toEqual(chicago);
            expect(sorted[0].distance).toBe((0, distance_1.calculateDistance)(newYork, chicago));
        });
        it('should maintain original coordinates unchanged', () => {
            const originalCoords = [...coordinates];
            (0, distance_1.sortByDistance)(newYork, coordinates);
            // Original array should be unchanged
            expect(coordinates).toEqual(originalCoords);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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