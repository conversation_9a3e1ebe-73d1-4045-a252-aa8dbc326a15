6539e9417b4220244a010eed23e98b45
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessSearchService = void 0;
const validation_1 = require("../utils/validation");
const distance_1 = require("../utils/distance");
/**
 * Main service for orchestrating business searches
 */
class BusinessSearchService {
    constructor(geocodingService, googlePlacesService, websiteVerificationService) {
        this.geocodingService = geocodingService;
        this.googlePlacesService = googlePlacesService;
        this.websiteVerificationService = websiteVerificationService;
        this.searchHistory = [];
        this.searchStats = {
            totalSearches: 0,
            totalResults: 0,
            totalWithWebsites: 0,
            totalSearchDuration: 0,
        };
    }
    /**
     * Searches for businesses and categorizes them by website presence
     * @param request - Search request parameters
     * @returns Promise resolving to categorized search results
     */
    async searchBusinesses(request) {
        const startTime = Date.now();
        try {
            // Validate search parameters
            (0, validation_1.validateSearchParams)({
                zipCode: request.zipCode,
                radius: request.radius,
                businessType: request.businessType,
            });
            // Convert zip code to coordinates
            const coordinates = await this.geocodingService.zipCodeToCoordinates(request.zipCode);
            // Search for places using Google Places API
            const radiusInMeters = request.radius * 1609.34; // Convert miles to meters
            const placesResult = await this.googlePlacesService.searchNearby(coordinates, radiusInMeters, request.businessType, {
                minprice: request.filters?.maxPrice ? 0 : undefined,
                maxprice: request.filters?.maxPrice,
                opennow: request.filters?.openNow,
            });
            // Transform places to businesses and calculate distances
            const businesses = await this.transformPlacesToBusinesses(placesResult.places, coordinates);
            // Filter by rating if specified
            const filteredBusinesses = request.filters?.minRating
                ? businesses.filter(b => b.rating && b.rating >= request.filters.minRating)
                : businesses;
            // Sort by distance
            filteredBusinesses.sort((a, b) => a.distance - b.distance);
            // Extract websites for verification
            const websitesToVerify = filteredBusinesses
                .filter(business => business.website)
                .map(business => business.website);
            // Verify websites (with error handling)
            let verificationResults = [];
            try {
                if (websitesToVerify.length > 0) {
                    verificationResults = await this.websiteVerificationService.verifyMultipleWebsites(websitesToVerify, 5 // Concurrency limit
                    );
                }
            }
            catch (error) {
                console.warn('Website verification failed:', error);
                // Continue without website verification
            }
            // Create verification map for quick lookup
            const verificationMap = new Map(verificationResults.map(result => [result.url, result]));
            // Categorize businesses by website status
            const withWebsites = [];
            const withoutWebsites = [];
            for (const business of filteredBusinesses) {
                if (business.website) {
                    const verification = verificationMap.get(business.website);
                    if (verification && verification.accessible) {
                        business.websiteStatus = 'verified';
                        business.metadata.confidence = verification.confidence;
                        withWebsites.push(business);
                    }
                    else {
                        business.websiteStatus = 'unverified';
                        withoutWebsites.push(business);
                    }
                }
                else {
                    business.websiteStatus = 'none';
                    withoutWebsites.push(business);
                }
            }
            // Calculate statistics
            const searchDuration = Date.now() - startTime;
            const totalFound = filteredBusinesses.length;
            const withWebsiteCount = withWebsites.length;
            const withoutWebsiteCount = withoutWebsites.length;
            const websiteAdoptionRate = totalFound > 0 ? withWebsiteCount / totalFound : 0;
            // Create search result
            const searchResult = {
                searchParams: {
                    zipCode: request.zipCode,
                    radius: request.radius,
                    businessType: request.businessType,
                    timestamp: new Date(),
                },
                results: {
                    withWebsites,
                    withoutWebsites,
                },
                statistics: {
                    totalFound,
                    withWebsiteCount,
                    withoutWebsiteCount,
                    websiteAdoptionRate,
                    searchDuration,
                },
            };
            // Update search history and statistics
            this.updateSearchHistory(searchResult);
            this.updateSearchStatistics(searchResult);
            return searchResult;
        }
        catch (error) {
            // Update statistics even for failed searches
            const searchDuration = Date.now() - startTime;
            this.searchStats.totalSearches++;
            this.searchStats.totalSearchDuration += searchDuration;
            throw error;
        }
    }
    /**
     * Transforms Google Places results to Business objects
     * @param places - Places from Google Places API
     * @param centerCoordinates - Center point for distance calculation
     * @returns Promise resolving to Business array
     */
    async transformPlacesToBusinesses(places, centerCoordinates) {
        return places.map(place => {
            const businessCoordinates = {
                latitude: place.geometry.location.lat,
                longitude: place.geometry.location.lng,
            };
            const distance = (0, distance_1.calculateDistance)(centerCoordinates, businessCoordinates);
            const business = {
                id: place.place_id,
                name: place.name,
                address: place.formatted_address,
                phone: place.formatted_phone_number,
                website: place.website,
                websiteStatus: 'none', // Will be updated during verification
                category: place.types,
                rating: place.rating,
                distance,
                metadata: {
                    lastUpdated: new Date(),
                    dataSource: 'google_places',
                    confidence: 0.8, // Base confidence for Google Places data
                },
            };
            return business;
        });
    }
    /**
     * Updates search history
     * @param searchResult - The search result to add to history
     */
    updateSearchHistory(searchResult) {
        this.searchHistory.unshift(searchResult);
        // Keep only last 50 searches
        if (this.searchHistory.length > 50) {
            this.searchHistory = this.searchHistory.slice(0, 50);
        }
    }
    /**
     * Updates search statistics
     * @param searchResult - The search result to include in statistics
     */
    updateSearchStatistics(searchResult) {
        this.searchStats.totalSearches++;
        this.searchStats.totalResults += searchResult.statistics.totalFound;
        this.searchStats.totalWithWebsites += searchResult.statistics.withWebsiteCount;
        this.searchStats.totalSearchDuration += searchResult.statistics.searchDuration;
    }
    /**
     * Gets search history
     * @returns Array of previous search results
     */
    getSearchHistory() {
        return [...this.searchHistory];
    }
    /**
     * Clears search history
     */
    clearSearchHistory() {
        this.searchHistory = [];
    }
    /**
     * Gets aggregated search statistics
     * @returns Search statistics
     */
    getSearchStatistics() {
        return {
            totalSearches: this.searchStats.totalSearches,
            averageResultsPerSearch: this.searchStats.totalSearches > 0
                ? this.searchStats.totalResults / this.searchStats.totalSearches
                : 0,
            averageWebsiteAdoptionRate: this.searchStats.totalResults > 0
                ? this.searchStats.totalWithWebsites / this.searchStats.totalResults
                : 0,
            averageSearchDuration: this.searchStats.totalSearches > 0
                ? this.searchStats.totalSearchDuration / this.searchStats.totalSearches
                : 0,
        };
    }
    /**
     * Resets all statistics and history
     */
    reset() {
        this.searchHistory = [];
        this.searchStats = {
            totalSearches: 0,
            totalResults: 0,
            totalWithWebsites: 0,
            totalSearchDuration: 0,
        };
    }
}
exports.BusinessSearchService = BusinessSearchService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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