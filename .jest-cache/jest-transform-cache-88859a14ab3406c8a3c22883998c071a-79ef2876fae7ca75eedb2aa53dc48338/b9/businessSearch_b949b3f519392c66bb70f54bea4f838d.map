{"file": "/Users/<USER>/WebstormProjects/goo/src/services/businessSearch.ts", "mappings": ";;;AAKA,oDAA2D;AAC3D,gDAAsD;AA0BtD;;GAEG;AACH,MAAa,qBAAqB;IAShC,YACU,gBAAkC,EAClC,mBAAwC,EACxC,0BAAsD;QAFtD,qBAAgB,GAAhB,gBAAgB,CAAkB;QAClC,wBAAmB,GAAnB,mBAAmB,CAAqB;QACxC,+BAA0B,GAA1B,0BAA0B,CAA4B;QAXxD,kBAAa,GAAmB,EAAE,CAAC;QACnC,gBAAW,GAAG;YACpB,aAAa,EAAE,CAAC;YAChB,YAAY,EAAE,CAAC;YACf,iBAAiB,EAAE,CAAC;YACpB,mBAAmB,EAAE,CAAC;SACvB,CAAC;IAMC,CAAC;IAEJ;;;;OAIG;IACH,KAAK,CAAC,gBAAgB,CAAC,OAAsB;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,6BAA6B;YAC7B,IAAA,iCAAoB,EAAC;gBACnB,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,YAAY,EAAE,OAAO,CAAC,YAAY;aACnC,CAAC,CAAC;YAEH,kCAAkC;YAClC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAEtF,4CAA4C;YAC5C,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,0BAA0B;YAC3E,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAC9D,WAAW,EACX,cAAc,EACd,OAAO,CAAC,YAAY,EACpB;gBACE,QAAQ,EAAE,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;gBACnD,QAAQ,EAAE,OAAO,CAAC,OAAO,EAAE,QAAQ;gBACnC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO;aAClC,CACF,CAAC;YAEF,yDAAyD;YACzD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,2BAA2B,CACvD,YAAY,CAAC,MAAM,EACnB,WAAW,CACZ,CAAC;YAEF,gCAAgC;YAChC,MAAM,kBAAkB,GAAG,OAAO,CAAC,OAAO,EAAE,SAAS;gBACnD,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,IAAI,OAAO,CAAC,OAAQ,CAAC,SAAU,CAAC;gBAC7E,CAAC,CAAC,UAAU,CAAC;YAEf,mBAAmB;YACnB,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;YAE3D,oCAAoC;YACpC,MAAM,gBAAgB,GAAG,kBAAkB;iBACxC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;iBACpC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAQ,CAAC,CAAC;YAEtC,wCAAwC;YACxC,IAAI,mBAAmB,GAAU,EAAE,CAAC;YACpC,IAAI,CAAC;gBACH,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAChC,mBAAmB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,sBAAsB,CAChF,gBAAgB,EAChB,CAAC,CAAC,oBAAoB;qBACvB,CAAC;gBACJ,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;gBACpD,wCAAwC;YAC1C,CAAC;YAED,2CAA2C;YAC3C,MAAM,eAAe,GAAG,IAAI,GAAG,CAC7B,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CACxD,CAAC;YAEF,0CAA0C;YAC1C,MAAM,YAAY,GAAe,EAAE,CAAC;YACpC,MAAM,eAAe,GAAe,EAAE,CAAC;YAEvC,KAAK,MAAM,QAAQ,IAAI,kBAAkB,EAAE,CAAC;gBAC1C,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;oBACrB,MAAM,YAAY,GAAG,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;oBAC3D,IAAI,YAAY,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;wBAC5C,QAAQ,CAAC,aAAa,GAAG,UAAU,CAAC;wBACpC,QAAQ,CAAC,QAAQ,CAAC,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;wBACvD,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC9B,CAAC;yBAAM,CAAC;wBACN,QAAQ,CAAC,aAAa,GAAG,YAAY,CAAC;wBACtC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACjC,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,QAAQ,CAAC,aAAa,GAAG,MAAM,CAAC;oBAChC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACjC,CAAC;YACH,CAAC;YAED,uBAAuB;YACvB,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,MAAM,UAAU,GAAG,kBAAkB,CAAC,MAAM,CAAC;YAC7C,MAAM,gBAAgB,GAAG,YAAY,CAAC,MAAM,CAAC;YAC7C,MAAM,mBAAmB,GAAG,eAAe,CAAC,MAAM,CAAC;YACnD,MAAM,mBAAmB,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;YAE/E,uBAAuB;YACvB,MAAM,YAAY,GAAiB;gBACjC,YAAY,EAAE;oBACZ,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,YAAY,EAAE,OAAO,CAAC,YAAY;oBAClC,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;gBACD,OAAO,EAAE;oBACP,YAAY;oBACZ,eAAe;iBAChB;gBACD,UAAU,EAAE;oBACV,UAAU;oBACV,gBAAgB;oBAChB,mBAAmB;oBACnB,mBAAmB;oBACnB,cAAc;iBACf;aACF,CAAC;YAEF,uCAAuC;YACvC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;YACvC,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;YAE1C,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,6CAA6C;YAC7C,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC;YACjC,IAAI,CAAC,WAAW,CAAC,mBAAmB,IAAI,cAAc,CAAC;YAEvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,2BAA2B,CACvC,MAAqB,EACrB,iBAA8B;QAE9B,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YACxB,MAAM,mBAAmB,GAAgB;gBACvC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG;gBACrC,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG;aACvC,CAAC;YAEF,MAAM,QAAQ,GAAG,IAAA,4BAAiB,EAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;YAE3E,MAAM,QAAQ,GAAa;gBACzB,EAAE,EAAE,KAAK,CAAC,QAAQ;gBAClB,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,KAAK,CAAC,iBAAiB;gBAChC,KAAK,EAAE,KAAK,CAAC,sBAAsB;gBACnC,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,aAAa,EAAE,MAAM,EAAE,sCAAsC;gBAC7D,QAAQ,EAAE,KAAK,CAAC,KAAK;gBACrB,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,QAAQ;gBACR,QAAQ,EAAE;oBACR,WAAW,EAAE,IAAI,IAAI,EAAE;oBACvB,UAAU,EAAE,eAAe;oBAC3B,UAAU,EAAE,GAAG,EAAE,yCAAyC;iBAC3D;aACF,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACK,mBAAmB,CAAC,YAA0B;QACpD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAEzC,6BAA6B;QAC7B,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACnC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED;;;OAGG;IACK,sBAAsB,CAAC,YAA0B;QACvD,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC;QACjC,IAAI,CAAC,WAAW,CAAC,YAAY,IAAI,YAAY,CAAC,UAAU,CAAC,UAAU,CAAC;QACpE,IAAI,CAAC,WAAW,CAAC,iBAAiB,IAAI,YAAY,CAAC,UAAU,CAAC,gBAAgB,CAAC;QAC/E,IAAI,CAAC,WAAW,CAAC,mBAAmB,IAAI,YAAY,CAAC,UAAU,CAAC,cAAc,CAAC;IACjF,CAAC;IAED;;;OAGG;IACH,gBAAgB;QACd,OAAO,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACH,mBAAmB;QACjB,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa;YAC7C,uBAAuB,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa,GAAG,CAAC;gBACzD,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;gBAChE,CAAC,CAAC,CAAC;YACL,0BAA0B,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,CAAC;gBAC3D,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY;gBACpE,CAAC,CAAC,CAAC;YACL,qBAAqB,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa,GAAG,CAAC;gBACvD,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,mBAAmB,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;gBACvE,CAAC,CAAC,CAAC;SACN,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG;YACjB,aAAa,EAAE,CAAC;YAChB,YAAY,EAAE,CAAC;YACf,iBAAiB,EAAE,CAAC;YACpB,mBAAmB,EAAE,CAAC;SACvB,CAAC;IACJ,CAAC;CACF;AAlQD,sDAkQC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/src/services/businessSearch.ts"], "sourcesContent": ["import { Business, SearchResult, SearchParams, Coordinates, PlaceResult } from '../models/Business';\nimport { ValidationError } from '../models/Errors';\nimport { GeocodingService } from './geocoding';\nimport { GooglePlacesService } from './googlePlaces';\nimport { WebsiteVerificationService } from './websiteVerification';\nimport { validateSearchParams } from '../utils/validation';\nimport { calculateDistance } from '../utils/distance';\n\n/**\n * Search request parameters\n */\nexport interface SearchRequest {\n  zipCode: string;\n  radius: number; // in miles\n  businessType: string;\n  filters?: {\n    minRating?: number;\n    maxPrice?: number;\n    openNow?: boolean;\n  };\n}\n\n/**\n * Search statistics for analytics\n */\nexport interface SearchStatistics {\n  totalSearches: number;\n  averageResultsPerSearch: number;\n  averageWebsiteAdoptionRate: number;\n  averageSearchDuration: number;\n}\n\n/**\n * Main service for orchestrating business searches\n */\nexport class BusinessSearchService {\n  private searchHistory: SearchResult[] = [];\n  private searchStats = {\n    totalSearches: 0,\n    totalResults: 0,\n    totalWithWebsites: 0,\n    totalSearchDuration: 0,\n  };\n\n  constructor(\n    private geocodingService: GeocodingService,\n    private googlePlacesService: GooglePlacesService,\n    private websiteVerificationService: WebsiteVerificationService\n  ) {}\n\n  /**\n   * Searches for businesses and categorizes them by website presence\n   * @param request - Search request parameters\n   * @returns Promise resolving to categorized search results\n   */\n  async searchBusinesses(request: SearchRequest): Promise<SearchResult> {\n    const startTime = Date.now();\n\n    try {\n      // Validate search parameters\n      validateSearchParams({\n        zipCode: request.zipCode,\n        radius: request.radius,\n        businessType: request.businessType,\n      });\n\n      // Convert zip code to coordinates\n      const coordinates = await this.geocodingService.zipCodeToCoordinates(request.zipCode);\n\n      // Search for places using Google Places API\n      const radiusInMeters = request.radius * 1609.34; // Convert miles to meters\n      const placesResult = await this.googlePlacesService.searchNearby(\n        coordinates,\n        radiusInMeters,\n        request.businessType,\n        {\n          minprice: request.filters?.maxPrice ? 0 : undefined,\n          maxprice: request.filters?.maxPrice,\n          opennow: request.filters?.openNow,\n        }\n      );\n\n      // Transform places to businesses and calculate distances\n      const businesses = await this.transformPlacesToBusinesses(\n        placesResult.places,\n        coordinates\n      );\n\n      // Filter by rating if specified\n      const filteredBusinesses = request.filters?.minRating\n        ? businesses.filter(b => b.rating && b.rating >= request.filters!.minRating!)\n        : businesses;\n\n      // Sort by distance\n      filteredBusinesses.sort((a, b) => a.distance - b.distance);\n\n      // Extract websites for verification\n      const websitesToVerify = filteredBusinesses\n        .filter(business => business.website)\n        .map(business => business.website!);\n\n      // Verify websites (with error handling)\n      let verificationResults: any[] = [];\n      try {\n        if (websitesToVerify.length > 0) {\n          verificationResults = await this.websiteVerificationService.verifyMultipleWebsites(\n            websitesToVerify,\n            5 // Concurrency limit\n          );\n        }\n      } catch (error) {\n        console.warn('Website verification failed:', error);\n        // Continue without website verification\n      }\n\n      // Create verification map for quick lookup\n      const verificationMap = new Map(\n        verificationResults.map(result => [result.url, result])\n      );\n\n      // Categorize businesses by website status\n      const withWebsites: Business[] = [];\n      const withoutWebsites: Business[] = [];\n\n      for (const business of filteredBusinesses) {\n        if (business.website) {\n          const verification = verificationMap.get(business.website);\n          if (verification && verification.accessible) {\n            business.websiteStatus = 'verified';\n            business.metadata.confidence = verification.confidence;\n            withWebsites.push(business);\n          } else {\n            business.websiteStatus = 'unverified';\n            withoutWebsites.push(business);\n          }\n        } else {\n          business.websiteStatus = 'none';\n          withoutWebsites.push(business);\n        }\n      }\n\n      // Calculate statistics\n      const searchDuration = Date.now() - startTime;\n      const totalFound = filteredBusinesses.length;\n      const withWebsiteCount = withWebsites.length;\n      const withoutWebsiteCount = withoutWebsites.length;\n      const websiteAdoptionRate = totalFound > 0 ? withWebsiteCount / totalFound : 0;\n\n      // Create search result\n      const searchResult: SearchResult = {\n        searchParams: {\n          zipCode: request.zipCode,\n          radius: request.radius,\n          businessType: request.businessType,\n          timestamp: new Date(),\n        },\n        results: {\n          withWebsites,\n          withoutWebsites,\n        },\n        statistics: {\n          totalFound,\n          withWebsiteCount,\n          withoutWebsiteCount,\n          websiteAdoptionRate,\n          searchDuration,\n        },\n      };\n\n      // Update search history and statistics\n      this.updateSearchHistory(searchResult);\n      this.updateSearchStatistics(searchResult);\n\n      return searchResult;\n    } catch (error) {\n      // Update statistics even for failed searches\n      const searchDuration = Date.now() - startTime;\n      this.searchStats.totalSearches++;\n      this.searchStats.totalSearchDuration += searchDuration;\n\n      throw error;\n    }\n  }\n\n  /**\n   * Transforms Google Places results to Business objects\n   * @param places - Places from Google Places API\n   * @param centerCoordinates - Center point for distance calculation\n   * @returns Promise resolving to Business array\n   */\n  private async transformPlacesToBusinesses(\n    places: PlaceResult[],\n    centerCoordinates: Coordinates\n  ): Promise<Business[]> {\n    return places.map(place => {\n      const businessCoordinates: Coordinates = {\n        latitude: place.geometry.location.lat,\n        longitude: place.geometry.location.lng,\n      };\n\n      const distance = calculateDistance(centerCoordinates, businessCoordinates);\n\n      const business: Business = {\n        id: place.place_id,\n        name: place.name,\n        address: place.formatted_address,\n        phone: place.formatted_phone_number,\n        website: place.website,\n        websiteStatus: 'none', // Will be updated during verification\n        category: place.types,\n        rating: place.rating,\n        distance,\n        metadata: {\n          lastUpdated: new Date(),\n          dataSource: 'google_places',\n          confidence: 0.8, // Base confidence for Google Places data\n        },\n      };\n\n      return business;\n    });\n  }\n\n  /**\n   * Updates search history\n   * @param searchResult - The search result to add to history\n   */\n  private updateSearchHistory(searchResult: SearchResult): void {\n    this.searchHistory.unshift(searchResult);\n\n    // Keep only last 50 searches\n    if (this.searchHistory.length > 50) {\n      this.searchHistory = this.searchHistory.slice(0, 50);\n    }\n  }\n\n  /**\n   * Updates search statistics\n   * @param searchResult - The search result to include in statistics\n   */\n  private updateSearchStatistics(searchResult: SearchResult): void {\n    this.searchStats.totalSearches++;\n    this.searchStats.totalResults += searchResult.statistics.totalFound;\n    this.searchStats.totalWithWebsites += searchResult.statistics.withWebsiteCount;\n    this.searchStats.totalSearchDuration += searchResult.statistics.searchDuration;\n  }\n\n  /**\n   * Gets search history\n   * @returns Array of previous search results\n   */\n  getSearchHistory(): SearchResult[] {\n    return [...this.searchHistory];\n  }\n\n  /**\n   * Clears search history\n   */\n  clearSearchHistory(): void {\n    this.searchHistory = [];\n  }\n\n  /**\n   * Gets aggregated search statistics\n   * @returns Search statistics\n   */\n  getSearchStatistics(): SearchStatistics {\n    return {\n      totalSearches: this.searchStats.totalSearches,\n      averageResultsPerSearch: this.searchStats.totalSearches > 0\n        ? this.searchStats.totalResults / this.searchStats.totalSearches\n        : 0,\n      averageWebsiteAdoptionRate: this.searchStats.totalResults > 0\n        ? this.searchStats.totalWithWebsites / this.searchStats.totalResults\n        : 0,\n      averageSearchDuration: this.searchStats.totalSearches > 0\n        ? this.searchStats.totalSearchDuration / this.searchStats.totalSearches\n        : 0,\n    };\n  }\n\n  /**\n   * Resets all statistics and history\n   */\n  reset(): void {\n    this.searchHistory = [];\n    this.searchStats = {\n      totalSearches: 0,\n      totalResults: 0,\n      totalWithWebsites: 0,\n      totalSearchDuration: 0,\n    };\n  }\n}\n"], "version": 3}