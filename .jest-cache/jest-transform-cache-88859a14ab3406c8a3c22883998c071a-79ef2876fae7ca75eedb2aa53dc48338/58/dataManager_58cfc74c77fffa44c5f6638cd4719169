ec66814ac83f603f2eef22da0d27c340
"use strict";

/* istanbul ignore next */
function cov_wkm373csk() {
  var path = "/Users/<USER>/WebstormProjects/goo/src/services/dataManager.ts";
  var hash = "9c78982dc75844bd0e229119da9def4534e4dbe1";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/WebstormProjects/goo/src/services/dataManager.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 3,
          column: 29
        }
      },
      "2": {
        start: {
          line: 4,
          column: 17
        },
        end: {
          line: 4,
          column: 44
        }
      },
      "3": {
        start: {
          line: 5,
          column: 20
        },
        end: {
          line: 5,
          column: 43
        }
      },
      "4": {
        start: {
          line: 11,
          column: 8
        },
        end: {
          line: 11,
          column: 61
        }
      },
      "5": {
        start: {
          line: 12,
          column: 8
        },
        end: {
          line: 12,
          column: 87
        }
      },
      "6": {
        start: {
          line: 20,
          column: 8
        },
        end: {
          line: 33,
          column: 9
        }
      },
      "7": {
        start: {
          line: 21,
          column: 24
        },
        end: {
          line: 21,
          column: 54
        }
      },
      "8": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "9": {
        start: {
          line: 23,
          column: 31
        },
        end: {
          line: 27,
          column: 13
        }
      },
      "10": {
        start: {
          line: 28,
          column: 12
        },
        end: {
          line: 28,
          column: 66
        }
      },
      "11": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 23
        }
      },
      "12": {
        start: {
          line: 32,
          column: 12
        },
        end: {
          line: 32,
          column: 133
        }
      },
      "13": {
        start: {
          line: 41,
          column: 8
        },
        end: {
          line: 58,
          column: 9
        }
      },
      "14": {
        start: {
          line: 42,
          column: 27
        },
        end: {
          line: 42,
          column: 52
        }
      },
      "15": {
        start: {
          line: 43,
          column: 12
        },
        end: {
          line: 45,
          column: 13
        }
      },
      "16": {
        start: {
          line: 44,
          column: 16
        },
        end: {
          line: 44,
          column: 28
        }
      },
      "17": {
        start: {
          line: 46,
          column: 31
        },
        end: {
          line: 46,
          column: 49
        }
      },
      "18": {
        start: {
          line: 48,
          column: 12
        },
        end: {
          line: 51,
          column: 13
        }
      },
      "19": {
        start: {
          line: 49,
          column: 16
        },
        end: {
          line: 49,
          column: 45
        }
      },
      "20": {
        start: {
          line: 50,
          column: 16
        },
        end: {
          line: 50,
          column: 28
        }
      },
      "21": {
        start: {
          line: 52,
          column: 12
        },
        end: {
          line: 52,
          column: 65
        }
      },
      "22": {
        start: {
          line: 56,
          column: 12
        },
        end: {
          line: 56,
          column: 41
        }
      },
      "23": {
        start: {
          line: 57,
          column: 12
        },
        end: {
          line: 57,
          column: 24
        }
      },
      "24": {
        start: {
          line: 65,
          column: 24
        },
        end: {
          line: 65,
          column: 26
        }
      },
      "25": {
        start: {
          line: 66,
          column: 29
        },
        end: {
          line: 66,
          column: 31
        }
      },
      "26": {
        start: {
          line: 67,
          column: 8
        },
        end: {
          line: 88,
          column: 9
        }
      },
      "27": {
        start: {
          line: 67,
          column: 21
        },
        end: {
          line: 67,
          column: 22
        }
      },
      "28": {
        start: {
          line: 68,
          column: 24
        },
        end: {
          line: 68,
          column: 43
        }
      },
      "29": {
        start: {
          line: 69,
          column: 12
        },
        end: {
          line: 71,
          column: 13
        }
      },
      "30": {
        start: {
          line: 70,
          column: 16
        },
        end: {
          line: 70,
          column: 25
        }
      },
      "31": {
        start: {
          line: 72,
          column: 12
        },
        end: {
          line: 87,
          column: 13
        }
      },
      "32": {
        start: {
          line: 73,
          column: 31
        },
        end: {
          line: 73,
          column: 56
        }
      },
      "33": {
        start: {
          line: 74,
          column: 16
        },
        end: {
          line: 75,
          column: 29
        }
      },
      "34": {
        start: {
          line: 75,
          column: 20
        },
        end: {
          line: 75,
          column: 29
        }
      },
      "35": {
        start: {
          line: 76,
          column: 35
        },
        end: {
          line: 76,
          column: 53
        }
      },
      "36": {
        start: {
          line: 78,
          column: 16
        },
        end: {
          line: 81,
          column: 17
        }
      },
      "37": {
        start: {
          line: 79,
          column: 20
        },
        end: {
          line: 79,
          column: 43
        }
      },
      "38": {
        start: {
          line: 80,
          column: 20
        },
        end: {
          line: 80,
          column: 29
        }
      },
      "39": {
        start: {
          line: 82,
          column: 16
        },
        end: {
          line: 82,
          column: 76
        }
      },
      "40": {
        start: {
          line: 86,
          column: 16
        },
        end: {
          line: 86,
          column: 39
        }
      },
      "41": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 90,
          column: 66
        }
      },
      "42": {
        start: {
          line: 90,
          column: 36
        },
        end: {
          line: 90,
          column: 64
        }
      },
      "43": {
        start: {
          line: 92,
          column: 8
        },
        end: {
          line: 92,
          column: 131
        }
      },
      "44": {
        start: {
          line: 92,
          column: 38
        },
        end: {
          line: 92,
          column: 129
        }
      },
      "45": {
        start: {
          line: 99,
          column: 8
        },
        end: {
          line: 99,
          column: 37
        }
      },
      "46": {
        start: {
          line: 105,
          column: 29
        },
        end: {
          line: 105,
          column: 31
        }
      },
      "47": {
        start: {
          line: 106,
          column: 8
        },
        end: {
          line: 111,
          column: 9
        }
      },
      "48": {
        start: {
          line: 106,
          column: 21
        },
        end: {
          line: 106,
          column: 22
        }
      },
      "49": {
        start: {
          line: 107,
          column: 24
        },
        end: {
          line: 107,
          column: 43
        }
      },
      "50": {
        start: {
          line: 108,
          column: 12
        },
        end: {
          line: 110,
          column: 13
        }
      },
      "51": {
        start: {
          line: 109,
          column: 16
        },
        end: {
          line: 109,
          column: 39
        }
      },
      "52": {
        start: {
          line: 112,
          column: 8
        },
        end: {
          line: 112,
          column: 66
        }
      },
      "53": {
        start: {
          line: 112,
          column: 36
        },
        end: {
          line: 112,
          column: 64
        }
      },
      "54": {
        start: {
          line: 119,
          column: 27
        },
        end: {
          line: 119,
          column: 28
        }
      },
      "55": {
        start: {
          line: 120,
          column: 29
        },
        end: {
          line: 120,
          column: 30
        }
      },
      "56": {
        start: {
          line: 121,
          column: 30
        },
        end: {
          line: 121,
          column: 34
        }
      },
      "57": {
        start: {
          line: 122,
          column: 30
        },
        end: {
          line: 122,
          column: 34
        }
      },
      "58": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 150,
          column: 9
        }
      },
      "59": {
        start: {
          line: 123,
          column: 21
        },
        end: {
          line: 123,
          column: 22
        }
      },
      "60": {
        start: {
          line: 124,
          column: 24
        },
        end: {
          line: 124,
          column: 43
        }
      },
      "61": {
        start: {
          line: 125,
          column: 12
        },
        end: {
          line: 127,
          column: 13
        }
      },
      "62": {
        start: {
          line: 126,
          column: 16
        },
        end: {
          line: 126,
          column: 25
        }
      },
      "63": {
        start: {
          line: 128,
          column: 12
        },
        end: {
          line: 149,
          column: 13
        }
      },
      "64": {
        start: {
          line: 129,
          column: 31
        },
        end: {
          line: 129,
          column: 56
        }
      },
      "65": {
        start: {
          line: 130,
          column: 16
        },
        end: {
          line: 131,
          column: 29
        }
      },
      "66": {
        start: {
          line: 131,
          column: 20
        },
        end: {
          line: 131,
          column: 29
        }
      },
      "67": {
        start: {
          line: 132,
          column: 35
        },
        end: {
          line: 132,
          column: 53
        }
      },
      "68": {
        start: {
          line: 134,
          column: 16
        },
        end: {
          line: 136,
          column: 17
        }
      },
      "69": {
        start: {
          line: 135,
          column: 20
        },
        end: {
          line: 135,
          column: 29
        }
      },
      "70": {
        start: {
          line: 137,
          column: 16
        },
        end: {
          line: 137,
          column: 31
        }
      },
      "71": {
        start: {
          line: 138,
          column: 16
        },
        end: {
          line: 138,
          column: 48
        }
      },
      "72": {
        start: {
          line: 139,
          column: 16
        },
        end: {
          line: 141,
          column: 17
        }
      },
      "73": {
        start: {
          line: 140,
          column: 20
        },
        end: {
          line: 140,
          column: 59
        }
      },
      "74": {
        start: {
          line: 142,
          column: 16
        },
        end: {
          line: 144,
          column: 17
        }
      },
      "75": {
        start: {
          line: 143,
          column: 20
        },
        end: {
          line: 143,
          column: 59
        }
      },
      "76": {
        start: {
          line: 148,
          column: 16
        },
        end: {
          line: 148,
          column: 25
        }
      },
      "77": {
        start: {
          line: 151,
          column: 8
        },
        end: {
          line: 156,
          column: 10
        }
      },
      "78": {
        start: {
          line: 163,
          column: 29
        },
        end: {
          line: 163,
          column: 31
        }
      },
      "79": {
        start: {
          line: 164,
          column: 20
        },
        end: {
          line: 164,
          column: 30
        }
      },
      "80": {
        start: {
          line: 165,
          column: 8
        },
        end: {
          line: 183,
          column: 9
        }
      },
      "81": {
        start: {
          line: 165,
          column: 21
        },
        end: {
          line: 165,
          column: 22
        }
      },
      "82": {
        start: {
          line: 166,
          column: 24
        },
        end: {
          line: 166,
          column: 43
        }
      },
      "83": {
        start: {
          line: 167,
          column: 12
        },
        end: {
          line: 169,
          column: 13
        }
      },
      "84": {
        start: {
          line: 168,
          column: 16
        },
        end: {
          line: 168,
          column: 25
        }
      },
      "85": {
        start: {
          line: 170,
          column: 12
        },
        end: {
          line: 182,
          column: 13
        }
      },
      "86": {
        start: {
          line: 171,
          column: 31
        },
        end: {
          line: 171,
          column: 56
        }
      },
      "87": {
        start: {
          line: 172,
          column: 16
        },
        end: {
          line: 173,
          column: 29
        }
      },
      "88": {
        start: {
          line: 173,
          column: 20
        },
        end: {
          line: 173,
          column: 29
        }
      },
      "89": {
        start: {
          line: 174,
          column: 35
        },
        end: {
          line: 174,
          column: 53
        }
      },
      "90": {
        start: {
          line: 175,
          column: 16
        },
        end: {
          line: 177,
          column: 17
        }
      },
      "91": {
        start: {
          line: 176,
          column: 20
        },
        end: {
          line: 176,
          column: 43
        }
      },
      "92": {
        start: {
          line: 181,
          column: 16
        },
        end: {
          line: 181,
          column: 39
        }
      },
      "93": {
        start: {
          line: 184,
          column: 8
        },
        end: {
          line: 184,
          column: 66
        }
      },
      "94": {
        start: {
          line: 184,
          column: 36
        },
        end: {
          line: 184,
          column: 64
        }
      },
      "95": {
        start: {
          line: 185,
          column: 8
        },
        end: {
          line: 185,
          column: 35
        }
      },
      "96": {
        start: {
          line: 192,
          column: 30
        },
        end: {
          line: 192,
          column: 56
        }
      },
      "97": {
        start: {
          line: 193,
          column: 27
        },
        end: {
          line: 197,
          column: 9
        }
      },
      "98": {
        start: {
          line: 198,
          column: 8
        },
        end: {
          line: 198,
          column: 51
        }
      },
      "99": {
        start: {
          line: 206,
          column: 8
        },
        end: {
          line: 229,
          column: 9
        }
      },
      "100": {
        start: {
          line: 207,
          column: 31
        },
        end: {
          line: 207,
          column: 51
        }
      },
      "101": {
        start: {
          line: 209,
          column: 12
        },
        end: {
          line: 211,
          column: 13
        }
      },
      "102": {
        start: {
          line: 210,
          column: 16
        },
        end: {
          line: 210,
          column: 65
        }
      },
      "103": {
        start: {
          line: 212,
          column: 32
        },
        end: {
          line: 212,
          column: 33
        }
      },
      "104": {
        start: {
          line: 213,
          column: 12
        },
        end: {
          line: 224,
          column: 13
        }
      },
      "105": {
        start: {
          line: 214,
          column: 16
        },
        end: {
          line: 223,
          column: 17
        }
      },
      "106": {
        start: {
          line: 216,
          column: 47
        },
        end: {
          line: 216,
          column: 89
        }
      },
      "107": {
        start: {
          line: 217,
          column: 20
        },
        end: {
          line: 217,
          column: 62
        }
      },
      "108": {
        start: {
          line: 218,
          column: 20
        },
        end: {
          line: 218,
          column: 36
        }
      },
      "109": {
        start: {
          line: 221,
          column: 20
        },
        end: {
          line: 221,
          column: 75
        }
      },
      "110": {
        start: {
          line: 225,
          column: 12
        },
        end: {
          line: 225,
          column: 33
        }
      },
      "111": {
        start: {
          line: 228,
          column: 12
        },
        end: {
          line: 228,
          column: 126
        }
      },
      "112": {
        start: {
          line: 237,
          column: 23
        },
        end: {
          line: 237,
          column: 48
        }
      },
      "113": {
        start: {
          line: 238,
          column: 21
        },
        end: {
          line: 238,
          column: 127
        }
      },
      "114": {
        start: {
          line: 239,
          column: 8
        },
        end: {
          line: 239,
          column: 42
        }
      },
      "115": {
        start: {
          line: 247,
          column: 19
        },
        end: {
          line: 247,
          column: 20
        }
      },
      "116": {
        start: {
          line: 248,
          column: 8
        },
        end: {
          line: 252,
          column: 9
        }
      },
      "117": {
        start: {
          line: 248,
          column: 21
        },
        end: {
          line: 248,
          column: 22
        }
      },
      "118": {
        start: {
          line: 249,
          column: 25
        },
        end: {
          line: 249,
          column: 42
        }
      },
      "119": {
        start: {
          line: 250,
          column: 12
        },
        end: {
          line: 250,
          column: 47
        }
      },
      "120": {
        start: {
          line: 251,
          column: 12
        },
        end: {
          line: 251,
          column: 31
        }
      },
      "121": {
        start: {
          line: 253,
          column: 8
        },
        end: {
          line: 253,
          column: 43
        }
      },
      "122": {
        start: {
          line: 260,
          column: 20
        },
        end: {
          line: 260,
          column: 21
        }
      },
      "123": {
        start: {
          line: 261,
          column: 8
        },
        end: {
          line: 266,
          column: 9
        }
      },
      "124": {
        start: {
          line: 261,
          column: 21
        },
        end: {
          line: 261,
          column: 22
        }
      },
      "125": {
        start: {
          line: 262,
          column: 24
        },
        end: {
          line: 262,
          column: 43
        }
      },
      "126": {
        start: {
          line: 263,
          column: 12
        },
        end: {
          line: 265,
          column: 13
        }
      },
      "127": {
        start: {
          line: 264,
          column: 16
        },
        end: {
          line: 264,
          column: 24
        }
      },
      "128": {
        start: {
          line: 267,
          column: 8
        },
        end: {
          line: 267,
          column: 21
        }
      },
      "129": {
        start: {
          line: 275,
          column: 8
        },
        end: {
          line: 297,
          column: 10
        }
      },
      "130": {
        start: {
          line: 282,
          column: 75
        },
        end: {
          line: 288,
          column: 17
        }
      },
      "131": {
        start: {
          line: 289,
          column: 81
        },
        end: {
          line: 295,
          column: 17
        }
      },
      "132": {
        start: {
          line: 300,
          column: 0
        },
        end: {
          line: 300,
          column: 34
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 10,
            column: 4
          },
          end: {
            line: 10,
            column: 5
          }
        },
        loc: {
          start: {
            line: 10,
            column: 18
          },
          end: {
            line: 13,
            column: 5
          }
        },
        line: 10
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 19,
            column: 4
          },
          end: {
            line: 19,
            column: 5
          }
        },
        loc: {
          start: {
            line: 19,
            column: 35
          },
          end: {
            line: 34,
            column: 5
          }
        },
        line: 19
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 40,
            column: 4
          },
          end: {
            line: 40,
            column: 5
          }
        },
        loc: {
          start: {
            line: 40,
            column: 25
          },
          end: {
            line: 59,
            column: 5
          }
        },
        line: 40
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 64,
            column: 4
          },
          end: {
            line: 64,
            column: 5
          }
        },
        loc: {
          start: {
            line: 64,
            column: 26
          },
          end: {
            line: 93,
            column: 5
          }
        },
        line: 64
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 90,
            column: 29
          },
          end: {
            line: 90,
            column: 30
          }
        },
        loc: {
          start: {
            line: 90,
            column: 36
          },
          end: {
            line: 90,
            column: 64
          }
        },
        line: 90
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 92,
            column: 28
          },
          end: {
            line: 92,
            column: 29
          }
        },
        loc: {
          start: {
            line: 92,
            column: 38
          },
          end: {
            line: 92,
            column: 129
          }
        },
        line: 92
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 98,
            column: 5
          }
        },
        loc: {
          start: {
            line: 98,
            column: 28
          },
          end: {
            line: 100,
            column: 5
          }
        },
        line: 98
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 104,
            column: 4
          },
          end: {
            line: 104,
            column: 5
          }
        },
        loc: {
          start: {
            line: 104,
            column: 19
          },
          end: {
            line: 113,
            column: 5
          }
        },
        line: 104
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 112,
            column: 29
          },
          end: {
            line: 112,
            column: 30
          }
        },
        loc: {
          start: {
            line: 112,
            column: 36
          },
          end: {
            line: 112,
            column: 64
          }
        },
        line: 112
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 118,
            column: 4
          },
          end: {
            line: 118,
            column: 5
          }
        },
        loc: {
          start: {
            line: 118,
            column: 21
          },
          end: {
            line: 157,
            column: 5
          }
        },
        line: 118
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 162,
            column: 4
          },
          end: {
            line: 162,
            column: 5
          }
        },
        loc: {
          start: {
            line: 162,
            column: 25
          },
          end: {
            line: 186,
            column: 5
          }
        },
        line: 162
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 184,
            column: 29
          },
          end: {
            line: 184,
            column: 30
          }
        },
        loc: {
          start: {
            line: 184,
            column: 36
          },
          end: {
            line: 184,
            column: 64
          }
        },
        line: 184
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 191,
            column: 4
          },
          end: {
            line: 191,
            column: 5
          }
        },
        loc: {
          start: {
            line: 191,
            column: 17
          },
          end: {
            line: 199,
            column: 5
          }
        },
        line: 191
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 205,
            column: 4
          },
          end: {
            line: 205,
            column: 5
          }
        },
        loc: {
          start: {
            line: 205,
            column: 25
          },
          end: {
            line: 230,
            column: 5
          }
        },
        line: 205
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 236,
            column: 4
          },
          end: {
            line: 236,
            column: 5
          }
        },
        loc: {
          start: {
            line: 236,
            column: 30
          },
          end: {
            line: 240,
            column: 5
          }
        },
        line: 236
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 246,
            column: 4
          },
          end: {
            line: 246,
            column: 5
          }
        },
        loc: {
          start: {
            line: 246,
            column: 20
          },
          end: {
            line: 254,
            column: 5
          }
        },
        line: 246
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 259,
            column: 4
          },
          end: {
            line: 259,
            column: 5
          }
        },
        loc: {
          start: {
            line: 259,
            column: 19
          },
          end: {
            line: 268,
            column: 5
          }
        },
        line: 259
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 274,
            column: 4
          },
          end: {
            line: 274,
            column: 5
          }
        },
        loc: {
          start: {
            line: 274,
            column: 34
          },
          end: {
            line: 298,
            column: 5
          }
        },
        line: 274
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 282,
            column: 60
          },
          end: {
            line: 282,
            column: 61
          }
        },
        loc: {
          start: {
            line: 282,
            column: 75
          },
          end: {
            line: 288,
            column: 17
          }
        },
        line: 282
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 289,
            column: 66
          },
          end: {
            line: 289,
            column: 67
          }
        },
        loc: {
          start: {
            line: 289,
            column: 81
          },
          end: {
            line: 295,
            column: 17
          }
        },
        line: 289
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 32,
            column: 75
          },
          end: {
            line: 32,
            column: 129
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 32,
            column: 100
          },
          end: {
            line: 32,
            column: 113
          }
        }, {
          start: {
            line: 32,
            column: 116
          },
          end: {
            line: 32,
            column: 129
          }
        }],
        line: 32
      },
      "1": {
        loc: {
          start: {
            line: 43,
            column: 12
          },
          end: {
            line: 45,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 12
          },
          end: {
            line: 45,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 43
      },
      "2": {
        loc: {
          start: {
            line: 48,
            column: 12
          },
          end: {
            line: 51,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 48,
            column: 12
          },
          end: {
            line: 51,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 48
      },
      "3": {
        loc: {
          start: {
            line: 69,
            column: 12
          },
          end: {
            line: 71,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 69,
            column: 12
          },
          end: {
            line: 71,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 69
      },
      "4": {
        loc: {
          start: {
            line: 69,
            column: 16
          },
          end: {
            line: 69,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 69,
            column: 16
          },
          end: {
            line: 69,
            column: 20
          }
        }, {
          start: {
            line: 69,
            column: 24
          },
          end: {
            line: 69,
            column: 55
          }
        }],
        line: 69
      },
      "5": {
        loc: {
          start: {
            line: 74,
            column: 16
          },
          end: {
            line: 75,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 16
          },
          end: {
            line: 75,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 74
      },
      "6": {
        loc: {
          start: {
            line: 78,
            column: 16
          },
          end: {
            line: 81,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 78,
            column: 16
          },
          end: {
            line: 81,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 78
      },
      "7": {
        loc: {
          start: {
            line: 108,
            column: 12
          },
          end: {
            line: 110,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 108,
            column: 12
          },
          end: {
            line: 110,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 108
      },
      "8": {
        loc: {
          start: {
            line: 108,
            column: 16
          },
          end: {
            line: 108,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 108,
            column: 16
          },
          end: {
            line: 108,
            column: 19
          }
        }, {
          start: {
            line: 108,
            column: 23
          },
          end: {
            line: 108,
            column: 53
          }
        }],
        line: 108
      },
      "9": {
        loc: {
          start: {
            line: 125,
            column: 12
          },
          end: {
            line: 127,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 125,
            column: 12
          },
          end: {
            line: 127,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 125
      },
      "10": {
        loc: {
          start: {
            line: 125,
            column: 16
          },
          end: {
            line: 125,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 125,
            column: 16
          },
          end: {
            line: 125,
            column: 20
          }
        }, {
          start: {
            line: 125,
            column: 24
          },
          end: {
            line: 125,
            column: 55
          }
        }],
        line: 125
      },
      "11": {
        loc: {
          start: {
            line: 130,
            column: 16
          },
          end: {
            line: 131,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 130,
            column: 16
          },
          end: {
            line: 131,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 130
      },
      "12": {
        loc: {
          start: {
            line: 134,
            column: 16
          },
          end: {
            line: 136,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 134,
            column: 16
          },
          end: {
            line: 136,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 134
      },
      "13": {
        loc: {
          start: {
            line: 139,
            column: 16
          },
          end: {
            line: 141,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 139,
            column: 16
          },
          end: {
            line: 141,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 139
      },
      "14": {
        loc: {
          start: {
            line: 139,
            column: 20
          },
          end: {
            line: 139,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 139,
            column: 20
          },
          end: {
            line: 139,
            column: 44
          }
        }, {
          start: {
            line: 139,
            column: 48
          },
          end: {
            line: 139,
            column: 86
          }
        }],
        line: 139
      },
      "15": {
        loc: {
          start: {
            line: 142,
            column: 16
          },
          end: {
            line: 144,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 142,
            column: 16
          },
          end: {
            line: 144,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 142
      },
      "16": {
        loc: {
          start: {
            line: 142,
            column: 20
          },
          end: {
            line: 142,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 142,
            column: 20
          },
          end: {
            line: 142,
            column: 44
          }
        }, {
          start: {
            line: 142,
            column: 48
          },
          end: {
            line: 142,
            column: 86
          }
        }],
        line: 142
      },
      "17": {
        loc: {
          start: {
            line: 154,
            column: 25
          },
          end: {
            line: 154,
            column: 75
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 154,
            column: 43
          },
          end: {
            line: 154,
            column: 68
          }
        }, {
          start: {
            line: 154,
            column: 71
          },
          end: {
            line: 154,
            column: 75
          }
        }],
        line: 154
      },
      "18": {
        loc: {
          start: {
            line: 155,
            column: 25
          },
          end: {
            line: 155,
            column: 75
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 155,
            column: 43
          },
          end: {
            line: 155,
            column: 68
          }
        }, {
          start: {
            line: 155,
            column: 71
          },
          end: {
            line: 155,
            column: 75
          }
        }],
        line: 155
      },
      "19": {
        loc: {
          start: {
            line: 167,
            column: 12
          },
          end: {
            line: 169,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 167,
            column: 12
          },
          end: {
            line: 169,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 167
      },
      "20": {
        loc: {
          start: {
            line: 167,
            column: 16
          },
          end: {
            line: 167,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 167,
            column: 16
          },
          end: {
            line: 167,
            column: 20
          }
        }, {
          start: {
            line: 167,
            column: 24
          },
          end: {
            line: 167,
            column: 55
          }
        }],
        line: 167
      },
      "21": {
        loc: {
          start: {
            line: 172,
            column: 16
          },
          end: {
            line: 173,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 172,
            column: 16
          },
          end: {
            line: 173,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 172
      },
      "22": {
        loc: {
          start: {
            line: 175,
            column: 16
          },
          end: {
            line: 177,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 175,
            column: 16
          },
          end: {
            line: 177,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 175
      },
      "23": {
        loc: {
          start: {
            line: 209,
            column: 12
          },
          end: {
            line: 211,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 209,
            column: 12
          },
          end: {
            line: 211,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 209
      },
      "24": {
        loc: {
          start: {
            line: 209,
            column: 16
          },
          end: {
            line: 209,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 209,
            column: 16
          },
          end: {
            line: 209,
            column: 41
          }
        }, {
          start: {
            line: 209,
            column: 45
          },
          end: {
            line: 209,
            column: 85
          }
        }],
        line: 209
      },
      "25": {
        loc: {
          start: {
            line: 228,
            column: 68
          },
          end: {
            line: 228,
            column: 122
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 228,
            column: 93
          },
          end: {
            line: 228,
            column: 106
          }
        }, {
          start: {
            line: 228,
            column: 109
          },
          end: {
            line: 228,
            column: 122
          }
        }],
        line: 228
      },
      "26": {
        loc: {
          start: {
            line: 263,
            column: 12
          },
          end: {
            line: 265,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 12
          },
          end: {
            line: 265,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 263
      },
      "27": {
        loc: {
          start: {
            line: 263,
            column: 16
          },
          end: {
            line: 263,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 263,
            column: 16
          },
          end: {
            line: 263,
            column: 19
          }
        }, {
          start: {
            line: 263,
            column: 23
          },
          end: {
            line: 263,
            column: 53
          }
        }],
        line: 263
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/WebstormProjects/goo/src/services/dataManager.ts",
      mappings: ";;;AACA,6CAA8C;AAC9C,4CAA4C;AA8B5C;;GAEG;AACH,MAAa,WAAW;IAAxB;QACmB,cAAS,GAAG,wBAAY,CAAC,UAAU,CAAC;QACpC,iBAAY,GAAG,wBAAY,CAAC,gBAAgB,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAmUjF,CAAC;IAjUC;;;;OAIG;IACH,gBAAgB,CAAC,YAA0B;QACzC,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;YAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAEvB,MAAM,UAAU,GAAe;gBAC7B,IAAI,EAAE,YAAY;gBAClB,SAAS,EAAE,GAAG;gBACd,SAAS,EAAE,GAAG,GAAG,IAAI,CAAC,YAAY;aACnC,CAAC;YAEF,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YACtD,OAAO,GAAG,CAAC;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,mBAAU,CAAC,iCAAiC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAClH,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,eAAe,CAAC,GAAW;QACzB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACzC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,UAAU,GAAe,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAElD,4BAA4B;YAC5B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;gBACtC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;gBAC7B,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,wBAAwB;YACxB,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,mBAAmB;QACjB,MAAM,OAAO,GAAmB,EAAE,CAAC;QACnC,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5C,SAAS;YACX,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACzC,IAAI,CAAC,MAAM;oBAAE,SAAS;gBAEtB,MAAM,UAAU,GAAe,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBAElD,4BAA4B;gBAC5B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;oBACtC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACvB,SAAS;gBACX,CAAC;gBAED,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;YAC9D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,wBAAwB;gBACxB,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,qCAAqC;QACrC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;QAE1D,mCAAmC;QACnC,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAC3B,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAC5F,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,kBAAkB,CAAC,GAAW;QAC5B,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,YAAY;QACV,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC1C,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED;;;OAGG;IACH,cAAc;QACZ,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,eAAe,GAAkB,IAAI,CAAC;QAC1C,IAAI,eAAe,GAAkB,IAAI,CAAC;QAE1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5C,SAAS;YACX,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACzC,IAAI,CAAC,MAAM;oBAAE,SAAS;gBAEtB,MAAM,UAAU,GAAe,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBAElD,oBAAoB;gBACpB,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;oBACtC,SAAS;gBACX,CAAC;gBAED,YAAY,EAAE,CAAC;gBACf,cAAc,IAAI,MAAM,CAAC,MAAM,CAAC;gBAEhC,IAAI,eAAe,KAAK,IAAI,IAAI,UAAU,CAAC,SAAS,GAAG,eAAe,EAAE,CAAC;oBACvE,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC;gBACzC,CAAC;gBACD,IAAI,eAAe,KAAK,IAAI,IAAI,UAAU,CAAC,SAAS,GAAG,eAAe,EAAE,CAAC;oBACvE,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC;gBACzC,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,sBAAsB;gBACtB,SAAS;YACX,CAAC;QACH,CAAC;QAED,OAAO;YACL,YAAY;YACZ,cAAc;YACd,WAAW,EAAE,eAAe,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI;YAC/D,WAAW,EAAE,eAAe,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI;SAChE,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,kBAAkB;QAChB,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5C,SAAS;YACX,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACzC,IAAI,CAAC,MAAM;oBAAE,SAAS;gBAEtB,MAAM,UAAU,GAAe,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBAElD,IAAI,GAAG,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;oBAC/B,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,wBAAwB;gBACxB,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1D,OAAO,YAAY,CAAC,MAAM,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACH,UAAU;QACR,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEjD,MAAM,UAAU,GAAe;YAC7B,OAAO,EAAE,KAAK;YACd,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACpC,aAAa;SACd,CAAC;QAEF,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACH,UAAU,CAAC,QAAgB;QACzB,IAAI,CAAC;YACH,MAAM,UAAU,GAAe,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAEpD,iCAAiC;YACjC,IAAI,CAAC,UAAU,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC1E,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;YACnD,CAAC;YAED,IAAI,aAAa,GAAG,CAAC,CAAC;YAEtB,KAAK,MAAM,YAAY,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC;gBACpD,IAAI,CAAC;oBACH,8CAA8C;oBAC9C,MAAM,kBAAkB,GAAG,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;oBACtE,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;oBAC1C,aAAa,EAAE,CAAC;gBAClB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;oBACvD,8BAA8B;gBAChC,CAAC;YACH,CAAC;YAED,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,mBAAU,CAAC,0BAA0B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC3G,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,WAAW,CAAC,YAA0B;QAC5C,MAAM,MAAM,GAAG,YAAY,CAAC,YAAY,CAAC;QACzC,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QACxH,OAAO,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,EAAE,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACK,UAAU,CAAC,GAAW;QAC5B,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC/B,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YACnC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,4BAA4B;QAClD,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACrC,CAAC;IAED;;;OAGG;IACH,YAAY;QACV,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC1C,KAAK,EAAE,CAAC;YACV,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;OAIG;IACK,uBAAuB,CAAC,IAAS;QACvC,OAAO;YACL,GAAG,IAAI;YACP,YAAY,EAAE;gBACZ,GAAG,IAAI,CAAC,YAAY;gBACpB,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;aACjD;YACD,OAAO,EAAE;gBACP,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,QAAa,EAAE,EAAE,CAAC,CAAC;oBAC9D,GAAG,QAAQ;oBACX,QAAQ,EAAE;wBACR,GAAG,QAAQ,CAAC,QAAQ;wBACpB,WAAW,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC;qBACrD;iBACF,CAAC,CAAC;gBACH,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,QAAa,EAAE,EAAE,CAAC,CAAC;oBACpE,GAAG,QAAQ;oBACX,QAAQ,EAAE;wBACR,GAAG,QAAQ,CAAC,QAAQ;wBACpB,WAAW,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC;qBACrD;iBACF,CAAC,CAAC;aACJ;SACF,CAAC;IACJ,CAAC;CACF;AArUD,kCAqUC",
      names: [],
      sources: ["/Users/<USER>/WebstormProjects/goo/src/services/dataManager.ts"],
      sourcesContent: ["import { SearchResult } from '../models/Business';\nimport { CacheError } from '../models/Errors';\nimport { CACHE_CONFIG } from '../constants';\n\n/**\n * Cached data structure\n */\ninterface CachedData {\n  data: SearchResult;\n  timestamp: number;\n  expiresAt: number;\n}\n\n/**\n * Storage information\n */\nexport interface StorageInfo {\n  totalEntries: number;\n  totalSizeBytes: number;\n  oldestEntry: Date | null;\n  newestEntry: Date | null;\n}\n\n/**\n * Export data structure\n */\ninterface ExportData {\n  version: string;\n  exportDate: string;\n  searchResults: SearchResult[];\n}\n\n/**\n * Service for managing data persistence using localStorage\n */\nexport class DataManager {\n  private readonly keyPrefix = CACHE_CONFIG.KEY_PREFIX;\n  private readonly expirationMs = CACHE_CONFIG.EXPIRATION_HOURS * 60 * 60 * 1000;\n\n  /**\n   * Saves a search result to localStorage\n   * @param searchResult - The search result to save\n   * @returns The storage key for the saved data\n   */\n  saveSearchResult(searchResult: SearchResult): string {\n    try {\n      const key = this.generateKey(searchResult);\n      const now = Date.now();\n      \n      const cachedData: CachedData = {\n        data: searchResult,\n        timestamp: now,\n        expiresAt: now + this.expirationMs,\n      };\n\n      localStorage.setItem(key, JSON.stringify(cachedData));\n      return key;\n    } catch (error) {\n      throw new CacheError(`Failed to save search result: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n\n  /**\n   * Retrieves a search result from localStorage\n   * @param key - The storage key\n   * @returns The search result or null if not found/expired\n   */\n  getSearchResult(key: string): SearchResult | null {\n    try {\n      const stored = localStorage.getItem(key);\n      if (!stored) {\n        return null;\n      }\n\n      const cachedData: CachedData = JSON.parse(stored);\n      \n      // Check if data has expired\n      if (Date.now() > cachedData.expiresAt) {\n        localStorage.removeItem(key);\n        return null;\n      }\n\n      return this.deserializeSearchResult(cachedData.data);\n    } catch (error) {\n      // Handle corrupted data\n      localStorage.removeItem(key);\n      return null;\n    }\n  }\n\n  /**\n   * Gets all stored search results (excluding expired ones)\n   * @returns Array of all valid search results\n   */\n  getAllSearchResults(): SearchResult[] {\n    const results: SearchResult[] = [];\n    const keysToRemove: string[] = [];\n\n    for (let i = 0; i < localStorage.length; i++) {\n      const key = localStorage.key(i);\n      if (!key || !key.startsWith(this.keyPrefix)) {\n        continue;\n      }\n\n      try {\n        const stored = localStorage.getItem(key);\n        if (!stored) continue;\n\n        const cachedData: CachedData = JSON.parse(stored);\n        \n        // Check if data has expired\n        if (Date.now() > cachedData.expiresAt) {\n          keysToRemove.push(key);\n          continue;\n        }\n\n        results.push(this.deserializeSearchResult(cachedData.data));\n      } catch (error) {\n        // Handle corrupted data\n        keysToRemove.push(key);\n      }\n    }\n\n    // Clean up expired/corrupted entries\n    keysToRemove.forEach(key => localStorage.removeItem(key));\n\n    // Sort by timestamp (newest first)\n    return results.sort((a, b) => \n      new Date(b.searchParams.timestamp).getTime() - new Date(a.searchParams.timestamp).getTime()\n    );\n  }\n\n  /**\n   * Deletes a specific search result\n   * @param key - The storage key to delete\n   */\n  deleteSearchResult(key: string): void {\n    localStorage.removeItem(key);\n  }\n\n  /**\n   * Clears all business search data from localStorage\n   */\n  clearAllData(): void {\n    const keysToRemove: string[] = [];\n\n    for (let i = 0; i < localStorage.length; i++) {\n      const key = localStorage.key(i);\n      if (key && key.startsWith(this.keyPrefix)) {\n        keysToRemove.push(key);\n      }\n    }\n\n    keysToRemove.forEach(key => localStorage.removeItem(key));\n  }\n\n  /**\n   * Gets information about current storage usage\n   * @returns Storage information\n   */\n  getStorageInfo(): StorageInfo {\n    let totalEntries = 0;\n    let totalSizeBytes = 0;\n    let oldestTimestamp: number | null = null;\n    let newestTimestamp: number | null = null;\n\n    for (let i = 0; i < localStorage.length; i++) {\n      const key = localStorage.key(i);\n      if (!key || !key.startsWith(this.keyPrefix)) {\n        continue;\n      }\n\n      try {\n        const stored = localStorage.getItem(key);\n        if (!stored) continue;\n\n        const cachedData: CachedData = JSON.parse(stored);\n        \n        // Skip expired data\n        if (Date.now() > cachedData.expiresAt) {\n          continue;\n        }\n\n        totalEntries++;\n        totalSizeBytes += stored.length;\n\n        if (oldestTimestamp === null || cachedData.timestamp < oldestTimestamp) {\n          oldestTimestamp = cachedData.timestamp;\n        }\n        if (newestTimestamp === null || cachedData.timestamp > newestTimestamp) {\n          newestTimestamp = cachedData.timestamp;\n        }\n      } catch (error) {\n        // Skip corrupted data\n        continue;\n      }\n    }\n\n    return {\n      totalEntries,\n      totalSizeBytes,\n      oldestEntry: oldestTimestamp ? new Date(oldestTimestamp) : null,\n      newestEntry: newestTimestamp ? new Date(newestTimestamp) : null,\n    };\n  }\n\n  /**\n   * Removes expired data from localStorage\n   * @returns Number of entries removed\n   */\n  cleanupExpiredData(): number {\n    const keysToRemove: string[] = [];\n    const now = Date.now();\n\n    for (let i = 0; i < localStorage.length; i++) {\n      const key = localStorage.key(i);\n      if (!key || !key.startsWith(this.keyPrefix)) {\n        continue;\n      }\n\n      try {\n        const stored = localStorage.getItem(key);\n        if (!stored) continue;\n\n        const cachedData: CachedData = JSON.parse(stored);\n        \n        if (now > cachedData.expiresAt) {\n          keysToRemove.push(key);\n        }\n      } catch (error) {\n        // Remove corrupted data\n        keysToRemove.push(key);\n      }\n    }\n\n    keysToRemove.forEach(key => localStorage.removeItem(key));\n    return keysToRemove.length;\n  }\n\n  /**\n   * Exports all search results as JSON\n   * @returns JSON string containing all search results\n   */\n  exportData(): string {\n    const searchResults = this.getAllSearchResults();\n    \n    const exportData: ExportData = {\n      version: '1.0',\n      exportDate: new Date().toISOString(),\n      searchResults,\n    };\n\n    return JSON.stringify(exportData, null, 2);\n  }\n\n  /**\n   * Imports search results from JSON\n   * @param jsonData - JSON string containing search results\n   * @returns Number of imported results\n   */\n  importData(jsonData: string): number {\n    try {\n      const importData: ExportData = JSON.parse(jsonData);\n      \n      // Validate import data structure\n      if (!importData.searchResults || !Array.isArray(importData.searchResults)) {\n        throw new Error('Invalid import data structure');\n      }\n\n      let importedCount = 0;\n      \n      for (const searchResult of importData.searchResults) {\n        try {\n          // Deserialize the search result before saving\n          const deserializedResult = this.deserializeSearchResult(searchResult);\n          this.saveSearchResult(deserializedResult);\n          importedCount++;\n        } catch (error) {\n          console.warn('Failed to import search result:', error);\n          // Continue with other results\n        }\n      }\n\n      return importedCount;\n    } catch (error) {\n      throw new CacheError(`Failed to import data: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n\n  /**\n   * Generates a unique key for a search result\n   * @param searchResult - The search result\n   * @returns Unique storage key\n   */\n  private generateKey(searchResult: SearchResult): string {\n    const params = searchResult.searchParams;\n    const hash = this.simpleHash(`${params.zipCode}_${params.radius}_${params.businessType}_${params.timestamp.getTime()}`);\n    return `${this.keyPrefix}${hash}`;\n  }\n\n  /**\n   * Simple hash function for generating keys\n   * @param str - String to hash\n   * @returns Hash string\n   */\n  private simpleHash(str: string): string {\n    let hash = 0;\n    for (let i = 0; i < str.length; i++) {\n      const char = str.charCodeAt(i);\n      hash = ((hash << 5) - hash) + char;\n      hash = hash & hash; // Convert to 32-bit integer\n    }\n    return Math.abs(hash).toString(36);\n  }\n\n  /**\n   * Gets the current cache size (number of entries)\n   * @returns Number of cached entries\n   */\n  getCacheSize(): number {\n    let count = 0;\n    for (let i = 0; i < localStorage.length; i++) {\n      const key = localStorage.key(i);\n      if (key && key.startsWith(this.keyPrefix)) {\n        count++;\n      }\n    }\n    return count;\n  }\n\n  /**\n   * Deserializes a search result, converting date strings back to Date objects\n   * @param data - Raw search result data\n   * @returns Properly typed search result\n   */\n  private deserializeSearchResult(data: any): SearchResult {\n    return {\n      ...data,\n      searchParams: {\n        ...data.searchParams,\n        timestamp: new Date(data.searchParams.timestamp),\n      },\n      results: {\n        withWebsites: data.results.withWebsites.map((business: any) => ({\n          ...business,\n          metadata: {\n            ...business.metadata,\n            lastUpdated: new Date(business.metadata.lastUpdated),\n          },\n        })),\n        withoutWebsites: data.results.withoutWebsites.map((business: any) => ({\n          ...business,\n          metadata: {\n            ...business.metadata,\n            lastUpdated: new Date(business.metadata.lastUpdated),\n          },\n        })),\n      },\n    };\n  }\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "9c78982dc75844bd0e229119da9def4534e4dbe1"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_wkm373csk = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_wkm373csk();
cov_wkm373csk().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_wkm373csk().s[1]++;
exports.DataManager = void 0;
const Errors_1 =
/* istanbul ignore next */
(cov_wkm373csk().s[2]++, require("../models/Errors"));
const constants_1 =
/* istanbul ignore next */
(cov_wkm373csk().s[3]++, require("../constants"));
/**
 * Service for managing data persistence using localStorage
 */
class DataManager {
  constructor() {
    /* istanbul ignore next */
    cov_wkm373csk().f[0]++;
    cov_wkm373csk().s[4]++;
    this.keyPrefix = constants_1.CACHE_CONFIG.KEY_PREFIX;
    /* istanbul ignore next */
    cov_wkm373csk().s[5]++;
    this.expirationMs = constants_1.CACHE_CONFIG.EXPIRATION_HOURS * 60 * 60 * 1000;
  }
  /**
   * Saves a search result to localStorage
   * @param searchResult - The search result to save
   * @returns The storage key for the saved data
   */
  saveSearchResult(searchResult) {
    /* istanbul ignore next */
    cov_wkm373csk().f[1]++;
    cov_wkm373csk().s[6]++;
    try {
      const key =
      /* istanbul ignore next */
      (cov_wkm373csk().s[7]++, this.generateKey(searchResult));
      const now =
      /* istanbul ignore next */
      (cov_wkm373csk().s[8]++, Date.now());
      const cachedData =
      /* istanbul ignore next */
      (cov_wkm373csk().s[9]++, {
        data: searchResult,
        timestamp: now,
        expiresAt: now + this.expirationMs
      });
      /* istanbul ignore next */
      cov_wkm373csk().s[10]++;
      localStorage.setItem(key, JSON.stringify(cachedData));
      /* istanbul ignore next */
      cov_wkm373csk().s[11]++;
      return key;
    } catch (error) {
      /* istanbul ignore next */
      cov_wkm373csk().s[12]++;
      throw new Errors_1.CacheError(`Failed to save search result: ${error instanceof Error ?
      /* istanbul ignore next */
      (cov_wkm373csk().b[0][0]++, error.message) :
      /* istanbul ignore next */
      (cov_wkm373csk().b[0][1]++, String(error))}`);
    }
  }
  /**
   * Retrieves a search result from localStorage
   * @param key - The storage key
   * @returns The search result or null if not found/expired
   */
  getSearchResult(key) {
    /* istanbul ignore next */
    cov_wkm373csk().f[2]++;
    cov_wkm373csk().s[13]++;
    try {
      const stored =
      /* istanbul ignore next */
      (cov_wkm373csk().s[14]++, localStorage.getItem(key));
      /* istanbul ignore next */
      cov_wkm373csk().s[15]++;
      if (!stored) {
        /* istanbul ignore next */
        cov_wkm373csk().b[1][0]++;
        cov_wkm373csk().s[16]++;
        return null;
      } else
      /* istanbul ignore next */
      {
        cov_wkm373csk().b[1][1]++;
      }
      const cachedData =
      /* istanbul ignore next */
      (cov_wkm373csk().s[17]++, JSON.parse(stored));
      // Check if data has expired
      /* istanbul ignore next */
      cov_wkm373csk().s[18]++;
      if (Date.now() > cachedData.expiresAt) {
        /* istanbul ignore next */
        cov_wkm373csk().b[2][0]++;
        cov_wkm373csk().s[19]++;
        localStorage.removeItem(key);
        /* istanbul ignore next */
        cov_wkm373csk().s[20]++;
        return null;
      } else
      /* istanbul ignore next */
      {
        cov_wkm373csk().b[2][1]++;
      }
      cov_wkm373csk().s[21]++;
      return this.deserializeSearchResult(cachedData.data);
    } catch (error) {
      /* istanbul ignore next */
      cov_wkm373csk().s[22]++;
      // Handle corrupted data
      localStorage.removeItem(key);
      /* istanbul ignore next */
      cov_wkm373csk().s[23]++;
      return null;
    }
  }
  /**
   * Gets all stored search results (excluding expired ones)
   * @returns Array of all valid search results
   */
  getAllSearchResults() {
    /* istanbul ignore next */
    cov_wkm373csk().f[3]++;
    const results =
    /* istanbul ignore next */
    (cov_wkm373csk().s[24]++, []);
    const keysToRemove =
    /* istanbul ignore next */
    (cov_wkm373csk().s[25]++, []);
    /* istanbul ignore next */
    cov_wkm373csk().s[26]++;
    for (let i =
    /* istanbul ignore next */
    (cov_wkm373csk().s[27]++, 0); i < localStorage.length; i++) {
      const key =
      /* istanbul ignore next */
      (cov_wkm373csk().s[28]++, localStorage.key(i));
      /* istanbul ignore next */
      cov_wkm373csk().s[29]++;
      if (
      /* istanbul ignore next */
      (cov_wkm373csk().b[4][0]++, !key) ||
      /* istanbul ignore next */
      (cov_wkm373csk().b[4][1]++, !key.startsWith(this.keyPrefix))) {
        /* istanbul ignore next */
        cov_wkm373csk().b[3][0]++;
        cov_wkm373csk().s[30]++;
        continue;
      } else
      /* istanbul ignore next */
      {
        cov_wkm373csk().b[3][1]++;
      }
      cov_wkm373csk().s[31]++;
      try {
        const stored =
        /* istanbul ignore next */
        (cov_wkm373csk().s[32]++, localStorage.getItem(key));
        /* istanbul ignore next */
        cov_wkm373csk().s[33]++;
        if (!stored) {
          /* istanbul ignore next */
          cov_wkm373csk().b[5][0]++;
          cov_wkm373csk().s[34]++;
          continue;
        } else
        /* istanbul ignore next */
        {
          cov_wkm373csk().b[5][1]++;
        }
        const cachedData =
        /* istanbul ignore next */
        (cov_wkm373csk().s[35]++, JSON.parse(stored));
        // Check if data has expired
        /* istanbul ignore next */
        cov_wkm373csk().s[36]++;
        if (Date.now() > cachedData.expiresAt) {
          /* istanbul ignore next */
          cov_wkm373csk().b[6][0]++;
          cov_wkm373csk().s[37]++;
          keysToRemove.push(key);
          /* istanbul ignore next */
          cov_wkm373csk().s[38]++;
          continue;
        } else
        /* istanbul ignore next */
        {
          cov_wkm373csk().b[6][1]++;
        }
        cov_wkm373csk().s[39]++;
        results.push(this.deserializeSearchResult(cachedData.data));
      } catch (error) {
        /* istanbul ignore next */
        cov_wkm373csk().s[40]++;
        // Handle corrupted data
        keysToRemove.push(key);
      }
    }
    // Clean up expired/corrupted entries
    /* istanbul ignore next */
    cov_wkm373csk().s[41]++;
    keysToRemove.forEach(key => {
      /* istanbul ignore next */
      cov_wkm373csk().f[4]++;
      cov_wkm373csk().s[42]++;
      return localStorage.removeItem(key);
    });
    // Sort by timestamp (newest first)
    /* istanbul ignore next */
    cov_wkm373csk().s[43]++;
    return results.sort((a, b) => {
      /* istanbul ignore next */
      cov_wkm373csk().f[5]++;
      cov_wkm373csk().s[44]++;
      return new Date(b.searchParams.timestamp).getTime() - new Date(a.searchParams.timestamp).getTime();
    });
  }
  /**
   * Deletes a specific search result
   * @param key - The storage key to delete
   */
  deleteSearchResult(key) {
    /* istanbul ignore next */
    cov_wkm373csk().f[6]++;
    cov_wkm373csk().s[45]++;
    localStorage.removeItem(key);
  }
  /**
   * Clears all business search data from localStorage
   */
  clearAllData() {
    /* istanbul ignore next */
    cov_wkm373csk().f[7]++;
    const keysToRemove =
    /* istanbul ignore next */
    (cov_wkm373csk().s[46]++, []);
    /* istanbul ignore next */
    cov_wkm373csk().s[47]++;
    for (let i =
    /* istanbul ignore next */
    (cov_wkm373csk().s[48]++, 0); i < localStorage.length; i++) {
      const key =
      /* istanbul ignore next */
      (cov_wkm373csk().s[49]++, localStorage.key(i));
      /* istanbul ignore next */
      cov_wkm373csk().s[50]++;
      if (
      /* istanbul ignore next */
      (cov_wkm373csk().b[8][0]++, key) &&
      /* istanbul ignore next */
      (cov_wkm373csk().b[8][1]++, key.startsWith(this.keyPrefix))) {
        /* istanbul ignore next */
        cov_wkm373csk().b[7][0]++;
        cov_wkm373csk().s[51]++;
        keysToRemove.push(key);
      } else
      /* istanbul ignore next */
      {
        cov_wkm373csk().b[7][1]++;
      }
    }
    /* istanbul ignore next */
    cov_wkm373csk().s[52]++;
    keysToRemove.forEach(key => {
      /* istanbul ignore next */
      cov_wkm373csk().f[8]++;
      cov_wkm373csk().s[53]++;
      return localStorage.removeItem(key);
    });
  }
  /**
   * Gets information about current storage usage
   * @returns Storage information
   */
  getStorageInfo() {
    /* istanbul ignore next */
    cov_wkm373csk().f[9]++;
    let totalEntries =
    /* istanbul ignore next */
    (cov_wkm373csk().s[54]++, 0);
    let totalSizeBytes =
    /* istanbul ignore next */
    (cov_wkm373csk().s[55]++, 0);
    let oldestTimestamp =
    /* istanbul ignore next */
    (cov_wkm373csk().s[56]++, null);
    let newestTimestamp =
    /* istanbul ignore next */
    (cov_wkm373csk().s[57]++, null);
    /* istanbul ignore next */
    cov_wkm373csk().s[58]++;
    for (let i =
    /* istanbul ignore next */
    (cov_wkm373csk().s[59]++, 0); i < localStorage.length; i++) {
      const key =
      /* istanbul ignore next */
      (cov_wkm373csk().s[60]++, localStorage.key(i));
      /* istanbul ignore next */
      cov_wkm373csk().s[61]++;
      if (
      /* istanbul ignore next */
      (cov_wkm373csk().b[10][0]++, !key) ||
      /* istanbul ignore next */
      (cov_wkm373csk().b[10][1]++, !key.startsWith(this.keyPrefix))) {
        /* istanbul ignore next */
        cov_wkm373csk().b[9][0]++;
        cov_wkm373csk().s[62]++;
        continue;
      } else
      /* istanbul ignore next */
      {
        cov_wkm373csk().b[9][1]++;
      }
      cov_wkm373csk().s[63]++;
      try {
        const stored =
        /* istanbul ignore next */
        (cov_wkm373csk().s[64]++, localStorage.getItem(key));
        /* istanbul ignore next */
        cov_wkm373csk().s[65]++;
        if (!stored) {
          /* istanbul ignore next */
          cov_wkm373csk().b[11][0]++;
          cov_wkm373csk().s[66]++;
          continue;
        } else
        /* istanbul ignore next */
        {
          cov_wkm373csk().b[11][1]++;
        }
        const cachedData =
        /* istanbul ignore next */
        (cov_wkm373csk().s[67]++, JSON.parse(stored));
        // Skip expired data
        /* istanbul ignore next */
        cov_wkm373csk().s[68]++;
        if (Date.now() > cachedData.expiresAt) {
          /* istanbul ignore next */
          cov_wkm373csk().b[12][0]++;
          cov_wkm373csk().s[69]++;
          continue;
        } else
        /* istanbul ignore next */
        {
          cov_wkm373csk().b[12][1]++;
        }
        cov_wkm373csk().s[70]++;
        totalEntries++;
        /* istanbul ignore next */
        cov_wkm373csk().s[71]++;
        totalSizeBytes += stored.length;
        /* istanbul ignore next */
        cov_wkm373csk().s[72]++;
        if (
        /* istanbul ignore next */
        (cov_wkm373csk().b[14][0]++, oldestTimestamp === null) ||
        /* istanbul ignore next */
        (cov_wkm373csk().b[14][1]++, cachedData.timestamp < oldestTimestamp)) {
          /* istanbul ignore next */
          cov_wkm373csk().b[13][0]++;
          cov_wkm373csk().s[73]++;
          oldestTimestamp = cachedData.timestamp;
        } else
        /* istanbul ignore next */
        {
          cov_wkm373csk().b[13][1]++;
        }
        cov_wkm373csk().s[74]++;
        if (
        /* istanbul ignore next */
        (cov_wkm373csk().b[16][0]++, newestTimestamp === null) ||
        /* istanbul ignore next */
        (cov_wkm373csk().b[16][1]++, cachedData.timestamp > newestTimestamp)) {
          /* istanbul ignore next */
          cov_wkm373csk().b[15][0]++;
          cov_wkm373csk().s[75]++;
          newestTimestamp = cachedData.timestamp;
        } else
        /* istanbul ignore next */
        {
          cov_wkm373csk().b[15][1]++;
        }
      } catch (error) {
        /* istanbul ignore next */
        cov_wkm373csk().s[76]++;
        // Skip corrupted data
        continue;
      }
    }
    /* istanbul ignore next */
    cov_wkm373csk().s[77]++;
    return {
      totalEntries,
      totalSizeBytes,
      oldestEntry: oldestTimestamp ?
      /* istanbul ignore next */
      (cov_wkm373csk().b[17][0]++, new Date(oldestTimestamp)) :
      /* istanbul ignore next */
      (cov_wkm373csk().b[17][1]++, null),
      newestEntry: newestTimestamp ?
      /* istanbul ignore next */
      (cov_wkm373csk().b[18][0]++, new Date(newestTimestamp)) :
      /* istanbul ignore next */
      (cov_wkm373csk().b[18][1]++, null)
    };
  }
  /**
   * Removes expired data from localStorage
   * @returns Number of entries removed
   */
  cleanupExpiredData() {
    /* istanbul ignore next */
    cov_wkm373csk().f[10]++;
    const keysToRemove =
    /* istanbul ignore next */
    (cov_wkm373csk().s[78]++, []);
    const now =
    /* istanbul ignore next */
    (cov_wkm373csk().s[79]++, Date.now());
    /* istanbul ignore next */
    cov_wkm373csk().s[80]++;
    for (let i =
    /* istanbul ignore next */
    (cov_wkm373csk().s[81]++, 0); i < localStorage.length; i++) {
      const key =
      /* istanbul ignore next */
      (cov_wkm373csk().s[82]++, localStorage.key(i));
      /* istanbul ignore next */
      cov_wkm373csk().s[83]++;
      if (
      /* istanbul ignore next */
      (cov_wkm373csk().b[20][0]++, !key) ||
      /* istanbul ignore next */
      (cov_wkm373csk().b[20][1]++, !key.startsWith(this.keyPrefix))) {
        /* istanbul ignore next */
        cov_wkm373csk().b[19][0]++;
        cov_wkm373csk().s[84]++;
        continue;
      } else
      /* istanbul ignore next */
      {
        cov_wkm373csk().b[19][1]++;
      }
      cov_wkm373csk().s[85]++;
      try {
        const stored =
        /* istanbul ignore next */
        (cov_wkm373csk().s[86]++, localStorage.getItem(key));
        /* istanbul ignore next */
        cov_wkm373csk().s[87]++;
        if (!stored) {
          /* istanbul ignore next */
          cov_wkm373csk().b[21][0]++;
          cov_wkm373csk().s[88]++;
          continue;
        } else
        /* istanbul ignore next */
        {
          cov_wkm373csk().b[21][1]++;
        }
        const cachedData =
        /* istanbul ignore next */
        (cov_wkm373csk().s[89]++, JSON.parse(stored));
        /* istanbul ignore next */
        cov_wkm373csk().s[90]++;
        if (now > cachedData.expiresAt) {
          /* istanbul ignore next */
          cov_wkm373csk().b[22][0]++;
          cov_wkm373csk().s[91]++;
          keysToRemove.push(key);
        } else
        /* istanbul ignore next */
        {
          cov_wkm373csk().b[22][1]++;
        }
      } catch (error) {
        /* istanbul ignore next */
        cov_wkm373csk().s[92]++;
        // Remove corrupted data
        keysToRemove.push(key);
      }
    }
    /* istanbul ignore next */
    cov_wkm373csk().s[93]++;
    keysToRemove.forEach(key => {
      /* istanbul ignore next */
      cov_wkm373csk().f[11]++;
      cov_wkm373csk().s[94]++;
      return localStorage.removeItem(key);
    });
    /* istanbul ignore next */
    cov_wkm373csk().s[95]++;
    return keysToRemove.length;
  }
  /**
   * Exports all search results as JSON
   * @returns JSON string containing all search results
   */
  exportData() {
    /* istanbul ignore next */
    cov_wkm373csk().f[12]++;
    const searchResults =
    /* istanbul ignore next */
    (cov_wkm373csk().s[96]++, this.getAllSearchResults());
    const exportData =
    /* istanbul ignore next */
    (cov_wkm373csk().s[97]++, {
      version: '1.0',
      exportDate: new Date().toISOString(),
      searchResults
    });
    /* istanbul ignore next */
    cov_wkm373csk().s[98]++;
    return JSON.stringify(exportData, null, 2);
  }
  /**
   * Imports search results from JSON
   * @param jsonData - JSON string containing search results
   * @returns Number of imported results
   */
  importData(jsonData) {
    /* istanbul ignore next */
    cov_wkm373csk().f[13]++;
    cov_wkm373csk().s[99]++;
    try {
      const importData =
      /* istanbul ignore next */
      (cov_wkm373csk().s[100]++, JSON.parse(jsonData));
      // Validate import data structure
      /* istanbul ignore next */
      cov_wkm373csk().s[101]++;
      if (
      /* istanbul ignore next */
      (cov_wkm373csk().b[24][0]++, !importData.searchResults) ||
      /* istanbul ignore next */
      (cov_wkm373csk().b[24][1]++, !Array.isArray(importData.searchResults))) {
        /* istanbul ignore next */
        cov_wkm373csk().b[23][0]++;
        cov_wkm373csk().s[102]++;
        throw new Error('Invalid import data structure');
      } else
      /* istanbul ignore next */
      {
        cov_wkm373csk().b[23][1]++;
      }
      let importedCount =
      /* istanbul ignore next */
      (cov_wkm373csk().s[103]++, 0);
      /* istanbul ignore next */
      cov_wkm373csk().s[104]++;
      for (const searchResult of importData.searchResults) {
        /* istanbul ignore next */
        cov_wkm373csk().s[105]++;
        try {
          // Deserialize the search result before saving
          const deserializedResult =
          /* istanbul ignore next */
          (cov_wkm373csk().s[106]++, this.deserializeSearchResult(searchResult));
          /* istanbul ignore next */
          cov_wkm373csk().s[107]++;
          this.saveSearchResult(deserializedResult);
          /* istanbul ignore next */
          cov_wkm373csk().s[108]++;
          importedCount++;
        } catch (error) {
          /* istanbul ignore next */
          cov_wkm373csk().s[109]++;
          console.warn('Failed to import search result:', error);
          // Continue with other results
        }
      }
      /* istanbul ignore next */
      cov_wkm373csk().s[110]++;
      return importedCount;
    } catch (error) {
      /* istanbul ignore next */
      cov_wkm373csk().s[111]++;
      throw new Errors_1.CacheError(`Failed to import data: ${error instanceof Error ?
      /* istanbul ignore next */
      (cov_wkm373csk().b[25][0]++, error.message) :
      /* istanbul ignore next */
      (cov_wkm373csk().b[25][1]++, String(error))}`);
    }
  }
  /**
   * Generates a unique key for a search result
   * @param searchResult - The search result
   * @returns Unique storage key
   */
  generateKey(searchResult) {
    /* istanbul ignore next */
    cov_wkm373csk().f[14]++;
    const params =
    /* istanbul ignore next */
    (cov_wkm373csk().s[112]++, searchResult.searchParams);
    const hash =
    /* istanbul ignore next */
    (cov_wkm373csk().s[113]++, this.simpleHash(`${params.zipCode}_${params.radius}_${params.businessType}_${params.timestamp.getTime()}`));
    /* istanbul ignore next */
    cov_wkm373csk().s[114]++;
    return `${this.keyPrefix}${hash}`;
  }
  /**
   * Simple hash function for generating keys
   * @param str - String to hash
   * @returns Hash string
   */
  simpleHash(str) {
    /* istanbul ignore next */
    cov_wkm373csk().f[15]++;
    let hash =
    /* istanbul ignore next */
    (cov_wkm373csk().s[115]++, 0);
    /* istanbul ignore next */
    cov_wkm373csk().s[116]++;
    for (let i =
    /* istanbul ignore next */
    (cov_wkm373csk().s[117]++, 0); i < str.length; i++) {
      const char =
      /* istanbul ignore next */
      (cov_wkm373csk().s[118]++, str.charCodeAt(i));
      /* istanbul ignore next */
      cov_wkm373csk().s[119]++;
      hash = (hash << 5) - hash + char;
      /* istanbul ignore next */
      cov_wkm373csk().s[120]++;
      hash = hash & hash; // Convert to 32-bit integer
    }
    /* istanbul ignore next */
    cov_wkm373csk().s[121]++;
    return Math.abs(hash).toString(36);
  }
  /**
   * Gets the current cache size (number of entries)
   * @returns Number of cached entries
   */
  getCacheSize() {
    /* istanbul ignore next */
    cov_wkm373csk().f[16]++;
    let count =
    /* istanbul ignore next */
    (cov_wkm373csk().s[122]++, 0);
    /* istanbul ignore next */
    cov_wkm373csk().s[123]++;
    for (let i =
    /* istanbul ignore next */
    (cov_wkm373csk().s[124]++, 0); i < localStorage.length; i++) {
      const key =
      /* istanbul ignore next */
      (cov_wkm373csk().s[125]++, localStorage.key(i));
      /* istanbul ignore next */
      cov_wkm373csk().s[126]++;
      if (
      /* istanbul ignore next */
      (cov_wkm373csk().b[27][0]++, key) &&
      /* istanbul ignore next */
      (cov_wkm373csk().b[27][1]++, key.startsWith(this.keyPrefix))) {
        /* istanbul ignore next */
        cov_wkm373csk().b[26][0]++;
        cov_wkm373csk().s[127]++;
        count++;
      } else
      /* istanbul ignore next */
      {
        cov_wkm373csk().b[26][1]++;
      }
    }
    /* istanbul ignore next */
    cov_wkm373csk().s[128]++;
    return count;
  }
  /**
   * Deserializes a search result, converting date strings back to Date objects
   * @param data - Raw search result data
   * @returns Properly typed search result
   */
  deserializeSearchResult(data) {
    /* istanbul ignore next */
    cov_wkm373csk().f[17]++;
    cov_wkm373csk().s[129]++;
    return {
      ...data,
      searchParams: {
        ...data.searchParams,
        timestamp: new Date(data.searchParams.timestamp)
      },
      results: {
        withWebsites: data.results.withWebsites.map(business => {
          /* istanbul ignore next */
          cov_wkm373csk().f[18]++;
          cov_wkm373csk().s[130]++;
          return {
            ...business,
            metadata: {
              ...business.metadata,
              lastUpdated: new Date(business.metadata.lastUpdated)
            }
          };
        }),
        withoutWebsites: data.results.withoutWebsites.map(business => {
          /* istanbul ignore next */
          cov_wkm373csk().f[19]++;
          cov_wkm373csk().s[131]++;
          return {
            ...business,
            metadata: {
              ...business.metadata,
              lastUpdated: new Date(business.metadata.lastUpdated)
            }
          };
        })
      }
    };
  }
}
/* istanbul ignore next */
cov_wkm373csk().s[132]++;
exports.DataManager = DataManager;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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