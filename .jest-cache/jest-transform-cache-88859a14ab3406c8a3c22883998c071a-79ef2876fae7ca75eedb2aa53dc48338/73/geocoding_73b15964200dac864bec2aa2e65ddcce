18324ae5a14bb7e95a068c85cf38b268
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeocodingService = void 0;
const Errors_1 = require("../models/Errors");
const validation_1 = require("../utils/validation");
const constants_1 = require("../constants");
/**
 * Service for converting between zip codes and coordinates using Google Geocoding API
 */
class GeocodingService {
    constructor(apiKey) {
        this.cache = new Map();
        this.apiKey = apiKey || process.env.GOOGLE_PLACES_API_KEY || '';
        this.baseUrl = constants_1.API_CONFIG.GEOCODING_BASE_URL;
        if (!this.apiKey) {
            throw new Errors_1.ConfigurationError('Google Places API key is required', 'GOOGLE_PLACES_API_KEY');
        }
    }
    /**
     * Converts a zip code to coordinates
     * @param zipCode - The zip code to convert
     * @returns Promise resolving to coordinates
     * @throws GeocodingError if conversion fails
     */
    async zipCodeToCoordinates(zipCode) {
        // Validate zip code format
        try {
            (0, validation_1.validateZipCode)(zipCode);
        }
        catch (error) {
            throw new Errors_1.GeocodingError(`Invalid zip code format: ${zipCode}`, zipCode);
        }
        // Check cache first
        const cacheKey = `zip_${zipCode}`;
        const cached = this.cache.get(cacheKey);
        if (cached && typeof cached === 'object') {
            return cached;
        }
        try {
            const url = `${this.baseUrl}/json?address=${encodeURIComponent(zipCode)}&key=${this.apiKey}`;
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                },
            });
            if (!response.ok) {
                throw new Errors_1.ApiError(`Geocoding API request failed: ${response.statusText}`, response.status, 'Google Geocoding API');
            }
            const data = await response.json();
            if (data.status !== 'OK') {
                if (data.status === 'ZERO_RESULTS') {
                    throw new Errors_1.GeocodingError(`No results found for zip code: ${zipCode}`, zipCode);
                }
                throw new Errors_1.GeocodingError(data.error_message || `Geocoding failed with status: ${data.status}`, zipCode);
            }
            if (!data.results || data.results.length === 0) {
                throw new Errors_1.GeocodingError(`No results found for zip code: ${zipCode}`, zipCode);
            }
            const result = data.results[0];
            if (!result.geometry || !result.geometry.location) {
                throw new Errors_1.GeocodingError(`Invalid response format for zip code: ${zipCode}`, zipCode);
            }
            const coordinates = {
                latitude: result.geometry.location.lat,
                longitude: result.geometry.location.lng,
            };
            // Cache the result
            this.cache.set(cacheKey, coordinates);
            return coordinates;
        }
        catch (error) {
            if (error instanceof Errors_1.GeocodingError || error instanceof Errors_1.ApiError) {
                throw error;
            }
            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    throw new Errors_1.NetworkError('Geocoding request timed out', error);
                }
                throw new Errors_1.NetworkError(`Network error during geocoding: ${error.message}`, error);
            }
            throw new Errors_1.GeocodingError(`Unknown error during geocoding: ${String(error)}`, zipCode);
        }
    }
    /**
     * Converts coordinates to zip code (reverse geocoding)
     * @param coordinates - The coordinates to convert
     * @returns Promise resolving to zip code
     * @throws GeocodingError if conversion fails
     */
    async coordinatesToZipCode(coordinates) {
        const cacheKey = `coords_${coordinates.latitude}_${coordinates.longitude}`;
        const cached = this.cache.get(cacheKey);
        if (cached && typeof cached === 'string') {
            return cached;
        }
        try {
            const url = `${this.baseUrl}/json?latlng=${coordinates.latitude},${coordinates.longitude}&key=${this.apiKey}`;
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                },
            });
            if (!response.ok) {
                throw new Errors_1.ApiError(`Reverse geocoding API request failed: ${response.statusText}`, response.status, 'Google Geocoding API');
            }
            const data = await response.json();
            if (data.status !== 'OK') {
                throw new Errors_1.GeocodingError(data.error_message || `Reverse geocoding failed with status: ${data.status}`);
            }
            if (!data.results || data.results.length === 0) {
                throw new Errors_1.GeocodingError('No results found for coordinates');
            }
            // Find postal code in address components
            for (const result of data.results) {
                if (result.address_components) {
                    for (const component of result.address_components) {
                        if (component.types.includes('postal_code')) {
                            const zipCode = component.long_name;
                            this.cache.set(cacheKey, zipCode);
                            return zipCode;
                        }
                    }
                }
            }
            throw new Errors_1.GeocodingError('No postal code found for coordinates');
        }
        catch (error) {
            if (error instanceof Errors_1.GeocodingError || error instanceof Errors_1.ApiError) {
                throw error;
            }
            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    throw new Errors_1.NetworkError('Reverse geocoding request timed out', error);
                }
                throw new Errors_1.NetworkError(`Network error during reverse geocoding: ${error.message}`, error);
            }
            throw new Errors_1.GeocodingError(`Unknown error during reverse geocoding: ${String(error)}`);
        }
    }
    /**
     * Clears the geocoding cache
     */
    clearCache() {
        this.cache.clear();
    }
    /**
     * Gets the current cache size
     * @returns Number of cached entries
     */
    getCacheSize() {
        return this.cache.size;
    }
}
exports.GeocodingService = GeocodingService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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