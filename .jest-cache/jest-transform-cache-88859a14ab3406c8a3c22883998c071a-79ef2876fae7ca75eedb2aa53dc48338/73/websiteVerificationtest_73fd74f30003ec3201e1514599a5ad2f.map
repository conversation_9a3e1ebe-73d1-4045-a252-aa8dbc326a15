{"file": "/Users/<USER>/WebstormProjects/goo/tests/services/websiteVerification.test.ts", "mappings": ";;AAAA,gFAAoF;AACpF,oDAAiF;AAGjF,sBAAsB;AACtB,MAAM,SAAS,GAAG,MAAM,CAAC,KAA0C,CAAC;AAEpE,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;IAC1C,IAAI,cAA0C,CAAC;IAE/C,UAAU,CAAC,GAAG,EAAE;QACd,cAAc,GAAG,IAAI,gDAA0B,EAAE,CAAC;QAClD,SAAS,CAAC,SAAS,EAAE,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,CAAC;aAC1C,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;YAEzE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,UAAU,EAAE,yBAAyB,EAAE,CAAC;aACpD,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;YAEzE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,WAAW;gBACvB,OAAO,EAAE,IAAI,OAAO,EAAE;aACX,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;YAEzE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,SAAS,CAAC,qBAAqB,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;YAE5D,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;YAEzE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,MAAM,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;iBACtD,OAAO,CAAC,OAAO,CAAC,iCAAwB,CAAC,CAAC;YAE7C,MAAM,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uBAAuB,EAAE,KAAK,IAAI,EAAE;YACrC,MAAM,UAAU,GAAG,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC1D,UAAU,CAAC,IAAI,GAAG,YAAY,CAAC;YAC/B,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;YAE5C,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;YAEzE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,EAAE;aACX,CAAC,CAAC;YAEf,aAAa;YACb,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;YAE1E,+BAA+B;YAC/B,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;YAE1E,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACjC,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,SAAS;iBACN,qBAAqB,CAAC;gBACrB,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,EAAE;aACX,CAAC;iBACb,qBAAqB,CAAC;gBACrB,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,WAAW;aACZ,CAAC,CAAC;YAEjB,MAAM,IAAI,GAAG,CAAC,sBAAsB,EAAE,sBAAsB,CAAC,CAAC;YAC9D,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YAElE,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC7C,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,SAAS;iBACN,qBAAqB,CAAC;gBACrB,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,EAAE;aACX,CAAC;iBACb,qBAAqB,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;YAErD,MAAM,IAAI,GAAG,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;YACrD,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YAElE,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAE7E,SAAS,CAAC,iBAAiB,CAAC;gBAC1B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,EAAE;aACX,CAAC,CAAC;YAEf,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,cAAc,CAAC,sBAAsB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,wBAAwB;YAC9E,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE3B,0EAA0E;YAC1E,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,SAAS;iBACN,qBAAqB,CAAC;gBACrB,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,EAAE;aACX,CAAC;iBACb,qBAAqB,CAAC;gBACrB,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,WAAW;aACZ,CAAC,CAAC;YAEjB,MAAM,cAAc,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;YACvD,MAAM,cAAc,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;YAEtD,MAAM,KAAK,GAAG,cAAc,CAAC,oBAAoB,EAAE,CAAC;YAEpD,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,EAAE;aACX,CAAC,CAAC;YAEf,MAAM,cAAc,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;YAC1D,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAE3C,cAAc,CAAC,UAAU,EAAE,CAAC;YAE5B,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,EAAE;aACX,CAAC,CAAC;YAEf,MAAM,cAAc,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;YAC1D,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,UAAU,GAAG,cAAc,CAAC,mBAAmB,CAAC,GAAG,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;YAC7E,MAAM,CAAC,UAAU,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,UAAU,GAAG,cAAc,CAAC,mBAAmB,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;YAClE,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,cAAc,GAAG,cAAc,CAAC,mBAAmB,CAAC,GAAG,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;YACjF,MAAM,cAAc,GAAG,cAAc,CAAC,mBAAmB,CAAC,GAAG,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;YAElF,MAAM,CAAC,cAAc,CAAC,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/tests/services/websiteVerification.test.ts"], "sourcesContent": ["import { WebsiteVerificationService } from '../../src/services/websiteVerification';\nimport { WebsiteVerificationError, NetworkError } from '../../src/models/Errors';\nimport { WebsiteStatus } from '../../src/models/Business';\n\n// Mock fetch globally\nconst mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;\n\ndescribe('WebsiteVerificationService', () => {\n  let websiteService: WebsiteVerificationService;\n\n  beforeEach(() => {\n    websiteService = new WebsiteVerificationService();\n    mockFetch.mockClear();\n  });\n\n  describe('verifyWebsite', () => {\n    it('should verify accessible website', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        headers: new Headers({ 'content-type': 'text/html' }),\n      } as Response);\n\n      const result = await websiteService.verifyWebsite('https://example.com');\n\n      expect(result.status).toBe('verified');\n      expect(result.accessible).toBe(true);\n      expect(result.statusCode).toBe(200);\n      expect(result.confidence).toBeGreaterThan(0.8);\n    });\n\n    it('should handle redirects as verified', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 301,\n        headers: new Headers({ 'location': 'https://example.com/new' }),\n      } as Response);\n\n      const result = await websiteService.verifyWebsite('https://example.com');\n\n      expect(result.status).toBe('verified');\n      expect(result.accessible).toBe(true);\n      expect(result.statusCode).toBe(301);\n    });\n\n    it('should handle 404 as unverified', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: false,\n        status: 404,\n        statusText: 'Not Found',\n        headers: new Headers(),\n      } as Response);\n\n      const result = await websiteService.verifyWebsite('https://example.com');\n\n      expect(result.status).toBe('unverified');\n      expect(result.accessible).toBe(false);\n      expect(result.statusCode).toBe(404);\n      expect(result.confidence).toBeLessThan(0.5);\n    });\n\n    it('should handle network errors', async () => {\n      mockFetch.mockRejectedValueOnce(new Error('Network error'));\n\n      const result = await websiteService.verifyWebsite('https://example.com');\n\n      expect(result.status).toBe('unverified');\n      expect(result.accessible).toBe(false);\n      expect(result.error).toContain('Network error');\n    });\n\n    it('should validate URL format', async () => {\n      await expect(websiteService.verifyWebsite('invalid-url'))\n        .rejects.toThrow(WebsiteVerificationError);\n\n      expect(mockFetch).not.toHaveBeenCalled();\n    });\n\n    it('should handle timeout', async () => {\n      const abortError = new Error('The operation was aborted');\n      abortError.name = 'AbortError';\n      mockFetch.mockRejectedValueOnce(abortError);\n\n      const result = await websiteService.verifyWebsite('https://example.com');\n\n      expect(result.status).toBe('unverified');\n      expect(result.accessible).toBe(false);\n      expect(result.error).toContain('timeout');\n    });\n\n    it('should cache verification results', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        headers: new Headers(),\n      } as Response);\n\n      // First call\n      const result1 = await websiteService.verifyWebsite('https://example.com');\n      \n      // Second call should use cache\n      const result2 = await websiteService.verifyWebsite('https://example.com');\n\n      expect(result1).toEqual(result2);\n      expect(mockFetch).toHaveBeenCalledTimes(1);\n    });\n  });\n\n  describe('verifyMultipleWebsites', () => {\n    it('should verify multiple websites concurrently', async () => {\n      mockFetch\n        .mockResolvedValueOnce({\n          ok: true,\n          status: 200,\n          headers: new Headers(),\n        } as Response)\n        .mockResolvedValueOnce({\n          ok: false,\n          status: 404,\n          statusText: 'Not Found',\n        } as Response);\n\n      const urls = ['https://example1.com', 'https://example2.com'];\n      const results = await websiteService.verifyMultipleWebsites(urls);\n\n      expect(results).toHaveLength(2);\n      expect(results[0].status).toBe('verified');\n      expect(results[1].status).toBe('unverified');\n      expect(mockFetch).toHaveBeenCalledTimes(2);\n    });\n\n    it('should handle mixed results', async () => {\n      mockFetch\n        .mockResolvedValueOnce({\n          ok: true,\n          status: 200,\n          headers: new Headers(),\n        } as Response)\n        .mockRejectedValueOnce(new Error('Network error'));\n\n      const urls = ['https://good.com', 'https://bad.com'];\n      const results = await websiteService.verifyMultipleWebsites(urls);\n\n      expect(results).toHaveLength(2);\n      expect(results[0].status).toBe('verified');\n      expect(results[1].status).toBe('unverified');\n    });\n\n    it('should respect concurrency limit', async () => {\n      const urls = Array.from({ length: 10 }, (_, i) => `https://example${i}.com`);\n      \n      mockFetch.mockResolvedValue({\n        ok: true,\n        status: 200,\n        headers: new Headers(),\n      } as Response);\n\n      const startTime = Date.now();\n      await websiteService.verifyMultipleWebsites(urls, 3); // Limit to 3 concurrent\n      const endTime = Date.now();\n\n      // With concurrency limit, it should take longer than if all were parallel\n      expect(mockFetch).toHaveBeenCalledTimes(10);\n    });\n  });\n\n  describe('getVerificationStats', () => {\n    it('should return verification statistics', async () => {\n      mockFetch\n        .mockResolvedValueOnce({\n          ok: true,\n          status: 200,\n          headers: new Headers(),\n        } as Response)\n        .mockResolvedValueOnce({\n          ok: false,\n          status: 404,\n          statusText: 'Not Found',\n        } as Response);\n\n      await websiteService.verifyWebsite('https://good.com');\n      await websiteService.verifyWebsite('https://bad.com');\n\n      const stats = websiteService.getVerificationStats();\n\n      expect(stats.totalVerified).toBe(2);\n      expect(stats.successfulVerifications).toBe(1);\n      expect(stats.failedVerifications).toBe(1);\n      expect(stats.successRate).toBe(0.5);\n    });\n  });\n\n  describe('clearCache', () => {\n    it('should clear verification cache', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        headers: new Headers(),\n      } as Response);\n\n      await websiteService.verifyWebsite('https://example.com');\n      expect(mockFetch).toHaveBeenCalledTimes(1);\n\n      websiteService.clearCache();\n\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        headers: new Headers(),\n      } as Response);\n\n      await websiteService.verifyWebsite('https://example.com');\n      expect(mockFetch).toHaveBeenCalledTimes(2);\n    });\n  });\n\n  describe('calculateConfidence', () => {\n    it('should calculate high confidence for successful responses', () => {\n      const confidence = websiteService.calculateConfidence(200, 'text/html', 500);\n      expect(confidence).toBeGreaterThan(0.8);\n    });\n\n    it('should calculate low confidence for error responses', () => {\n      const confidence = websiteService.calculateConfidence(404, '', 0);\n      expect(confidence).toBeLessThan(0.3);\n    });\n\n    it('should consider response time in confidence calculation', () => {\n      const fastConfidence = websiteService.calculateConfidence(200, 'text/html', 100);\n      const slowConfidence = websiteService.calculateConfidence(200, 'text/html', 5000);\n      \n      expect(fastConfidence).toBeGreaterThan(slowConfidence);\n    });\n  });\n});\n"], "version": 3}