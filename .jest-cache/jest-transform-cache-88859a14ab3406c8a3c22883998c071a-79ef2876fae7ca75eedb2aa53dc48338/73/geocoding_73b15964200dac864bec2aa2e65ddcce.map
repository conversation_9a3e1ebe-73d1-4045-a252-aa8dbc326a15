{"file": "/Users/<USER>/WebstormProjects/goo/src/services/geocoding.ts", "mappings": ";;;AACA,6CAA8F;AAC9F,oDAAsD;AACtD,4CAA0C;AA4B1C;;GAEG;AACH,MAAa,gBAAgB;IAK3B,YAAY,MAAe;QAJnB,UAAK,GAAG,IAAI,GAAG,EAAgC,CAAC;QAKtD,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,EAAE,CAAC;QAChE,IAAI,CAAC,OAAO,GAAG,sBAAU,CAAC,kBAAkB,CAAC;QAE7C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,2BAAkB,CAAC,mCAAmC,EAAE,uBAAuB,CAAC,CAAC;QAC7F,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,oBAAoB,CAAC,OAAe;QACxC,2BAA2B;QAC3B,IAAI,CAAC;YACH,IAAA,4BAAe,EAAC,OAAO,CAAC,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,uBAAc,CAAC,4BAA4B,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;QAC3E,CAAC;QAED,oBAAoB;QACpB,MAAM,QAAQ,GAAG,OAAO,OAAO,EAAE,CAAC;QAClC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YACzC,OAAO,MAAqB,CAAC;QAC/B,CAAC;QAED,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,iBAAiB,kBAAkB,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YAE7F,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;gBAChC,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE;oBACP,QAAQ,EAAE,kBAAkB;iBAC7B;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,iBAAQ,CAChB,iCAAiC,QAAQ,CAAC,UAAU,EAAE,EACtD,QAAQ,CAAC,MAAM,EACf,sBAAsB,CACvB,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,GAAoB,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEpD,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,MAAM,KAAK,cAAc,EAAE,CAAC;oBACnC,MAAM,IAAI,uBAAc,CAAC,kCAAkC,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;gBACjF,CAAC;gBACD,MAAM,IAAI,uBAAc,CACtB,IAAI,CAAC,aAAa,IAAI,iCAAiC,IAAI,CAAC,MAAM,EAAE,EACpE,OAAO,CACR,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/C,MAAM,IAAI,uBAAc,CAAC,kCAAkC,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;YACjF,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC/B,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBAClD,MAAM,IAAI,uBAAc,CAAC,yCAAyC,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;YACxF,CAAC;YAED,MAAM,WAAW,GAAgB;gBAC/B,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG;gBACtC,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG;aACxC,CAAC;YAEF,mBAAmB;YACnB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YAEtC,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,uBAAc,IAAI,KAAK,YAAY,iBAAQ,EAAE,CAAC;gBACjE,MAAM,KAAK,CAAC;YACd,CAAC;YAED,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;oBAChC,MAAM,IAAI,qBAAY,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;gBAC/D,CAAC;gBACD,MAAM,IAAI,qBAAY,CAAC,mCAAmC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;YACpF,CAAC;YAED,MAAM,IAAI,uBAAc,CAAC,mCAAmC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACxF,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,oBAAoB,CAAC,WAAwB;QACjD,MAAM,QAAQ,GAAG,UAAU,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;QAC3E,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YACzC,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,gBAAgB,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,SAAS,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YAE9G,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;gBAChC,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE;oBACP,QAAQ,EAAE,kBAAkB;iBAC7B;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,iBAAQ,CAChB,yCAAyC,QAAQ,CAAC,UAAU,EAAE,EAC9D,QAAQ,CAAC,MAAM,EACf,sBAAsB,CACvB,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,GAAoB,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEpD,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;gBACzB,MAAM,IAAI,uBAAc,CACtB,IAAI,CAAC,aAAa,IAAI,yCAAyC,IAAI,CAAC,MAAM,EAAE,CAC7E,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/C,MAAM,IAAI,uBAAc,CAAC,kCAAkC,CAAC,CAAC;YAC/D,CAAC;YAED,yCAAyC;YACzC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBAClC,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;oBAC9B,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;wBAClD,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;4BAC5C,MAAM,OAAO,GAAG,SAAS,CAAC,SAAS,CAAC;4BACpC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;4BAClC,OAAO,OAAO,CAAC;wBACjB,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,MAAM,IAAI,uBAAc,CAAC,sCAAsC,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,uBAAc,IAAI,KAAK,YAAY,iBAAQ,EAAE,CAAC;gBACjE,MAAM,KAAK,CAAC;YACd,CAAC;YAED,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;oBAChC,MAAM,IAAI,qBAAY,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;gBACvE,CAAC;gBACD,MAAM,IAAI,qBAAY,CAAC,2CAA2C,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;YAC5F,CAAC;YAED,MAAM,IAAI,uBAAc,CAAC,2CAA2C,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACvF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACrB,CAAC;IAED;;;OAGG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;CACF;AA1LD,4CA0LC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/src/services/geocoding.ts"], "sourcesContent": ["import { Coordinates } from '../models/Business';\nimport { GeocodingError, ApiError, NetworkError, ConfigurationError } from '../models/Errors';\nimport { validateZipCode } from '../utils/validation';\nimport { API_CONFIG } from '../constants';\n\n/**\n * Google Geocoding API response interfaces\n */\ninterface GeocodeResult {\n  geometry: {\n    location: {\n      lat: number;\n      lng: number;\n    };\n  };\n  formatted_address: string;\n  address_components?: AddressComponent[];\n}\n\ninterface AddressComponent {\n  long_name: string;\n  short_name: string;\n  types: string[];\n}\n\ninterface GeocodeResponse {\n  results: GeocodeResult[];\n  status: string;\n  error_message?: string;\n}\n\n/**\n * Service for converting between zip codes and coordinates using Google Geocoding API\n */\nexport class GeocodingService {\n  private cache = new Map<string, Coordinates | string>();\n  private readonly apiKey: string;\n  private readonly baseUrl: string;\n\n  constructor(apiKey?: string) {\n    this.apiKey = apiKey || process.env.GOOGLE_PLACES_API_KEY || '';\n    this.baseUrl = API_CONFIG.GEOCODING_BASE_URL;\n\n    if (!this.apiKey) {\n      throw new ConfigurationError('Google Places API key is required', 'GOOGLE_PLACES_API_KEY');\n    }\n  }\n\n  /**\n   * Converts a zip code to coordinates\n   * @param zipCode - The zip code to convert\n   * @returns Promise resolving to coordinates\n   * @throws GeocodingError if conversion fails\n   */\n  async zipCodeToCoordinates(zipCode: string): Promise<Coordinates> {\n    // Validate zip code format\n    try {\n      validateZipCode(zipCode);\n    } catch (error) {\n      throw new GeocodingError(`Invalid zip code format: ${zipCode}`, zipCode);\n    }\n\n    // Check cache first\n    const cacheKey = `zip_${zipCode}`;\n    const cached = this.cache.get(cacheKey);\n    if (cached && typeof cached === 'object') {\n      return cached as Coordinates;\n    }\n\n    try {\n      const url = `${this.baseUrl}/json?address=${encodeURIComponent(zipCode)}&key=${this.apiKey}`;\n      \n      const response = await fetch(url, {\n        method: 'GET',\n        headers: {\n          'Accept': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new ApiError(\n          `Geocoding API request failed: ${response.statusText}`,\n          response.status,\n          'Google Geocoding API'\n        );\n      }\n\n      const data: GeocodeResponse = await response.json();\n\n      if (data.status !== 'OK') {\n        if (data.status === 'ZERO_RESULTS') {\n          throw new GeocodingError(`No results found for zip code: ${zipCode}`, zipCode);\n        }\n        throw new GeocodingError(\n          data.error_message || `Geocoding failed with status: ${data.status}`,\n          zipCode\n        );\n      }\n\n      if (!data.results || data.results.length === 0) {\n        throw new GeocodingError(`No results found for zip code: ${zipCode}`, zipCode);\n      }\n\n      const result = data.results[0];\n      if (!result.geometry || !result.geometry.location) {\n        throw new GeocodingError(`Invalid response format for zip code: ${zipCode}`, zipCode);\n      }\n\n      const coordinates: Coordinates = {\n        latitude: result.geometry.location.lat,\n        longitude: result.geometry.location.lng,\n      };\n\n      // Cache the result\n      this.cache.set(cacheKey, coordinates);\n\n      return coordinates;\n    } catch (error) {\n      if (error instanceof GeocodingError || error instanceof ApiError) {\n        throw error;\n      }\n      \n      if (error instanceof Error) {\n        if (error.name === 'AbortError') {\n          throw new NetworkError('Geocoding request timed out', error);\n        }\n        throw new NetworkError(`Network error during geocoding: ${error.message}`, error);\n      }\n      \n      throw new GeocodingError(`Unknown error during geocoding: ${String(error)}`, zipCode);\n    }\n  }\n\n  /**\n   * Converts coordinates to zip code (reverse geocoding)\n   * @param coordinates - The coordinates to convert\n   * @returns Promise resolving to zip code\n   * @throws GeocodingError if conversion fails\n   */\n  async coordinatesToZipCode(coordinates: Coordinates): Promise<string> {\n    const cacheKey = `coords_${coordinates.latitude}_${coordinates.longitude}`;\n    const cached = this.cache.get(cacheKey);\n    if (cached && typeof cached === 'string') {\n      return cached;\n    }\n\n    try {\n      const url = `${this.baseUrl}/json?latlng=${coordinates.latitude},${coordinates.longitude}&key=${this.apiKey}`;\n      \n      const response = await fetch(url, {\n        method: 'GET',\n        headers: {\n          'Accept': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new ApiError(\n          `Reverse geocoding API request failed: ${response.statusText}`,\n          response.status,\n          'Google Geocoding API'\n        );\n      }\n\n      const data: GeocodeResponse = await response.json();\n\n      if (data.status !== 'OK') {\n        throw new GeocodingError(\n          data.error_message || `Reverse geocoding failed with status: ${data.status}`\n        );\n      }\n\n      if (!data.results || data.results.length === 0) {\n        throw new GeocodingError('No results found for coordinates');\n      }\n\n      // Find postal code in address components\n      for (const result of data.results) {\n        if (result.address_components) {\n          for (const component of result.address_components) {\n            if (component.types.includes('postal_code')) {\n              const zipCode = component.long_name;\n              this.cache.set(cacheKey, zipCode);\n              return zipCode;\n            }\n          }\n        }\n      }\n\n      throw new GeocodingError('No postal code found for coordinates');\n    } catch (error) {\n      if (error instanceof GeocodingError || error instanceof ApiError) {\n        throw error;\n      }\n      \n      if (error instanceof Error) {\n        if (error.name === 'AbortError') {\n          throw new NetworkError('Reverse geocoding request timed out', error);\n        }\n        throw new NetworkError(`Network error during reverse geocoding: ${error.message}`, error);\n      }\n      \n      throw new GeocodingError(`Unknown error during reverse geocoding: ${String(error)}`);\n    }\n  }\n\n  /**\n   * Clears the geocoding cache\n   */\n  clearCache(): void {\n    this.cache.clear();\n  }\n\n  /**\n   * Gets the current cache size\n   * @returns Number of cached entries\n   */\n  getCacheSize(): number {\n    return this.cache.size;\n  }\n}\n"], "version": 3}