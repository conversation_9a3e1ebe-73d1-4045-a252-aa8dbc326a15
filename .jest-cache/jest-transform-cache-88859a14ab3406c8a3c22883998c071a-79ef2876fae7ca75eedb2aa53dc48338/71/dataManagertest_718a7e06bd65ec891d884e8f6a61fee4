583baa1bdb4ac185cf513eb8c837f444
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const dataManager_1 = require("../../src/services/dataManager");
const Errors_1 = require("../../src/models/Errors");
// Create a proper localStorage mock
const localStorageData = {};
const localStorageMock = {
    getItem: jest.fn((key) => localStorageData[key] || null),
    setItem: jest.fn((key, value) => {
        localStorageData[key] = value;
    }),
    removeItem: jest.fn((key) => {
        delete localStorageData[key];
    }),
    clear: jest.fn(() => {
        Object.keys(localStorageData).forEach(key => delete localStorageData[key]);
    }),
    key: jest.fn((index) => {
        const keys = Object.keys(localStorageData);
        return keys[index] || null;
    }),
    get length() {
        return Object.keys(localStorageData).length;
    },
};
// Replace global localStorage
Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
    writable: true,
});
describe('DataManager', () => {
    let dataManager;
    beforeEach(() => {
        dataManager = new dataManager_1.DataManager();
        // Clear the actual storage data
        Object.keys(localStorageData).forEach(key => delete localStorageData[key]);
        localStorageMock.getItem.mockClear();
        localStorageMock.setItem.mockClear();
        localStorageMock.removeItem.mockClear();
        localStorageMock.clear.mockClear();
        localStorageMock.key.mockClear();
    });
    const mockSearchResult = {
        searchParams: {
            zipCode: '10001',
            radius: 10,
            businessType: 'restaurant',
            timestamp: new Date('2023-01-01T00:00:00Z'),
        },
        results: {
            withWebsites: [
                {
                    id: '1',
                    name: 'Restaurant A',
                    address: '123 Main St',
                    websiteStatus: 'verified',
                    category: ['restaurant'],
                    distance: 1.5,
                    metadata: {
                        lastUpdated: new Date('2023-01-01T00:00:00Z'),
                        dataSource: 'google_places',
                        confidence: 0.9,
                    },
                },
            ],
            withoutWebsites: [
                {
                    id: '2',
                    name: 'Restaurant B',
                    address: '456 Oak Ave',
                    websiteStatus: 'none',
                    category: ['restaurant'],
                    distance: 2.1,
                    metadata: {
                        lastUpdated: new Date('2023-01-01T00:00:00Z'),
                        dataSource: 'google_places',
                        confidence: 0.8,
                    },
                },
            ],
        },
        statistics: {
            totalFound: 2,
            withWebsiteCount: 1,
            withoutWebsiteCount: 1,
            websiteAdoptionRate: 0.5,
            searchDuration: 1500,
        },
    };
    describe('saveSearchResult', () => {
        it('should save search result to localStorage', () => {
            const key = dataManager.saveSearchResult(mockSearchResult);
            expect(key).toBeTruthy();
            expect(localStorageMock.setItem).toHaveBeenCalled();
        });
        it('should generate unique keys for different searches', () => {
            const key1 = dataManager.saveSearchResult(mockSearchResult);
            const key2 = dataManager.saveSearchResult({
                ...mockSearchResult,
                searchParams: {
                    ...mockSearchResult.searchParams,
                    zipCode: '10002',
                },
            });
            expect(key1).not.toBe(key2);
        });
        it('should handle localStorage errors gracefully', () => {
            // Mock localStorage.setItem to throw an error
            localStorageMock.setItem.mockImplementationOnce(() => {
                throw new Error('Storage quota exceeded');
            });
            expect(() => dataManager.saveSearchResult(mockSearchResult)).toThrow(Errors_1.CacheError);
        });
    });
    describe('getSearchResult', () => {
        it('should retrieve saved search result', () => {
            const key = dataManager.saveSearchResult(mockSearchResult);
            const retrieved = dataManager.getSearchResult(key);
            expect(retrieved).toBeTruthy();
            expect(retrieved.searchParams.zipCode).toBe('10001');
            expect(retrieved.results.withWebsites).toHaveLength(1);
            expect(retrieved.results.withoutWebsites).toHaveLength(1);
        });
        it('should return null for non-existent key', () => {
            const result = dataManager.getSearchResult('non-existent-key');
            expect(result).toBeNull();
        });
        it('should return null for expired data', () => {
            // Mock an expired entry
            const expiredData = {
                data: mockSearchResult,
                timestamp: Date.now() - (25 * 60 * 60 * 1000), // 25 hours ago
                expiresAt: Date.now() - (1 * 60 * 60 * 1000), // 1 hour ago
            };
            localStorage.getItem.mockReturnValueOnce(JSON.stringify(expiredData));
            const result = dataManager.getSearchResult('expired-key');
            expect(result).toBeNull();
        });
        it('should handle corrupted data gracefully', () => {
            localStorage.getItem.mockReturnValueOnce('invalid-json');
            const result = dataManager.getSearchResult('corrupted-key');
            expect(result).toBeNull();
        });
    });
    describe('getAllSearchResults', () => {
        it('should return all saved search results', () => {
            const key1 = dataManager.saveSearchResult(mockSearchResult);
            const key2 = dataManager.saveSearchResult({
                ...mockSearchResult,
                searchParams: {
                    ...mockSearchResult.searchParams,
                    zipCode: '10002',
                },
            });
            const allResults = dataManager.getAllSearchResults();
            expect(allResults).toHaveLength(2);
            expect(allResults.some(r => r.searchParams.zipCode === '10001')).toBe(true);
            expect(allResults.some(r => r.searchParams.zipCode === '10002')).toBe(true);
        });
        it('should exclude expired results', () => {
            // Save a valid result
            dataManager.saveSearchResult(mockSearchResult);
            // Manually add an expired entry to the localStorage data
            const expiredKey = 'business_search_expired';
            const expiredData = {
                data: mockSearchResult,
                timestamp: Date.now() - (25 * 60 * 60 * 1000),
                expiresAt: Date.now() - (1 * 60 * 60 * 1000),
            };
            localStorageData[expiredKey] = JSON.stringify(expiredData);
            const allResults = dataManager.getAllSearchResults();
            expect(allResults).toHaveLength(1); // Only the valid result
        });
    });
    describe('deleteSearchResult', () => {
        it('should delete search result from localStorage', () => {
            const key = dataManager.saveSearchResult(mockSearchResult);
            dataManager.deleteSearchResult(key);
            expect(localStorage.removeItem).toHaveBeenCalledWith(key);
            expect(dataManager.getSearchResult(key)).toBeNull();
        });
        it('should handle deletion of non-existent key gracefully', () => {
            expect(() => dataManager.deleteSearchResult('non-existent')).not.toThrow();
        });
    });
    describe('clearAllData', () => {
        it('should clear all business search data', () => {
            dataManager.saveSearchResult(mockSearchResult);
            dataManager.saveSearchResult({
                ...mockSearchResult,
                searchParams: {
                    ...mockSearchResult.searchParams,
                    zipCode: '10002',
                },
            });
            dataManager.clearAllData();
            expect(dataManager.getAllSearchResults()).toHaveLength(0);
        });
        it('should only clear business search data, not other localStorage data', () => {
            // Add some non-business search data
            localStorageData['other_app_data'] = 'should_remain';
            dataManager.saveSearchResult(mockSearchResult);
            dataManager.clearAllData();
            expect(localStorageData['other_app_data']).toBe('should_remain');
            expect(dataManager.getAllSearchResults()).toHaveLength(0);
        });
    });
    describe('getStorageInfo', () => {
        it('should return storage information', () => {
            dataManager.saveSearchResult(mockSearchResult);
            const info = dataManager.getStorageInfo();
            expect(info).toHaveProperty('totalEntries');
            expect(info).toHaveProperty('totalSizeBytes');
            expect(info).toHaveProperty('oldestEntry');
            expect(info).toHaveProperty('newestEntry');
            expect(info.totalEntries).toBeGreaterThan(0);
        });
        it('should return zero values for empty storage', () => {
            const info = dataManager.getStorageInfo();
            expect(info.totalEntries).toBe(0);
            expect(info.totalSizeBytes).toBe(0);
            expect(info.oldestEntry).toBeNull();
            expect(info.newestEntry).toBeNull();
        });
    });
    describe('cleanupExpiredData', () => {
        it('should remove expired entries', () => {
            // Save a valid result
            const validKey = dataManager.saveSearchResult(mockSearchResult);
            // Manually add an expired entry
            const expiredKey = 'business_search_expired';
            const expiredData = {
                data: mockSearchResult,
                timestamp: Date.now() - (25 * 60 * 60 * 1000),
                expiresAt: Date.now() - (1 * 60 * 60 * 1000),
            };
            localStorageData[expiredKey] = JSON.stringify(expiredData);
            const removedCount = dataManager.cleanupExpiredData();
            expect(removedCount).toBe(1);
            expect(localStorageData[expiredKey]).toBeUndefined();
        });
        it('should return count of removed entries', () => {
            // All entries are valid by default
            dataManager.saveSearchResult(mockSearchResult);
            const removedCount = dataManager.cleanupExpiredData();
            expect(removedCount).toBe(0);
        });
    });
    describe('exportData', () => {
        it('should export all search results as JSON', () => {
            dataManager.saveSearchResult(mockSearchResult);
            const exported = dataManager.exportData();
            const parsed = JSON.parse(exported);
            expect(parsed).toHaveProperty('version');
            expect(parsed).toHaveProperty('exportDate');
            expect(parsed).toHaveProperty('searchResults');
            expect(parsed.searchResults).toHaveLength(1);
        });
        it('should include metadata in export', () => {
            dataManager.saveSearchResult(mockSearchResult);
            const exported = dataManager.exportData();
            const parsed = JSON.parse(exported);
            expect(parsed.version).toBe('1.0');
            expect(parsed.exportDate).toBeTruthy();
            expect(new Date(parsed.exportDate)).toBeInstanceOf(Date);
        });
    });
    describe('importData', () => {
        it('should import search results from JSON', () => {
            const exportData = {
                version: '1.0',
                exportDate: new Date().toISOString(),
                searchResults: [mockSearchResult],
            };
            const importedCount = dataManager.importData(JSON.stringify(exportData));
            expect(importedCount).toBe(1);
            expect(dataManager.getAllSearchResults()).toHaveLength(1);
        });
        it('should handle invalid JSON gracefully', () => {
            expect(() => dataManager.importData('invalid-json')).toThrow(Errors_1.CacheError);
        });
        it('should validate import data structure', () => {
            const invalidData = {
                version: '1.0',
                // Missing searchResults
            };
            expect(() => dataManager.importData(JSON.stringify(invalidData))).toThrow(Errors_1.CacheError);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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