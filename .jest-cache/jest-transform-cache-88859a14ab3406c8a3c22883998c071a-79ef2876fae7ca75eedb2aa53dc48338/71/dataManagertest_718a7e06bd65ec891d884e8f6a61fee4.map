{"file": "/Users/<USER>/WebstormProjects/goo/tests/services/dataManager.test.ts", "mappings": ";;AAAA,gEAA6D;AAE7D,oDAAqD;AAErD,oCAAoC;AACpC,MAAM,gBAAgB,GAA8B,EAAE,CAAC;AACvD,MAAM,gBAAgB,GAAG;IACvB,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;IAChE,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,GAAW,EAAE,KAAa,EAAE,EAAE;QAC9C,gBAAgB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IAChC,CAAC,CAAC;IACF,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,GAAW,EAAE,EAAE;QAClC,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC,CAAC;IACF,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE;QAClB,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7E,CAAC,CAAC;IACF,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,KAAa,EAAE,EAAE;QAC7B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;IAC7B,CAAC,CAAC;IACF,IAAI,MAAM;QACR,OAAO,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC;IAC9C,CAAC;CACF,CAAC;AAEF,8BAA8B;AAC9B,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,cAAc,EAAE;IAC5C,KAAK,EAAE,gBAAgB;IACvB,QAAQ,EAAE,IAAI;CACf,CAAC,CAAC;AAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;IAC3B,IAAI,WAAwB,CAAC;IAE7B,UAAU,CAAC,GAAG,EAAE;QACd,WAAW,GAAG,IAAI,yBAAW,EAAE,CAAC;QAChC,gCAAgC;QAChC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3E,gBAAgB,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACrC,gBAAgB,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACrC,gBAAgB,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QACxC,gBAAgB,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;QACnC,gBAAgB,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,MAAM,gBAAgB,GAAiB;QACrC,YAAY,EAAE;YACZ,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,EAAE;YACV,YAAY,EAAE,YAAY;YAC1B,SAAS,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;SAC5C;QACD,OAAO,EAAE;YACP,YAAY,EAAE;gBACZ;oBACE,EAAE,EAAE,GAAG;oBACP,IAAI,EAAE,cAAc;oBACpB,OAAO,EAAE,aAAa;oBACtB,aAAa,EAAE,UAAU;oBACzB,QAAQ,EAAE,CAAC,YAAY,CAAC;oBACxB,QAAQ,EAAE,GAAG;oBACb,QAAQ,EAAE;wBACR,WAAW,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;wBAC7C,UAAU,EAAE,eAAe;wBAC3B,UAAU,EAAE,GAAG;qBAChB;iBACU;aACd;YACD,eAAe,EAAE;gBACf;oBACE,EAAE,EAAE,GAAG;oBACP,IAAI,EAAE,cAAc;oBACpB,OAAO,EAAE,aAAa;oBACtB,aAAa,EAAE,MAAM;oBACrB,QAAQ,EAAE,CAAC,YAAY,CAAC;oBACxB,QAAQ,EAAE,GAAG;oBACb,QAAQ,EAAE;wBACR,WAAW,EAAE,IAAI,IAAI,CAAC,sBAAsB,CAAC;wBAC7C,UAAU,EAAE,eAAe;wBAC3B,UAAU,EAAE,GAAG;qBAChB;iBACU;aACd;SACF;QACD,UAAU,EAAE;YACV,UAAU,EAAE,CAAC;YACb,gBAAgB,EAAE,CAAC;YACnB,mBAAmB,EAAE,CAAC;YACtB,mBAAmB,EAAE,GAAG;YACxB,cAAc,EAAE,IAAI;SACrB;KACF,CAAC;IAEF,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,GAAG,GAAG,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAE3D,MAAM,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC;YACzB,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,gBAAgB,EAAE,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,IAAI,GAAG,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAC5D,MAAM,IAAI,GAAG,WAAW,CAAC,gBAAgB,CAAC;gBACxC,GAAG,gBAAgB;gBACnB,YAAY,EAAE;oBACZ,GAAG,gBAAgB,CAAC,YAAY;oBAChC,OAAO,EAAE,OAAO;iBACjB;aACF,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,8CAA8C;YAC9C,gBAAgB,CAAC,OAAO,CAAC,sBAAsB,CAAC,GAAG,EAAE;gBACnD,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAU,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,GAAG,GAAG,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAC3D,MAAM,SAAS,GAAG,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAEnD,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,CAAC;YAC/B,MAAM,CAAC,SAAU,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtD,MAAM,CAAC,SAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACxD,MAAM,CAAC,SAAU,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,MAAM,GAAG,WAAW,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;YAC/D,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,wBAAwB;YACxB,MAAM,WAAW,GAAG;gBAClB,IAAI,EAAE,gBAAgB;gBACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,eAAe;gBAC9D,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,aAAa;aAC5D,CAAC;YAED,YAAY,CAAC,OAAqB,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;YAErF,MAAM,MAAM,GAAG,WAAW,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YAChD,YAAY,CAAC,OAAqB,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;YAExE,MAAM,MAAM,GAAG,WAAW,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAC5D,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,IAAI,GAAG,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAC5D,MAAM,IAAI,GAAG,WAAW,CAAC,gBAAgB,CAAC;gBACxC,GAAG,gBAAgB;gBACnB,YAAY,EAAE;oBACZ,GAAG,gBAAgB,CAAC,YAAY;oBAChC,OAAO,EAAE,OAAO;iBACjB;aACF,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,WAAW,CAAC,mBAAmB,EAAE,CAAC;YAErD,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5E,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,sBAAsB;YACtB,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAE/C,yDAAyD;YACzD,MAAM,UAAU,GAAG,yBAAyB,CAAC;YAC7C,MAAM,WAAW,GAAG;gBAClB,IAAI,EAAE,gBAAgB;gBACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;gBAC7C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;aAC7C,CAAC;YACF,gBAAgB,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAE3D,MAAM,UAAU,GAAG,WAAW,CAAC,mBAAmB,EAAE,CAAC;YAErD,MAAM,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,GAAG,GAAG,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAE3D,WAAW,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;YAEpC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;YAC1D,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QAC7E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAC/C,WAAW,CAAC,gBAAgB,CAAC;gBAC3B,GAAG,gBAAgB;gBACnB,YAAY,EAAE;oBACZ,GAAG,gBAAgB,CAAC,YAAY;oBAChC,OAAO,EAAE,OAAO;iBACjB;aACF,CAAC,CAAC;YAEH,WAAW,CAAC,YAAY,EAAE,CAAC;YAE3B,MAAM,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qEAAqE,EAAE,GAAG,EAAE;YAC7E,oCAAoC;YACpC,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,eAAe,CAAC;YAErD,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAC/C,WAAW,CAAC,YAAY,EAAE,CAAC;YAE3B,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACjE,MAAM,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAE/C,MAAM,IAAI,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC;YAE1C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,IAAI,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC;YAE1C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,sBAAsB;YACtB,MAAM,QAAQ,GAAG,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAEhE,gCAAgC;YAChC,MAAM,UAAU,GAAG,yBAAyB,CAAC;YAC7C,MAAM,WAAW,GAAG;gBAClB,IAAI,EAAE,gBAAgB;gBACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;gBAC7C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;aAC7C,CAAC;YACF,gBAAgB,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAE3D,MAAM,YAAY,GAAG,WAAW,CAAC,kBAAkB,EAAE,CAAC;YAEtD,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,mCAAmC;YACnC,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAE/C,MAAM,YAAY,GAAG,WAAW,CAAC,kBAAkB,EAAE,CAAC;YAEtD,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAE/C,MAAM,QAAQ,GAAG,WAAW,CAAC,UAAU,EAAE,CAAC;YAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAEpC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAE/C,MAAM,QAAQ,GAAG,WAAW,CAAC,UAAU,EAAE,CAAC;YAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAEpC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,UAAU,EAAE,CAAC;YACvC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,UAAU,GAAG;gBACjB,OAAO,EAAE,KAAK;gBACd,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACpC,aAAa,EAAE,CAAC,gBAAgB,CAAC;aAClC,CAAC;YAEF,MAAM,aAAa,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YAEzE,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAU,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,WAAW,GAAG;gBAClB,OAAO,EAAE,KAAK;gBACd,wBAAwB;aACzB,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAU,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/tests/services/dataManager.test.ts"], "sourcesContent": ["import { DataManager } from '../../src/services/dataManager';\nimport { SearchResult, Business } from '../../src/models/Business';\nimport { CacheError } from '../../src/models/Errors';\n\n// Create a proper localStorage mock\nconst localStorageData: { [key: string]: string } = {};\nconst localStorageMock = {\n  getItem: jest.fn((key: string) => localStorageData[key] || null),\n  setItem: jest.fn((key: string, value: string) => {\n    localStorageData[key] = value;\n  }),\n  removeItem: jest.fn((key: string) => {\n    delete localStorageData[key];\n  }),\n  clear: jest.fn(() => {\n    Object.keys(localStorageData).forEach(key => delete localStorageData[key]);\n  }),\n  key: jest.fn((index: number) => {\n    const keys = Object.keys(localStorageData);\n    return keys[index] || null;\n  }),\n  get length() {\n    return Object.keys(localStorageData).length;\n  },\n};\n\n// Replace global localStorage\nObject.defineProperty(window, 'localStorage', {\n  value: localStorageMock,\n  writable: true,\n});\n\ndescribe('DataManager', () => {\n  let dataManager: DataManager;\n\n  beforeEach(() => {\n    dataManager = new DataManager();\n    // Clear the actual storage data\n    Object.keys(localStorageData).forEach(key => delete localStorageData[key]);\n    localStorageMock.getItem.mockClear();\n    localStorageMock.setItem.mockClear();\n    localStorageMock.removeItem.mockClear();\n    localStorageMock.clear.mockClear();\n    localStorageMock.key.mockClear();\n  });\n\n  const mockSearchResult: SearchResult = {\n    searchParams: {\n      zipCode: '10001',\n      radius: 10,\n      businessType: 'restaurant',\n      timestamp: new Date('2023-01-01T00:00:00Z'),\n    },\n    results: {\n      withWebsites: [\n        {\n          id: '1',\n          name: 'Restaurant A',\n          address: '123 Main St',\n          websiteStatus: 'verified',\n          category: ['restaurant'],\n          distance: 1.5,\n          metadata: {\n            lastUpdated: new Date('2023-01-01T00:00:00Z'),\n            dataSource: 'google_places',\n            confidence: 0.9,\n          },\n        } as Business,\n      ],\n      withoutWebsites: [\n        {\n          id: '2',\n          name: 'Restaurant B',\n          address: '456 Oak Ave',\n          websiteStatus: 'none',\n          category: ['restaurant'],\n          distance: 2.1,\n          metadata: {\n            lastUpdated: new Date('2023-01-01T00:00:00Z'),\n            dataSource: 'google_places',\n            confidence: 0.8,\n          },\n        } as Business,\n      ],\n    },\n    statistics: {\n      totalFound: 2,\n      withWebsiteCount: 1,\n      withoutWebsiteCount: 1,\n      websiteAdoptionRate: 0.5,\n      searchDuration: 1500,\n    },\n  };\n\n  describe('saveSearchResult', () => {\n    it('should save search result to localStorage', () => {\n      const key = dataManager.saveSearchResult(mockSearchResult);\n\n      expect(key).toBeTruthy();\n      expect(localStorageMock.setItem).toHaveBeenCalled();\n    });\n\n    it('should generate unique keys for different searches', () => {\n      const key1 = dataManager.saveSearchResult(mockSearchResult);\n      const key2 = dataManager.saveSearchResult({\n        ...mockSearchResult,\n        searchParams: {\n          ...mockSearchResult.searchParams,\n          zipCode: '10002',\n        },\n      });\n\n      expect(key1).not.toBe(key2);\n    });\n\n    it('should handle localStorage errors gracefully', () => {\n      // Mock localStorage.setItem to throw an error\n      localStorageMock.setItem.mockImplementationOnce(() => {\n        throw new Error('Storage quota exceeded');\n      });\n\n      expect(() => dataManager.saveSearchResult(mockSearchResult)).toThrow(CacheError);\n    });\n  });\n\n  describe('getSearchResult', () => {\n    it('should retrieve saved search result', () => {\n      const key = dataManager.saveSearchResult(mockSearchResult);\n      const retrieved = dataManager.getSearchResult(key);\n\n      expect(retrieved).toBeTruthy();\n      expect(retrieved!.searchParams.zipCode).toBe('10001');\n      expect(retrieved!.results.withWebsites).toHaveLength(1);\n      expect(retrieved!.results.withoutWebsites).toHaveLength(1);\n    });\n\n    it('should return null for non-existent key', () => {\n      const result = dataManager.getSearchResult('non-existent-key');\n      expect(result).toBeNull();\n    });\n\n    it('should return null for expired data', () => {\n      // Mock an expired entry\n      const expiredData = {\n        data: mockSearchResult,\n        timestamp: Date.now() - (25 * 60 * 60 * 1000), // 25 hours ago\n        expiresAt: Date.now() - (1 * 60 * 60 * 1000), // 1 hour ago\n      };\n\n      (localStorage.getItem as jest.Mock).mockReturnValueOnce(JSON.stringify(expiredData));\n\n      const result = dataManager.getSearchResult('expired-key');\n      expect(result).toBeNull();\n    });\n\n    it('should handle corrupted data gracefully', () => {\n      (localStorage.getItem as jest.Mock).mockReturnValueOnce('invalid-json');\n\n      const result = dataManager.getSearchResult('corrupted-key');\n      expect(result).toBeNull();\n    });\n  });\n\n  describe('getAllSearchResults', () => {\n    it('should return all saved search results', () => {\n      const key1 = dataManager.saveSearchResult(mockSearchResult);\n      const key2 = dataManager.saveSearchResult({\n        ...mockSearchResult,\n        searchParams: {\n          ...mockSearchResult.searchParams,\n          zipCode: '10002',\n        },\n      });\n\n      const allResults = dataManager.getAllSearchResults();\n\n      expect(allResults).toHaveLength(2);\n      expect(allResults.some(r => r.searchParams.zipCode === '10001')).toBe(true);\n      expect(allResults.some(r => r.searchParams.zipCode === '10002')).toBe(true);\n    });\n\n    it('should exclude expired results', () => {\n      // Save a valid result\n      dataManager.saveSearchResult(mockSearchResult);\n\n      // Manually add an expired entry to the localStorage data\n      const expiredKey = 'business_search_expired';\n      const expiredData = {\n        data: mockSearchResult,\n        timestamp: Date.now() - (25 * 60 * 60 * 1000),\n        expiresAt: Date.now() - (1 * 60 * 60 * 1000),\n      };\n      localStorageData[expiredKey] = JSON.stringify(expiredData);\n\n      const allResults = dataManager.getAllSearchResults();\n\n      expect(allResults).toHaveLength(1); // Only the valid result\n    });\n  });\n\n  describe('deleteSearchResult', () => {\n    it('should delete search result from localStorage', () => {\n      const key = dataManager.saveSearchResult(mockSearchResult);\n      \n      dataManager.deleteSearchResult(key);\n\n      expect(localStorage.removeItem).toHaveBeenCalledWith(key);\n      expect(dataManager.getSearchResult(key)).toBeNull();\n    });\n\n    it('should handle deletion of non-existent key gracefully', () => {\n      expect(() => dataManager.deleteSearchResult('non-existent')).not.toThrow();\n    });\n  });\n\n  describe('clearAllData', () => {\n    it('should clear all business search data', () => {\n      dataManager.saveSearchResult(mockSearchResult);\n      dataManager.saveSearchResult({\n        ...mockSearchResult,\n        searchParams: {\n          ...mockSearchResult.searchParams,\n          zipCode: '10002',\n        },\n      });\n\n      dataManager.clearAllData();\n\n      expect(dataManager.getAllSearchResults()).toHaveLength(0);\n    });\n\n    it('should only clear business search data, not other localStorage data', () => {\n      // Add some non-business search data\n      localStorageData['other_app_data'] = 'should_remain';\n      \n      dataManager.saveSearchResult(mockSearchResult);\n      dataManager.clearAllData();\n\n      expect(localStorageData['other_app_data']).toBe('should_remain');\n      expect(dataManager.getAllSearchResults()).toHaveLength(0);\n    });\n  });\n\n  describe('getStorageInfo', () => {\n    it('should return storage information', () => {\n      dataManager.saveSearchResult(mockSearchResult);\n      \n      const info = dataManager.getStorageInfo();\n\n      expect(info).toHaveProperty('totalEntries');\n      expect(info).toHaveProperty('totalSizeBytes');\n      expect(info).toHaveProperty('oldestEntry');\n      expect(info).toHaveProperty('newestEntry');\n      expect(info.totalEntries).toBeGreaterThan(0);\n    });\n\n    it('should return zero values for empty storage', () => {\n      const info = dataManager.getStorageInfo();\n\n      expect(info.totalEntries).toBe(0);\n      expect(info.totalSizeBytes).toBe(0);\n      expect(info.oldestEntry).toBeNull();\n      expect(info.newestEntry).toBeNull();\n    });\n  });\n\n  describe('cleanupExpiredData', () => {\n    it('should remove expired entries', () => {\n      // Save a valid result\n      const validKey = dataManager.saveSearchResult(mockSearchResult);\n\n      // Manually add an expired entry\n      const expiredKey = 'business_search_expired';\n      const expiredData = {\n        data: mockSearchResult,\n        timestamp: Date.now() - (25 * 60 * 60 * 1000),\n        expiresAt: Date.now() - (1 * 60 * 60 * 1000),\n      };\n      localStorageData[expiredKey] = JSON.stringify(expiredData);\n\n      const removedCount = dataManager.cleanupExpiredData();\n\n      expect(removedCount).toBe(1);\n      expect(localStorageData[expiredKey]).toBeUndefined();\n    });\n\n    it('should return count of removed entries', () => {\n      // All entries are valid by default\n      dataManager.saveSearchResult(mockSearchResult);\n      \n      const removedCount = dataManager.cleanupExpiredData();\n      \n      expect(removedCount).toBe(0);\n    });\n  });\n\n  describe('exportData', () => {\n    it('should export all search results as JSON', () => {\n      dataManager.saveSearchResult(mockSearchResult);\n      \n      const exported = dataManager.exportData();\n      const parsed = JSON.parse(exported);\n\n      expect(parsed).toHaveProperty('version');\n      expect(parsed).toHaveProperty('exportDate');\n      expect(parsed).toHaveProperty('searchResults');\n      expect(parsed.searchResults).toHaveLength(1);\n    });\n\n    it('should include metadata in export', () => {\n      dataManager.saveSearchResult(mockSearchResult);\n      \n      const exported = dataManager.exportData();\n      const parsed = JSON.parse(exported);\n\n      expect(parsed.version).toBe('1.0');\n      expect(parsed.exportDate).toBeTruthy();\n      expect(new Date(parsed.exportDate)).toBeInstanceOf(Date);\n    });\n  });\n\n  describe('importData', () => {\n    it('should import search results from JSON', () => {\n      const exportData = {\n        version: '1.0',\n        exportDate: new Date().toISOString(),\n        searchResults: [mockSearchResult],\n      };\n\n      const importedCount = dataManager.importData(JSON.stringify(exportData));\n\n      expect(importedCount).toBe(1);\n      expect(dataManager.getAllSearchResults()).toHaveLength(1);\n    });\n\n    it('should handle invalid JSON gracefully', () => {\n      expect(() => dataManager.importData('invalid-json')).toThrow(CacheError);\n    });\n\n    it('should validate import data structure', () => {\n      const invalidData = {\n        version: '1.0',\n        // Missing searchResults\n      };\n\n      expect(() => dataManager.importData(JSON.stringify(invalidData))).toThrow(CacheError);\n    });\n  });\n});\n"], "version": 3}