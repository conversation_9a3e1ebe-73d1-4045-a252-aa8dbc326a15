{"file": "/Users/<USER>/WebstormProjects/goo/tests/services/geocoding.test.ts", "mappings": ";;AAAA,4DAAgE;AAChE,oDAAiF;AAGjF,sBAAsB;AACtB,MAAM,SAAS,GAAG,MAAM,CAAC,KAA0C,CAAC;AAEpE,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAChC,IAAI,gBAAkC,CAAC;IAEvC,UAAU,CAAC,GAAG,EAAE;QACd,gBAAgB,GAAG,IAAI,4BAAgB,EAAE,CAAC;QAC1C,SAAS,CAAC,SAAS,EAAE,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,MAAM,YAAY,GAAG,OAAO,CAAC;QAC7B,MAAM,eAAe,GAAG;YACtB,OAAO,EAAE;gBACP;oBACE,QAAQ,EAAE;wBACR,QAAQ,EAAE;4BACR,GAAG,EAAE,OAAO;4BACZ,GAAG,EAAE,CAAC,OAAO;yBACd;qBACF;oBACD,iBAAiB,EAAE,yBAAyB;iBAC7C;aACF;YACD,MAAM,EAAE,IAAI;SACb,CAAC;QAEF,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,eAAe;aACtB,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAEzE,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,QAAQ,EAAE,OAAO;gBACjB,SAAS,EAAE,CAAC,OAAO;aACpB,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CACpC,MAAM,CAAC,gBAAgB,CAAC,cAAc,CAAC,EACvC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,KAAK;aACd,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,aAAa;aACd,CAAC,CAAC;YAEf,MAAM,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;iBAC9D,OAAO,CAAC,OAAO,CAAC,iBAAQ,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,SAAS,CAAC,qBAAqB,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;YAE5D,MAAM,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;iBAC9D,OAAO,CAAC,OAAO,CAAC,qBAAY,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,OAAO,EAAE,EAAE;oBACX,MAAM,EAAE,cAAc;iBACvB,CAAC;aACS,CAAC,CAAC;YAEf,MAAM,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;iBAC9D,OAAO,CAAC,OAAO,CAAC,uBAAc,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;oBAChC,MAAM,EAAE,IAAI;iBACb,CAAC;aACS,CAAC,CAAC;YAEf,MAAM,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;iBAC9D,OAAO,CAAC,OAAO,CAAC,uBAAc,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,eAAe;aACtB,CAAC,CAAC;YAEf,aAAa;YACb,MAAM,OAAO,GAAG,MAAM,gBAAgB,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAE1E,+BAA+B;YAC/B,MAAM,OAAO,GAAG,MAAM,gBAAgB,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAE1E,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACjC,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,MAAM,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;iBAC3D,OAAO,CAAC,OAAO,CAAC,uBAAc,CAAC,CAAC;YAEnC,MAAM,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,mBAAmB;aACpB,CAAC,CAAC;YAEf,MAAM,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;iBAC9D,OAAO,CAAC,OAAO,CAAC,iBAAQ,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,MAAM,gBAAgB,GAAgB;YACpC,QAAQ,EAAE,OAAO;YACjB,SAAS,EAAE,CAAC,OAAO;SACpB,CAAC;QAEF,MAAM,sBAAsB,GAAG;YAC7B,OAAO,EAAE;gBACP;oBACE,kBAAkB,EAAE;wBAClB;4BACE,SAAS,EAAE,OAAO;4BAClB,UAAU,EAAE,OAAO;4BACnB,KAAK,EAAE,CAAC,aAAa,CAAC;yBACvB;qBACF;oBACD,iBAAiB,EAAE,yBAAyB;iBAC7C;aACF;YACD,MAAM,EAAE,IAAI;SACb,CAAC;QAEF,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,sBAAsB;aAC7B,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;YAE7E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7B,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CACpC,MAAM,CAAC,gBAAgB,CAAC,cAAc,CAAC,EACvC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,KAAK;aACd,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,OAAO,EAAE;wBACP;4BACE,kBAAkB,EAAE;gCAClB;oCACE,SAAS,EAAE,UAAU;oCACrB,UAAU,EAAE,IAAI;oCAChB,KAAK,EAAE,CAAC,UAAU,CAAC;iCACpB;6BACF;yBACF;qBACF;oBACD,MAAM,EAAE,IAAI;iBACb,CAAC;aACS,CAAC,CAAC;YAEf,MAAM,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;iBAClE,OAAO,CAAC,OAAO,CAAC,uBAAc,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,+BAA+B;YAC/B,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,OAAO,EAAE;wBACP;4BACE,QAAQ,EAAE;gCACR,QAAQ,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE;6BAC1C;yBACF;qBACF;oBACD,MAAM,EAAE,IAAI;iBACb,CAAC;aACS,CAAC,CAAC;YAEf,MAAM,gBAAgB,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAE3C,cAAc;YACd,gBAAgB,CAAC,UAAU,EAAE,CAAC;YAE9B,6BAA6B;YAC7B,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,OAAO,EAAE;wBACP;4BACE,QAAQ,EAAE;gCACR,QAAQ,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE;6BAC1C;yBACF;qBACF;oBACD,MAAM,EAAE,IAAI;iBACb,CAAC;aACS,CAAC,CAAC;YAEf,MAAM,gBAAgB,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,WAAW,GAAG,gBAAgB,CAAC,YAAY,EAAE,CAAC;YACpD,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE5B,yBAAyB;YACzB,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,OAAO,EAAE;wBACP;4BACE,QAAQ,EAAE;gCACR,QAAQ,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE;6BAC1C;yBACF;qBACF;oBACD,MAAM,EAAE,IAAI;iBACb,CAAC;aACS,CAAC,CAAC;YAEf,MAAM,gBAAgB,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAErD,MAAM,OAAO,GAAG,gBAAgB,CAAC,YAAY,EAAE,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,MAAM,EAAE,gBAAgB;oBACxB,aAAa,EAAE,iBAAiB;iBACjC,CAAC;aACS,CAAC,CAAC;YAEf,MAAM,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;iBACzD,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,MAAM,EAAE,kBAAkB;oBAC1B,aAAa,EAAE,sBAAsB;iBACtC,CAAC;aACS,CAAC,CAAC;YAEf,MAAM,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;iBACzD,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,OAAO,EAAE;wBACP;4BACE,iBAAiB,EAAE,cAAc;4BACjC,mBAAmB;yBACpB;qBACF;oBACD,MAAM,EAAE,IAAI;iBACb,CAAC;aACS,CAAC,CAAC;YAEf,MAAM,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;iBACzD,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,SAAS,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,CAAC;YAEtD,MAAM,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;iBACzD,OAAO,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;YACtD,OAAO,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;YAEzC,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,4BAAgB,EAAE,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;YAElF,kBAAkB;YAClB,IAAI,WAAW,EAAE,CAAC;gBAChB,OAAO,CAAC,GAAG,CAAC,qBAAqB,GAAG,WAAW,CAAC;YAClD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,4BAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,sBAAsB,GAAG;gBAC7B,OAAO,EAAE;oBACP;wBACE,kBAAkB,EAAE;4BAClB;gCACE,SAAS,EAAE,UAAU;gCACrB,UAAU,EAAE,IAAI;gCAChB,KAAK,EAAE,CAAC,UAAU,CAAC;6BACpB;4BACD;gCACE,SAAS,EAAE,OAAO;gCAClB,UAAU,EAAE,OAAO;gCACnB,KAAK,EAAE,CAAC,aAAa,CAAC;6BACvB;yBACF;qBACF;iBACF;gBACD,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,sBAAsB;aAC7B,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,oBAAoB,CAAC;gBACzD,QAAQ,EAAE,OAAO;gBACjB,SAAS,EAAE,CAAC,OAAO;aACpB,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,aAAa;aACd,CAAC,CAAC;YAEf,MAAM,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,CAAC;gBACjD,QAAQ,EAAE,OAAO;gBACjB,SAAS,EAAE,CAAC,OAAO;aACpB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,SAAS,CAAC,qBAAqB,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;YAE5D,MAAM,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,CAAC;gBACjD,QAAQ,EAAE,OAAO;gBACjB,SAAS,EAAE,CAAC,OAAO;aACpB,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,sBAAsB,GAAG;gBAC7B,OAAO,EAAE;oBACP;wBACE,kBAAkB,EAAE;4BAClB;gCACE,SAAS,EAAE,OAAO;gCAClB,UAAU,EAAE,OAAO;gCACnB,KAAK,EAAE,CAAC,aAAa,CAAC;6BACvB;yBACF;qBACF;iBACF;gBACD,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,sBAAsB;aAC7B,CAAC,CAAC;YAEf,MAAM,WAAW,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC;YAE/D,aAAa;YACb,MAAM,OAAO,GAAG,MAAM,gBAAgB,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YAEzE,+BAA+B;YAC/B,MAAM,OAAO,GAAG,MAAM,gBAAgB,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YAEzE,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9B,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/tests/services/geocoding.test.ts"], "sourcesContent": ["import { GeocodingService } from '../../src/services/geocoding';\nimport { GeocodingError, ApiError, NetworkError } from '../../src/models/Errors';\nimport { Coordinates } from '../../src/models/Business';\n\n// Mock fetch globally\nconst mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;\n\ndescribe('GeocodingService', () => {\n  let geocodingService: GeocodingService;\n\n  beforeEach(() => {\n    geocodingService = new GeocodingService();\n    mockFetch.mockClear();\n  });\n\n  describe('zipCodeToCoordinates', () => {\n    const validZipCode = '12345';\n    const mockApiResponse = {\n      results: [\n        {\n          geometry: {\n            location: {\n              lat: 40.7128,\n              lng: -74.0060\n            }\n          },\n          formatted_address: 'New York, NY 12345, USA'\n        }\n      ],\n      status: 'OK'\n    };\n\n    it('should convert valid zip code to coordinates', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => mockApiResponse\n      } as Response);\n\n      const result = await geocodingService.zipCodeToCoordinates(validZipCode);\n\n      expect(result).toEqual({\n        latitude: 40.7128,\n        longitude: -74.0060\n      });\n\n      expect(mockFetch).toHaveBeenCalledWith(\n        expect.stringContaining('geocode/json'),\n        expect.objectContaining({\n          method: 'GET'\n        })\n      );\n    });\n\n    it('should handle API errors gracefully', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: false,\n        status: 400,\n        statusText: 'Bad Request'\n      } as Response);\n\n      await expect(geocodingService.zipCodeToCoordinates(validZipCode))\n        .rejects.toThrow(ApiError);\n    });\n\n    it('should handle network errors', async () => {\n      mockFetch.mockRejectedValueOnce(new Error('Network error'));\n\n      await expect(geocodingService.zipCodeToCoordinates(validZipCode))\n        .rejects.toThrow(NetworkError);\n    });\n\n    it('should handle zero results from API', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => ({\n          results: [],\n          status: 'ZERO_RESULTS'\n        })\n      } as Response);\n\n      await expect(geocodingService.zipCodeToCoordinates(validZipCode))\n        .rejects.toThrow(GeocodingError);\n    });\n\n    it('should handle invalid API response format', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => ({\n          results: [{ invalid: 'format' }],\n          status: 'OK'\n        })\n      } as Response);\n\n      await expect(geocodingService.zipCodeToCoordinates(validZipCode))\n        .rejects.toThrow(GeocodingError);\n    });\n\n    it('should cache successful results', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => mockApiResponse\n      } as Response);\n\n      // First call\n      const result1 = await geocodingService.zipCodeToCoordinates(validZipCode);\n      \n      // Second call should use cache\n      const result2 = await geocodingService.zipCodeToCoordinates(validZipCode);\n\n      expect(result1).toEqual(result2);\n      expect(mockFetch).toHaveBeenCalledTimes(1);\n    });\n\n    it('should validate zip code format before making API call', async () => {\n      await expect(geocodingService.zipCodeToCoordinates('invalid'))\n        .rejects.toThrow(GeocodingError);\n\n      expect(mockFetch).not.toHaveBeenCalled();\n    });\n\n    it('should handle rate limiting', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: false,\n        status: 429,\n        statusText: 'Too Many Requests'\n      } as Response);\n\n      await expect(geocodingService.zipCodeToCoordinates(validZipCode))\n        .rejects.toThrow(ApiError);\n    });\n  });\n\n  describe('coordinatesToZipCode', () => {\n    const validCoordinates: Coordinates = {\n      latitude: 40.7128,\n      longitude: -74.0060\n    };\n\n    const mockReverseApiResponse = {\n      results: [\n        {\n          address_components: [\n            {\n              long_name: '12345',\n              short_name: '12345',\n              types: ['postal_code']\n            }\n          ],\n          formatted_address: 'New York, NY 12345, USA'\n        }\n      ],\n      status: 'OK'\n    };\n\n    it('should convert coordinates to zip code', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => mockReverseApiResponse\n      } as Response);\n\n      const result = await geocodingService.coordinatesToZipCode(validCoordinates);\n\n      expect(result).toBe('12345');\n      expect(mockFetch).toHaveBeenCalledWith(\n        expect.stringContaining('geocode/json'),\n        expect.objectContaining({\n          method: 'GET'\n        })\n      );\n    });\n\n    it('should handle coordinates with no postal code', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => ({\n          results: [\n            {\n              address_components: [\n                {\n                  long_name: 'New York',\n                  short_name: 'NY',\n                  types: ['locality']\n                }\n              ]\n            }\n          ],\n          status: 'OK'\n        })\n      } as Response);\n\n      await expect(geocodingService.coordinatesToZipCode(validCoordinates))\n        .rejects.toThrow(GeocodingError);\n    });\n  });\n\n  describe('clearCache', () => {\n    it('should clear the geocoding cache', async () => {\n      // Add something to cache first\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => ({\n          results: [\n            {\n              geometry: {\n                location: { lat: 40.7128, lng: -74.0060 }\n              }\n            }\n          ],\n          status: 'OK'\n        })\n      } as Response);\n\n      await geocodingService.zipCodeToCoordinates('12345');\n      expect(mockFetch).toHaveBeenCalledTimes(1);\n\n      // Clear cache\n      geocodingService.clearCache();\n\n      // Should make API call again\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => ({\n          results: [\n            {\n              geometry: {\n                location: { lat: 40.7128, lng: -74.0060 }\n              }\n            }\n          ],\n          status: 'OK'\n        })\n      } as Response);\n\n      await geocodingService.zipCodeToCoordinates('12345');\n      expect(mockFetch).toHaveBeenCalledTimes(2);\n    });\n  });\n\n  describe('getCacheSize', () => {\n    it('should return current cache size', async () => {\n      const initialSize = geocodingService.getCacheSize();\n      expect(initialSize).toBe(0);\n\n      // Add something to cache\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => ({\n          results: [\n            {\n              geometry: {\n                location: { lat: 40.7128, lng: -74.0060 }\n              }\n            }\n          ],\n          status: 'OK'\n        })\n      } as Response);\n\n      await geocodingService.zipCodeToCoordinates('12345');\n\n      const newSize = geocodingService.getCacheSize();\n      expect(newSize).toBe(1);\n    });\n  });\n\n  describe('error handling edge cases', () => {\n    it('should handle API status errors', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => ({\n          status: 'REQUEST_DENIED',\n          error_message: 'API key invalid'\n        })\n      } as Response);\n\n      await expect(geocodingService.zipCodeToCoordinates('12345'))\n        .rejects.toThrow('API key invalid');\n    });\n\n    it('should handle OVER_QUERY_LIMIT status', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => ({\n          status: 'OVER_QUERY_LIMIT',\n          error_message: 'Query limit exceeded'\n        })\n      } as Response);\n\n      await expect(geocodingService.zipCodeToCoordinates('12345'))\n        .rejects.toThrow('Query limit exceeded');\n    });\n\n    it('should handle missing geometry in response', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => ({\n          results: [\n            {\n              formatted_address: 'Some address'\n              // Missing geometry\n            }\n          ],\n          status: 'OK'\n        })\n      } as Response);\n\n      await expect(geocodingService.zipCodeToCoordinates('12345'))\n        .rejects.toThrow('Invalid response format');\n    });\n\n    it('should handle unknown errors', async () => {\n      mockFetch.mockRejectedValueOnce('unknown error type');\n\n      await expect(geocodingService.zipCodeToCoordinates('12345'))\n        .rejects.toThrow('Unknown error during geocoding');\n    });\n  });\n\n  describe('constructor error handling', () => {\n    it('should throw ConfigurationError when no API key provided', () => {\n      const originalKey = process.env.GOOGLE_PLACES_API_KEY;\n      delete process.env.GOOGLE_PLACES_API_KEY;\n\n      expect(() => new GeocodingService()).toThrow('Google Places API key is required');\n\n      // Restore API key\n      if (originalKey) {\n        process.env.GOOGLE_PLACES_API_KEY = originalKey;\n      }\n    });\n\n    it('should accept API key from constructor parameter', () => {\n      expect(() => new GeocodingService('test-api-key')).not.toThrow();\n    });\n  });\n\n  describe('coordinatesToZipCode edge cases', () => {\n    it('should handle multiple address components', async () => {\n      const mockReverseApiResponse = {\n        results: [\n          {\n            address_components: [\n              {\n                long_name: 'New York',\n                short_name: 'NY',\n                types: ['locality']\n              },\n              {\n                long_name: '12345',\n                short_name: '12345',\n                types: ['postal_code']\n              }\n            ]\n          }\n        ],\n        status: 'OK'\n      };\n\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => mockReverseApiResponse\n      } as Response);\n\n      const result = await geocodingService.coordinatesToZipCode({\n        latitude: 40.7128,\n        longitude: -74.0060\n      });\n\n      expect(result).toBe('12345');\n    });\n\n    it('should handle API errors in reverse geocoding', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: false,\n        status: 400,\n        statusText: 'Bad Request'\n      } as Response);\n\n      await expect(geocodingService.coordinatesToZipCode({\n        latitude: 40.7128,\n        longitude: -74.0060\n      })).rejects.toThrow('Reverse geocoding API request failed');\n    });\n\n    it('should handle network errors in reverse geocoding', async () => {\n      mockFetch.mockRejectedValueOnce(new Error('Network error'));\n\n      await expect(geocodingService.coordinatesToZipCode({\n        latitude: 40.7128,\n        longitude: -74.0060\n      })).rejects.toThrow('Network error during reverse geocoding');\n    });\n\n    it('should cache reverse geocoding results', async () => {\n      const mockReverseApiResponse = {\n        results: [\n          {\n            address_components: [\n              {\n                long_name: '12345',\n                short_name: '12345',\n                types: ['postal_code']\n              }\n            ]\n          }\n        ],\n        status: 'OK'\n      };\n\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        json: async () => mockReverseApiResponse\n      } as Response);\n\n      const coordinates = { latitude: 40.7128, longitude: -74.0060 };\n\n      // First call\n      const result1 = await geocodingService.coordinatesToZipCode(coordinates);\n\n      // Second call should use cache\n      const result2 = await geocodingService.coordinatesToZipCode(coordinates);\n\n      expect(result1).toBe(result2);\n      expect(mockFetch).toHaveBeenCalledTimes(1);\n    });\n  });\n});\n"], "version": 3}