23cf84ed5826ba0d8ab6bd2a80946147
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebsiteVerificationService = void 0;
const Errors_1 = require("../models/Errors");
const url_1 = require("../utils/url");
const constants_1 = require("../constants");
/**
 * Service for verifying website accessibility and status
 */
class WebsiteVerificationService {
    constructor() {
        this.cache = new Map();
        this.stats = {
            totalVerified: 0,
            successfulVerifications: 0,
            failedVerifications: 0,
            totalResponseTime: 0,
        };
    }
    /**
     * Verifies if a website is accessible
     * @param url - The URL to verify
     * @returns Promise resolving to verification result
     */
    async verifyWebsite(url) {
        // Validate and normalize URL
        if (!(0, url_1.isValidUrl)(url)) {
            throw new Errors_1.WebsiteVerificationError(`Invalid URL format: ${url}`, url);
        }
        const normalizedUrl = (0, url_1.normalizeUrl)(url);
        // Check cache first
        const cached = this.cache.get(normalizedUrl);
        if (cached) {
            return cached;
        }
        const startTime = Date.now();
        let result;
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), constants_1.WEBSITE_CONFIG.TIMEOUT);
            const response = await fetch(normalizedUrl, {
                method: 'HEAD',
                headers: {
                    'User-Agent': constants_1.WEBSITE_CONFIG.USER_AGENT,
                },
                signal: controller.signal,
                redirect: 'manual', // Handle redirects manually to track them
            });
            clearTimeout(timeoutId);
            const responseTime = Date.now() - startTime;
            const contentType = response.headers.get('content-type') || '';
            // Check if the status code indicates success
            const isAccessible = constants_1.WEBSITE_CONFIG.VALID_STATUS_CODES.includes(response.status);
            const confidence = this.calculateConfidence(response.status, contentType, responseTime);
            result = {
                url: normalizedUrl,
                status: isAccessible ? 'verified' : 'unverified',
                accessible: isAccessible,
                statusCode: response.status,
                responseTime,
                contentType,
                confidence,
                verifiedAt: new Date(),
            };
            if (isAccessible) {
                this.stats.successfulVerifications++;
            }
            else {
                this.stats.failedVerifications++;
            }
            this.stats.totalResponseTime += responseTime;
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            let errorMessage = 'Unknown error';
            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    errorMessage = 'Request timeout';
                }
                else {
                    errorMessage = error.message;
                }
            }
            result = {
                url: normalizedUrl,
                status: 'unverified',
                accessible: false,
                responseTime,
                confidence: 0.1,
                error: errorMessage,
                verifiedAt: new Date(),
            };
            this.stats.failedVerifications++;
            this.stats.totalResponseTime += responseTime;
        }
        this.stats.totalVerified++;
        // Cache the result
        this.cache.set(normalizedUrl, result);
        return result;
    }
    /**
     * Verifies multiple websites concurrently
     * @param urls - Array of URLs to verify
     * @param concurrency - Maximum number of concurrent requests
     * @returns Promise resolving to array of verification results
     */
    async verifyMultipleWebsites(urls, concurrency = 5) {
        const results = [];
        // Process URLs in batches to respect concurrency limit
        for (let i = 0; i < urls.length; i += concurrency) {
            const batch = urls.slice(i, i + concurrency);
            const batchPromises = batch.map(url => this.verifyWebsite(url).catch(error => ({
                url,
                status: 'unverified',
                accessible: false,
                confidence: 0,
                error: error.message,
                verifiedAt: new Date(),
            })));
            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);
        }
        return results;
    }
    /**
     * Calculates confidence score for website verification
     * @param statusCode - HTTP status code
     * @param contentType - Response content type
     * @param responseTime - Response time in milliseconds
     * @returns Confidence score between 0 and 1
     */
    calculateConfidence(statusCode, contentType, responseTime) {
        let confidence = 0;
        // Base confidence from status code
        if (statusCode >= 200 && statusCode < 300) {
            confidence += 0.6; // Success responses
        }
        else if (statusCode >= 300 && statusCode < 400) {
            confidence += 0.4; // Redirects
        }
        else if (statusCode >= 400 && statusCode < 500) {
            confidence += 0.05; // Client errors
        }
        else {
            confidence += 0.05; // Server errors
        }
        // Bonus for HTML content
        if (contentType.includes('text/html')) {
            confidence += 0.2;
        }
        else if (contentType.includes('text/')) {
            confidence += 0.1;
        }
        // Response time factor (faster is better)
        if (responseTime < 1000) {
            confidence += 0.2;
        }
        else if (responseTime < 3000) {
            confidence += 0.1;
        }
        else if (responseTime > 5000) {
            confidence -= 0.1;
        }
        return Math.max(0, Math.min(1, confidence));
    }
    /**
     * Gets verification statistics
     * @returns Verification statistics
     */
    getVerificationStats() {
        return {
            totalVerified: this.stats.totalVerified,
            successfulVerifications: this.stats.successfulVerifications,
            failedVerifications: this.stats.failedVerifications,
            successRate: this.stats.totalVerified > 0
                ? this.stats.successfulVerifications / this.stats.totalVerified
                : 0,
            averageResponseTime: this.stats.totalVerified > 0
                ? this.stats.totalResponseTime / this.stats.totalVerified
                : 0,
        };
    }
    /**
     * Clears the verification cache
     */
    clearCache() {
        this.cache.clear();
    }
    /**
     * Gets the current cache size
     * @returns Number of cached verification results
     */
    getCacheSize() {
        return this.cache.size;
    }
    /**
     * Resets verification statistics
     */
    resetStats() {
        this.stats = {
            totalVerified: 0,
            successfulVerifications: 0,
            failedVerifications: 0,
            totalResponseTime: 0,
        };
    }
}
exports.WebsiteVerificationService = WebsiteVerificationService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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