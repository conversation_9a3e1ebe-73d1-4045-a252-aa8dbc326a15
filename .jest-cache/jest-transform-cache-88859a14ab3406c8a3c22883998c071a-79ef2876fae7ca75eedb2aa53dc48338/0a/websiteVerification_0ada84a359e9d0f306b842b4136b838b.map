{"file": "/Users/<USER>/WebstormProjects/goo/src/services/websiteVerification.ts", "mappings": ";;;AACA,6CAAwE;AAExE,sCAAsD;AACtD,4CAA4C;AA4B5C;;GAEG;AACH,MAAa,0BAA0B;IAAvC;QACY,UAAK,GAAG,IAAI,GAAG,EAA8B,CAAC;QAC9C,UAAK,GAAG;YACZ,aAAa,EAAE,CAAC;YAChB,uBAAuB,EAAE,CAAC;YAC1B,mBAAmB,EAAE,CAAC;YACtB,iBAAiB,EAAE,CAAC;SACvB,CAAC;IAsNN,CAAC;IApNG;;;;OAIG;IACH,KAAK,CAAC,aAAa,CAAC,GAAW;QAC3B,6BAA6B;QAC7B,IAAI,CAAC,IAAA,gBAAU,EAAC,GAAG,CAAC,EAAE,CAAC;YACnB,MAAM,IAAI,iCAAwB,CAAC,uBAAuB,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,aAAa,GAAG,IAAA,kBAAY,EAAC,GAAG,CAAC,CAAC;QAExC,oBAAoB;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC7C,IAAI,MAAM,EAAE,CAAC;YACT,OAAO,MAAM,CAAC;QAClB,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,MAA0B,CAAC;QAE/B,IAAI,CAAC;YACD,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;YACzC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,0BAAc,CAAC,OAAO,CAAC,CAAC;YAE/E,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,aAAa,EAAE;gBACxC,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACL,YAAY,EAAE,0BAAc,CAAC,UAAU;iBAC1C;gBACD,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,QAAQ,EAAE,QAAQ,EAAE,0CAA0C;aACjE,CAAC,CAAC;YAEH,YAAY,CAAC,SAAS,CAAC,CAAC;YACxB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC5C,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;YAE/D,6CAA6C;YAC7C,MAAM,YAAY,GAAG,0BAAc,CAAC,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACjF,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,MAAM,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;YAExF,MAAM,GAAG;gBACL,GAAG,EAAE,aAAa;gBAClB,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY;gBAChD,UAAU,EAAE,YAAY;gBACxB,UAAU,EAAE,QAAQ,CAAC,MAAM;gBAC3B,YAAY;gBACZ,WAAW;gBACX,UAAU;gBACV,UAAU,EAAE,IAAI,IAAI,EAAE;aACzB,CAAC;YAEF,IAAI,YAAY,EAAE,CAAC;gBACf,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,CAAC;YACzC,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC;YACrC,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,iBAAiB,IAAI,YAAY,CAAC;QAEjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC5C,IAAI,YAAY,GAAG,eAAe,CAAC;YAEnC,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBACzB,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;oBAC9B,YAAY,GAAG,iBAAiB,CAAC;gBACrC,CAAC;qBAAM,CAAC;oBACJ,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;gBACjC,CAAC;YACL,CAAC;YAED,MAAM,GAAG;gBACL,GAAG,EAAE,aAAa;gBAClB,MAAM,EAAE,YAAY;gBACpB,UAAU,EAAE,KAAK;gBACjB,YAAY;gBACZ,UAAU,EAAE,GAAG;gBACf,KAAK,EAAE,YAAY;gBACnB,UAAU,EAAE,IAAI,IAAI,EAAE;aACzB,CAAC;YAEF,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,iBAAiB,IAAI,YAAY,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;QAE3B,mBAAmB;QACnB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QAEtC,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,sBAAsB,CACxB,IAAc,EACd,cAAsB,CAAC;QAEvB,MAAM,OAAO,GAAyB,EAAE,CAAC;QAEzC,uDAAuD;QACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,WAAW,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC;YAC7C,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAClC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACpC,GAAG;gBACH,MAAM,EAAE,YAA6B;gBACrC,UAAU,EAAE,KAAK;gBACjB,UAAU,EAAE,CAAC;gBACb,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,UAAU,EAAE,IAAI,IAAI,EAAE;aACzB,CAAC,CAAC,CACN,CAAC;YAEF,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;QAClC,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;OAMG;IACH,mBAAmB,CAAC,UAAkB,EAAE,WAAmB,EAAE,YAAoB;QAC7E,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,mCAAmC;QACnC,IAAI,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YACxC,UAAU,IAAI,GAAG,CAAC,CAAC,oBAAoB;QAC3C,CAAC;aAAM,IAAI,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YAC/C,UAAU,IAAI,GAAG,CAAC,CAAC,YAAY;QACnC,CAAC;aAAM,IAAI,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YAC/C,UAAU,IAAI,IAAI,CAAC,CAAC,gBAAgB;QACxC,CAAC;aAAM,CAAC;YACJ,UAAU,IAAI,IAAI,CAAC,CAAC,gBAAgB;QACxC,CAAC;QAED,yBAAyB;QACzB,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACpC,UAAU,IAAI,GAAG,CAAC;QACtB,CAAC;aAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACvC,UAAU,IAAI,GAAG,CAAC;QACtB,CAAC;QAED,0CAA0C;QAC1C,IAAI,YAAY,GAAG,IAAI,EAAE,CAAC;YACtB,UAAU,IAAI,GAAG,CAAC;QACtB,CAAC;aAAM,IAAI,YAAY,GAAG,IAAI,EAAE,CAAC;YAC7B,UAAU,IAAI,GAAG,CAAC;QACtB,CAAC;aAAM,IAAI,YAAY,GAAG,IAAI,EAAE,CAAC;YAC7B,UAAU,IAAI,GAAG,CAAC;QACtB,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;IAChD,CAAC;IAED;;;OAGG;IACH,oBAAoB;QAChB,OAAO;YACH,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;YACvC,uBAAuB,EAAE,IAAI,CAAC,KAAK,CAAC,uBAAuB;YAC3D,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB;YACnD,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC;gBACrC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,uBAAuB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa;gBAC/D,CAAC,CAAC,CAAC;YACP,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC;gBAC7C,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa;gBACzD,CAAC,CAAC,CAAC;SACV,CAAC;IACN,CAAC;IAED;;OAEG;IACH,UAAU;QACN,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;IAED;;;OAGG;IACH,YAAY;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,UAAU;QACN,IAAI,CAAC,KAAK,GAAG;YACT,aAAa,EAAE,CAAC;YAChB,uBAAuB,EAAE,CAAC;YAC1B,mBAAmB,EAAE,CAAC;YACtB,iBAAiB,EAAE,CAAC;SACvB,CAAC;IACN,CAAC;CACJ;AA7ND,gEA6NC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/src/services/websiteVerification.ts"], "sourcesContent": ["import {WebsiteStatus} from '../models/Business';\nimport {WebsiteVerificationError, NetworkError} from '../models/Errors';\nimport {validateUrl} from '../utils/validation';\nimport {normalizeUrl, isValidUrl} from '../utils/url';\nimport {WEBSITE_CONFIG} from '../constants';\n\n/**\n * Website verification result\n */\nexport interface VerificationResult {\n    url: string;\n    status: WebsiteStatus;\n    accessible: boolean;\n    statusCode?: number;\n    responseTime?: number;\n    contentType?: string;\n    confidence: number;\n    error?: string;\n    verifiedAt: Date;\n}\n\n/**\n * Website verification statistics\n */\nexport interface VerificationStats {\n    totalVerified: number;\n    successfulVerifications: number;\n    failedVerifications: number;\n    successRate: number;\n    averageResponseTime: number;\n}\n\n/**\n * Service for verifying website accessibility and status\n */\nexport class WebsiteVerificationService {\n    private cache = new Map<string, VerificationResult>();\n    private stats = {\n        totalVerified: 0,\n        successfulVerifications: 0,\n        failedVerifications: 0,\n        totalResponseTime: 0,\n    };\n\n    /**\n     * Verifies if a website is accessible\n     * @param url - The URL to verify\n     * @returns Promise resolving to verification result\n     */\n    async verifyWebsite(url: string): Promise<VerificationResult> {\n        // Validate and normalize URL\n        if (!isValidUrl(url)) {\n            throw new WebsiteVerificationError(`Invalid URL format: ${url}`, url);\n        }\n\n        const normalizedUrl = normalizeUrl(url);\n\n        // Check cache first\n        const cached = this.cache.get(normalizedUrl);\n        if (cached) {\n            return cached;\n        }\n\n        const startTime = Date.now();\n        let result: VerificationResult;\n\n        try {\n            const controller = new AbortController();\n            const timeoutId = setTimeout(() => controller.abort(), WEBSITE_CONFIG.TIMEOUT);\n\n            const response = await fetch(normalizedUrl, {\n                method: 'HEAD',\n                headers: {\n                    'User-Agent': WEBSITE_CONFIG.USER_AGENT,\n                },\n                signal: controller.signal,\n                redirect: 'manual', // Handle redirects manually to track them\n            });\n\n            clearTimeout(timeoutId);\n            const responseTime = Date.now() - startTime;\n            const contentType = response.headers.get('content-type') || '';\n\n            // Check if the status code indicates success\n            const isAccessible = WEBSITE_CONFIG.VALID_STATUS_CODES.includes(response.status);\n            const confidence = this.calculateConfidence(response.status, contentType, responseTime);\n\n            result = {\n                url: normalizedUrl,\n                status: isAccessible ? 'verified' : 'unverified',\n                accessible: isAccessible,\n                statusCode: response.status,\n                responseTime,\n                contentType,\n                confidence,\n                verifiedAt: new Date(),\n            };\n\n            if (isAccessible) {\n                this.stats.successfulVerifications++;\n            } else {\n                this.stats.failedVerifications++;\n            }\n            this.stats.totalResponseTime += responseTime;\n\n        } catch (error) {\n            const responseTime = Date.now() - startTime;\n            let errorMessage = 'Unknown error';\n\n            if (error instanceof Error) {\n                if (error.name === 'AbortError') {\n                    errorMessage = 'Request timeout';\n                } else {\n                    errorMessage = error.message;\n                }\n            }\n\n            result = {\n                url: normalizedUrl,\n                status: 'unverified',\n                accessible: false,\n                responseTime,\n                confidence: 0.1,\n                error: errorMessage,\n                verifiedAt: new Date(),\n            };\n\n            this.stats.failedVerifications++;\n            this.stats.totalResponseTime += responseTime;\n        }\n\n        this.stats.totalVerified++;\n\n        // Cache the result\n        this.cache.set(normalizedUrl, result);\n\n        return result;\n    }\n\n    /**\n     * Verifies multiple websites concurrently\n     * @param urls - Array of URLs to verify\n     * @param concurrency - Maximum number of concurrent requests\n     * @returns Promise resolving to array of verification results\n     */\n    async verifyMultipleWebsites(\n        urls: string[],\n        concurrency: number = 5\n    ): Promise<VerificationResult[]> {\n        const results: VerificationResult[] = [];\n\n        // Process URLs in batches to respect concurrency limit\n        for (let i = 0; i < urls.length; i += concurrency) {\n            const batch = urls.slice(i, i + concurrency);\n            const batchPromises = batch.map(url =>\n                this.verifyWebsite(url).catch(error => ({\n                    url,\n                    status: 'unverified' as WebsiteStatus,\n                    accessible: false,\n                    confidence: 0,\n                    error: error.message,\n                    verifiedAt: new Date(),\n                }))\n            );\n\n            const batchResults = await Promise.all(batchPromises);\n            results.push(...batchResults);\n        }\n\n        return results;\n    }\n\n    /**\n     * Calculates confidence score for website verification\n     * @param statusCode - HTTP status code\n     * @param contentType - Response content type\n     * @param responseTime - Response time in milliseconds\n     * @returns Confidence score between 0 and 1\n     */\n    calculateConfidence(statusCode: number, contentType: string, responseTime: number): number {\n        let confidence = 0;\n\n        // Base confidence from status code\n        if (statusCode >= 200 && statusCode < 300) {\n            confidence += 0.6; // Success responses\n        } else if (statusCode >= 300 && statusCode < 400) {\n            confidence += 0.4; // Redirects\n        } else if (statusCode >= 400 && statusCode < 500) {\n            confidence += 0.05; // Client errors\n        } else {\n            confidence += 0.05; // Server errors\n        }\n\n        // Bonus for HTML content\n        if (contentType.includes('text/html')) {\n            confidence += 0.2;\n        } else if (contentType.includes('text/')) {\n            confidence += 0.1;\n        }\n\n        // Response time factor (faster is better)\n        if (responseTime < 1000) {\n            confidence += 0.2;\n        } else if (responseTime < 3000) {\n            confidence += 0.1;\n        } else if (responseTime > 5000) {\n            confidence -= 0.1;\n        }\n\n        return Math.max(0, Math.min(1, confidence));\n    }\n\n    /**\n     * Gets verification statistics\n     * @returns Verification statistics\n     */\n    getVerificationStats(): VerificationStats {\n        return {\n            totalVerified: this.stats.totalVerified,\n            successfulVerifications: this.stats.successfulVerifications,\n            failedVerifications: this.stats.failedVerifications,\n            successRate: this.stats.totalVerified > 0\n                ? this.stats.successfulVerifications / this.stats.totalVerified\n                : 0,\n            averageResponseTime: this.stats.totalVerified > 0\n                ? this.stats.totalResponseTime / this.stats.totalVerified\n                : 0,\n        };\n    }\n\n    /**\n     * Clears the verification cache\n     */\n    clearCache(): void {\n        this.cache.clear();\n    }\n\n    /**\n     * Gets the current cache size\n     * @returns Number of cached verification results\n     */\n    getCacheSize(): number {\n        return this.cache.size;\n    }\n\n    /**\n     * Resets verification statistics\n     */\n    resetStats(): void {\n        this.stats = {\n            totalVerified: 0,\n            successfulVerifications: 0,\n            failedVerifications: 0,\n            totalResponseTime: 0,\n        };\n    }\n}\n"], "version": 3}