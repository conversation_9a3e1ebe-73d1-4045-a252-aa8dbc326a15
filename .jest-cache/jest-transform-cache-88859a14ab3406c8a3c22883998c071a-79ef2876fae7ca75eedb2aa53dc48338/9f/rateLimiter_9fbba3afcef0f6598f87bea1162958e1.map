{"file": "/Users/<USER>/WebstormProjects/goo/src/utils/rateLimiter.ts", "mappings": ";;;AAqIA,oDAQC;AAKD,wDAEC;AApJD,6CAAkD;AAElD;;;GAGG;AACH,MAAa,sBAAsB;IAKjC;;;;OAIG;IACH,YACmB,eAAuB,EACvB,UAAkB;QADlB,oBAAe,GAAf,eAAe,CAAQ;QACvB,eAAU,GAAV,UAAU,CAAQ;QAEnC,IAAI,eAAe,IAAI,CAAC,IAAI,UAAU,IAAI,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,eAAe,CAAC,CAAC,eAAe;IAC/D,CAAC;IAED;;;OAGG;IACI,UAAU;QACf,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;YACjB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,YAAY,CAAC,YAAoB,IAAI;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,UAAU,GAAG,GAAG,EAAE;gBACtB,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;oBACtB,OAAO,EAAE,CAAC;oBACV,OAAO;gBACT,CAAC;gBAED,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,IAAI,SAAS,EAAE,CAAC;oBACxC,MAAM,CAAC,IAAI,uBAAc,CAAC,6BAA6B,CAAC,CAAC,CAAC;oBAC1D,OAAO;gBACT,CAAC;gBAED,kCAAkC;gBAClC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAChE,CAAC,CAAC;YAEF,UAAU,EAAE,CAAC;QACf,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,kBAAkB;QACvB,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,KAAK;QACV,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,qBAAqB;QAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACrB,OAAO,CAAC,CAAC;QACX,CAAC;QAED,MAAM,mBAAmB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC;QACzD,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;QAE3F,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,YAAY;QAClB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;QAEzC,IAAI,UAAU,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;YACjE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC;YACnE,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;CACF;AAlHD,wDAkHC;AAED;;GAEG;AACH,IAAI,iBAAiB,GAAkC,IAAI,CAAC;AAE5D;;;;;GAKG;AACH,SAAgB,oBAAoB,CAClC,kBAA0B,EAAE,EAC5B,aAAqB,EAAE;IAEvB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvB,iBAAiB,GAAG,IAAI,sBAAsB,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;IAC9E,CAAC;IACD,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED;;GAEG;AACH,SAAgB,sBAAsB;IACpC,iBAAiB,GAAG,IAAI,CAAC;AAC3B,CAAC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/src/utils/rateLimiter.ts"], "sourcesContent": ["import { RateLimitError } from '../models/Errors';\n\n/**\n * Token bucket rate limiter implementation\n * Allows burst requests up to bucket size, then limits to refill rate\n */\nexport class TokenBucketRateLimiter {\n  private tokens: number;\n  private lastRefill: number;\n  private readonly refillInterval: number; // milliseconds per token\n\n  /**\n   * Creates a new token bucket rate limiter\n   * @param tokensPerSecond - Number of tokens to add per second\n   * @param bucketSize - Maximum number of tokens in bucket\n   */\n  constructor(\n    private readonly tokensPerSecond: number,\n    private readonly bucketSize: number\n  ) {\n    if (tokensPerSecond <= 0 || bucketSize <= 0) {\n      throw new Error('Tokens per second and bucket size must be positive');\n    }\n\n    this.tokens = bucketSize;\n    this.lastRefill = Date.now();\n    this.refillInterval = 1000 / tokensPerSecond; // ms per token\n  }\n\n  /**\n   * Attempts to consume a token from the bucket\n   * @returns True if token was consumed, false if bucket is empty\n   */\n  public tryConsume(): boolean {\n    this.refillTokens();\n\n    if (this.tokens >= 1) {\n      this.tokens -= 1;\n      return true;\n    }\n\n    return false;\n  }\n\n  /**\n   * Waits for a token to become available\n   * @param timeoutMs - Maximum time to wait in milliseconds\n   * @returns Promise that resolves when token is available\n   * @throws RateLimitError if timeout is reached\n   */\n  public async waitForToken(timeoutMs: number = 5000): Promise<void> {\n    const startTime = Date.now();\n\n    return new Promise((resolve, reject) => {\n      const checkToken = () => {\n        if (this.tryConsume()) {\n          resolve();\n          return;\n        }\n\n        if (Date.now() - startTime >= timeoutMs) {\n          reject(new RateLimitError('Rate limit timeout exceeded'));\n          return;\n        }\n\n        // Check again after a short delay\n        setTimeout(checkToken, Math.min(this.refillInterval / 2, 50));\n      };\n\n      checkToken();\n    });\n  }\n\n  /**\n   * Gets the number of available tokens\n   * @returns Number of available tokens\n   */\n  public getAvailableTokens(): number {\n    this.refillTokens();\n    return Math.floor(this.tokens);\n  }\n\n  /**\n   * Resets the bucket to full capacity\n   */\n  public reset(): void {\n    this.tokens = this.bucketSize;\n    this.lastRefill = Date.now();\n  }\n\n  /**\n   * Gets the time until next token is available\n   * @returns Milliseconds until next token, or 0 if tokens are available\n   */\n  public getTimeUntilNextToken(): number {\n    this.refillTokens();\n\n    if (this.tokens >= 1) {\n      return 0;\n    }\n\n    const timeSinceLastRefill = Date.now() - this.lastRefill;\n    const timeForNextToken = this.refillInterval - (timeSinceLastRefill % this.refillInterval);\n    \n    return Math.max(0, timeForNextToken);\n  }\n\n  /**\n   * Refills tokens based on elapsed time\n   */\n  private refillTokens(): void {\n    const now = Date.now();\n    const timePassed = now - this.lastRefill;\n\n    if (timePassed >= this.refillInterval) {\n      const tokensToAdd = Math.floor(timePassed / this.refillInterval);\n      this.tokens = Math.min(this.bucketSize, this.tokens + tokensToAdd);\n      this.lastRefill = now - (timePassed % this.refillInterval);\n    }\n  }\n}\n\n/**\n * Global rate limiter instance for API requests\n */\nlet globalRateLimiter: TokenBucketRateLimiter | null = null;\n\n/**\n * Gets or creates the global rate limiter instance\n * @param tokensPerSecond - Tokens per second (default from config)\n * @param bucketSize - Bucket size (default from config)\n * @returns Global rate limiter instance\n */\nexport function getGlobalRateLimiter(\n  tokensPerSecond: number = 10,\n  bucketSize: number = 20\n): TokenBucketRateLimiter {\n  if (!globalRateLimiter) {\n    globalRateLimiter = new TokenBucketRateLimiter(tokensPerSecond, bucketSize);\n  }\n  return globalRateLimiter;\n}\n\n/**\n * Resets the global rate limiter\n */\nexport function resetGlobalRateLimiter(): void {\n  globalRateLimiter = null;\n}\n"], "version": 3}