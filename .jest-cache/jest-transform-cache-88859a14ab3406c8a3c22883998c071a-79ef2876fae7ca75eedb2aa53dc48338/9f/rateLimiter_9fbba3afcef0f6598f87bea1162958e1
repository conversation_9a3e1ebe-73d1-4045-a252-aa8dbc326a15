5aed17bf1f293ef65478a2036e81c0eb
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenBucketRateLimiter = void 0;
exports.getGlobalRateLimiter = getGlobalRateLimiter;
exports.resetGlobalRateLimiter = resetGlobalRateLimiter;
const Errors_1 = require("../models/Errors");
/**
 * Token bucket rate limiter implementation
 * Allows burst requests up to bucket size, then limits to refill rate
 */
class TokenBucketRateLimiter {
    /**
     * Creates a new token bucket rate limiter
     * @param tokensPerSecond - Number of tokens to add per second
     * @param bucketSize - Maximum number of tokens in bucket
     */
    constructor(tokensPerSecond, bucketSize) {
        this.tokensPerSecond = tokensPerSecond;
        this.bucketSize = bucketSize;
        if (tokensPerSecond <= 0 || bucketSize <= 0) {
            throw new Error('Tokens per second and bucket size must be positive');
        }
        this.tokens = bucketSize;
        this.lastRefill = Date.now();
        this.refillInterval = 1000 / tokensPerSecond; // ms per token
    }
    /**
     * Attempts to consume a token from the bucket
     * @returns True if token was consumed, false if bucket is empty
     */
    tryConsume() {
        this.refillTokens();
        if (this.tokens >= 1) {
            this.tokens -= 1;
            return true;
        }
        return false;
    }
    /**
     * Waits for a token to become available
     * @param timeoutMs - Maximum time to wait in milliseconds
     * @returns Promise that resolves when token is available
     * @throws RateLimitError if timeout is reached
     */
    async waitForToken(timeoutMs = 5000) {
        const startTime = Date.now();
        return new Promise((resolve, reject) => {
            const checkToken = () => {
                if (this.tryConsume()) {
                    resolve();
                    return;
                }
                if (Date.now() - startTime >= timeoutMs) {
                    reject(new Errors_1.RateLimitError('Rate limit timeout exceeded'));
                    return;
                }
                // Check again after a short delay
                setTimeout(checkToken, Math.min(this.refillInterval / 2, 50));
            };
            checkToken();
        });
    }
    /**
     * Gets the number of available tokens
     * @returns Number of available tokens
     */
    getAvailableTokens() {
        this.refillTokens();
        return Math.floor(this.tokens);
    }
    /**
     * Resets the bucket to full capacity
     */
    reset() {
        this.tokens = this.bucketSize;
        this.lastRefill = Date.now();
    }
    /**
     * Gets the time until next token is available
     * @returns Milliseconds until next token, or 0 if tokens are available
     */
    getTimeUntilNextToken() {
        this.refillTokens();
        if (this.tokens >= 1) {
            return 0;
        }
        const timeSinceLastRefill = Date.now() - this.lastRefill;
        const timeForNextToken = this.refillInterval - (timeSinceLastRefill % this.refillInterval);
        return Math.max(0, timeForNextToken);
    }
    /**
     * Refills tokens based on elapsed time
     */
    refillTokens() {
        const now = Date.now();
        const timePassed = now - this.lastRefill;
        if (timePassed >= this.refillInterval) {
            const tokensToAdd = Math.floor(timePassed / this.refillInterval);
            this.tokens = Math.min(this.bucketSize, this.tokens + tokensToAdd);
            this.lastRefill = now - (timePassed % this.refillInterval);
        }
    }
}
exports.TokenBucketRateLimiter = TokenBucketRateLimiter;
/**
 * Global rate limiter instance for API requests
 */
let globalRateLimiter = null;
/**
 * Gets or creates the global rate limiter instance
 * @param tokensPerSecond - Tokens per second (default from config)
 * @param bucketSize - Bucket size (default from config)
 * @returns Global rate limiter instance
 */
function getGlobalRateLimiter(tokensPerSecond = 10, bucketSize = 20) {
    if (!globalRateLimiter) {
        globalRateLimiter = new TokenBucketRateLimiter(tokensPerSecond, bucketSize);
    }
    return globalRateLimiter;
}
/**
 * Resets the global rate limiter
 */
function resetGlobalRateLimiter() {
    globalRateLimiter = null;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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