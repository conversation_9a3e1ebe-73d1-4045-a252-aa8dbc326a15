8b5575b654ea5cafbe654c45b2db95f1
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateDistance = calculateDistance;
exports.calculateDistanceKm = calculateDistanceKm;
exports.findClosest = findClosest;
exports.isWithinRadius = isWithinRadius;
exports.sortByDistance = sortByDistance;
/**
 * Calculates the distance between two coordinates using the Haversine formula
 * @param coord1 - First coordinate
 * @param coord2 - Second coordinate
 * @returns Distance in miles
 */
function calculateDistance(coord1, coord2) {
    const R = 3959; // Earth's radius in miles
    const dLat = toRadians(coord2.latitude - coord1.latitude);
    const dLon = toRadians(coord2.longitude - coord1.longitude);
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(toRadians(coord1.latitude)) * Math.cos(toRadians(coord2.latitude)) *
            Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    return Math.round(distance * 100) / 100; // Round to 2 decimal places
}
/**
 * Converts degrees to radians
 * @param degrees - Angle in degrees
 * @returns Angle in radians
 */
function toRadians(degrees) {
    return degrees * (Math.PI / 180);
}
/**
 * Calculates the distance between two coordinates in kilometers
 * @param coord1 - First coordinate
 * @param coord2 - Second coordinate
 * @returns Distance in kilometers
 */
function calculateDistanceKm(coord1, coord2) {
    const distanceInMiles = calculateDistance(coord1, coord2);
    return Math.round(distanceInMiles * 1.60934 * 100) / 100; // Convert to km and round
}
/**
 * Finds the closest coordinate from an array of coordinates
 * @param target - Target coordinate
 * @param coordinates - Array of coordinates to search
 * @returns Closest coordinate and its distance
 */
function findClosest(target, coordinates) {
    if (coordinates.length === 0) {
        return null;
    }
    let closest = coordinates[0];
    let minDistance = calculateDistance(target, closest);
    for (let i = 1; i < coordinates.length; i++) {
        const distance = calculateDistance(target, coordinates[i]);
        if (distance < minDistance) {
            minDistance = distance;
            closest = coordinates[i];
        }
    }
    return { coordinate: closest, distance: minDistance };
}
/**
 * Checks if a coordinate is within a certain radius of a center point
 * @param center - Center coordinate
 * @param point - Point to check
 * @param radiusMiles - Radius in miles
 * @returns True if point is within radius
 */
function isWithinRadius(center, point, radiusMiles) {
    const distance = calculateDistance(center, point);
    return distance <= radiusMiles;
}
/**
 * Sorts an array of coordinates by distance from a target point
 * @param target - Target coordinate
 * @param coordinates - Array of coordinates to sort
 * @returns Sorted array with distances
 */
function sortByDistance(target, coordinates) {
    return coordinates
        .map(coord => ({
        coordinate: coord,
        distance: calculateDistance(target, coord)
    }))
        .sort((a, b) => a.distance - b.distance);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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