{"file": "/Users/<USER>/WebstormProjects/goo/src/utils/distance.ts", "mappings": ";;AAQA,8CAcC;AAiBD,kDAGC;AAQD,kCAoBC;AASD,wCAOC;AAQD,wCAUC;AAtGD;;;;;GAKG;AACH,SAAgB,iBAAiB,CAAC,MAAmB,EAAE,MAAmB;IACxE,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,0BAA0B;IAC1C,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1D,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;IAE5D,MAAM,CAAC,GACL,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;QACvC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC3E,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;IAE1C,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzD,MAAM,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;IAEvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,4BAA4B;AACvE,CAAC;AAED;;;;GAIG;AACH,SAAS,SAAS,CAAC,OAAe;IAChC,OAAO,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;AACnC,CAAC;AAED;;;;;GAKG;AACH,SAAgB,mBAAmB,CAAC,MAAmB,EAAE,MAAmB;IAC1E,MAAM,eAAe,GAAG,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC1D,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,0BAA0B;AACtF,CAAC;AAED;;;;;GAKG;AACH,SAAgB,WAAW,CACzB,MAAmB,EACnB,WAA0B;IAE1B,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;IAC7B,IAAI,WAAW,GAAG,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAErD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5C,MAAM,QAAQ,GAAG,iBAAiB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,IAAI,QAAQ,GAAG,WAAW,EAAE,CAAC;YAC3B,WAAW,GAAG,QAAQ,CAAC;YACvB,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC;AACxD,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,cAAc,CAC5B,MAAmB,EACnB,KAAkB,EAClB,WAAmB;IAEnB,MAAM,QAAQ,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAClD,OAAO,QAAQ,IAAI,WAAW,CAAC;AACjC,CAAC;AAED;;;;;GAKG;AACH,SAAgB,cAAc,CAC5B,MAAmB,EACnB,WAA0B;IAE1B,OAAO,WAAW;SACf,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACb,UAAU,EAAE,KAAK;QACjB,QAAQ,EAAE,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC;KAC3C,CAAC,CAAC;SACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC7C,CAAC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/src/utils/distance.ts"], "sourcesContent": ["import { Coordinates } from '../models/Business';\n\n/**\n * Calculates the distance between two coordinates using the Haversine formula\n * @param coord1 - First coordinate\n * @param coord2 - Second coordinate\n * @returns Distance in miles\n */\nexport function calculateDistance(coord1: Coordinates, coord2: Coordinates): number {\n  const R = 3959; // Earth's radius in miles\n  const dLat = toRadians(coord2.latitude - coord1.latitude);\n  const dLon = toRadians(coord2.longitude - coord1.longitude);\n  \n  const a = \n    Math.sin(dLat / 2) * Math.sin(dLat / 2) +\n    Math.cos(toRadians(coord1.latitude)) * Math.cos(toRadians(coord2.latitude)) *\n    Math.sin(dLon / 2) * Math.sin(dLon / 2);\n  \n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n  const distance = R * c;\n  \n  return Math.round(distance * 100) / 100; // Round to 2 decimal places\n}\n\n/**\n * Converts degrees to radians\n * @param degrees - Angle in degrees\n * @returns Angle in radians\n */\nfunction toRadians(degrees: number): number {\n  return degrees * (Math.PI / 180);\n}\n\n/**\n * Calculates the distance between two coordinates in kilometers\n * @param coord1 - First coordinate\n * @param coord2 - Second coordinate\n * @returns Distance in kilometers\n */\nexport function calculateDistanceKm(coord1: Coordinates, coord2: Coordinates): number {\n  const distanceInMiles = calculateDistance(coord1, coord2);\n  return Math.round(distanceInMiles * 1.60934 * 100) / 100; // Convert to km and round\n}\n\n/**\n * Finds the closest coordinate from an array of coordinates\n * @param target - Target coordinate\n * @param coordinates - Array of coordinates to search\n * @returns Closest coordinate and its distance\n */\nexport function findClosest(\n  target: Coordinates,\n  coordinates: Coordinates[]\n): { coordinate: Coordinates; distance: number } | null {\n  if (coordinates.length === 0) {\n    return null;\n  }\n\n  let closest = coordinates[0];\n  let minDistance = calculateDistance(target, closest);\n\n  for (let i = 1; i < coordinates.length; i++) {\n    const distance = calculateDistance(target, coordinates[i]);\n    if (distance < minDistance) {\n      minDistance = distance;\n      closest = coordinates[i];\n    }\n  }\n\n  return { coordinate: closest, distance: minDistance };\n}\n\n/**\n * Checks if a coordinate is within a certain radius of a center point\n * @param center - Center coordinate\n * @param point - Point to check\n * @param radiusMiles - Radius in miles\n * @returns True if point is within radius\n */\nexport function isWithinRadius(\n  center: Coordinates,\n  point: Coordinates,\n  radiusMiles: number\n): boolean {\n  const distance = calculateDistance(center, point);\n  return distance <= radiusMiles;\n}\n\n/**\n * Sorts an array of coordinates by distance from a target point\n * @param target - Target coordinate\n * @param coordinates - Array of coordinates to sort\n * @returns Sorted array with distances\n */\nexport function sortByDistance(\n  target: Coordinates,\n  coordinates: Coordinates[]\n): Array<{ coordinate: Coordinates; distance: number }> {\n  return coordinates\n    .map(coord => ({\n      coordinate: coord,\n      distance: calculateDistance(target, coord)\n    }))\n    .sort((a, b) => a.distance - b.distance);\n}\n"], "version": 3}