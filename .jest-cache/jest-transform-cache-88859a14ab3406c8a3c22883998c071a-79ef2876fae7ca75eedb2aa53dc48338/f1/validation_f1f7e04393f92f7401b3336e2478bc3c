b192bc8b407646866c975eb29406e74c
"use strict";

/* istanbul ignore next */
function cov_2v4c18x81() {
  var path = "/Users/<USER>/WebstormProjects/goo/src/utils/validation.ts";
  var hash = "5c9718f5eff98b6f851eba7453b3cba4823fba73";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/WebstormProjects/goo/src/utils/validation.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 42
        }
      },
      "4": {
        start: {
          line: 7,
          column: 0
        },
        end: {
          line: 7,
          column: 40
        }
      },
      "5": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 8,
          column: 52
        }
      },
      "6": {
        start: {
          line: 9,
          column: 0
        },
        end: {
          line: 9,
          column: 34
        }
      },
      "7": {
        start: {
          line: 10,
          column: 0
        },
        end: {
          line: 10,
          column: 38
        }
      },
      "8": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 38
        }
      },
      "9": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 52
        }
      },
      "10": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 44
        }
      },
      "11": {
        start: {
          line: 14,
          column: 0
        },
        end: {
          line: 14,
          column: 38
        }
      },
      "12": {
        start: {
          line: 15,
          column: 17
        },
        end: {
          line: 15,
          column: 44
        }
      },
      "13": {
        start: {
          line: 16,
          column: 20
        },
        end: {
          line: 16,
          column: 43
        }
      },
      "14": {
        start: {
          line: 17,
          column: 28
        },
        end: {
          line: 17,
          column: 56
        }
      },
      "15": {
        start: {
          line: 18,
          column: 20
        },
        end: {
          line: 18,
          column: 57
        }
      },
      "16": {
        start: {
          line: 19,
          column: 19
        },
        end: {
          line: 19,
          column: 55
        }
      },
      "17": {
        start: {
          line: 26,
          column: 4
        },
        end: {
          line: 28,
          column: 5
        }
      },
      "18": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 99
        }
      },
      "19": {
        start: {
          line: 30,
          column: 4
        },
        end: {
          line: 32,
          column: 5
        }
      },
      "20": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 99
        }
      },
      "21": {
        start: {
          line: 35,
          column: 4
        },
        end: {
          line: 40,
          column: 5
        }
      },
      "22": {
        start: {
          line: 36,
          column: 24
        },
        end: {
          line: 36,
          column: 58
        }
      },
      "23": {
        start: {
          line: 37,
          column: 8
        },
        end: {
          line: 39,
          column: 9
        }
      },
      "24": {
        start: {
          line: 38,
          column: 12
        },
        end: {
          line: 38,
          column: 103
        }
      },
      "25": {
        start: {
          line: 48,
          column: 4
        },
        end: {
          line: 50,
          column: 5
        }
      },
      "26": {
        start: {
          line: 49,
          column: 8
        },
        end: {
          line: 49,
          column: 96
        }
      },
      "27": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 60,
          column: 5
        }
      },
      "28": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 59,
          column: 109
        }
      },
      "29": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 70,
          column: 5
        }
      },
      "30": {
        start: {
          line: 69,
          column: 8
        },
        end: {
          line: 69,
          column: 109
        }
      },
      "31": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 81,
          column: 5
        }
      },
      "32": {
        start: {
          line: 80,
          column: 8
        },
        end: {
          line: 80,
          column: 109
        }
      },
      "33": {
        start: {
          line: 89,
          column: 4
        },
        end: {
          line: 91,
          column: 5
        }
      },
      "34": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 90,
          column: 15
        }
      },
      "35": {
        start: {
          line: 93,
          column: 30
        },
        end: {
          line: 93,
          column: 57
        }
      },
      "36": {
        start: {
          line: 94,
          column: 4
        },
        end: {
          line: 96,
          column: 5
        }
      },
      "37": {
        start: {
          line: 95,
          column: 8
        },
        end: {
          line: 95,
          column: 83
        }
      },
      "38": {
        start: {
          line: 98,
          column: 27
        },
        end: {
          line: 98,
          column: 92
        }
      },
      "39": {
        start: {
          line: 99,
          column: 4
        },
        end: {
          line: 101,
          column: 5
        }
      },
      "40": {
        start: {
          line: 100,
          column: 8
        },
        end: {
          line: 100,
          column: 15
        }
      },
      "41": {
        start: {
          line: 103,
          column: 30
        },
        end: {
          line: 103,
          column: 87
        }
      },
      "42": {
        start: {
          line: 104,
          column: 4
        },
        end: {
          line: 106,
          column: 5
        }
      },
      "43": {
        start: {
          line: 105,
          column: 8
        },
        end: {
          line: 105,
          column: 15
        }
      },
      "44": {
        start: {
          line: 108,
          column: 18
        },
        end: {
          line: 108,
          column: 23
        }
      },
      "45": {
        start: {
          line: 109,
          column: 4
        },
        end: {
          line: 118,
          column: 5
        }
      },
      "46": {
        start: {
          line: 111,
          column: 28
        },
        end: {
          line: 111,
          column: 82
        }
      },
      "47": {
        start: {
          line: 112,
          column: 8
        },
        end: {
          line: 114,
          column: 9
        }
      },
      "48": {
        start: {
          line: 113,
          column: 12
        },
        end: {
          line: 113,
          column: 27
        }
      },
      "49": {
        start: {
          line: 119,
          column: 4
        },
        end: {
          line: 130,
          column: 5
        }
      },
      "50": {
        start: {
          line: 120,
          column: 8
        },
        end: {
          line: 129,
          column: 9
        }
      },
      "51": {
        start: {
          line: 122,
          column: 32
        },
        end: {
          line: 122,
          column: 80
        }
      },
      "52": {
        start: {
          line: 123,
          column: 12
        },
        end: {
          line: 125,
          column: 13
        }
      },
      "53": {
        start: {
          line: 124,
          column: 16
        },
        end: {
          line: 124,
          column: 31
        }
      },
      "54": {
        start: {
          line: 131,
          column: 4
        },
        end: {
          line: 141,
          column: 5
        }
      },
      "55": {
        start: {
          line: 132,
          column: 8
        },
        end: {
          line: 140,
          column: 9
        }
      },
      "56": {
        start: {
          line: 134,
          column: 12
        },
        end: {
          line: 136,
          column: 13
        }
      },
      "57": {
        start: {
          line: 135,
          column: 16
        },
        end: {
          line: 135,
          column: 31
        }
      },
      "58": {
        start: {
          line: 143,
          column: 4
        },
        end: {
          line: 145,
          column: 5
        }
      },
      "59": {
        start: {
          line: 144,
          column: 8
        },
        end: {
          line: 144,
          column: 83
        }
      },
      "60": {
        start: {
          line: 153,
          column: 4
        },
        end: {
          line: 155,
          column: 5
        }
      },
      "61": {
        start: {
          line: 154,
          column: 8
        },
        end: {
          line: 154,
          column: 15
        }
      },
      "62": {
        start: {
          line: 157,
          column: 4
        },
        end: {
          line: 164,
          column: 5
        }
      },
      "63": {
        start: {
          line: 163,
          column: 8
        },
        end: {
          line: 163,
          column: 76
        }
      },
      "64": {
        start: {
          line: 172,
          column: 4
        },
        end: {
          line: 172,
          column: 36
        }
      },
      "65": {
        start: {
          line: 173,
          column: 4
        },
        end: {
          line: 173,
          column: 34
        }
      },
      "66": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 174,
          column: 46
        }
      },
      "67": {
        start: {
          line: 183,
          column: 4
        },
        end: {
          line: 185,
          column: 5
        }
      },
      "68": {
        start: {
          line: 184,
          column: 8
        },
        end: {
          line: 184,
          column: 72
        }
      },
      "69": {
        start: {
          line: 196,
          column: 4
        },
        end: {
          line: 198,
          column: 5
        }
      },
      "70": {
        start: {
          line: 197,
          column: 8
        },
        end: {
          line: 197,
          column: 104
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "validateZipCode",
        decl: {
          start: {
            line: 25,
            column: 9
          },
          end: {
            line: 25,
            column: 24
          }
        },
        loc: {
          start: {
            line: 25,
            column: 34
          },
          end: {
            line: 41,
            column: 1
          }
        },
        line: 25
      },
      "2": {
        name: "validateRadius",
        decl: {
          start: {
            line: 47,
            column: 9
          },
          end: {
            line: 47,
            column: 23
          }
        },
        loc: {
          start: {
            line: 47,
            column: 32
          },
          end: {
            line: 51,
            column: 1
          }
        },
        line: 47
      },
      "3": {
        name: "validateBusinessType",
        decl: {
          start: {
            line: 57,
            column: 9
          },
          end: {
            line: 57,
            column: 29
          }
        },
        loc: {
          start: {
            line: 57,
            column: 44
          },
          end: {
            line: 61,
            column: 1
          }
        },
        line: 57
      },
      "4": {
        name: "validateUrl",
        decl: {
          start: {
            line: 67,
            column: 9
          },
          end: {
            line: 67,
            column: 20
          }
        },
        loc: {
          start: {
            line: 67,
            column: 26
          },
          end: {
            line: 82,
            column: 1
          }
        },
        line: 67
      },
      "5": {
        name: "validatePhone",
        decl: {
          start: {
            line: 88,
            column: 9
          },
          end: {
            line: 88,
            column: 22
          }
        },
        loc: {
          start: {
            line: 88,
            column: 30
          },
          end: {
            line: 146,
            column: 1
          }
        },
        line: 88
      },
      "6": {
        name: "validateEmail",
        decl: {
          start: {
            line: 152,
            column: 9
          },
          end: {
            line: 152,
            column: 22
          }
        },
        loc: {
          start: {
            line: 152,
            column: 30
          },
          end: {
            line: 165,
            column: 1
          }
        },
        line: 152
      },
      "7": {
        name: "validateSearchParams",
        decl: {
          start: {
            line: 171,
            column: 9
          },
          end: {
            line: 171,
            column: 29
          }
        },
        loc: {
          start: {
            line: 171,
            column: 38
          },
          end: {
            line: 175,
            column: 1
          }
        },
        line: 171
      },
      "8": {
        name: "validateRequired",
        decl: {
          start: {
            line: 182,
            column: 9
          },
          end: {
            line: 182,
            column: 25
          }
        },
        loc: {
          start: {
            line: 182,
            column: 39
          },
          end: {
            line: 186,
            column: 1
          }
        },
        line: 182
      },
      "9": {
        name: "validateRange",
        decl: {
          start: {
            line: 195,
            column: 9
          },
          end: {
            line: 195,
            column: 22
          }
        },
        loc: {
          start: {
            line: 195,
            column: 51
          },
          end: {
            line: 199,
            column: 1
          }
        },
        line: 195
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      },
      "3": {
        loc: {
          start: {
            line: 26,
            column: 4
          },
          end: {
            line: 28,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 4
          },
          end: {
            line: 28,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "4": {
        loc: {
          start: {
            line: 30,
            column: 4
          },
          end: {
            line: 32,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 4
          },
          end: {
            line: 32,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "5": {
        loc: {
          start: {
            line: 35,
            column: 4
          },
          end: {
            line: 40,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 4
          },
          end: {
            line: 40,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "6": {
        loc: {
          start: {
            line: 37,
            column: 8
          },
          end: {
            line: 39,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 8
          },
          end: {
            line: 39,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "7": {
        loc: {
          start: {
            line: 48,
            column: 4
          },
          end: {
            line: 50,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 48,
            column: 4
          },
          end: {
            line: 50,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 48
      },
      "8": {
        loc: {
          start: {
            line: 48,
            column: 8
          },
          end: {
            line: 48,
            column: 131
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 48,
            column: 8
          },
          end: {
            line: 48,
            column: 33
          }
        }, {
          start: {
            line: 48,
            column: 37
          },
          end: {
            line: 48,
            column: 82
          }
        }, {
          start: {
            line: 48,
            column: 86
          },
          end: {
            line: 48,
            column: 131
          }
        }],
        line: 48
      },
      "9": {
        loc: {
          start: {
            line: 58,
            column: 4
          },
          end: {
            line: 60,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 58,
            column: 4
          },
          end: {
            line: 60,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 58
      },
      "10": {
        loc: {
          start: {
            line: 58,
            column: 8
          },
          end: {
            line: 58,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 58,
            column: 8
          },
          end: {
            line: 58,
            column: 21
          }
        }, {
          start: {
            line: 58,
            column: 25
          },
          end: {
            line: 58,
            column: 75
          }
        }],
        line: 58
      },
      "11": {
        loc: {
          start: {
            line: 68,
            column: 4
          },
          end: {
            line: 70,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 68,
            column: 4
          },
          end: {
            line: 70,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 68
      },
      "12": {
        loc: {
          start: {
            line: 72,
            column: 4
          },
          end: {
            line: 81,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 4
          },
          end: {
            line: 81,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "13": {
        loc: {
          start: {
            line: 89,
            column: 4
          },
          end: {
            line: 91,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 89,
            column: 4
          },
          end: {
            line: 91,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 89
      },
      "14": {
        loc: {
          start: {
            line: 94,
            column: 4
          },
          end: {
            line: 96,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 94,
            column: 4
          },
          end: {
            line: 96,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 94
      },
      "15": {
        loc: {
          start: {
            line: 99,
            column: 4
          },
          end: {
            line: 101,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 99,
            column: 4
          },
          end: {
            line: 101,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 99
      },
      "16": {
        loc: {
          start: {
            line: 104,
            column: 4
          },
          end: {
            line: 106,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 104,
            column: 4
          },
          end: {
            line: 106,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 104
      },
      "17": {
        loc: {
          start: {
            line: 112,
            column: 8
          },
          end: {
            line: 114,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 112,
            column: 8
          },
          end: {
            line: 114,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 112
      },
      "18": {
        loc: {
          start: {
            line: 112,
            column: 12
          },
          end: {
            line: 112,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 112,
            column: 12
          },
          end: {
            line: 112,
            column: 23
          }
        }, {
          start: {
            line: 112,
            column: 27
          },
          end: {
            line: 112,
            column: 48
          }
        }],
        line: 112
      },
      "19": {
        loc: {
          start: {
            line: 119,
            column: 4
          },
          end: {
            line: 130,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 119,
            column: 4
          },
          end: {
            line: 130,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 119
      },
      "20": {
        loc: {
          start: {
            line: 123,
            column: 12
          },
          end: {
            line: 125,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 123,
            column: 12
          },
          end: {
            line: 125,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 123
      },
      "21": {
        loc: {
          start: {
            line: 123,
            column: 16
          },
          end: {
            line: 123,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 123,
            column: 16
          },
          end: {
            line: 123,
            column: 27
          }
        }, {
          start: {
            line: 123,
            column: 31
          },
          end: {
            line: 123,
            column: 52
          }
        }],
        line: 123
      },
      "22": {
        loc: {
          start: {
            line: 131,
            column: 4
          },
          end: {
            line: 141,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 131,
            column: 4
          },
          end: {
            line: 141,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 131
      },
      "23": {
        loc: {
          start: {
            line: 134,
            column: 12
          },
          end: {
            line: 136,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 134,
            column: 12
          },
          end: {
            line: 136,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 134
      },
      "24": {
        loc: {
          start: {
            line: 143,
            column: 4
          },
          end: {
            line: 145,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 143,
            column: 4
          },
          end: {
            line: 145,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 143
      },
      "25": {
        loc: {
          start: {
            line: 153,
            column: 4
          },
          end: {
            line: 155,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 153,
            column: 4
          },
          end: {
            line: 155,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 153
      },
      "26": {
        loc: {
          start: {
            line: 157,
            column: 4
          },
          end: {
            line: 164,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 157,
            column: 4
          },
          end: {
            line: 164,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 157
      },
      "27": {
        loc: {
          start: {
            line: 183,
            column: 4
          },
          end: {
            line: 185,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 183,
            column: 4
          },
          end: {
            line: 185,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 183
      },
      "28": {
        loc: {
          start: {
            line: 183,
            column: 8
          },
          end: {
            line: 183,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 183,
            column: 8
          },
          end: {
            line: 183,
            column: 27
          }
        }, {
          start: {
            line: 183,
            column: 31
          },
          end: {
            line: 183,
            column: 45
          }
        }, {
          start: {
            line: 183,
            column: 49
          },
          end: {
            line: 183,
            column: 61
          }
        }],
        line: 183
      },
      "29": {
        loc: {
          start: {
            line: 196,
            column: 4
          },
          end: {
            line: 198,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 196,
            column: 4
          },
          end: {
            line: 198,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 196
      },
      "30": {
        loc: {
          start: {
            line: 196,
            column: 8
          },
          end: {
            line: 196,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 196,
            column: 8
          },
          end: {
            line: 196,
            column: 19
          }
        }, {
          start: {
            line: 196,
            column: 23
          },
          end: {
            line: 196,
            column: 34
          }
        }],
        line: 196
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0, 0],
      "29": [0, 0],
      "30": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/WebstormProjects/goo/src/utils/validation.ts",
      mappings: ";;;;;AAWA,0CAkBC;AAOD,wCAIC;AAOD,oDAIC;AAOD,kCAgBC;AAOD,sCA+DC;AAOD,sCAcC;AAOD,oDAQC;AAQD,4CAIC;AAUD,sCAIC;AA9MD,6CAAmD;AACnD,4CAAkG;AAClG,yDAAyE;AACzE,0DAAkC;AAClC,wDAAgC;AAEhC;;;;GAIG;AACH,SAAgB,eAAe,CAAC,OAAe;IAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,MAAM,IAAI,wBAAe,CAAC,0BAAc,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;IACxE,CAAC;IAED,sCAAsC;IACtC,IAAI,CAAC,+BAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;QAChD,MAAM,IAAI,wBAAe,CAAC,0BAAc,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;IACxE,CAAC;IAED,4EAA4E;IAC5E,4FAA4F;IAC5F,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACzB,MAAM,OAAO,GAAG,kBAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACzC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,wBAAe,CAAC,0BAAc,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,cAAc,CAAC,MAAc;IAC3C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,MAAM,GAAG,yBAAa,CAAC,UAAU,IAAI,MAAM,GAAG,yBAAa,CAAC,UAAU,EAAE,CAAC;QACxG,MAAM,IAAI,wBAAe,CAAC,0BAAc,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;IACrE,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,oBAAoB,CAAC,YAAoB;IACvD,IAAI,CAAC,YAAY,IAAI,CAAC,0BAAc,CAAC,QAAQ,CAAC,YAAmB,CAAC,EAAE,CAAC;QACnE,MAAM,IAAI,wBAAe,CAAC,0BAAc,CAAC,qBAAqB,EAAE,cAAc,CAAC,CAAC;IAClF,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,WAAW,CAAC,GAAW;IACrC,IAAI,CAAC,GAAG,EAAE,CAAC;QACT,MAAM,IAAI,wBAAe,CAAC,yDAAyD,EAAE,KAAK,CAAC,CAAC;IAC9F,CAAC;IAED,6CAA6C;IAC7C,IAAI,CAAC,mBAAS,CAAC,KAAK,CAAC,GAAG,EAAE;QACxB,SAAS,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;QAC5B,gBAAgB,EAAE,IAAI;QACtB,sBAAsB,EAAE,IAAI;QAC5B,iBAAiB,EAAE,IAAI;QACvB,kBAAkB,EAAE,KAAK;QACzB,4BAA4B,EAAE,KAAK;KACpC,CAAC,EAAE,CAAC;QACH,MAAM,IAAI,wBAAe,CAAC,yDAAyD,EAAE,KAAK,CAAC,CAAC;IAC9F,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,aAAa,CAAC,KAAa;IACzC,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,CAAC,yBAAyB;IACnC,CAAC;IAED,wFAAwF;IACxF,MAAM,iBAAiB,GAAG,2BAA2B,CAAC;IACtD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACnC,MAAM,IAAI,wBAAe,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,2CAA2C;IAC3C,MAAM,cAAc,GAAG,iEAAiE,CAAC;IACzF,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QAC/B,OAAO,CAAC,wBAAwB;IAClC,CAAC;IAED,oDAAoD;IACpD,MAAM,iBAAiB,GAAG,yDAAyD,CAAC;IACpF,IAAI,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QAClC,OAAO,CAAC,yCAAyC;IACnD,CAAC;IAED,wDAAwD;IACxD,IAAI,OAAO,GAAG,KAAK,CAAC;IAEpB,IAAI,CAAC;QACH,wDAAwD;QACxD,MAAM,WAAW,GAAG,IAAA,oCAAgB,EAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAClD,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;YACzC,OAAO,GAAG,IAAI,CAAC;QACjB,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,qCAAqC;IACvC,CAAC;IAED,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,IAAI,CAAC;YACH,iFAAiF;YACjF,MAAM,WAAW,GAAG,IAAA,oCAAgB,EAAC,KAAK,CAAC,CAAC;YAC5C,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;gBACzC,OAAO,GAAG,IAAI,CAAC;YACjB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,qCAAqC;QACvC,CAAC;IACH,CAAC;IAED,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,IAAI,CAAC;YACH,uCAAuC;YACvC,IAAI,IAAA,sCAAkB,EAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC;gBACpC,OAAO,GAAG,IAAI,CAAC;YACjB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,qCAAqC;QACvC,CAAC;IACH,CAAC;IAED,2DAA2D;IAC3D,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,MAAM,IAAI,wBAAe,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,aAAa,CAAC,KAAa;IACzC,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,CAAC,yBAAyB;IACnC,CAAC;IAED,sDAAsD;IACtD,IAAI,CAAC,mBAAS,CAAC,OAAO,CAAC,KAAK,EAAE;QAC5B,qBAAqB,EAAE,KAAK;QAC5B,WAAW,EAAE,IAAI;QACjB,eAAe,EAAE,KAAK;QACtB,0BAA0B,EAAE,IAAI;KACjC,CAAC,EAAE,CAAC;QACH,MAAM,IAAI,wBAAe,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,oBAAoB,CAAC,MAIpC;IACC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAChC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC9B,oBAAoB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AAC5C,CAAC;AAED;;;;;GAKG;AACH,SAAgB,gBAAgB,CAAC,KAAU,EAAE,IAAY;IACvD,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;QAC1D,MAAM,IAAI,wBAAe,CAAC,GAAG,IAAI,cAAc,EAAE,IAAI,CAAC,CAAC;IACzD,CAAC;AACH,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,aAAa,CAAC,KAAa,EAAE,GAAW,EAAE,GAAW,EAAE,SAAiB;IACtF,IAAI,KAAK,GAAG,GAAG,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC;QAC/B,MAAM,IAAI,wBAAe,CAAC,GAAG,SAAS,oBAAoB,GAAG,QAAQ,GAAG,EAAE,EAAE,SAAS,CAAC,CAAC;IACzF,CAAC;AACH,CAAC",
      names: [],
      sources: ["/Users/<USER>/WebstormProjects/goo/src/utils/validation.ts"],
      sourcesContent: ["import { ValidationError } from '../models/Errors';\nimport { VALIDATION_PATTERNS, SEARCH_CONFIG, BUSINESS_TYPES, ERROR_MESSAGES } from '../constants';\nimport { parsePhoneNumber, isValidPhoneNumber } from 'libphonenumber-js';\nimport validator from 'validator';\nimport zipcodes from 'zipcodes';\n\n/**\n * Validates a zip code format using zipcodes package for US zip codes\n * @param zipCode - The zip code to validate\n * @throws ValidationError if zip code is invalid\n */\nexport function validateZipCode(zipCode: string): void {\n  if (!zipCode) {\n    throw new ValidationError(ERROR_MESSAGES.INVALID_ZIP_CODE, 'zipCode');\n  }\n\n  // First check basic format with regex\n  if (!VALIDATION_PATTERNS.ZIP_CODE.test(zipCode)) {\n    throw new ValidationError(ERROR_MESSAGES.INVALID_ZIP_CODE, 'zipCode');\n  }\n\n  // For 5-digit codes, also validate with zipcodes package for real zip codes\n  // For ZIP+4, just use regex validation since zipcodes package may not have all combinations\n  if (zipCode.length === 5) {\n    const zipInfo = zipcodes.lookup(zipCode);\n    if (!zipInfo) {\n      throw new ValidationError(ERROR_MESSAGES.INVALID_ZIP_CODE, 'zipCode');\n    }\n  }\n}\n\n/**\n * Validates a search radius\n * @param radius - The radius to validate (in miles)\n * @throws ValidationError if radius is invalid\n */\nexport function validateRadius(radius: number): void {\n  if (!Number.isInteger(radius) || radius < SEARCH_CONFIG.MIN_RADIUS || radius > SEARCH_CONFIG.MAX_RADIUS) {\n    throw new ValidationError(ERROR_MESSAGES.INVALID_RADIUS, 'radius');\n  }\n}\n\n/**\n * Validates a business type\n * @param businessType - The business type to validate\n * @throws ValidationError if business type is invalid\n */\nexport function validateBusinessType(businessType: string): void {\n  if (!businessType || !BUSINESS_TYPES.includes(businessType as any)) {\n    throw new ValidationError(ERROR_MESSAGES.INVALID_BUSINESS_TYPE, 'businessType');\n  }\n}\n\n/**\n * Validates a URL format using validator.js for comprehensive validation\n * @param url - The URL to validate\n * @throws ValidationError if URL is invalid\n */\nexport function validateUrl(url: string): void {\n  if (!url) {\n    throw new ValidationError('Invalid URL format. Must start with http:// or https://', 'url');\n  }\n\n  // Use validator.js for robust URL validation\n  if (!validator.isURL(url, {\n    protocols: ['http', 'https'],\n    require_protocol: true,\n    require_valid_protocol: true,\n    allow_underscores: true,\n    allow_trailing_dot: false,\n    allow_protocol_relative_urls: false\n  })) {\n    throw new ValidationError('Invalid URL format. Must start with http:// or https://', 'url');\n  }\n}\n\n/**\n * Validates phone number format using libphonenumber-js for international support\n * @param phone - The phone number to validate\n * @throws ValidationError if phone number is invalid\n */\nexport function validatePhone(phone: string): void {\n  if (!phone) {\n    return; // Empty phone is allowed\n  }\n\n  // First try basic format validation - if it doesn't look like a phone number, reject it\n  const basicPhonePattern = /^[\\+]?[\\d\\s\\-\\(\\)\\.]{7,}$/;\n  if (!basicPhonePattern.test(phone)) {\n    throw new ValidationError('Invalid phone number format', 'phone');\n  }\n\n  // For US phone numbers, be more permissive\n  const usPhonePattern = /^[\\+]?1?[\\s\\-\\(\\)]?[\\d]{3}[\\s\\-\\(\\)]?[\\d]{3}[\\s\\-\\(\\)]?[\\d]{4}$/;\n  if (usPhonePattern.test(phone)) {\n    return; // Valid US phone number\n  }\n\n  // Also check for common US formats with parentheses\n  const usPhoneWithParens = /^[\\+]?1?[\\s\\-]?\\([\\d]{3}\\)[\\s\\-]?[\\d]{3}[\\s\\-]?[\\d]{4}$/;\n  if (usPhoneWithParens.test(phone)) {\n    return; // Valid US phone number with parentheses\n  }\n\n  // For international numbers, try more strict validation\n  let isValid = false;\n\n  try {\n    // Try parsing with US country code first for US numbers\n    const phoneNumber = parsePhoneNumber(phone, 'US');\n    if (phoneNumber && phoneNumber.isValid()) {\n      isValid = true;\n    }\n  } catch (error) {\n    // Continue to next validation method\n  }\n\n  if (!isValid) {\n    try {\n      // Try parsing without country code (for international numbers with country code)\n      const phoneNumber = parsePhoneNumber(phone);\n      if (phoneNumber && phoneNumber.isValid()) {\n        isValid = true;\n      }\n    } catch (error) {\n      // Continue to next validation method\n    }\n  }\n\n  if (!isValid) {\n    try {\n      // Try global validation for any format\n      if (isValidPhoneNumber(phone, 'US')) {\n        isValid = true;\n      }\n    } catch (error) {\n      // Continue to next validation method\n    }\n  }\n\n  // If none of the validation methods succeeded, throw error\n  if (!isValid) {\n    throw new ValidationError('Invalid phone number format', 'phone');\n  }\n}\n\n/**\n * Validates email format using validator.js for RFC-compliant validation\n * @param email - The email to validate\n * @throws ValidationError if email is invalid\n */\nexport function validateEmail(email: string): void {\n  if (!email) {\n    return; // Empty email is allowed\n  }\n\n  // Use validator.js for RFC-compliant email validation\n  if (!validator.isEmail(email, {\n    allow_utf8_local_part: false,\n    require_tld: true,\n    allow_ip_domain: false,\n    domain_specific_validation: true\n  })) {\n    throw new ValidationError('Invalid email format', 'email');\n  }\n}\n\n/**\n * Validates search parameters\n * @param params - The search parameters to validate\n * @throws ValidationError if any parameter is invalid\n */\nexport function validateSearchParams(params: {\n  zipCode: string;\n  radius: number;\n  businessType: string;\n}): void {\n  validateZipCode(params.zipCode);\n  validateRadius(params.radius);\n  validateBusinessType(params.businessType);\n}\n\n/**\n * Validates that a required configuration value exists\n * @param value - The configuration value\n * @param name - The name of the configuration\n * @throws ValidationError if value is missing\n */\nexport function validateRequired(value: any, name: string): void {\n  if (value === undefined || value === null || value === '') {\n    throw new ValidationError(`${name} is required`, name);\n  }\n}\n\n/**\n * Validates that a number is within a specified range\n * @param value - The number to validate\n * @param min - Minimum allowed value\n * @param max - Maximum allowed value\n * @param fieldName - Name of the field being validated\n * @throws ValidationError if value is out of range\n */\nexport function validateRange(value: number, min: number, max: number, fieldName: string): void {\n  if (value < min || value > max) {\n    throw new ValidationError(`${fieldName} must be between ${min} and ${max}`, fieldName);\n  }\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5c9718f5eff98b6f851eba7453b3cba4823fba73"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2v4c18x81 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2v4c18x81();
var __importDefault =
/* istanbul ignore next */
(cov_2v4c18x81().s[0]++,
/* istanbul ignore next */
(cov_2v4c18x81().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2v4c18x81().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_2v4c18x81().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_2v4c18x81().f[0]++;
  cov_2v4c18x81().s[1]++;
  return /* istanbul ignore next */(cov_2v4c18x81().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_2v4c18x81().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_2v4c18x81().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_2v4c18x81().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_2v4c18x81().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2v4c18x81().s[3]++;
exports.validateZipCode = validateZipCode;
/* istanbul ignore next */
cov_2v4c18x81().s[4]++;
exports.validateRadius = validateRadius;
/* istanbul ignore next */
cov_2v4c18x81().s[5]++;
exports.validateBusinessType = validateBusinessType;
/* istanbul ignore next */
cov_2v4c18x81().s[6]++;
exports.validateUrl = validateUrl;
/* istanbul ignore next */
cov_2v4c18x81().s[7]++;
exports.validatePhone = validatePhone;
/* istanbul ignore next */
cov_2v4c18x81().s[8]++;
exports.validateEmail = validateEmail;
/* istanbul ignore next */
cov_2v4c18x81().s[9]++;
exports.validateSearchParams = validateSearchParams;
/* istanbul ignore next */
cov_2v4c18x81().s[10]++;
exports.validateRequired = validateRequired;
/* istanbul ignore next */
cov_2v4c18x81().s[11]++;
exports.validateRange = validateRange;
const Errors_1 =
/* istanbul ignore next */
(cov_2v4c18x81().s[12]++, require("../models/Errors"));
const constants_1 =
/* istanbul ignore next */
(cov_2v4c18x81().s[13]++, require("../constants"));
const libphonenumber_js_1 =
/* istanbul ignore next */
(cov_2v4c18x81().s[14]++, require("libphonenumber-js"));
const validator_1 =
/* istanbul ignore next */
(cov_2v4c18x81().s[15]++, __importDefault(require("validator")));
const zipcodes_1 =
/* istanbul ignore next */
(cov_2v4c18x81().s[16]++, __importDefault(require("zipcodes")));
/**
 * Validates a zip code format using zipcodes package for US zip codes
 * @param zipCode - The zip code to validate
 * @throws ValidationError if zip code is invalid
 */
function validateZipCode(zipCode) {
  /* istanbul ignore next */
  cov_2v4c18x81().f[1]++;
  cov_2v4c18x81().s[17]++;
  if (!zipCode) {
    /* istanbul ignore next */
    cov_2v4c18x81().b[3][0]++;
    cov_2v4c18x81().s[18]++;
    throw new Errors_1.ValidationError(constants_1.ERROR_MESSAGES.INVALID_ZIP_CODE, 'zipCode');
  } else
  /* istanbul ignore next */
  {
    cov_2v4c18x81().b[3][1]++;
  }
  // First check basic format with regex
  cov_2v4c18x81().s[19]++;
  if (!constants_1.VALIDATION_PATTERNS.ZIP_CODE.test(zipCode)) {
    /* istanbul ignore next */
    cov_2v4c18x81().b[4][0]++;
    cov_2v4c18x81().s[20]++;
    throw new Errors_1.ValidationError(constants_1.ERROR_MESSAGES.INVALID_ZIP_CODE, 'zipCode');
  } else
  /* istanbul ignore next */
  {
    cov_2v4c18x81().b[4][1]++;
  }
  // For 5-digit codes, also validate with zipcodes package for real zip codes
  // For ZIP+4, just use regex validation since zipcodes package may not have all combinations
  cov_2v4c18x81().s[21]++;
  if (zipCode.length === 5) {
    /* istanbul ignore next */
    cov_2v4c18x81().b[5][0]++;
    const zipInfo =
    /* istanbul ignore next */
    (cov_2v4c18x81().s[22]++, zipcodes_1.default.lookup(zipCode));
    /* istanbul ignore next */
    cov_2v4c18x81().s[23]++;
    if (!zipInfo) {
      /* istanbul ignore next */
      cov_2v4c18x81().b[6][0]++;
      cov_2v4c18x81().s[24]++;
      throw new Errors_1.ValidationError(constants_1.ERROR_MESSAGES.INVALID_ZIP_CODE, 'zipCode');
    } else
    /* istanbul ignore next */
    {
      cov_2v4c18x81().b[6][1]++;
    }
  } else
  /* istanbul ignore next */
  {
    cov_2v4c18x81().b[5][1]++;
  }
}
/**
 * Validates a search radius
 * @param radius - The radius to validate (in miles)
 * @throws ValidationError if radius is invalid
 */
function validateRadius(radius) {
  /* istanbul ignore next */
  cov_2v4c18x81().f[2]++;
  cov_2v4c18x81().s[25]++;
  if (
  /* istanbul ignore next */
  (cov_2v4c18x81().b[8][0]++, !Number.isInteger(radius)) ||
  /* istanbul ignore next */
  (cov_2v4c18x81().b[8][1]++, radius < constants_1.SEARCH_CONFIG.MIN_RADIUS) ||
  /* istanbul ignore next */
  (cov_2v4c18x81().b[8][2]++, radius > constants_1.SEARCH_CONFIG.MAX_RADIUS)) {
    /* istanbul ignore next */
    cov_2v4c18x81().b[7][0]++;
    cov_2v4c18x81().s[26]++;
    throw new Errors_1.ValidationError(constants_1.ERROR_MESSAGES.INVALID_RADIUS, 'radius');
  } else
  /* istanbul ignore next */
  {
    cov_2v4c18x81().b[7][1]++;
  }
}
/**
 * Validates a business type
 * @param businessType - The business type to validate
 * @throws ValidationError if business type is invalid
 */
function validateBusinessType(businessType) {
  /* istanbul ignore next */
  cov_2v4c18x81().f[3]++;
  cov_2v4c18x81().s[27]++;
  if (
  /* istanbul ignore next */
  (cov_2v4c18x81().b[10][0]++, !businessType) ||
  /* istanbul ignore next */
  (cov_2v4c18x81().b[10][1]++, !constants_1.BUSINESS_TYPES.includes(businessType))) {
    /* istanbul ignore next */
    cov_2v4c18x81().b[9][0]++;
    cov_2v4c18x81().s[28]++;
    throw new Errors_1.ValidationError(constants_1.ERROR_MESSAGES.INVALID_BUSINESS_TYPE, 'businessType');
  } else
  /* istanbul ignore next */
  {
    cov_2v4c18x81().b[9][1]++;
  }
}
/**
 * Validates a URL format using validator.js for comprehensive validation
 * @param url - The URL to validate
 * @throws ValidationError if URL is invalid
 */
function validateUrl(url) {
  /* istanbul ignore next */
  cov_2v4c18x81().f[4]++;
  cov_2v4c18x81().s[29]++;
  if (!url) {
    /* istanbul ignore next */
    cov_2v4c18x81().b[11][0]++;
    cov_2v4c18x81().s[30]++;
    throw new Errors_1.ValidationError('Invalid URL format. Must start with http:// or https://', 'url');
  } else
  /* istanbul ignore next */
  {
    cov_2v4c18x81().b[11][1]++;
  }
  // Use validator.js for robust URL validation
  cov_2v4c18x81().s[31]++;
  if (!validator_1.default.isURL(url, {
    protocols: ['http', 'https'],
    require_protocol: true,
    require_valid_protocol: true,
    allow_underscores: true,
    allow_trailing_dot: false,
    allow_protocol_relative_urls: false
  })) {
    /* istanbul ignore next */
    cov_2v4c18x81().b[12][0]++;
    cov_2v4c18x81().s[32]++;
    throw new Errors_1.ValidationError('Invalid URL format. Must start with http:// or https://', 'url');
  } else
  /* istanbul ignore next */
  {
    cov_2v4c18x81().b[12][1]++;
  }
}
/**
 * Validates phone number format using libphonenumber-js for international support
 * @param phone - The phone number to validate
 * @throws ValidationError if phone number is invalid
 */
function validatePhone(phone) {
  /* istanbul ignore next */
  cov_2v4c18x81().f[5]++;
  cov_2v4c18x81().s[33]++;
  if (!phone) {
    /* istanbul ignore next */
    cov_2v4c18x81().b[13][0]++;
    cov_2v4c18x81().s[34]++;
    return; // Empty phone is allowed
  } else
  /* istanbul ignore next */
  {
    cov_2v4c18x81().b[13][1]++;
  }
  // First try basic format validation - if it doesn't look like a phone number, reject it
  const basicPhonePattern =
  /* istanbul ignore next */
  (cov_2v4c18x81().s[35]++, /^[\+]?[\d\s\-\(\)\.]{7,}$/);
  /* istanbul ignore next */
  cov_2v4c18x81().s[36]++;
  if (!basicPhonePattern.test(phone)) {
    /* istanbul ignore next */
    cov_2v4c18x81().b[14][0]++;
    cov_2v4c18x81().s[37]++;
    throw new Errors_1.ValidationError('Invalid phone number format', 'phone');
  } else
  /* istanbul ignore next */
  {
    cov_2v4c18x81().b[14][1]++;
  }
  // For US phone numbers, be more permissive
  const usPhonePattern =
  /* istanbul ignore next */
  (cov_2v4c18x81().s[38]++, /^[\+]?1?[\s\-\(\)]?[\d]{3}[\s\-\(\)]?[\d]{3}[\s\-\(\)]?[\d]{4}$/);
  /* istanbul ignore next */
  cov_2v4c18x81().s[39]++;
  if (usPhonePattern.test(phone)) {
    /* istanbul ignore next */
    cov_2v4c18x81().b[15][0]++;
    cov_2v4c18x81().s[40]++;
    return; // Valid US phone number
  } else
  /* istanbul ignore next */
  {
    cov_2v4c18x81().b[15][1]++;
  }
  // Also check for common US formats with parentheses
  const usPhoneWithParens =
  /* istanbul ignore next */
  (cov_2v4c18x81().s[41]++, /^[\+]?1?[\s\-]?\([\d]{3}\)[\s\-]?[\d]{3}[\s\-]?[\d]{4}$/);
  /* istanbul ignore next */
  cov_2v4c18x81().s[42]++;
  if (usPhoneWithParens.test(phone)) {
    /* istanbul ignore next */
    cov_2v4c18x81().b[16][0]++;
    cov_2v4c18x81().s[43]++;
    return; // Valid US phone number with parentheses
  } else
  /* istanbul ignore next */
  {
    cov_2v4c18x81().b[16][1]++;
  }
  // For international numbers, try more strict validation
  let isValid =
  /* istanbul ignore next */
  (cov_2v4c18x81().s[44]++, false);
  /* istanbul ignore next */
  cov_2v4c18x81().s[45]++;
  try {
    // Try parsing with US country code first for US numbers
    const phoneNumber =
    /* istanbul ignore next */
    (cov_2v4c18x81().s[46]++, (0, libphonenumber_js_1.parsePhoneNumber)(phone, 'US'));
    /* istanbul ignore next */
    cov_2v4c18x81().s[47]++;
    if (
    /* istanbul ignore next */
    (cov_2v4c18x81().b[18][0]++, phoneNumber) &&
    /* istanbul ignore next */
    (cov_2v4c18x81().b[18][1]++, phoneNumber.isValid())) {
      /* istanbul ignore next */
      cov_2v4c18x81().b[17][0]++;
      cov_2v4c18x81().s[48]++;
      isValid = true;
    } else
    /* istanbul ignore next */
    {
      cov_2v4c18x81().b[17][1]++;
    }
  } catch (error) {
    // Continue to next validation method
  }
  /* istanbul ignore next */
  cov_2v4c18x81().s[49]++;
  if (!isValid) {
    /* istanbul ignore next */
    cov_2v4c18x81().b[19][0]++;
    cov_2v4c18x81().s[50]++;
    try {
      // Try parsing without country code (for international numbers with country code)
      const phoneNumber =
      /* istanbul ignore next */
      (cov_2v4c18x81().s[51]++, (0, libphonenumber_js_1.parsePhoneNumber)(phone));
      /* istanbul ignore next */
      cov_2v4c18x81().s[52]++;
      if (
      /* istanbul ignore next */
      (cov_2v4c18x81().b[21][0]++, phoneNumber) &&
      /* istanbul ignore next */
      (cov_2v4c18x81().b[21][1]++, phoneNumber.isValid())) {
        /* istanbul ignore next */
        cov_2v4c18x81().b[20][0]++;
        cov_2v4c18x81().s[53]++;
        isValid = true;
      } else
      /* istanbul ignore next */
      {
        cov_2v4c18x81().b[20][1]++;
      }
    } catch (error) {
      // Continue to next validation method
    }
  } else
  /* istanbul ignore next */
  {
    cov_2v4c18x81().b[19][1]++;
  }
  cov_2v4c18x81().s[54]++;
  if (!isValid) {
    /* istanbul ignore next */
    cov_2v4c18x81().b[22][0]++;
    cov_2v4c18x81().s[55]++;
    try {
      /* istanbul ignore next */
      cov_2v4c18x81().s[56]++;
      // Try global validation for any format
      if ((0, libphonenumber_js_1.isValidPhoneNumber)(phone, 'US')) {
        /* istanbul ignore next */
        cov_2v4c18x81().b[23][0]++;
        cov_2v4c18x81().s[57]++;
        isValid = true;
      } else
      /* istanbul ignore next */
      {
        cov_2v4c18x81().b[23][1]++;
      }
    } catch (error) {
      // Continue to next validation method
    }
  } else
  /* istanbul ignore next */
  {
    cov_2v4c18x81().b[22][1]++;
  }
  // If none of the validation methods succeeded, throw error
  cov_2v4c18x81().s[58]++;
  if (!isValid) {
    /* istanbul ignore next */
    cov_2v4c18x81().b[24][0]++;
    cov_2v4c18x81().s[59]++;
    throw new Errors_1.ValidationError('Invalid phone number format', 'phone');
  } else
  /* istanbul ignore next */
  {
    cov_2v4c18x81().b[24][1]++;
  }
}
/**
 * Validates email format using validator.js for RFC-compliant validation
 * @param email - The email to validate
 * @throws ValidationError if email is invalid
 */
function validateEmail(email) {
  /* istanbul ignore next */
  cov_2v4c18x81().f[6]++;
  cov_2v4c18x81().s[60]++;
  if (!email) {
    /* istanbul ignore next */
    cov_2v4c18x81().b[25][0]++;
    cov_2v4c18x81().s[61]++;
    return; // Empty email is allowed
  } else
  /* istanbul ignore next */
  {
    cov_2v4c18x81().b[25][1]++;
  }
  // Use validator.js for RFC-compliant email validation
  cov_2v4c18x81().s[62]++;
  if (!validator_1.default.isEmail(email, {
    allow_utf8_local_part: false,
    require_tld: true,
    allow_ip_domain: false,
    domain_specific_validation: true
  })) {
    /* istanbul ignore next */
    cov_2v4c18x81().b[26][0]++;
    cov_2v4c18x81().s[63]++;
    throw new Errors_1.ValidationError('Invalid email format', 'email');
  } else
  /* istanbul ignore next */
  {
    cov_2v4c18x81().b[26][1]++;
  }
}
/**
 * Validates search parameters
 * @param params - The search parameters to validate
 * @throws ValidationError if any parameter is invalid
 */
function validateSearchParams(params) {
  /* istanbul ignore next */
  cov_2v4c18x81().f[7]++;
  cov_2v4c18x81().s[64]++;
  validateZipCode(params.zipCode);
  /* istanbul ignore next */
  cov_2v4c18x81().s[65]++;
  validateRadius(params.radius);
  /* istanbul ignore next */
  cov_2v4c18x81().s[66]++;
  validateBusinessType(params.businessType);
}
/**
 * Validates that a required configuration value exists
 * @param value - The configuration value
 * @param name - The name of the configuration
 * @throws ValidationError if value is missing
 */
function validateRequired(value, name) {
  /* istanbul ignore next */
  cov_2v4c18x81().f[8]++;
  cov_2v4c18x81().s[67]++;
  if (
  /* istanbul ignore next */
  (cov_2v4c18x81().b[28][0]++, value === undefined) ||
  /* istanbul ignore next */
  (cov_2v4c18x81().b[28][1]++, value === null) ||
  /* istanbul ignore next */
  (cov_2v4c18x81().b[28][2]++, value === '')) {
    /* istanbul ignore next */
    cov_2v4c18x81().b[27][0]++;
    cov_2v4c18x81().s[68]++;
    throw new Errors_1.ValidationError(`${name} is required`, name);
  } else
  /* istanbul ignore next */
  {
    cov_2v4c18x81().b[27][1]++;
  }
}
/**
 * Validates that a number is within a specified range
 * @param value - The number to validate
 * @param min - Minimum allowed value
 * @param max - Maximum allowed value
 * @param fieldName - Name of the field being validated
 * @throws ValidationError if value is out of range
 */
function validateRange(value, min, max, fieldName) {
  /* istanbul ignore next */
  cov_2v4c18x81().f[9]++;
  cov_2v4c18x81().s[69]++;
  if (
  /* istanbul ignore next */
  (cov_2v4c18x81().b[30][0]++, value < min) ||
  /* istanbul ignore next */
  (cov_2v4c18x81().b[30][1]++, value > max)) {
    /* istanbul ignore next */
    cov_2v4c18x81().b[29][0]++;
    cov_2v4c18x81().s[70]++;
    throw new Errors_1.ValidationError(`${fieldName} must be between ${min} and ${max}`, fieldName);
  } else
  /* istanbul ignore next */
  {
    cov_2v4c18x81().b[29][1]++;
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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