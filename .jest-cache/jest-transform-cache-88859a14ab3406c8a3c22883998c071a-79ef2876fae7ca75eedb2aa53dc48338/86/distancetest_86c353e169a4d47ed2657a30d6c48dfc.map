{"file": "/Users/<USER>/WebstormProjects/goo/tests/utils/distance.test.ts", "mappings": ";;AAAA,uDAMkC;AAGlC,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;IAC9B,MAAM,OAAO,GAAgB,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC;IACxE,MAAM,UAAU,GAAgB,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC;IAC5E,MAAM,OAAO,GAAgB,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC;IACxE,MAAM,MAAM,GAAgB,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,eAAe;IAEvF,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,QAAQ,GAAG,IAAA,4BAAiB,EAAC,OAAO,EAAE,UAAU,CAAC,CAAC;YAExD,0DAA0D;YAC1D,MAAM,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,QAAQ,GAAG,IAAA,4BAAiB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,QAAQ,GAAG,IAAA,4BAAiB,EAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAEpD,uDAAuD;YACvD,MAAM,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,MAAM,SAAS,GAAG,IAAA,4BAAiB,EAAC,OAAO,EAAE,UAAU,CAAC,CAAC;YACzD,MAAM,SAAS,GAAG,IAAA,4BAAiB,EAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAEzD,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAClC,MAAM,CAAC,SAAS,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,MAAM,GAAgB,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,SAAS,EAAE,CAAC;YAC3E,MAAM,MAAM,GAAgB,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,SAAS,EAAE,CAAC;YAE3E,MAAM,QAAQ,GAAG,IAAA,4BAAiB,EAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,sBAAsB;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,aAAa,GAAG,IAAA,4BAAiB,EAAC,OAAO,EAAE,UAAU,CAAC,CAAC;YAC7D,MAAM,UAAU,GAAG,IAAA,8BAAmB,EAAC,OAAO,EAAE,UAAU,CAAC,CAAC;YAE5D,sBAAsB;YACtB,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,CAAC,aAAa,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,QAAQ,GAAG,IAAA,8BAAmB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACvD,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,MAAM,WAAW,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAElD,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,MAAM,GAAG,IAAA,sBAAW,EAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAEjD,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YAC9B,MAAM,CAAC,MAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAO,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,+BAA+B;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,MAAM,GAAG,IAAA,sBAAW,EAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,MAAM,GAAG,IAAA,sBAAW,EAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;YAE/C,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YAC9B,MAAM,CAAC,MAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,MAAM,GAAG,IAAA,sBAAW,EAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YACjD,MAAM,cAAc,GAAG,IAAA,4BAAiB,EAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAE1D,MAAM,CAAC,MAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,QAAQ,GAAG,IAAA,yBAAc,EAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,QAAQ,GAAG,IAAA,yBAAc,EAAC,OAAO,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;YAC1D,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,QAAQ,GAAG,IAAA,yBAAc,EAAC,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;YACrD,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,QAAQ,GAAG,IAAA,4BAAiB,EAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAEpD,6CAA6C;YAC7C,MAAM,CAAC,IAAA,yBAAc,EAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE7D,6DAA6D;YAC7D,MAAM,CAAC,IAAA,yBAAc,EAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,MAAM,WAAW,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAElD,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,MAAM,GAAG,IAAA,yBAAc,EAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAEpD,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAE/B,+CAA+C;YAC/C,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YACnE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YAEnE,0CAA0C;YAC1C,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,MAAM,GAAG,IAAA,yBAAc,EAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAEpD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACpB,MAAM,gBAAgB,GAAG,IAAA,4BAAiB,EAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACrE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,MAAM,GAAG,IAAA,yBAAc,EAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,MAAM,GAAG,IAAA,yBAAc,EAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;YAElD,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAA,4BAAiB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,cAAc,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC;YACxC,IAAA,yBAAc,EAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAErC,qCAAqC;YACrC,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/tests/utils/distance.test.ts"], "sourcesContent": ["import {\n  calculateDistance,\n  calculateDistanceKm,\n  findClosest,\n  isWithinRadius,\n  sortByDistance\n} from '../../src/utils/distance';\nimport { Coordinates } from '../../src/models/Business';\n\ndescribe('Distance Utils', () => {\n  const newYork: Coordinates = { latitude: 40.7128, longitude: -74.0060 };\n  const losAngeles: Coordinates = { latitude: 34.0522, longitude: -118.2437 };\n  const chicago: Coordinates = { latitude: 41.8781, longitude: -87.6298 };\n  const nearby: Coordinates = { latitude: 40.7589, longitude: -73.9851 }; // Times Square\n\n  describe('calculateDistance', () => {\n    it('should calculate distance between two coordinates', () => {\n      const distance = calculateDistance(newYork, losAngeles);\n      \n      // Distance between NYC and LA is approximately 2445 miles\n      expect(distance).toBeGreaterThan(2400);\n      expect(distance).toBeLessThan(2500);\n    });\n\n    it('should return 0 for identical coordinates', () => {\n      const distance = calculateDistance(newYork, newYork);\n      expect(distance).toBe(0);\n    });\n\n    it('should calculate short distances accurately', () => {\n      const distance = calculateDistance(newYork, nearby);\n      \n      // Distance from NYC to Times Square is about 4-5 miles\n      expect(distance).toBeGreaterThan(3);\n      expect(distance).toBeLessThan(6);\n    });\n\n    it('should return positive distances regardless of coordinate order', () => {\n      const distance1 = calculateDistance(newYork, losAngeles);\n      const distance2 = calculateDistance(losAngeles, newYork);\n      \n      expect(distance1).toBe(distance2);\n      expect(distance1).toBeGreaterThan(0);\n    });\n\n    it('should handle coordinates with decimal precision', () => {\n      const coord1: Coordinates = { latitude: 40.712800, longitude: -74.006000 };\n      const coord2: Coordinates = { latitude: 40.712801, longitude: -74.006001 };\n      \n      const distance = calculateDistance(coord1, coord2);\n      expect(distance).toBeGreaterThanOrEqual(0);\n      expect(distance).toBeLessThan(0.01); // Very small distance\n    });\n  });\n\n  describe('calculateDistanceKm', () => {\n    it('should calculate distance in kilometers', () => {\n      const distanceMiles = calculateDistance(newYork, losAngeles);\n      const distanceKm = calculateDistanceKm(newYork, losAngeles);\n      \n      // 1 mile ≈ 1.60934 km\n      expect(distanceKm).toBeCloseTo(distanceMiles * 1.60934, 1);\n    });\n\n    it('should return 0 for identical coordinates', () => {\n      const distance = calculateDistanceKm(newYork, newYork);\n      expect(distance).toBe(0);\n    });\n  });\n\n  describe('findClosest', () => {\n    const coordinates = [losAngeles, chicago, nearby];\n\n    it('should find the closest coordinate', () => {\n      const result = findClosest(newYork, coordinates);\n      \n      expect(result).not.toBeNull();\n      expect(result!.coordinate).toEqual(nearby);\n      expect(result!.distance).toBeLessThan(10); // Should be less than 10 miles\n    });\n\n    it('should return null for empty array', () => {\n      const result = findClosest(newYork, []);\n      expect(result).toBeNull();\n    });\n\n    it('should return the only coordinate if array has one element', () => {\n      const result = findClosest(newYork, [chicago]);\n      \n      expect(result).not.toBeNull();\n      expect(result!.coordinate).toEqual(chicago);\n    });\n\n    it('should calculate correct distances', () => {\n      const result = findClosest(newYork, coordinates);\n      const manualDistance = calculateDistance(newYork, nearby);\n      \n      expect(result!.distance).toBe(manualDistance);\n    });\n  });\n\n  describe('isWithinRadius', () => {\n    it('should return true for points within radius', () => {\n      const isWithin = isWithinRadius(newYork, nearby, 10);\n      expect(isWithin).toBe(true);\n    });\n\n    it('should return false for points outside radius', () => {\n      const isWithin = isWithinRadius(newYork, losAngeles, 100);\n      expect(isWithin).toBe(false);\n    });\n\n    it('should return true for identical coordinates', () => {\n      const isWithin = isWithinRadius(newYork, newYork, 1);\n      expect(isWithin).toBe(true);\n    });\n\n    it('should handle edge cases at radius boundary', () => {\n      const distance = calculateDistance(newYork, nearby);\n      \n      // Should be true when radius equals distance\n      expect(isWithinRadius(newYork, nearby, distance)).toBe(true);\n      \n      // Should be false when radius is slightly less than distance\n      expect(isWithinRadius(newYork, nearby, distance - 0.01)).toBe(false);\n    });\n  });\n\n  describe('sortByDistance', () => {\n    const coordinates = [losAngeles, chicago, nearby];\n\n    it('should sort coordinates by distance from target', () => {\n      const sorted = sortByDistance(newYork, coordinates);\n      \n      expect(sorted).toHaveLength(3);\n      \n      // Should be sorted by distance (closest first)\n      expect(sorted[0].distance).toBeLessThanOrEqual(sorted[1].distance);\n      expect(sorted[1].distance).toBeLessThanOrEqual(sorted[2].distance);\n      \n      // Closest should be Times Square (nearby)\n      expect(sorted[0].coordinate).toEqual(nearby);\n    });\n\n    it('should include correct distances', () => {\n      const sorted = sortByDistance(newYork, coordinates);\n      \n      sorted.forEach(item => {\n        const expectedDistance = calculateDistance(newYork, item.coordinate);\n        expect(item.distance).toBe(expectedDistance);\n      });\n    });\n\n    it('should handle empty array', () => {\n      const sorted = sortByDistance(newYork, []);\n      expect(sorted).toHaveLength(0);\n    });\n\n    it('should handle single coordinate', () => {\n      const sorted = sortByDistance(newYork, [chicago]);\n      \n      expect(sorted).toHaveLength(1);\n      expect(sorted[0].coordinate).toEqual(chicago);\n      expect(sorted[0].distance).toBe(calculateDistance(newYork, chicago));\n    });\n\n    it('should maintain original coordinates unchanged', () => {\n      const originalCoords = [...coordinates];\n      sortByDistance(newYork, coordinates);\n      \n      // Original array should be unchanged\n      expect(coordinates).toEqual(originalCoords);\n    });\n  });\n});\n"], "version": 3}