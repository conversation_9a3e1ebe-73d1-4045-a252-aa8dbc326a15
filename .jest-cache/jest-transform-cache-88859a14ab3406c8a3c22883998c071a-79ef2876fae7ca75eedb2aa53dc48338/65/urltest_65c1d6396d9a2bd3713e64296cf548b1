a6abb51f59150fff8f39d9f6fc814d59
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const url_1 = require("../../src/utils/url");
describe('URL Utils', () => {
    describe('normalizeUrl', () => {
        it('should normalize URLs by adding protocol and removing trailing slash', () => {
            expect((0, url_1.normalizeUrl)('example.com')).toBe('https://example.com');
            expect((0, url_1.normalizeUrl)('www.example.com')).toBe('https://www.example.com');
            expect((0, url_1.normalizeUrl)('https://example.com/')).toBe('https://example.com');
            expect((0, url_1.normalizeUrl)('http://example.com/')).toBe('http://example.com');
        });
        it('should preserve existing protocols', () => {
            expect((0, url_1.normalizeUrl)('https://example.com')).toBe('https://example.com');
            expect((0, url_1.normalizeUrl)('http://example.com')).toBe('http://example.com');
        });
        it('should handle URLs with paths', () => {
            expect((0, url_1.normalizeUrl)('example.com/path')).toBe('https://example.com/path');
            expect((0, url_1.normalizeUrl)('https://example.com/path/')).toBe('https://example.com/path');
        });
        it('should handle URLs with query parameters', () => {
            expect((0, url_1.normalizeUrl)('example.com?param=value')).toBe('https://example.com?param=value');
            expect((0, url_1.normalizeUrl)('https://example.com/path?param=value')).toBe('https://example.com/path?param=value');
        });
        it('should return empty string for invalid input', () => {
            expect((0, url_1.normalizeUrl)('')).toBe('');
            expect((0, url_1.normalizeUrl)('   ')).toBe('');
        });
    });
    describe('isValidUrl', () => {
        it('should return true for valid URLs', () => {
            expect((0, url_1.isValidUrl)('https://example.com')).toBe(true);
            expect((0, url_1.isValidUrl)('http://test.org')).toBe(true);
            expect((0, url_1.isValidUrl)('https://subdomain.example.com/path')).toBe(true);
            expect((0, url_1.isValidUrl)('https://example.com:8080')).toBe(true);
        });
        it('should return false for invalid URLs', () => {
            expect((0, url_1.isValidUrl)('example.com')).toBe(false);
            expect((0, url_1.isValidUrl)('ftp://example.com')).toBe(false);
            expect((0, url_1.isValidUrl)('')).toBe(false);
            expect((0, url_1.isValidUrl)('not-a-url')).toBe(false);
            expect((0, url_1.isValidUrl)('javascript:alert(1)')).toBe(false);
        });
    });
    describe('extractDomain', () => {
        it('should extract domain from URLs', () => {
            expect((0, url_1.extractDomain)('https://example.com')).toBe('example.com');
            expect((0, url_1.extractDomain)('http://www.test.org')).toBe('www.test.org');
            expect((0, url_1.extractDomain)('https://subdomain.example.com/path')).toBe('subdomain.example.com');
            expect((0, url_1.extractDomain)('https://example.com:8080/path')).toBe('example.com');
        });
        it('should return empty string for invalid URLs', () => {
            expect((0, url_1.extractDomain)('invalid-url')).toBe('');
            expect((0, url_1.extractDomain)('')).toBe('');
        });
    });
    describe('addProtocol', () => {
        it('should add https protocol to URLs without protocol', () => {
            expect((0, url_1.addProtocol)('example.com')).toBe('https://example.com');
            expect((0, url_1.addProtocol)('www.example.com')).toBe('https://www.example.com');
        });
        it('should preserve existing protocols', () => {
            expect((0, url_1.addProtocol)('https://example.com')).toBe('https://example.com');
            expect((0, url_1.addProtocol)('http://example.com')).toBe('http://example.com');
        });
        it('should handle empty or invalid input', () => {
            expect((0, url_1.addProtocol)('')).toBe('');
            expect((0, url_1.addProtocol)('   ')).toBe('');
        });
    });
    describe('cleanUrl', () => {
        it('should remove common tracking parameters', () => {
            expect((0, url_1.cleanUrl)('https://example.com?utm_source=google')).toBe('https://example.com');
            expect((0, url_1.cleanUrl)('https://example.com?utm_medium=cpc&param=value')).toBe('https://example.com/?param=value');
            expect((0, url_1.cleanUrl)('https://example.com?fbclid=123&gclid=456')).toBe('https://example.com');
        });
        it('should preserve important parameters', () => {
            expect((0, url_1.cleanUrl)('https://example.com?id=123&page=2')).toBe('https://example.com/?id=123&page=2');
        });
        it('should handle URLs without parameters', () => {
            expect((0, url_1.cleanUrl)('https://example.com')).toBe('https://example.com');
            expect((0, url_1.cleanUrl)('https://example.com/path')).toBe('https://example.com/path');
        });
        it('should handle malformed URLs gracefully', () => {
            expect((0, url_1.cleanUrl)('not-a-url')).toBe('not-a-url');
            expect((0, url_1.cleanUrl)('')).toBe('');
        });
        it('should handle all tracking parameters', () => {
            const urlWithAllTracking = 'https://example.com?utm_source=google&utm_medium=cpc&utm_campaign=test&utm_term=keyword&utm_content=ad&fbclid=123&gclid=456&msclkid=789&ref=twitter&source=facebook';
            // Note: ref and source are not in the tracking parameters list, so they remain
            expect((0, url_1.cleanUrl)(urlWithAllTracking)).toBe('https://example.com/?ref=twitter&source=facebook');
        });
        it('should handle mixed tracking and non-tracking parameters', () => {
            const mixedUrl = 'https://example.com?id=123&utm_source=google&page=2&fbclid=456&category=tech';
            expect((0, url_1.cleanUrl)(mixedUrl)).toBe('https://example.com/?id=123&page=2&category=tech');
        });
        it('should preserve hash fragments', () => {
            expect((0, url_1.cleanUrl)('https://example.com?utm_source=google#section1')).toBe('https://example.com/#section1');
        });
        it('should handle URLs with ports', () => {
            expect((0, url_1.cleanUrl)('https://example.com:8080?utm_source=google')).toBe('https://example.com:8080');
        });
        it('should handle URLs with paths', () => {
            expect((0, url_1.cleanUrl)('https://example.com/path/to/page?utm_source=google&id=123')).toBe('https://example.com/path/to/page?id=123');
        });
    });
    describe('edge cases and error handling', () => {
        it('should handle malformed URLs in isValidUrl', () => {
            expect((0, url_1.isValidUrl)('http://')).toBe(false);
            expect((0, url_1.isValidUrl)('https://')).toBe(false);
            expect((0, url_1.isValidUrl)('ftp://example.com')).toBe(false);
            expect((0, url_1.isValidUrl)('javascript:alert(1)')).toBe(false);
        });
        it('should handle special characters in URLs', () => {
            // URLs with spaces are actually valid in modern browsers
            expect((0, url_1.isValidUrl)('https://example.com/path with spaces')).toBe(true);
            expect((0, url_1.isValidUrl)('https://example.com/path%20with%20encoded%20spaces')).toBe(true);
        });
        it('should handle international domain names', () => {
            expect((0, url_1.isValidUrl)('https://例え.テスト')).toBe(true);
            expect((0, url_1.isValidUrl)('https://xn--r8jz45g.xn--zckzah')).toBe(true); // Punycode
        });
        it('should handle URLs with authentication', () => {
            expect((0, url_1.isValidUrl)('https://user:<EMAIL>')).toBe(true);
        });
        it('should handle normalizeUrl edge cases', () => {
            // normalizeUrl checks for lowercase protocols, so uppercase gets https:// added
            expect((0, url_1.normalizeUrl)('HTTP://EXAMPLE.COM')).toBe('https://HTTP://EXAMPLE.COM');
            // normalizeUrl removes trailing slash for root path, which also removes default ports
            expect((0, url_1.normalizeUrl)('https://example.com:443')).toBe('https://example.com');
            expect((0, url_1.normalizeUrl)('http://example.com:80')).toBe('http://example.com');
            expect((0, url_1.normalizeUrl)('https://example.com:8080')).toBe('https://example.com:8080');
        });
        it('should handle extractDomain edge cases', () => {
            expect((0, url_1.extractDomain)('https://sub.example.com')).toBe('sub.example.com');
            expect((0, url_1.extractDomain)('https://example.com:8080')).toBe('example.com');
            expect((0, url_1.extractDomain)('https://user:<EMAIL>')).toBe('example.com');
        });
        it('should handle URLs with unusual but valid schemes', () => {
            expect((0, url_1.isValidUrl)('https://example.com')).toBe(true);
            expect((0, url_1.isValidUrl)('http://example.com')).toBe(true);
        });
        it('should handle empty and null inputs', () => {
            expect((0, url_1.isValidUrl)('')).toBe(false);
            expect((0, url_1.extractDomain)('')).toBe('');
            expect((0, url_1.normalizeUrl)('')).toBe('');
        });
        it('should handle URLs with query parameters only', () => {
            expect((0, url_1.cleanUrl)('https://example.com?')).toBe('https://example.com');
            expect((0, url_1.cleanUrl)('https://example.com?&')).toBe('https://example.com');
        });
        it('should handle URLs with encoded characters', () => {
            expect((0, url_1.cleanUrl)('https://example.com?utm_source=google%20ads&id=123')).toBe('https://example.com/?id=123');
        });
        it('should handle case sensitivity in domains', () => {
            // normalizeUrl preserves case in URLs
            expect((0, url_1.normalizeUrl)('https://EXAMPLE.COM/PATH')).toBe('https://EXAMPLE.COM/PATH');
        });
        it('should handle trailing slashes consistently', () => {
            // normalizeUrl removes trailing slash for root path
            expect((0, url_1.normalizeUrl)('https://example.com/')).toBe('https://example.com');
            expect((0, url_1.normalizeUrl)('https://example.com')).toBe('https://example.com');
        });
        it('should handle IPv4 addresses', () => {
            expect((0, url_1.isValidUrl)('https://***********')).toBe(true);
            expect((0, url_1.extractDomain)('https://***********:8080')).toBe('***********');
        });
        it('should handle IPv6 addresses', () => {
            expect((0, url_1.isValidUrl)('https://[::1]')).toBe(true);
            expect((0, url_1.isValidUrl)('https://[2001:db8::1]')).toBe(true);
        });
        it('should handle localhost variations', () => {
            expect((0, url_1.isValidUrl)('http://localhost')).toBe(true);
            expect((0, url_1.isValidUrl)('http://localhost:3000')).toBe(true);
            expect((0, url_1.isValidUrl)('https://127.0.0.1')).toBe(true);
        });
        it('should handle URL parsing failures in normalizeUrl', () => {
            // Test with a URL that would cause parsing issues
            // The normalizeUrl function will try to add protocol if missing
            expect((0, url_1.normalizeUrl)('invalid://malformed/url/')).toBe('https://invalid//malformed/url');
            // Test with malformed URLs that get protocol added
            expect((0, url_1.normalizeUrl)('malformed-url')).toBe('https://malformed-url');
        });
        it('should handle errors in areUrlsEquivalent', () => {
            // Test with invalid URLs that cause parsing errors
            expect((0, url_1.areUrlsEquivalent)('not-a-url', 'also-not-a-url')).toBe(false);
            expect((0, url_1.areUrlsEquivalent)('http://valid.com', 'not-a-url')).toBe(false);
            expect((0, url_1.areUrlsEquivalent)('', '')).toBe(false);
        });
        it('should handle validateAndNormalizeUrl edge cases', () => {
            // Test with empty/null inputs
            expect((0, url_1.validateAndNormalizeUrl)('')).toBe(null);
            expect((0, url_1.validateAndNormalizeUrl)('   ')).toBe(null);
            // Test with URLs that get protocol added (validateAndNormalizeUrl adds https:// to domain-like strings)
            expect((0, url_1.validateAndNormalizeUrl)('not-a-url')).toBe('https://not-a-url'); // Gets protocol added
            expect((0, url_1.validateAndNormalizeUrl)('ftp://invalid-protocol.com')).toBe('https://ftp://invalid-protocol.com'); // Gets protocol added
            // Test with valid URLs
            expect((0, url_1.validateAndNormalizeUrl)('example.com')).toBe('https://example.com');
            expect((0, url_1.validateAndNormalizeUrl)('http://example.com')).toBe('http://example.com');
        });
        it('should handle areUrlsEquivalent with www normalization', () => {
            expect((0, url_1.areUrlsEquivalent)('http://www.example.com', 'https://example.com')).toBe(true);
            expect((0, url_1.areUrlsEquivalent)('https://www.example.com/path', 'http://example.com/path')).toBe(true);
            expect((0, url_1.areUrlsEquivalent)('www.example.com', 'example.com')).toBe(true);
        });
        it('should handle normalizeUrl with very short URLs', () => {
            // Test URLs that are too short for trailing slash removal
            expect((0, url_1.normalizeUrl)('http://')).toBe('http://');
            // This gets protocol added since it doesn't look like a complete URL
            expect((0, url_1.normalizeUrl)('https:/')).toBe('https://https');
        });
        it('should handle URL parsing edge cases', () => {
            // Test malformed URLs that might cause parsing issues
            expect((0, url_1.extractDomain)('://malformed')).toBe('');
            expect((0, url_1.isValidUrl)('://malformed')).toBe(false);
            expect((0, url_1.addProtocol)('://malformed')).toBe('https://://malformed');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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