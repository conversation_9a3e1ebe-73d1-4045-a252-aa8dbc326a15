{"file": "/Users/<USER>/WebstormProjects/goo/src/BusinessSearchApp.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,4CAAqD;AACrD,8DAAmG;AACnG,oDAAwD;AACxD,0DAA8D;AAC9D,wEAA4E;AAC5E,wDAAkE;AAClE,qDAA2D;AAC3D,2CAAgD;AAchD;;GAEG;AACH,MAAa,iBAAiB;IAK5B,YAAY,SAAoB,EAAE;QAChC,mBAAmB;QACnB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;QAClE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,2BAAkB,CAC1B,6GAA6G,EAC7G,uBAAuB,CACxB,CAAC;QACJ,CAAC;QAED,4BAA4B;QAC5B,IAAI,CAAC,MAAM,GAAG;YACZ,MAAM;YACN,oBAAoB,EAAE,MAAM,CAAC,oBAAoB,IAAI,EAAE;YACvD,qBAAqB,EAAE,MAAM,CAAC,qBAAqB,IAAI,CAAC;YACxD,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,IAAI,IAAI;YACjD,0BAA0B,EAAE,MAAM,CAAC,0BAA0B,IAAI,6BAAiB,CAAC,mBAAmB;YACtG,kBAAkB,EAAE,MAAM,CAAC,kBAAkB,IAAI,6BAAiB,CAAC,UAAU;SAC9E,CAAC;QAEF,sBAAsB;QACtB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,gBAAgB,CAAC,OAAsB;QAC3C,mCAAmC;QACnC,MAAM,EAAE,oBAAoB,EAAE,GAAG,wDAAa,oBAAoB,GAAC,CAAC;QACpE,oBAAoB,CAAC;YACnB,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,YAAY,EAAE,OAAO,CAAC,YAAY;SACnC,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAE1E,mEAAmE;QACnE,IAAI,CAAC;YACH,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,4DAA4D;YAC5D,OAAO,CAAC,IAAI,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,gBAAgB;QACd,oDAAoD;QACpD,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,CAAC;QACpE,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC;QAEjE,0DAA0D;QAC1D,MAAM,UAAU,GAAG,CAAC,GAAG,aAAa,EAAE,GAAG,iBAAiB,CAAC,CAAC;QAC5D,MAAM,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;QAEhE,mCAAmC;QACnC,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACjC,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAC5F,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,EAAE,CAAC;QAChD,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;IAClC,CAAC;IAED;;;OAGG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,EAAE,CAAC;IAC1D,CAAC;IAED;;;OAGG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC;IACvC,CAAC;IAED;;;;OAIG;IACH,UAAU,CAAC,QAAgB;QACzB,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,YAAY;QACV,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;QAEhC,uBAAuB;QACvB,MAAM,gBAAgB,GAAG,IAAI,4BAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAClE,MAAM,mBAAmB,GAAG,IAAI,kCAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACxE,MAAM,0BAA0B,GAAG,IAAI,gDAA0B,EAAE,CAAC;QAEpE,gBAAgB,CAAC,UAAU,EAAE,CAAC;QAC9B,mBAAmB,CAAC,UAAU,EAAE,CAAC;QACjC,0BAA0B,CAAC,UAAU,EAAE,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACH,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,eAAe;QAMnB,MAAM,MAAM,GAAG;YACb,SAAS,EAAE,KAAK;YAChB,MAAM,EAAE,KAAK;YACb,mBAAmB,EAAE,KAAK;YAC1B,OAAO,EAAE,KAAK;SACf,CAAC;QAEF,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,gBAAgB,GAAG,IAAI,4BAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAClE,MAAM,gBAAgB,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,CAAC;YACH,eAAe;YACf,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC;gBAChD,YAAY,EAAE;oBACZ,OAAO,EAAE,OAAO;oBAChB,MAAM,EAAE,CAAC;oBACT,YAAY,EAAE,MAAM;oBACpB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;gBACD,OAAO,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE;gBAClD,UAAU,EAAE;oBACV,UAAU,EAAE,CAAC;oBACb,gBAAgB,EAAE,CAAC;oBACnB,mBAAmB,EAAE,CAAC;oBACtB,mBAAmB,EAAE,CAAC;oBACtB,cAAc,EAAE,CAAC;iBAClB;aACF,CAAC,CAAC;YACH,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;QAED,wDAAwD;QACxD,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAElC,yCAAyC;QACzC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC;QAEjC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,0BAA0B;QAC1B,IAAA,kCAAoB,EAClB,IAAI,CAAC,MAAM,CAAC,0BAA0B,EACtC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAC/B,CAAC;QAEF,2BAA2B;QAC3B,MAAM,gBAAgB,GAAG,IAAI,4BAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAClE,MAAM,mBAAmB,GAAG,IAAI,kCAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACxE,MAAM,0BAA0B,GAAG,IAAI,gDAA0B,EAAE,CAAC;QAEpE,0CAA0C;QAC1C,IAAI,CAAC,qBAAqB,GAAG,IAAI,sCAAqB,CACpD,gBAAgB,EAChB,mBAAmB,EACnB,0BAA0B,CAC3B,CAAC;QAEF,0BAA0B;QAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,EAAE,CAAC;IACvC,CAAC;IAED;;;;OAIG;IACK,wBAAwB,CAAC,OAAuB;QACtD,MAAM,IAAI,GAAG,IAAI,GAAG,EAAU,CAAC;QAC/B,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC7B,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,IAAI,MAAM,CAAC,YAAY,CAAC,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC;YAC1J,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAClB,OAAO,KAAK,CAAC;YACf,CAAC;YACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACd,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AA1PD,8CA0PC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/src/BusinessSearchApp.ts"], "sourcesContent": ["import { SearchResult } from './models/Business';\nimport { ConfigurationError } from './models/Errors';\nimport { BusinessSearchService, SearchRequest, SearchStatistics } from './services/businessSearch';\nimport { GeocodingService } from './services/geocoding';\nimport { GooglePlacesService } from './services/googlePlaces';\nimport { WebsiteVerificationService } from './services/websiteVerification';\nimport { DataManager, StorageInfo } from './services/dataManager';\nimport { getGlobalRateLimiter } from './utils/rateLimiter';\nimport { RATE_LIMIT_CONFIG } from './constants';\n\n/**\n * Application configuration options\n */\nexport interface AppConfig {\n  apiKey?: string;\n  cacheExpirationHours?: number;\n  maxConcurrentRequests?: number;\n  requestTimeoutMs?: number;\n  rateLimitRequestsPerSecond?: number;\n  rateLimitBurstSize?: number;\n}\n\n/**\n * Main application class that orchestrates all business search functionality\n */\nexport class BusinessSearchApp {\n  private businessSearchService: BusinessSearchService;\n  private dataManager: DataManager;\n  private config: Required<AppConfig>;\n\n  constructor(config: AppConfig = {}) {\n    // Validate API key\n    const apiKey = config.apiKey || process.env.GOOGLE_PLACES_API_KEY;\n    if (!apiKey) {\n      throw new ConfigurationError(\n        'Google Places API key is required. Set GOOGLE_PLACES_API_KEY environment variable or pass apiKey in config.',\n        'GOOGLE_PLACES_API_KEY'\n      );\n    }\n\n    // Set default configuration\n    this.config = {\n      apiKey,\n      cacheExpirationHours: config.cacheExpirationHours || 24,\n      maxConcurrentRequests: config.maxConcurrentRequests || 5,\n      requestTimeoutMs: config.requestTimeoutMs || 5000,\n      rateLimitRequestsPerSecond: config.rateLimitRequestsPerSecond || RATE_LIMIT_CONFIG.REQUESTS_PER_SECOND,\n      rateLimitBurstSize: config.rateLimitBurstSize || RATE_LIMIT_CONFIG.BURST_SIZE,\n    };\n\n    // Initialize services\n    this.initializeServices();\n  }\n\n  /**\n   * Searches for businesses and categorizes them by website presence\n   * @param request - Search request parameters\n   * @returns Promise resolving to categorized search results\n   */\n  async searchBusinesses(request: SearchRequest): Promise<SearchResult> {\n    // Validate search parameters first\n    const { validateSearchParams } = await import('./utils/validation');\n    validateSearchParams({\n      zipCode: request.zipCode,\n      radius: request.radius,\n      businessType: request.businessType\n    });\n\n    const result = await this.businessSearchService.searchBusinesses(request);\n    \n    // Save result to persistent storage (but don't fail if save fails)\n    try {\n      this.dataManager.saveSearchResult(result);\n    } catch (error) {\n      // Log the error but don't throw - the search was successful\n      console.warn('Failed to save search result:', error);\n    }\n    \n    return result;\n  }\n\n  /**\n   * Gets search history from both memory and persistent storage\n   * @returns Array of previous search results\n   */\n  getSearchHistory(): SearchResult[] {\n    // Combine in-memory history with persistent storage\n    const memoryHistory = this.businessSearchService.getSearchHistory();\n    const persistentHistory = this.dataManager.getAllSearchResults();\n    \n    // Merge and deduplicate based on timestamp and parameters\n    const allResults = [...memoryHistory, ...persistentHistory];\n    const uniqueResults = this.deduplicateSearchResults(allResults);\n    \n    // Sort by timestamp (newest first)\n    return uniqueResults.sort((a, b) => \n      new Date(b.searchParams.timestamp).getTime() - new Date(a.searchParams.timestamp).getTime()\n    );\n  }\n\n  /**\n   * Clears all search history from memory and persistent storage\n   */\n  clearSearchHistory(): void {\n    this.businessSearchService.clearSearchHistory();\n    this.dataManager.clearAllData();\n  }\n\n  /**\n   * Gets aggregated search statistics\n   * @returns Search statistics\n   */\n  getSearchStatistics(): SearchStatistics {\n    return this.businessSearchService.getSearchStatistics();\n  }\n\n  /**\n   * Exports all search data as JSON\n   * @returns JSON string containing all search results\n   */\n  exportData(): string {\n    return this.dataManager.exportData();\n  }\n\n  /**\n   * Imports search data from JSON\n   * @param jsonData - JSON string containing search results\n   * @returns Number of imported results\n   */\n  importData(jsonData: string): number {\n    return this.dataManager.importData(jsonData);\n  }\n\n  /**\n   * Gets information about current storage usage\n   * @returns Storage information\n   */\n  getStorageInfo(): StorageInfo {\n    return this.dataManager.getStorageInfo();\n  }\n\n  /**\n   * Clears all application data and resets statistics\n   */\n  clearAllData(): void {\n    this.businessSearchService.reset();\n    this.dataManager.clearAllData();\n    \n    // Clear service caches\n    const geocodingService = new GeocodingService(this.config.apiKey);\n    const googlePlacesService = new GooglePlacesService(this.config.apiKey);\n    const websiteVerificationService = new WebsiteVerificationService();\n    \n    geocodingService.clearCache();\n    googlePlacesService.clearCache();\n    websiteVerificationService.clearCache();\n  }\n\n  /**\n   * Performs cleanup of expired data\n   * @returns Number of expired entries removed\n   */\n  cleanupExpiredData(): number {\n    return this.dataManager.cleanupExpiredData();\n  }\n\n  /**\n   * Gets the current application configuration\n   * @returns Application configuration\n   */\n  getConfig(): Readonly<Required<AppConfig>> {\n    return { ...this.config };\n  }\n\n  /**\n   * Gets service health status\n   * @returns Health status of all services\n   */\n  async getHealthStatus(): Promise<{\n    geocoding: boolean;\n    places: boolean;\n    websiteVerification: boolean;\n    storage: boolean;\n  }> {\n    const status = {\n      geocoding: false,\n      places: false,\n      websiteVerification: false,\n      storage: false,\n    };\n\n    try {\n      // Test geocoding service\n      const geocodingService = new GeocodingService(this.config.apiKey);\n      await geocodingService.zipCodeToCoordinates('10001');\n      status.geocoding = true;\n    } catch (error) {\n      console.warn('Geocoding service health check failed:', error);\n    }\n\n    try {\n      // Test storage\n      const testKey = this.dataManager.saveSearchResult({\n        searchParams: {\n          zipCode: '00000',\n          radius: 1,\n          businessType: 'test',\n          timestamp: new Date(),\n        },\n        results: { withWebsites: [], withoutWebsites: [] },\n        statistics: {\n          totalFound: 0,\n          withWebsiteCount: 0,\n          withoutWebsiteCount: 0,\n          websiteAdoptionRate: 0,\n          searchDuration: 0,\n        },\n      });\n      this.dataManager.deleteSearchResult(testKey);\n      status.storage = true;\n    } catch (error) {\n      console.warn('Storage health check failed:', error);\n    }\n\n    // Website verification is always available (uses fetch)\n    status.websiteVerification = true;\n\n    // Places API health depends on geocoding\n    status.places = status.geocoding;\n\n    return status;\n  }\n\n  /**\n   * Initializes all required services\n   */\n  private initializeServices(): void {\n    // Initialize rate limiter\n    getGlobalRateLimiter(\n      this.config.rateLimitRequestsPerSecond,\n      this.config.rateLimitBurstSize\n    );\n\n    // Initialize core services\n    const geocodingService = new GeocodingService(this.config.apiKey);\n    const googlePlacesService = new GooglePlacesService(this.config.apiKey);\n    const websiteVerificationService = new WebsiteVerificationService();\n\n    // Initialize main business search service\n    this.businessSearchService = new BusinessSearchService(\n      geocodingService,\n      googlePlacesService,\n      websiteVerificationService\n    );\n\n    // Initialize data manager\n    this.dataManager = new DataManager();\n  }\n\n  /**\n   * Deduplicates search results based on search parameters and timestamp\n   * @param results - Array of search results to deduplicate\n   * @returns Deduplicated array\n   */\n  private deduplicateSearchResults(results: SearchResult[]): SearchResult[] {\n    const seen = new Set<string>();\n    return results.filter(result => {\n      const key = `${result.searchParams.zipCode}_${result.searchParams.radius}_${result.searchParams.businessType}_${result.searchParams.timestamp.getTime()}`;\n      if (seen.has(key)) {\n        return false;\n      }\n      seen.add(key);\n      return true;\n    });\n  }\n}\n"], "version": 3}