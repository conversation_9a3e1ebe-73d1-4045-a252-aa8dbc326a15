747257eca258ff445b5f26a42e53780e
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessSearchApp = void 0;
const Errors_1 = require("./models/Errors");
const businessSearch_1 = require("./services/businessSearch");
const geocoding_1 = require("./services/geocoding");
const googlePlaces_1 = require("./services/googlePlaces");
const websiteVerification_1 = require("./services/websiteVerification");
const dataManager_1 = require("./services/dataManager");
const rateLimiter_1 = require("./utils/rateLimiter");
const constants_1 = require("./constants");
/**
 * Main application class that orchestrates all business search functionality
 */
class BusinessSearchApp {
    constructor(config = {}) {
        // Validate API key
        const apiKey = config.apiKey || process.env.GOOGLE_PLACES_API_KEY;
        if (!apiKey) {
            throw new Errors_1.ConfigurationError('Google Places API key is required. Set GOOGLE_PLACES_API_KEY environment variable or pass apiKey in config.', 'GOOGLE_PLACES_API_KEY');
        }
        // Set default configuration
        this.config = {
            apiKey,
            cacheExpirationHours: config.cacheExpirationHours || 24,
            maxConcurrentRequests: config.maxConcurrentRequests || 5,
            requestTimeoutMs: config.requestTimeoutMs || 5000,
            rateLimitRequestsPerSecond: config.rateLimitRequestsPerSecond || constants_1.RATE_LIMIT_CONFIG.REQUESTS_PER_SECOND,
            rateLimitBurstSize: config.rateLimitBurstSize || constants_1.RATE_LIMIT_CONFIG.BURST_SIZE,
        };
        // Initialize services
        this.initializeServices();
    }
    /**
     * Searches for businesses and categorizes them by website presence
     * @param request - Search request parameters
     * @returns Promise resolving to categorized search results
     */
    async searchBusinesses(request) {
        // Validate search parameters first
        const { validateSearchParams } = await Promise.resolve().then(() => __importStar(require('./utils/validation')));
        validateSearchParams({
            zipCode: request.zipCode,
            radius: request.radius,
            businessType: request.businessType
        });
        const result = await this.businessSearchService.searchBusinesses(request);
        // Save result to persistent storage (but don't fail if save fails)
        try {
            this.dataManager.saveSearchResult(result);
        }
        catch (error) {
            // Log the error but don't throw - the search was successful
            console.warn('Failed to save search result:', error);
        }
        return result;
    }
    /**
     * Gets search history from both memory and persistent storage
     * @returns Array of previous search results
     */
    getSearchHistory() {
        // Combine in-memory history with persistent storage
        const memoryHistory = this.businessSearchService.getSearchHistory();
        const persistentHistory = this.dataManager.getAllSearchResults();
        // Merge and deduplicate based on timestamp and parameters
        const allResults = [...memoryHistory, ...persistentHistory];
        const uniqueResults = this.deduplicateSearchResults(allResults);
        // Sort by timestamp (newest first)
        return uniqueResults.sort((a, b) => new Date(b.searchParams.timestamp).getTime() - new Date(a.searchParams.timestamp).getTime());
    }
    /**
     * Clears all search history from memory and persistent storage
     */
    clearSearchHistory() {
        this.businessSearchService.clearSearchHistory();
        this.dataManager.clearAllData();
    }
    /**
     * Gets aggregated search statistics
     * @returns Search statistics
     */
    getSearchStatistics() {
        return this.businessSearchService.getSearchStatistics();
    }
    /**
     * Exports all search data as JSON
     * @returns JSON string containing all search results
     */
    exportData() {
        return this.dataManager.exportData();
    }
    /**
     * Imports search data from JSON
     * @param jsonData - JSON string containing search results
     * @returns Number of imported results
     */
    importData(jsonData) {
        return this.dataManager.importData(jsonData);
    }
    /**
     * Gets information about current storage usage
     * @returns Storage information
     */
    getStorageInfo() {
        return this.dataManager.getStorageInfo();
    }
    /**
     * Clears all application data and resets statistics
     */
    clearAllData() {
        this.businessSearchService.reset();
        this.dataManager.clearAllData();
        // Clear service caches
        const geocodingService = new geocoding_1.GeocodingService(this.config.apiKey);
        const googlePlacesService = new googlePlaces_1.GooglePlacesService(this.config.apiKey);
        const websiteVerificationService = new websiteVerification_1.WebsiteVerificationService();
        geocodingService.clearCache();
        googlePlacesService.clearCache();
        websiteVerificationService.clearCache();
    }
    /**
     * Performs cleanup of expired data
     * @returns Number of expired entries removed
     */
    cleanupExpiredData() {
        return this.dataManager.cleanupExpiredData();
    }
    /**
     * Gets the current application configuration
     * @returns Application configuration
     */
    getConfig() {
        return { ...this.config };
    }
    /**
     * Gets service health status
     * @returns Health status of all services
     */
    async getHealthStatus() {
        const status = {
            geocoding: false,
            places: false,
            websiteVerification: false,
            storage: false,
        };
        try {
            // Test geocoding service
            const geocodingService = new geocoding_1.GeocodingService(this.config.apiKey);
            await geocodingService.zipCodeToCoordinates('10001');
            status.geocoding = true;
        }
        catch (error) {
            console.warn('Geocoding service health check failed:', error);
        }
        try {
            // Test storage
            const testKey = this.dataManager.saveSearchResult({
                searchParams: {
                    zipCode: '00000',
                    radius: 1,
                    businessType: 'test',
                    timestamp: new Date(),
                },
                results: { withWebsites: [], withoutWebsites: [] },
                statistics: {
                    totalFound: 0,
                    withWebsiteCount: 0,
                    withoutWebsiteCount: 0,
                    websiteAdoptionRate: 0,
                    searchDuration: 0,
                },
            });
            this.dataManager.deleteSearchResult(testKey);
            status.storage = true;
        }
        catch (error) {
            console.warn('Storage health check failed:', error);
        }
        // Website verification is always available (uses fetch)
        status.websiteVerification = true;
        // Places API health depends on geocoding
        status.places = status.geocoding;
        return status;
    }
    /**
     * Initializes all required services
     */
    initializeServices() {
        // Initialize rate limiter
        (0, rateLimiter_1.getGlobalRateLimiter)(this.config.rateLimitRequestsPerSecond, this.config.rateLimitBurstSize);
        // Initialize core services
        const geocodingService = new geocoding_1.GeocodingService(this.config.apiKey);
        const googlePlacesService = new googlePlaces_1.GooglePlacesService(this.config.apiKey);
        const websiteVerificationService = new websiteVerification_1.WebsiteVerificationService();
        // Initialize main business search service
        this.businessSearchService = new businessSearch_1.BusinessSearchService(geocodingService, googlePlacesService, websiteVerificationService);
        // Initialize data manager
        this.dataManager = new dataManager_1.DataManager();
    }
    /**
     * Deduplicates search results based on search parameters and timestamp
     * @param results - Array of search results to deduplicate
     * @returns Deduplicated array
     */
    deduplicateSearchResults(results) {
        const seen = new Set();
        return results.filter(result => {
            const key = `${result.searchParams.zipCode}_${result.searchParams.radius}_${result.searchParams.businessType}_${result.searchParams.timestamp.getTime()}`;
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }
}
exports.BusinessSearchApp = BusinessSearchApp;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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