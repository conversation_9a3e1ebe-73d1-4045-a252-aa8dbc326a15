{"file": "/Users/<USER>/WebstormProjects/goo/src/models/Errors.ts", "mappings": ";;;AAAA;;GAEG;AACH,MAAsB,QAAS,SAAQ,KAAK;IAI1C,YAAY,OAAe,EAAkB,KAAa;QACxD,KAAK,CAAC,OAAO,CAAC,CAAC;QAD4B,UAAK,GAAL,KAAK,CAAQ;QAExD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAElC,8DAA8D;QAC9D,IAAI,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC5B,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;CACF;AAbD,4BAaC;AAED;;GAEG;AACH,MAAa,eAAgB,SAAQ,QAAQ;IAI3C,YAAY,OAAe,EAAkB,KAAc;QACzD,KAAK,CAAC,OAAO,CAAC,CAAC;QAD4B,UAAK,GAAL,KAAK,CAAS;QAHlD,SAAI,GAAG,kBAAkB,CAAC;QAC1B,eAAU,GAAG,GAAG,CAAC;IAI1B,CAAC;CACF;AAPD,0CAOC;AAED;;GAEG;AACH,MAAa,QAAS,SAAQ,QAAQ;IAIpC,YACE,OAAe,EACf,aAAqB,GAAG,EACR,OAAgB,EAChC,KAAa;QAEb,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAHN,YAAO,GAAP,OAAO,CAAS;QANzB,SAAI,GAAG,WAAW,CAAC;QAU1B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;CACF;AAbD,4BAaC;AAED;;GAEG;AACH,MAAa,cAAe,SAAQ,QAAQ;IAI1C,YACE,UAAkB,qBAAqB,EACvB,UAAmB;QAEnC,KAAK,CAAC,OAAO,CAAC,CAAC;QAFC,eAAU,GAAV,UAAU,CAAS;QAL5B,SAAI,GAAG,kBAAkB,CAAC;QAC1B,eAAU,GAAG,GAAG,CAAC;IAO1B,CAAC;CACF;AAVD,wCAUC;AAED;;GAEG;AACH,MAAa,YAAa,SAAQ,QAAQ;IAIxC,YAAY,OAAe,EAAE,KAAa;QACxC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAJf,SAAI,GAAG,eAAe,CAAC;QACvB,eAAU,GAAG,GAAG,CAAC;IAI1B,CAAC;CACF;AAPD,oCAOC;AAED;;GAEG;AACH,MAAa,cAAe,SAAQ,QAAQ;IAI1C,YAAY,OAAe,EAAkB,OAAgB;QAC3D,KAAK,CAAC,OAAO,CAAC,CAAC;QAD4B,YAAO,GAAP,OAAO,CAAS;QAHpD,SAAI,GAAG,iBAAiB,CAAC;QACzB,eAAU,GAAG,GAAG,CAAC;IAI1B,CAAC;CACF;AAPD,wCAOC;AAED;;GAEG;AACH,MAAa,wBAAyB,SAAQ,QAAQ;IAIpD,YAAY,OAAe,EAAkB,GAAY,EAAE,KAAa;QACtE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QADqB,QAAG,GAAH,GAAG,CAAS;QAHhD,SAAI,GAAG,4BAA4B,CAAC;QACpC,eAAU,GAAG,GAAG,CAAC;IAI1B,CAAC;CACF;AAPD,4DAOC;AAED;;GAEG;AACH,MAAa,UAAW,SAAQ,QAAQ;IAItC,YAAY,OAAe,EAAE,KAAa;QACxC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAJf,SAAI,GAAG,aAAa,CAAC;QACrB,eAAU,GAAG,GAAG,CAAC;IAI1B,CAAC;CACF;AAPD,gCAOC;AAED;;GAEG;AACH,MAAa,kBAAmB,SAAQ,QAAQ;IAI9C,YAAY,OAAe,EAAkB,SAAkB;QAC7D,KAAK,CAAC,OAAO,CAAC,CAAC;QAD4B,cAAS,GAAT,SAAS,CAAS;QAHtD,SAAI,GAAG,qBAAqB,CAAC;QAC7B,eAAU,GAAG,GAAG,CAAC;IAI1B,CAAC;CACF;AAPD,gDAOC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/src/models/Errors.ts"], "sourcesContent": ["/**\n * Base error class for all application errors\n */\nexport abstract class AppError extends Error {\n  abstract readonly code: string;\n  abstract readonly statusCode: number;\n  \n  constructor(message: string, public readonly cause?: Error) {\n    super(message);\n    this.name = this.constructor.name;\n    \n    // Maintains proper stack trace for where our error was thrown\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n  }\n}\n\n/**\n * Error thrown when validation fails\n */\nexport class ValidationError extends AppError {\n  readonly code = 'VALIDATION_ERROR';\n  readonly statusCode = 400;\n  \n  constructor(message: string, public readonly field?: string) {\n    super(message);\n  }\n}\n\n/**\n * Error thrown when API requests fail\n */\nexport class ApiError extends AppError {\n  readonly code = 'API_ERROR';\n  readonly statusCode: number;\n  \n  constructor(\n    message: string,\n    statusCode: number = 500,\n    public readonly apiName?: string,\n    cause?: Error\n  ) {\n    super(message, cause);\n    this.statusCode = statusCode;\n  }\n}\n\n/**\n * Error thrown when rate limits are exceeded\n */\nexport class RateLimitError extends AppError {\n  readonly code = 'RATE_LIMIT_ERROR';\n  readonly statusCode = 429;\n  \n  constructor(\n    message: string = 'Rate limit exceeded',\n    public readonly retryAfter?: number\n  ) {\n    super(message);\n  }\n}\n\n/**\n * Error thrown when network requests fail\n */\nexport class NetworkError extends AppError {\n  readonly code = 'NETWORK_ERROR';\n  readonly statusCode = 503;\n  \n  constructor(message: string, cause?: Error) {\n    super(message, cause);\n  }\n}\n\n/**\n * Error thrown when geocoding fails\n */\nexport class GeocodingError extends AppError {\n  readonly code = 'GEOCODING_ERROR';\n  readonly statusCode = 400;\n  \n  constructor(message: string, public readonly zipCode?: string) {\n    super(message);\n  }\n}\n\n/**\n * Error thrown when website verification fails\n */\nexport class WebsiteVerificationError extends AppError {\n  readonly code = 'WEBSITE_VERIFICATION_ERROR';\n  readonly statusCode = 500;\n  \n  constructor(message: string, public readonly url?: string, cause?: Error) {\n    super(message, cause);\n  }\n}\n\n/**\n * Error thrown when cache operations fail\n */\nexport class CacheError extends AppError {\n  readonly code = 'CACHE_ERROR';\n  readonly statusCode = 500;\n  \n  constructor(message: string, cause?: Error) {\n    super(message, cause);\n  }\n}\n\n/**\n * Error thrown when configuration is invalid\n */\nexport class ConfigurationError extends AppError {\n  readonly code = 'CONFIGURATION_ERROR';\n  readonly statusCode = 500;\n  \n  constructor(message: string, public readonly configKey?: string) {\n    super(message);\n  }\n}\n"], "version": 3}