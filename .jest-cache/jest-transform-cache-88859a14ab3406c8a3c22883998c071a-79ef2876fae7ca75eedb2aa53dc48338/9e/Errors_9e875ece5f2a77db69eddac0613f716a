49f7f993fcf401578853e3d6e8312be2
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigurationError = exports.CacheError = exports.WebsiteVerificationError = exports.GeocodingError = exports.NetworkError = exports.RateLimitError = exports.ApiError = exports.ValidationError = exports.AppError = void 0;
/**
 * Base error class for all application errors
 */
class AppError extends Error {
    constructor(message, cause) {
        super(message);
        this.cause = cause;
        this.name = this.constructor.name;
        // Maintains proper stack trace for where our error was thrown
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, this.constructor);
        }
    }
}
exports.AppError = AppError;
/**
 * Error thrown when validation fails
 */
class ValidationError extends AppError {
    constructor(message, field) {
        super(message);
        this.field = field;
        this.code = 'VALIDATION_ERROR';
        this.statusCode = 400;
    }
}
exports.ValidationError = ValidationError;
/**
 * Error thrown when API requests fail
 */
class ApiError extends AppError {
    constructor(message, statusCode = 500, apiName, cause) {
        super(message, cause);
        this.apiName = apiName;
        this.code = 'API_ERROR';
        this.statusCode = statusCode;
    }
}
exports.ApiError = ApiError;
/**
 * Error thrown when rate limits are exceeded
 */
class RateLimitError extends AppError {
    constructor(message = 'Rate limit exceeded', retryAfter) {
        super(message);
        this.retryAfter = retryAfter;
        this.code = 'RATE_LIMIT_ERROR';
        this.statusCode = 429;
    }
}
exports.RateLimitError = RateLimitError;
/**
 * Error thrown when network requests fail
 */
class NetworkError extends AppError {
    constructor(message, cause) {
        super(message, cause);
        this.code = 'NETWORK_ERROR';
        this.statusCode = 503;
    }
}
exports.NetworkError = NetworkError;
/**
 * Error thrown when geocoding fails
 */
class GeocodingError extends AppError {
    constructor(message, zipCode) {
        super(message);
        this.zipCode = zipCode;
        this.code = 'GEOCODING_ERROR';
        this.statusCode = 400;
    }
}
exports.GeocodingError = GeocodingError;
/**
 * Error thrown when website verification fails
 */
class WebsiteVerificationError extends AppError {
    constructor(message, url, cause) {
        super(message, cause);
        this.url = url;
        this.code = 'WEBSITE_VERIFICATION_ERROR';
        this.statusCode = 500;
    }
}
exports.WebsiteVerificationError = WebsiteVerificationError;
/**
 * Error thrown when cache operations fail
 */
class CacheError extends AppError {
    constructor(message, cause) {
        super(message, cause);
        this.code = 'CACHE_ERROR';
        this.statusCode = 500;
    }
}
exports.CacheError = CacheError;
/**
 * Error thrown when configuration is invalid
 */
class ConfigurationError extends AppError {
    constructor(message, configKey) {
        super(message);
        this.configKey = configKey;
        this.code = 'CONFIGURATION_ERROR';
        this.statusCode = 500;
    }
}
exports.ConfigurationError = ConfigurationError;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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