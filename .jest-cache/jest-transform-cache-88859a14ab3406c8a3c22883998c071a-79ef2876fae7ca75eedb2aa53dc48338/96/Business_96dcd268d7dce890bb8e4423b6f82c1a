55722c6f93b2cec7fc535ea563f40aae
"use strict";

/* istanbul ignore next */
function cov_l96yrcbgr() {
  var path = "/Users/<USER>/WebstormProjects/goo/src/models/Business.ts";
  var hash = "f071b39d71067520a0f6b9b4afdedfa748de3b7a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/WebstormProjects/goo/src/models/Business.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      file: "/Users/<USER>/WebstormProjects/goo/src/models/Business.ts",
      mappings: "",
      names: [],
      sources: ["/Users/<USER>/WebstormProjects/goo/src/models/Business.ts"],
      sourcesContent: ["/**\n * Represents the website verification status of a business\n */\nexport type WebsiteStatus = 'verified' | 'unverified' | 'none';\n\n/**\n * Represents the data source for business information\n */\nexport type DataSource = 'google_places' | 'cache' | 'fallback';\n\n/**\n * Metadata associated with business data\n */\nexport interface BusinessMetadata {\n  lastUpdated: Date;\n  dataSource: DataSource;\n  confidence: number; // 0-1 scale\n}\n\n/**\n * Core business entity with all required information\n */\nexport interface Business {\n  id: string;\n  name: string;\n  address: string;\n  phone?: string;\n  website?: string;\n  websiteStatus: WebsiteStatus;\n  category: string[];\n  rating?: number;\n  distance: number; // in miles\n  metadata: BusinessMetadata;\n}\n\n/**\n * Search parameters for business queries\n */\nexport interface SearchParams {\n  zipCode: string;\n  radius: number; // in miles (1-50)\n  businessType: string;\n  timestamp: Date;\n}\n\n/**\n * Statistics about search results\n */\nexport interface SearchStatistics {\n  totalFound: number;\n  withWebsiteCount: number;\n  withoutWebsiteCount: number;\n  websiteAdoptionRate: number; // percentage\n  searchDuration: number; // in milliseconds\n}\n\n/**\n * Categorized search results\n */\nexport interface CategorizedResults {\n  withWebsites: Business[];\n  withoutWebsites: Business[];\n}\n\n/**\n * Complete search result structure\n */\nexport interface SearchResult {\n  searchParams: SearchParams;\n  results: CategorizedResults;\n  statistics: SearchStatistics;\n}\n\n/**\n * Coordinates for geographic location\n */\nexport interface Coordinates {\n  latitude: number;\n  longitude: number;\n}\n\n/**\n * Google Places API place result\n */\nexport interface PlaceResult {\n  place_id: string;\n  name: string;\n  formatted_address: string;\n  formatted_phone_number?: string;\n  website?: string;\n  types: string[];\n  rating?: number;\n  geometry: {\n    location: {\n      lat: number;\n      lng: number;\n    };\n  };\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "f071b39d71067520a0f6b9b4afdedfa748de3b7a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_l96yrcbgr = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_l96yrcbgr();
cov_l96yrcbgr().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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