{"version": 3, "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/src/models/Business.ts"], "sourcesContent": ["/**\n * Represents the website verification status of a business\n */\nexport type WebsiteStatus = 'verified' | 'unverified' | 'none';\n\n/**\n * Represents the data source for business information\n */\nexport type DataSource = 'google_places' | 'cache' | 'fallback';\n\n/**\n * Metadata associated with business data\n */\nexport interface BusinessMetadata {\n  lastUpdated: Date;\n  dataSource: DataSource;\n  confidence: number; // 0-1 scale\n}\n\n/**\n * Core business entity with all required information\n */\nexport interface Business {\n  id: string;\n  name: string;\n  address: string;\n  phone?: string;\n  website?: string;\n  websiteStatus: WebsiteStatus;\n  category: string[];\n  rating?: number;\n  distance: number; // in miles\n  metadata: BusinessMetadata;\n}\n\n/**\n * Search parameters for business queries\n */\nexport interface SearchParams {\n  zipCode: string;\n  radius: number; // in miles (1-50)\n  businessType: string;\n  timestamp: Date;\n}\n\n/**\n * Statistics about search results\n */\nexport interface SearchStatistics {\n  totalFound: number;\n  withWebsiteCount: number;\n  withoutWebsiteCount: number;\n  websiteAdoptionRate: number; // percentage\n  searchDuration: number; // in milliseconds\n}\n\n/**\n * Categorized search results\n */\nexport interface CategorizedResults {\n  withWebsites: Business[];\n  withoutWebsites: Business[];\n}\n\n/**\n * Complete search result structure\n */\nexport interface SearchResult {\n  searchParams: SearchParams;\n  results: CategorizedResults;\n  statistics: SearchStatistics;\n}\n\n/**\n * Coordinates for geographic location\n */\nexport interface Coordinates {\n  latitude: number;\n  longitude: number;\n}\n\n/**\n * Google Places API place result\n */\nexport interface PlaceResult {\n  place_id: string;\n  name: string;\n  formatted_address: string;\n  formatted_phone_number?: string;\n  website?: string;\n  types: string[];\n  rating?: number;\n  geometry: {\n    location: {\n      lat: number;\n      lng: number;\n    };\n  };\n}\n"], "mappings": "", "ignoreList": []}