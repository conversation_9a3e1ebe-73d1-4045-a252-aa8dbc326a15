ea84998b1439babf359f37797035da9f
"use strict";

/* istanbul ignore next */
function cov_2eemwtsw1v() {
  var path = "/Users/<USER>/WebstormProjects/goo/src/services/businessSearch.ts";
  var hash = "3b53530020caa3ea99771e5ab0e5254fc02725a6";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/WebstormProjects/goo/src/services/businessSearch.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 3,
          column: 39
        }
      },
      "2": {
        start: {
          line: 4,
          column: 21
        },
        end: {
          line: 4,
          column: 51
        }
      },
      "3": {
        start: {
          line: 5,
          column: 19
        },
        end: {
          line: 5,
          column: 47
        }
      },
      "4": {
        start: {
          line: 11,
          column: 8
        },
        end: {
          line: 11,
          column: 49
        }
      },
      "5": {
        start: {
          line: 12,
          column: 8
        },
        end: {
          line: 12,
          column: 55
        }
      },
      "6": {
        start: {
          line: 13,
          column: 8
        },
        end: {
          line: 13,
          column: 69
        }
      },
      "7": {
        start: {
          line: 14,
          column: 8
        },
        end: {
          line: 14,
          column: 32
        }
      },
      "8": {
        start: {
          line: 15,
          column: 8
        },
        end: {
          line: 20,
          column: 10
        }
      },
      "9": {
        start: {
          line: 28,
          column: 26
        },
        end: {
          line: 28,
          column: 36
        }
      },
      "10": {
        start: {
          line: 29,
          column: 8
        },
        end: {
          line: 129,
          column: 9
        }
      },
      "11": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 35,
          column: 15
        }
      },
      "12": {
        start: {
          line: 37,
          column: 32
        },
        end: {
          line: 37,
          column: 97
        }
      },
      "13": {
        start: {
          line: 39,
          column: 35
        },
        end: {
          line: 39,
          column: 59
        }
      },
      "14": {
        start: {
          line: 40,
          column: 33
        },
        end: {
          line: 44,
          column: 14
        }
      },
      "15": {
        start: {
          line: 46,
          column: 31
        },
        end: {
          line: 46,
          column: 103
        }
      },
      "16": {
        start: {
          line: 48,
          column: 39
        },
        end: {
          line: 50,
          column: 28
        }
      },
      "17": {
        start: {
          line: 49,
          column: 41
        },
        end: {
          line: 49,
          column: 90
        }
      },
      "18": {
        start: {
          line: 52,
          column: 12
        },
        end: {
          line: 52,
          column: 71
        }
      },
      "19": {
        start: {
          line: 52,
          column: 46
        },
        end: {
          line: 52,
          column: 69
        }
      },
      "20": {
        start: {
          line: 54,
          column: 37
        },
        end: {
          line: 56,
          column: 50
        }
      },
      "21": {
        start: {
          line: 55,
          column: 36
        },
        end: {
          line: 55,
          column: 52
        }
      },
      "22": {
        start: {
          line: 56,
          column: 33
        },
        end: {
          line: 56,
          column: 49
        }
      },
      "23": {
        start: {
          line: 58,
          column: 38
        },
        end: {
          line: 58,
          column: 40
        }
      },
      "24": {
        start: {
          line: 59,
          column: 12
        },
        end: {
          line: 68,
          column: 13
        }
      },
      "25": {
        start: {
          line: 60,
          column: 16
        },
        end: {
          line: 63,
          column: 17
        }
      },
      "26": {
        start: {
          line: 61,
          column: 20
        },
        end: {
          line: 62,
          column: 22
        }
      },
      "27": {
        start: {
          line: 66,
          column: 16
        },
        end: {
          line: 66,
          column: 68
        }
      },
      "28": {
        start: {
          line: 70,
          column: 36
        },
        end: {
          line: 70,
          column: 100
        }
      },
      "29": {
        start: {
          line: 70,
          column: 78
        },
        end: {
          line: 70,
          column: 98
        }
      },
      "30": {
        start: {
          line: 72,
          column: 33
        },
        end: {
          line: 72,
          column: 35
        }
      },
      "31": {
        start: {
          line: 73,
          column: 36
        },
        end: {
          line: 73,
          column: 38
        }
      },
      "32": {
        start: {
          line: 74,
          column: 12
        },
        end: {
          line: 91,
          column: 13
        }
      },
      "33": {
        start: {
          line: 75,
          column: 16
        },
        end: {
          line: 90,
          column: 17
        }
      },
      "34": {
        start: {
          line: 76,
          column: 41
        },
        end: {
          line: 76,
          column: 78
        }
      },
      "35": {
        start: {
          line: 77,
          column: 20
        },
        end: {
          line: 85,
          column: 21
        }
      },
      "36": {
        start: {
          line: 78,
          column: 24
        },
        end: {
          line: 78,
          column: 60
        }
      },
      "37": {
        start: {
          line: 79,
          column: 24
        },
        end: {
          line: 79,
          column: 79
        }
      },
      "38": {
        start: {
          line: 80,
          column: 24
        },
        end: {
          line: 80,
          column: 52
        }
      },
      "39": {
        start: {
          line: 83,
          column: 24
        },
        end: {
          line: 83,
          column: 62
        }
      },
      "40": {
        start: {
          line: 84,
          column: 24
        },
        end: {
          line: 84,
          column: 55
        }
      },
      "41": {
        start: {
          line: 88,
          column: 20
        },
        end: {
          line: 88,
          column: 52
        }
      },
      "42": {
        start: {
          line: 89,
          column: 20
        },
        end: {
          line: 89,
          column: 51
        }
      },
      "43": {
        start: {
          line: 93,
          column: 35
        },
        end: {
          line: 93,
          column: 57
        }
      },
      "44": {
        start: {
          line: 94,
          column: 31
        },
        end: {
          line: 94,
          column: 56
        }
      },
      "45": {
        start: {
          line: 95,
          column: 37
        },
        end: {
          line: 95,
          column: 56
        }
      },
      "46": {
        start: {
          line: 96,
          column: 40
        },
        end: {
          line: 96,
          column: 62
        }
      },
      "47": {
        start: {
          line: 97,
          column: 40
        },
        end: {
          line: 97,
          column: 90
        }
      },
      "48": {
        start: {
          line: 99,
          column: 33
        },
        end: {
          line: 117,
          column: 13
        }
      },
      "49": {
        start: {
          line: 119,
          column: 12
        },
        end: {
          line: 119,
          column: 51
        }
      },
      "50": {
        start: {
          line: 120,
          column: 12
        },
        end: {
          line: 120,
          column: 54
        }
      },
      "51": {
        start: {
          line: 121,
          column: 12
        },
        end: {
          line: 121,
          column: 32
        }
      },
      "52": {
        start: {
          line: 125,
          column: 35
        },
        end: {
          line: 125,
          column: 57
        }
      },
      "53": {
        start: {
          line: 126,
          column: 12
        },
        end: {
          line: 126,
          column: 45
        }
      },
      "54": {
        start: {
          line: 127,
          column: 12
        },
        end: {
          line: 127,
          column: 67
        }
      },
      "55": {
        start: {
          line: 128,
          column: 12
        },
        end: {
          line: 128,
          column: 24
        }
      },
      "56": {
        start: {
          line: 138,
          column: 8
        },
        end: {
          line: 161,
          column: 11
        }
      },
      "57": {
        start: {
          line: 139,
          column: 40
        },
        end: {
          line: 142,
          column: 13
        }
      },
      "58": {
        start: {
          line: 143,
          column: 29
        },
        end: {
          line: 143,
          column: 102
        }
      },
      "59": {
        start: {
          line: 144,
          column: 29
        },
        end: {
          line: 159,
          column: 13
        }
      },
      "60": {
        start: {
          line: 160,
          column: 12
        },
        end: {
          line: 160,
          column: 28
        }
      },
      "61": {
        start: {
          line: 168,
          column: 8
        },
        end: {
          line: 168,
          column: 49
        }
      },
      "62": {
        start: {
          line: 170,
          column: 8
        },
        end: {
          line: 172,
          column: 9
        }
      },
      "63": {
        start: {
          line: 171,
          column: 12
        },
        end: {
          line: 171,
          column: 65
        }
      },
      "64": {
        start: {
          line: 179,
          column: 8
        },
        end: {
          line: 179,
          column: 41
        }
      },
      "65": {
        start: {
          line: 180,
          column: 8
        },
        end: {
          line: 180,
          column: 76
        }
      },
      "66": {
        start: {
          line: 181,
          column: 8
        },
        end: {
          line: 181,
          column: 87
        }
      },
      "67": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 182,
          column: 87
        }
      },
      "68": {
        start: {
          line: 189,
          column: 8
        },
        end: {
          line: 189,
          column: 39
        }
      },
      "69": {
        start: {
          line: 195,
          column: 8
        },
        end: {
          line: 195,
          column: 32
        }
      },
      "70": {
        start: {
          line: 202,
          column: 8
        },
        end: {
          line: 213,
          column: 10
        }
      },
      "71": {
        start: {
          line: 219,
          column: 8
        },
        end: {
          line: 219,
          column: 32
        }
      },
      "72": {
        start: {
          line: 220,
          column: 8
        },
        end: {
          line: 225,
          column: 10
        }
      },
      "73": {
        start: {
          line: 228,
          column: 0
        },
        end: {
          line: 228,
          column: 54
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 10,
            column: 4
          },
          end: {
            line: 10,
            column: 5
          }
        },
        loc: {
          start: {
            line: 10,
            column: 83
          },
          end: {
            line: 21,
            column: 5
          }
        },
        line: 10
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 27,
            column: 4
          },
          end: {
            line: 27,
            column: 5
          }
        },
        loc: {
          start: {
            line: 27,
            column: 36
          },
          end: {
            line: 130,
            column: 5
          }
        },
        line: 27
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 49,
            column: 36
          },
          end: {
            line: 49,
            column: 37
          }
        },
        loc: {
          start: {
            line: 49,
            column: 41
          },
          end: {
            line: 49,
            column: 90
          }
        },
        line: 49
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 52,
            column: 36
          },
          end: {
            line: 52,
            column: 37
          }
        },
        loc: {
          start: {
            line: 52,
            column: 46
          },
          end: {
            line: 52,
            column: 69
          }
        },
        line: 52
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 55,
            column: 24
          },
          end: {
            line: 55,
            column: 25
          }
        },
        loc: {
          start: {
            line: 55,
            column: 36
          },
          end: {
            line: 55,
            column: 52
          }
        },
        line: 55
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 56,
            column: 21
          },
          end: {
            line: 56,
            column: 22
          }
        },
        loc: {
          start: {
            line: 56,
            column: 33
          },
          end: {
            line: 56,
            column: 49
          }
        },
        line: 56
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 70,
            column: 68
          },
          end: {
            line: 70,
            column: 69
          }
        },
        loc: {
          start: {
            line: 70,
            column: 78
          },
          end: {
            line: 70,
            column: 98
          }
        },
        line: 70
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 137,
            column: 4
          },
          end: {
            line: 137,
            column: 5
          }
        },
        loc: {
          start: {
            line: 137,
            column: 65
          },
          end: {
            line: 162,
            column: 5
          }
        },
        line: 137
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 138,
            column: 26
          },
          end: {
            line: 138,
            column: 27
          }
        },
        loc: {
          start: {
            line: 138,
            column: 35
          },
          end: {
            line: 161,
            column: 9
          }
        },
        line: 138
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 167,
            column: 4
          },
          end: {
            line: 167,
            column: 5
          }
        },
        loc: {
          start: {
            line: 167,
            column: 38
          },
          end: {
            line: 173,
            column: 5
          }
        },
        line: 167
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 178,
            column: 4
          },
          end: {
            line: 178,
            column: 5
          }
        },
        loc: {
          start: {
            line: 178,
            column: 41
          },
          end: {
            line: 183,
            column: 5
          }
        },
        line: 178
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 188,
            column: 4
          },
          end: {
            line: 188,
            column: 5
          }
        },
        loc: {
          start: {
            line: 188,
            column: 23
          },
          end: {
            line: 190,
            column: 5
          }
        },
        line: 188
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 194,
            column: 4
          },
          end: {
            line: 194,
            column: 5
          }
        },
        loc: {
          start: {
            line: 194,
            column: 25
          },
          end: {
            line: 196,
            column: 5
          }
        },
        line: 194
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 201,
            column: 4
          },
          end: {
            line: 201,
            column: 5
          }
        },
        loc: {
          start: {
            line: 201,
            column: 26
          },
          end: {
            line: 214,
            column: 5
          }
        },
        line: 201
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 218,
            column: 4
          },
          end: {
            line: 218,
            column: 5
          }
        },
        loc: {
          start: {
            line: 218,
            column: 12
          },
          end: {
            line: 226,
            column: 5
          }
        },
        line: 218
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 41,
            column: 26
          },
          end: {
            line: 41,
            column: 67
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 41,
            column: 54
          },
          end: {
            line: 41,
            column: 55
          }
        }, {
          start: {
            line: 41,
            column: 58
          },
          end: {
            line: 41,
            column: 67
          }
        }],
        line: 41
      },
      "1": {
        loc: {
          start: {
            line: 48,
            column: 39
          },
          end: {
            line: 50,
            column: 28
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 49,
            column: 18
          },
          end: {
            line: 49,
            column: 91
          }
        }, {
          start: {
            line: 50,
            column: 18
          },
          end: {
            line: 50,
            column: 28
          }
        }],
        line: 48
      },
      "2": {
        loc: {
          start: {
            line: 49,
            column: 41
          },
          end: {
            line: 49,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 41
          },
          end: {
            line: 49,
            column: 49
          }
        }, {
          start: {
            line: 49,
            column: 53
          },
          end: {
            line: 49,
            column: 90
          }
        }],
        line: 49
      },
      "3": {
        loc: {
          start: {
            line: 60,
            column: 16
          },
          end: {
            line: 63,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 60,
            column: 16
          },
          end: {
            line: 63,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 60
      },
      "4": {
        loc: {
          start: {
            line: 75,
            column: 16
          },
          end: {
            line: 90,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 75,
            column: 16
          },
          end: {
            line: 90,
            column: 17
          }
        }, {
          start: {
            line: 87,
            column: 21
          },
          end: {
            line: 90,
            column: 17
          }
        }],
        line: 75
      },
      "5": {
        loc: {
          start: {
            line: 77,
            column: 20
          },
          end: {
            line: 85,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 20
          },
          end: {
            line: 85,
            column: 21
          }
        }, {
          start: {
            line: 82,
            column: 25
          },
          end: {
            line: 85,
            column: 21
          }
        }],
        line: 77
      },
      "6": {
        loc: {
          start: {
            line: 77,
            column: 24
          },
          end: {
            line: 77,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 77,
            column: 24
          },
          end: {
            line: 77,
            column: 36
          }
        }, {
          start: {
            line: 77,
            column: 40
          },
          end: {
            line: 77,
            column: 63
          }
        }],
        line: 77
      },
      "7": {
        loc: {
          start: {
            line: 97,
            column: 40
          },
          end: {
            line: 97,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 97,
            column: 57
          },
          end: {
            line: 97,
            column: 86
          }
        }, {
          start: {
            line: 97,
            column: 89
          },
          end: {
            line: 97,
            column: 90
          }
        }],
        line: 97
      },
      "8": {
        loc: {
          start: {
            line: 170,
            column: 8
          },
          end: {
            line: 172,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 170,
            column: 8
          },
          end: {
            line: 172,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 170
      },
      "9": {
        loc: {
          start: {
            line: 204,
            column: 37
          },
          end: {
            line: 206,
            column: 19
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 205,
            column: 18
          },
          end: {
            line: 205,
            column: 80
          }
        }, {
          start: {
            line: 206,
            column: 18
          },
          end: {
            line: 206,
            column: 19
          }
        }],
        line: 204
      },
      "10": {
        loc: {
          start: {
            line: 207,
            column: 40
          },
          end: {
            line: 209,
            column: 19
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 208,
            column: 18
          },
          end: {
            line: 208,
            column: 84
          }
        }, {
          start: {
            line: 209,
            column: 18
          },
          end: {
            line: 209,
            column: 19
          }
        }],
        line: 207
      },
      "11": {
        loc: {
          start: {
            line: 210,
            column: 35
          },
          end: {
            line: 212,
            column: 19
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 211,
            column: 18
          },
          end: {
            line: 211,
            column: 87
          }
        }, {
          start: {
            line: 212,
            column: 18
          },
          end: {
            line: 212,
            column: 19
          }
        }],
        line: 210
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/WebstormProjects/goo/src/services/businessSearch.ts",
      mappings: ";;;AAKA,oDAA2D;AAC3D,gDAAsD;AA0BtD;;GAEG;AACH,MAAa,qBAAqB;IAShC,YACU,gBAAkC,EAClC,mBAAwC,EACxC,0BAAsD;QAFtD,qBAAgB,GAAhB,gBAAgB,CAAkB;QAClC,wBAAmB,GAAnB,mBAAmB,CAAqB;QACxC,+BAA0B,GAA1B,0BAA0B,CAA4B;QAXxD,kBAAa,GAAmB,EAAE,CAAC;QACnC,gBAAW,GAAG;YACpB,aAAa,EAAE,CAAC;YAChB,YAAY,EAAE,CAAC;YACf,iBAAiB,EAAE,CAAC;YACpB,mBAAmB,EAAE,CAAC;SACvB,CAAC;IAMC,CAAC;IAEJ;;;;OAIG;IACH,KAAK,CAAC,gBAAgB,CAAC,OAAsB;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,6BAA6B;YAC7B,IAAA,iCAAoB,EAAC;gBACnB,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,YAAY,EAAE,OAAO,CAAC,YAAY;aACnC,CAAC,CAAC;YAEH,kCAAkC;YAClC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAEtF,4CAA4C;YAC5C,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,0BAA0B;YAC3E,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAC9D,WAAW,EACX,cAAc,EACd,OAAO,CAAC,YAAY,EACpB;gBACE,QAAQ,EAAE,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;gBACnD,QAAQ,EAAE,OAAO,CAAC,OAAO,EAAE,QAAQ;gBACnC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO;aAClC,CACF,CAAC;YAEF,yDAAyD;YACzD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,2BAA2B,CACvD,YAAY,CAAC,MAAM,EACnB,WAAW,CACZ,CAAC;YAEF,gCAAgC;YAChC,MAAM,kBAAkB,GAAG,OAAO,CAAC,OAAO,EAAE,SAAS;gBACnD,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,IAAI,OAAO,CAAC,OAAQ,CAAC,SAAU,CAAC;gBAC7E,CAAC,CAAC,UAAU,CAAC;YAEf,mBAAmB;YACnB,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;YAE3D,oCAAoC;YACpC,MAAM,gBAAgB,GAAG,kBAAkB;iBACxC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;iBACpC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAQ,CAAC,CAAC;YAEtC,wCAAwC;YACxC,IAAI,mBAAmB,GAAU,EAAE,CAAC;YACpC,IAAI,CAAC;gBACH,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAChC,mBAAmB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,sBAAsB,CAChF,gBAAgB,EAChB,CAAC,CAAC,oBAAoB;qBACvB,CAAC;gBACJ,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;gBACpD,wCAAwC;YAC1C,CAAC;YAED,2CAA2C;YAC3C,MAAM,eAAe,GAAG,IAAI,GAAG,CAC7B,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CACxD,CAAC;YAEF,0CAA0C;YAC1C,MAAM,YAAY,GAAe,EAAE,CAAC;YACpC,MAAM,eAAe,GAAe,EAAE,CAAC;YAEvC,KAAK,MAAM,QAAQ,IAAI,kBAAkB,EAAE,CAAC;gBAC1C,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;oBACrB,MAAM,YAAY,GAAG,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;oBAC3D,IAAI,YAAY,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;wBAC5C,QAAQ,CAAC,aAAa,GAAG,UAAU,CAAC;wBACpC,QAAQ,CAAC,QAAQ,CAAC,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;wBACvD,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC9B,CAAC;yBAAM,CAAC;wBACN,QAAQ,CAAC,aAAa,GAAG,YAAY,CAAC;wBACtC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACjC,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,QAAQ,CAAC,aAAa,GAAG,MAAM,CAAC;oBAChC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACjC,CAAC;YACH,CAAC;YAED,uBAAuB;YACvB,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,MAAM,UAAU,GAAG,kBAAkB,CAAC,MAAM,CAAC;YAC7C,MAAM,gBAAgB,GAAG,YAAY,CAAC,MAAM,CAAC;YAC7C,MAAM,mBAAmB,GAAG,eAAe,CAAC,MAAM,CAAC;YACnD,MAAM,mBAAmB,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;YAE/E,uBAAuB;YACvB,MAAM,YAAY,GAAiB;gBACjC,YAAY,EAAE;oBACZ,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,YAAY,EAAE,OAAO,CAAC,YAAY;oBAClC,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;gBACD,OAAO,EAAE;oBACP,YAAY;oBACZ,eAAe;iBAChB;gBACD,UAAU,EAAE;oBACV,UAAU;oBACV,gBAAgB;oBAChB,mBAAmB;oBACnB,mBAAmB;oBACnB,cAAc;iBACf;aACF,CAAC;YAEF,uCAAuC;YACvC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;YACvC,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;YAE1C,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,6CAA6C;YAC7C,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC;YACjC,IAAI,CAAC,WAAW,CAAC,mBAAmB,IAAI,cAAc,CAAC;YAEvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,2BAA2B,CACvC,MAAqB,EACrB,iBAA8B;QAE9B,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YACxB,MAAM,mBAAmB,GAAgB;gBACvC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG;gBACrC,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG;aACvC,CAAC;YAEF,MAAM,QAAQ,GAAG,IAAA,4BAAiB,EAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;YAE3E,MAAM,QAAQ,GAAa;gBACzB,EAAE,EAAE,KAAK,CAAC,QAAQ;gBAClB,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,KAAK,CAAC,iBAAiB;gBAChC,KAAK,EAAE,KAAK,CAAC,sBAAsB;gBACnC,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,aAAa,EAAE,MAAM,EAAE,sCAAsC;gBAC7D,QAAQ,EAAE,KAAK,CAAC,KAAK;gBACrB,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,QAAQ;gBACR,QAAQ,EAAE;oBACR,WAAW,EAAE,IAAI,IAAI,EAAE;oBACvB,UAAU,EAAE,eAAe;oBAC3B,UAAU,EAAE,GAAG,EAAE,yCAAyC;iBAC3D;aACF,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACK,mBAAmB,CAAC,YAA0B;QACpD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAEzC,6BAA6B;QAC7B,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACnC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED;;;OAGG;IACK,sBAAsB,CAAC,YAA0B;QACvD,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC;QACjC,IAAI,CAAC,WAAW,CAAC,YAAY,IAAI,YAAY,CAAC,UAAU,CAAC,UAAU,CAAC;QACpE,IAAI,CAAC,WAAW,CAAC,iBAAiB,IAAI,YAAY,CAAC,UAAU,CAAC,gBAAgB,CAAC;QAC/E,IAAI,CAAC,WAAW,CAAC,mBAAmB,IAAI,YAAY,CAAC,UAAU,CAAC,cAAc,CAAC;IACjF,CAAC;IAED;;;OAGG;IACH,gBAAgB;QACd,OAAO,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACH,mBAAmB;QACjB,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa;YAC7C,uBAAuB,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa,GAAG,CAAC;gBACzD,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;gBAChE,CAAC,CAAC,CAAC;YACL,0BAA0B,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,CAAC;gBAC3D,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY;gBACpE,CAAC,CAAC,CAAC;YACL,qBAAqB,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa,GAAG,CAAC;gBACvD,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,mBAAmB,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;gBACvE,CAAC,CAAC,CAAC;SACN,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG;YACjB,aAAa,EAAE,CAAC;YAChB,YAAY,EAAE,CAAC;YACf,iBAAiB,EAAE,CAAC;YACpB,mBAAmB,EAAE,CAAC;SACvB,CAAC;IACJ,CAAC;CACF;AAlQD,sDAkQC",
      names: [],
      sources: ["/Users/<USER>/WebstormProjects/goo/src/services/businessSearch.ts"],
      sourcesContent: ["import { Business, SearchResult, SearchParams, Coordinates, PlaceResult } from '../models/Business';\nimport { ValidationError } from '../models/Errors';\nimport { GeocodingService } from './geocoding';\nimport { GooglePlacesService } from './googlePlaces';\nimport { WebsiteVerificationService } from './websiteVerification';\nimport { validateSearchParams } from '../utils/validation';\nimport { calculateDistance } from '../utils/distance';\n\n/**\n * Search request parameters\n */\nexport interface SearchRequest {\n  zipCode: string;\n  radius: number; // in miles\n  businessType: string;\n  filters?: {\n    minRating?: number;\n    maxPrice?: number;\n    openNow?: boolean;\n  };\n}\n\n/**\n * Search statistics for analytics\n */\nexport interface SearchStatistics {\n  totalSearches: number;\n  averageResultsPerSearch: number;\n  averageWebsiteAdoptionRate: number;\n  averageSearchDuration: number;\n}\n\n/**\n * Main service for orchestrating business searches\n */\nexport class BusinessSearchService {\n  private searchHistory: SearchResult[] = [];\n  private searchStats = {\n    totalSearches: 0,\n    totalResults: 0,\n    totalWithWebsites: 0,\n    totalSearchDuration: 0,\n  };\n\n  constructor(\n    private geocodingService: GeocodingService,\n    private googlePlacesService: GooglePlacesService,\n    private websiteVerificationService: WebsiteVerificationService\n  ) {}\n\n  /**\n   * Searches for businesses and categorizes them by website presence\n   * @param request - Search request parameters\n   * @returns Promise resolving to categorized search results\n   */\n  async searchBusinesses(request: SearchRequest): Promise<SearchResult> {\n    const startTime = Date.now();\n\n    try {\n      // Validate search parameters\n      validateSearchParams({\n        zipCode: request.zipCode,\n        radius: request.radius,\n        businessType: request.businessType,\n      });\n\n      // Convert zip code to coordinates\n      const coordinates = await this.geocodingService.zipCodeToCoordinates(request.zipCode);\n\n      // Search for places using Google Places API\n      const radiusInMeters = request.radius * 1609.34; // Convert miles to meters\n      const placesResult = await this.googlePlacesService.searchNearby(\n        coordinates,\n        radiusInMeters,\n        request.businessType,\n        {\n          minprice: request.filters?.maxPrice ? 0 : undefined,\n          maxprice: request.filters?.maxPrice,\n          opennow: request.filters?.openNow,\n        }\n      );\n\n      // Transform places to businesses and calculate distances\n      const businesses = await this.transformPlacesToBusinesses(\n        placesResult.places,\n        coordinates\n      );\n\n      // Filter by rating if specified\n      const filteredBusinesses = request.filters?.minRating\n        ? businesses.filter(b => b.rating && b.rating >= request.filters!.minRating!)\n        : businesses;\n\n      // Sort by distance\n      filteredBusinesses.sort((a, b) => a.distance - b.distance);\n\n      // Extract websites for verification\n      const websitesToVerify = filteredBusinesses\n        .filter(business => business.website)\n        .map(business => business.website!);\n\n      // Verify websites (with error handling)\n      let verificationResults: any[] = [];\n      try {\n        if (websitesToVerify.length > 0) {\n          verificationResults = await this.websiteVerificationService.verifyMultipleWebsites(\n            websitesToVerify,\n            5 // Concurrency limit\n          );\n        }\n      } catch (error) {\n        console.warn('Website verification failed:', error);\n        // Continue without website verification\n      }\n\n      // Create verification map for quick lookup\n      const verificationMap = new Map(\n        verificationResults.map(result => [result.url, result])\n      );\n\n      // Categorize businesses by website status\n      const withWebsites: Business[] = [];\n      const withoutWebsites: Business[] = [];\n\n      for (const business of filteredBusinesses) {\n        if (business.website) {\n          const verification = verificationMap.get(business.website);\n          if (verification && verification.accessible) {\n            business.websiteStatus = 'verified';\n            business.metadata.confidence = verification.confidence;\n            withWebsites.push(business);\n          } else {\n            business.websiteStatus = 'unverified';\n            withoutWebsites.push(business);\n          }\n        } else {\n          business.websiteStatus = 'none';\n          withoutWebsites.push(business);\n        }\n      }\n\n      // Calculate statistics\n      const searchDuration = Date.now() - startTime;\n      const totalFound = filteredBusinesses.length;\n      const withWebsiteCount = withWebsites.length;\n      const withoutWebsiteCount = withoutWebsites.length;\n      const websiteAdoptionRate = totalFound > 0 ? withWebsiteCount / totalFound : 0;\n\n      // Create search result\n      const searchResult: SearchResult = {\n        searchParams: {\n          zipCode: request.zipCode,\n          radius: request.radius,\n          businessType: request.businessType,\n          timestamp: new Date(),\n        },\n        results: {\n          withWebsites,\n          withoutWebsites,\n        },\n        statistics: {\n          totalFound,\n          withWebsiteCount,\n          withoutWebsiteCount,\n          websiteAdoptionRate,\n          searchDuration,\n        },\n      };\n\n      // Update search history and statistics\n      this.updateSearchHistory(searchResult);\n      this.updateSearchStatistics(searchResult);\n\n      return searchResult;\n    } catch (error) {\n      // Update statistics even for failed searches\n      const searchDuration = Date.now() - startTime;\n      this.searchStats.totalSearches++;\n      this.searchStats.totalSearchDuration += searchDuration;\n\n      throw error;\n    }\n  }\n\n  /**\n   * Transforms Google Places results to Business objects\n   * @param places - Places from Google Places API\n   * @param centerCoordinates - Center point for distance calculation\n   * @returns Promise resolving to Business array\n   */\n  private async transformPlacesToBusinesses(\n    places: PlaceResult[],\n    centerCoordinates: Coordinates\n  ): Promise<Business[]> {\n    return places.map(place => {\n      const businessCoordinates: Coordinates = {\n        latitude: place.geometry.location.lat,\n        longitude: place.geometry.location.lng,\n      };\n\n      const distance = calculateDistance(centerCoordinates, businessCoordinates);\n\n      const business: Business = {\n        id: place.place_id,\n        name: place.name,\n        address: place.formatted_address,\n        phone: place.formatted_phone_number,\n        website: place.website,\n        websiteStatus: 'none', // Will be updated during verification\n        category: place.types,\n        rating: place.rating,\n        distance,\n        metadata: {\n          lastUpdated: new Date(),\n          dataSource: 'google_places',\n          confidence: 0.8, // Base confidence for Google Places data\n        },\n      };\n\n      return business;\n    });\n  }\n\n  /**\n   * Updates search history\n   * @param searchResult - The search result to add to history\n   */\n  private updateSearchHistory(searchResult: SearchResult): void {\n    this.searchHistory.unshift(searchResult);\n\n    // Keep only last 50 searches\n    if (this.searchHistory.length > 50) {\n      this.searchHistory = this.searchHistory.slice(0, 50);\n    }\n  }\n\n  /**\n   * Updates search statistics\n   * @param searchResult - The search result to include in statistics\n   */\n  private updateSearchStatistics(searchResult: SearchResult): void {\n    this.searchStats.totalSearches++;\n    this.searchStats.totalResults += searchResult.statistics.totalFound;\n    this.searchStats.totalWithWebsites += searchResult.statistics.withWebsiteCount;\n    this.searchStats.totalSearchDuration += searchResult.statistics.searchDuration;\n  }\n\n  /**\n   * Gets search history\n   * @returns Array of previous search results\n   */\n  getSearchHistory(): SearchResult[] {\n    return [...this.searchHistory];\n  }\n\n  /**\n   * Clears search history\n   */\n  clearSearchHistory(): void {\n    this.searchHistory = [];\n  }\n\n  /**\n   * Gets aggregated search statistics\n   * @returns Search statistics\n   */\n  getSearchStatistics(): SearchStatistics {\n    return {\n      totalSearches: this.searchStats.totalSearches,\n      averageResultsPerSearch: this.searchStats.totalSearches > 0\n        ? this.searchStats.totalResults / this.searchStats.totalSearches\n        : 0,\n      averageWebsiteAdoptionRate: this.searchStats.totalResults > 0\n        ? this.searchStats.totalWithWebsites / this.searchStats.totalResults\n        : 0,\n      averageSearchDuration: this.searchStats.totalSearches > 0\n        ? this.searchStats.totalSearchDuration / this.searchStats.totalSearches\n        : 0,\n    };\n  }\n\n  /**\n   * Resets all statistics and history\n   */\n  reset(): void {\n    this.searchHistory = [];\n    this.searchStats = {\n      totalSearches: 0,\n      totalResults: 0,\n      totalWithWebsites: 0,\n      totalSearchDuration: 0,\n    };\n  }\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "3b53530020caa3ea99771e5ab0e5254fc02725a6"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2eemwtsw1v = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2eemwtsw1v();
cov_2eemwtsw1v().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2eemwtsw1v().s[1]++;
exports.BusinessSearchService = void 0;
const validation_1 =
/* istanbul ignore next */
(cov_2eemwtsw1v().s[2]++, require("../utils/validation"));
const distance_1 =
/* istanbul ignore next */
(cov_2eemwtsw1v().s[3]++, require("../utils/distance"));
/**
 * Main service for orchestrating business searches
 */
class BusinessSearchService {
  constructor(geocodingService, googlePlacesService, websiteVerificationService) {
    /* istanbul ignore next */
    cov_2eemwtsw1v().f[0]++;
    cov_2eemwtsw1v().s[4]++;
    this.geocodingService = geocodingService;
    /* istanbul ignore next */
    cov_2eemwtsw1v().s[5]++;
    this.googlePlacesService = googlePlacesService;
    /* istanbul ignore next */
    cov_2eemwtsw1v().s[6]++;
    this.websiteVerificationService = websiteVerificationService;
    /* istanbul ignore next */
    cov_2eemwtsw1v().s[7]++;
    this.searchHistory = [];
    /* istanbul ignore next */
    cov_2eemwtsw1v().s[8]++;
    this.searchStats = {
      totalSearches: 0,
      totalResults: 0,
      totalWithWebsites: 0,
      totalSearchDuration: 0
    };
  }
  /**
   * Searches for businesses and categorizes them by website presence
   * @param request - Search request parameters
   * @returns Promise resolving to categorized search results
   */
  async searchBusinesses(request) {
    /* istanbul ignore next */
    cov_2eemwtsw1v().f[1]++;
    const startTime =
    /* istanbul ignore next */
    (cov_2eemwtsw1v().s[9]++, Date.now());
    /* istanbul ignore next */
    cov_2eemwtsw1v().s[10]++;
    try {
      /* istanbul ignore next */
      cov_2eemwtsw1v().s[11]++;
      // Validate search parameters
      (0, validation_1.validateSearchParams)({
        zipCode: request.zipCode,
        radius: request.radius,
        businessType: request.businessType
      });
      // Convert zip code to coordinates
      const coordinates =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[12]++, await this.geocodingService.zipCodeToCoordinates(request.zipCode));
      // Search for places using Google Places API
      const radiusInMeters =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[13]++, request.radius * 1609.34); // Convert miles to meters
      const placesResult =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[14]++, await this.googlePlacesService.searchNearby(coordinates, radiusInMeters, request.businessType, {
        minprice: request.filters?.maxPrice ?
        /* istanbul ignore next */
        (cov_2eemwtsw1v().b[0][0]++, 0) :
        /* istanbul ignore next */
        (cov_2eemwtsw1v().b[0][1]++, undefined),
        maxprice: request.filters?.maxPrice,
        opennow: request.filters?.openNow
      }));
      // Transform places to businesses and calculate distances
      const businesses =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[15]++, await this.transformPlacesToBusinesses(placesResult.places, coordinates));
      // Filter by rating if specified
      const filteredBusinesses =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[16]++, request.filters?.minRating ?
      /* istanbul ignore next */
      (cov_2eemwtsw1v().b[1][0]++, businesses.filter(b => {
        /* istanbul ignore next */
        cov_2eemwtsw1v().f[2]++;
        cov_2eemwtsw1v().s[17]++;
        return /* istanbul ignore next */(cov_2eemwtsw1v().b[2][0]++, b.rating) &&
        /* istanbul ignore next */
        (cov_2eemwtsw1v().b[2][1]++, b.rating >= request.filters.minRating);
      })) :
      /* istanbul ignore next */
      (cov_2eemwtsw1v().b[1][1]++, businesses));
      // Sort by distance
      /* istanbul ignore next */
      cov_2eemwtsw1v().s[18]++;
      filteredBusinesses.sort((a, b) => {
        /* istanbul ignore next */
        cov_2eemwtsw1v().f[3]++;
        cov_2eemwtsw1v().s[19]++;
        return a.distance - b.distance;
      });
      // Extract websites for verification
      const websitesToVerify =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[20]++, filteredBusinesses.filter(business => {
        /* istanbul ignore next */
        cov_2eemwtsw1v().f[4]++;
        cov_2eemwtsw1v().s[21]++;
        return business.website;
      }).map(business => {
        /* istanbul ignore next */
        cov_2eemwtsw1v().f[5]++;
        cov_2eemwtsw1v().s[22]++;
        return business.website;
      }));
      // Verify websites (with error handling)
      let verificationResults =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[23]++, []);
      /* istanbul ignore next */
      cov_2eemwtsw1v().s[24]++;
      try {
        /* istanbul ignore next */
        cov_2eemwtsw1v().s[25]++;
        if (websitesToVerify.length > 0) {
          /* istanbul ignore next */
          cov_2eemwtsw1v().b[3][0]++;
          cov_2eemwtsw1v().s[26]++;
          verificationResults = await this.websiteVerificationService.verifyMultipleWebsites(websitesToVerify, 5 // Concurrency limit
          );
        } else
        /* istanbul ignore next */
        {
          cov_2eemwtsw1v().b[3][1]++;
        }
      } catch (error) {
        /* istanbul ignore next */
        cov_2eemwtsw1v().s[27]++;
        console.warn('Website verification failed:', error);
        // Continue without website verification
      }
      // Create verification map for quick lookup
      const verificationMap =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[28]++, new Map(verificationResults.map(result => {
        /* istanbul ignore next */
        cov_2eemwtsw1v().f[6]++;
        cov_2eemwtsw1v().s[29]++;
        return [result.url, result];
      })));
      // Categorize businesses by website status
      const withWebsites =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[30]++, []);
      const withoutWebsites =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[31]++, []);
      /* istanbul ignore next */
      cov_2eemwtsw1v().s[32]++;
      for (const business of filteredBusinesses) {
        /* istanbul ignore next */
        cov_2eemwtsw1v().s[33]++;
        if (business.website) {
          /* istanbul ignore next */
          cov_2eemwtsw1v().b[4][0]++;
          const verification =
          /* istanbul ignore next */
          (cov_2eemwtsw1v().s[34]++, verificationMap.get(business.website));
          /* istanbul ignore next */
          cov_2eemwtsw1v().s[35]++;
          if (
          /* istanbul ignore next */
          (cov_2eemwtsw1v().b[6][0]++, verification) &&
          /* istanbul ignore next */
          (cov_2eemwtsw1v().b[6][1]++, verification.accessible)) {
            /* istanbul ignore next */
            cov_2eemwtsw1v().b[5][0]++;
            cov_2eemwtsw1v().s[36]++;
            business.websiteStatus = 'verified';
            /* istanbul ignore next */
            cov_2eemwtsw1v().s[37]++;
            business.metadata.confidence = verification.confidence;
            /* istanbul ignore next */
            cov_2eemwtsw1v().s[38]++;
            withWebsites.push(business);
          } else {
            /* istanbul ignore next */
            cov_2eemwtsw1v().b[5][1]++;
            cov_2eemwtsw1v().s[39]++;
            business.websiteStatus = 'unverified';
            /* istanbul ignore next */
            cov_2eemwtsw1v().s[40]++;
            withoutWebsites.push(business);
          }
        } else {
          /* istanbul ignore next */
          cov_2eemwtsw1v().b[4][1]++;
          cov_2eemwtsw1v().s[41]++;
          business.websiteStatus = 'none';
          /* istanbul ignore next */
          cov_2eemwtsw1v().s[42]++;
          withoutWebsites.push(business);
        }
      }
      // Calculate statistics
      const searchDuration =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[43]++, Date.now() - startTime);
      const totalFound =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[44]++, filteredBusinesses.length);
      const withWebsiteCount =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[45]++, withWebsites.length);
      const withoutWebsiteCount =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[46]++, withoutWebsites.length);
      const websiteAdoptionRate =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[47]++, totalFound > 0 ?
      /* istanbul ignore next */
      (cov_2eemwtsw1v().b[7][0]++, withWebsiteCount / totalFound) :
      /* istanbul ignore next */
      (cov_2eemwtsw1v().b[7][1]++, 0));
      // Create search result
      const searchResult =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[48]++, {
        searchParams: {
          zipCode: request.zipCode,
          radius: request.radius,
          businessType: request.businessType,
          timestamp: new Date()
        },
        results: {
          withWebsites,
          withoutWebsites
        },
        statistics: {
          totalFound,
          withWebsiteCount,
          withoutWebsiteCount,
          websiteAdoptionRate,
          searchDuration
        }
      });
      // Update search history and statistics
      /* istanbul ignore next */
      cov_2eemwtsw1v().s[49]++;
      this.updateSearchHistory(searchResult);
      /* istanbul ignore next */
      cov_2eemwtsw1v().s[50]++;
      this.updateSearchStatistics(searchResult);
      /* istanbul ignore next */
      cov_2eemwtsw1v().s[51]++;
      return searchResult;
    } catch (error) {
      // Update statistics even for failed searches
      const searchDuration =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[52]++, Date.now() - startTime);
      /* istanbul ignore next */
      cov_2eemwtsw1v().s[53]++;
      this.searchStats.totalSearches++;
      /* istanbul ignore next */
      cov_2eemwtsw1v().s[54]++;
      this.searchStats.totalSearchDuration += searchDuration;
      /* istanbul ignore next */
      cov_2eemwtsw1v().s[55]++;
      throw error;
    }
  }
  /**
   * Transforms Google Places results to Business objects
   * @param places - Places from Google Places API
   * @param centerCoordinates - Center point for distance calculation
   * @returns Promise resolving to Business array
   */
  async transformPlacesToBusinesses(places, centerCoordinates) {
    /* istanbul ignore next */
    cov_2eemwtsw1v().f[7]++;
    cov_2eemwtsw1v().s[56]++;
    return places.map(place => {
      /* istanbul ignore next */
      cov_2eemwtsw1v().f[8]++;
      const businessCoordinates =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[57]++, {
        latitude: place.geometry.location.lat,
        longitude: place.geometry.location.lng
      });
      const distance =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[58]++, (0, distance_1.calculateDistance)(centerCoordinates, businessCoordinates));
      const business =
      /* istanbul ignore next */
      (cov_2eemwtsw1v().s[59]++, {
        id: place.place_id,
        name: place.name,
        address: place.formatted_address,
        phone: place.formatted_phone_number,
        website: place.website,
        websiteStatus: 'none',
        // Will be updated during verification
        category: place.types,
        rating: place.rating,
        distance,
        metadata: {
          lastUpdated: new Date(),
          dataSource: 'google_places',
          confidence: 0.8 // Base confidence for Google Places data
        }
      });
      /* istanbul ignore next */
      cov_2eemwtsw1v().s[60]++;
      return business;
    });
  }
  /**
   * Updates search history
   * @param searchResult - The search result to add to history
   */
  updateSearchHistory(searchResult) {
    /* istanbul ignore next */
    cov_2eemwtsw1v().f[9]++;
    cov_2eemwtsw1v().s[61]++;
    this.searchHistory.unshift(searchResult);
    // Keep only last 50 searches
    /* istanbul ignore next */
    cov_2eemwtsw1v().s[62]++;
    if (this.searchHistory.length > 50) {
      /* istanbul ignore next */
      cov_2eemwtsw1v().b[8][0]++;
      cov_2eemwtsw1v().s[63]++;
      this.searchHistory = this.searchHistory.slice(0, 50);
    } else
    /* istanbul ignore next */
    {
      cov_2eemwtsw1v().b[8][1]++;
    }
  }
  /**
   * Updates search statistics
   * @param searchResult - The search result to include in statistics
   */
  updateSearchStatistics(searchResult) {
    /* istanbul ignore next */
    cov_2eemwtsw1v().f[10]++;
    cov_2eemwtsw1v().s[64]++;
    this.searchStats.totalSearches++;
    /* istanbul ignore next */
    cov_2eemwtsw1v().s[65]++;
    this.searchStats.totalResults += searchResult.statistics.totalFound;
    /* istanbul ignore next */
    cov_2eemwtsw1v().s[66]++;
    this.searchStats.totalWithWebsites += searchResult.statistics.withWebsiteCount;
    /* istanbul ignore next */
    cov_2eemwtsw1v().s[67]++;
    this.searchStats.totalSearchDuration += searchResult.statistics.searchDuration;
  }
  /**
   * Gets search history
   * @returns Array of previous search results
   */
  getSearchHistory() {
    /* istanbul ignore next */
    cov_2eemwtsw1v().f[11]++;
    cov_2eemwtsw1v().s[68]++;
    return [...this.searchHistory];
  }
  /**
   * Clears search history
   */
  clearSearchHistory() {
    /* istanbul ignore next */
    cov_2eemwtsw1v().f[12]++;
    cov_2eemwtsw1v().s[69]++;
    this.searchHistory = [];
  }
  /**
   * Gets aggregated search statistics
   * @returns Search statistics
   */
  getSearchStatistics() {
    /* istanbul ignore next */
    cov_2eemwtsw1v().f[13]++;
    cov_2eemwtsw1v().s[70]++;
    return {
      totalSearches: this.searchStats.totalSearches,
      averageResultsPerSearch: this.searchStats.totalSearches > 0 ?
      /* istanbul ignore next */
      (cov_2eemwtsw1v().b[9][0]++, this.searchStats.totalResults / this.searchStats.totalSearches) :
      /* istanbul ignore next */
      (cov_2eemwtsw1v().b[9][1]++, 0),
      averageWebsiteAdoptionRate: this.searchStats.totalResults > 0 ?
      /* istanbul ignore next */
      (cov_2eemwtsw1v().b[10][0]++, this.searchStats.totalWithWebsites / this.searchStats.totalResults) :
      /* istanbul ignore next */
      (cov_2eemwtsw1v().b[10][1]++, 0),
      averageSearchDuration: this.searchStats.totalSearches > 0 ?
      /* istanbul ignore next */
      (cov_2eemwtsw1v().b[11][0]++, this.searchStats.totalSearchDuration / this.searchStats.totalSearches) :
      /* istanbul ignore next */
      (cov_2eemwtsw1v().b[11][1]++, 0)
    };
  }
  /**
   * Resets all statistics and history
   */
  reset() {
    /* istanbul ignore next */
    cov_2eemwtsw1v().f[14]++;
    cov_2eemwtsw1v().s[71]++;
    this.searchHistory = [];
    /* istanbul ignore next */
    cov_2eemwtsw1v().s[72]++;
    this.searchStats = {
      totalSearches: 0,
      totalResults: 0,
      totalWithWebsites: 0,
      totalSearchDuration: 0
    };
  }
}
/* istanbul ignore next */
cov_2eemwtsw1v().s[73]++;
exports.BusinessSearchService = BusinessSearchService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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