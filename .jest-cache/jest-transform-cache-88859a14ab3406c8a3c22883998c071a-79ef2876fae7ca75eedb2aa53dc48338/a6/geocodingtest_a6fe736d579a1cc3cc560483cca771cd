360786f712f4a1d8a6079717cf240e7f
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const geocoding_1 = require("../../src/services/geocoding");
const Errors_1 = require("../../src/models/Errors");
// Mock fetch globally
const mockFetch = global.fetch;
describe('GeocodingService', () => {
    let geocodingService;
    beforeEach(() => {
        geocodingService = new geocoding_1.GeocodingService();
        mockFetch.mockClear();
    });
    describe('zipCodeToCoordinates', () => {
        const validZipCode = '12345';
        const mockApiResponse = {
            results: [
                {
                    geometry: {
                        location: {
                            lat: 40.7128,
                            lng: -74.0060
                        }
                    },
                    formatted_address: 'New York, NY 12345, USA'
                }
            ],
            status: 'OK'
        };
        it('should convert valid zip code to coordinates', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => mockApiResponse
            });
            const result = await geocodingService.zipCodeToCoordinates(validZipCode);
            expect(result).toEqual({
                latitude: 40.7128,
                longitude: -74.0060
            });
            expect(mockFetch).toHaveBeenCalledWith(expect.stringContaining('geocode/json'), expect.objectContaining({
                method: 'GET'
            }));
        });
        it('should handle API errors gracefully', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: false,
                status: 400,
                statusText: 'Bad Request'
            });
            await expect(geocodingService.zipCodeToCoordinates(validZipCode))
                .rejects.toThrow(Errors_1.ApiError);
        });
        it('should handle network errors', async () => {
            mockFetch.mockRejectedValueOnce(new Error('Network error'));
            await expect(geocodingService.zipCodeToCoordinates(validZipCode))
                .rejects.toThrow(Errors_1.NetworkError);
        });
        it('should handle zero results from API', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => ({
                    results: [],
                    status: 'ZERO_RESULTS'
                })
            });
            await expect(geocodingService.zipCodeToCoordinates(validZipCode))
                .rejects.toThrow(Errors_1.GeocodingError);
        });
        it('should handle invalid API response format', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => ({
                    results: [{ invalid: 'format' }],
                    status: 'OK'
                })
            });
            await expect(geocodingService.zipCodeToCoordinates(validZipCode))
                .rejects.toThrow(Errors_1.GeocodingError);
        });
        it('should cache successful results', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => mockApiResponse
            });
            // First call
            const result1 = await geocodingService.zipCodeToCoordinates(validZipCode);
            // Second call should use cache
            const result2 = await geocodingService.zipCodeToCoordinates(validZipCode);
            expect(result1).toEqual(result2);
            expect(mockFetch).toHaveBeenCalledTimes(1);
        });
        it('should validate zip code format before making API call', async () => {
            await expect(geocodingService.zipCodeToCoordinates('invalid'))
                .rejects.toThrow(Errors_1.GeocodingError);
            expect(mockFetch).not.toHaveBeenCalled();
        });
        it('should handle rate limiting', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: false,
                status: 429,
                statusText: 'Too Many Requests'
            });
            await expect(geocodingService.zipCodeToCoordinates(validZipCode))
                .rejects.toThrow(Errors_1.ApiError);
        });
    });
    describe('coordinatesToZipCode', () => {
        const validCoordinates = {
            latitude: 40.7128,
            longitude: -74.0060
        };
        const mockReverseApiResponse = {
            results: [
                {
                    address_components: [
                        {
                            long_name: '12345',
                            short_name: '12345',
                            types: ['postal_code']
                        }
                    ],
                    formatted_address: 'New York, NY 12345, USA'
                }
            ],
            status: 'OK'
        };
        it('should convert coordinates to zip code', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => mockReverseApiResponse
            });
            const result = await geocodingService.coordinatesToZipCode(validCoordinates);
            expect(result).toBe('12345');
            expect(mockFetch).toHaveBeenCalledWith(expect.stringContaining('geocode/json'), expect.objectContaining({
                method: 'GET'
            }));
        });
        it('should handle coordinates with no postal code', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => ({
                    results: [
                        {
                            address_components: [
                                {
                                    long_name: 'New York',
                                    short_name: 'NY',
                                    types: ['locality']
                                }
                            ]
                        }
                    ],
                    status: 'OK'
                })
            });
            await expect(geocodingService.coordinatesToZipCode(validCoordinates))
                .rejects.toThrow(Errors_1.GeocodingError);
        });
    });
    describe('clearCache', () => {
        it('should clear the geocoding cache', async () => {
            // Add something to cache first
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => ({
                    results: [
                        {
                            geometry: {
                                location: { lat: 40.7128, lng: -74.0060 }
                            }
                        }
                    ],
                    status: 'OK'
                })
            });
            await geocodingService.zipCodeToCoordinates('12345');
            expect(mockFetch).toHaveBeenCalledTimes(1);
            // Clear cache
            geocodingService.clearCache();
            // Should make API call again
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => ({
                    results: [
                        {
                            geometry: {
                                location: { lat: 40.7128, lng: -74.0060 }
                            }
                        }
                    ],
                    status: 'OK'
                })
            });
            await geocodingService.zipCodeToCoordinates('12345');
            expect(mockFetch).toHaveBeenCalledTimes(2);
        });
    });
    describe('getCacheSize', () => {
        it('should return current cache size', async () => {
            const initialSize = geocodingService.getCacheSize();
            expect(initialSize).toBe(0);
            // Add something to cache
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => ({
                    results: [
                        {
                            geometry: {
                                location: { lat: 40.7128, lng: -74.0060 }
                            }
                        }
                    ],
                    status: 'OK'
                })
            });
            await geocodingService.zipCodeToCoordinates('12345');
            const newSize = geocodingService.getCacheSize();
            expect(newSize).toBe(1);
        });
    });
    describe('error handling edge cases', () => {
        it('should handle API status errors', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => ({
                    status: 'REQUEST_DENIED',
                    error_message: 'API key invalid'
                })
            });
            await expect(geocodingService.zipCodeToCoordinates('12345'))
                .rejects.toThrow('API key invalid');
        });
        it('should handle OVER_QUERY_LIMIT status', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => ({
                    status: 'OVER_QUERY_LIMIT',
                    error_message: 'Query limit exceeded'
                })
            });
            await expect(geocodingService.zipCodeToCoordinates('12345'))
                .rejects.toThrow('Query limit exceeded');
        });
        it('should handle missing geometry in response', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => ({
                    results: [
                        {
                            formatted_address: 'Some address'
                            // Missing geometry
                        }
                    ],
                    status: 'OK'
                })
            });
            await expect(geocodingService.zipCodeToCoordinates('12345'))
                .rejects.toThrow('Invalid response format');
        });
        it('should handle unknown errors', async () => {
            mockFetch.mockRejectedValueOnce('unknown error type');
            await expect(geocodingService.zipCodeToCoordinates('12345'))
                .rejects.toThrow('Unknown error during geocoding');
        });
    });
    describe('constructor error handling', () => {
        it('should throw ConfigurationError when no API key provided', () => {
            const originalKey = process.env.GOOGLE_PLACES_API_KEY;
            delete process.env.GOOGLE_PLACES_API_KEY;
            expect(() => new geocoding_1.GeocodingService()).toThrow('Google Places API key is required');
            // Restore API key
            if (originalKey) {
                process.env.GOOGLE_PLACES_API_KEY = originalKey;
            }
        });
        it('should accept API key from constructor parameter', () => {
            expect(() => new geocoding_1.GeocodingService('test-api-key')).not.toThrow();
        });
    });
    describe('coordinatesToZipCode edge cases', () => {
        it('should handle multiple address components', async () => {
            const mockReverseApiResponse = {
                results: [
                    {
                        address_components: [
                            {
                                long_name: 'New York',
                                short_name: 'NY',
                                types: ['locality']
                            },
                            {
                                long_name: '12345',
                                short_name: '12345',
                                types: ['postal_code']
                            }
                        ]
                    }
                ],
                status: 'OK'
            };
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => mockReverseApiResponse
            });
            const result = await geocodingService.coordinatesToZipCode({
                latitude: 40.7128,
                longitude: -74.0060
            });
            expect(result).toBe('12345');
        });
        it('should handle API errors in reverse geocoding', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: false,
                status: 400,
                statusText: 'Bad Request'
            });
            await expect(geocodingService.coordinatesToZipCode({
                latitude: 40.7128,
                longitude: -74.0060
            })).rejects.toThrow('Reverse geocoding API request failed');
        });
        it('should handle network errors in reverse geocoding', async () => {
            mockFetch.mockRejectedValueOnce(new Error('Network error'));
            await expect(geocodingService.coordinatesToZipCode({
                latitude: 40.7128,
                longitude: -74.0060
            })).rejects.toThrow('Network error during reverse geocoding');
        });
        it('should cache reverse geocoding results', async () => {
            const mockReverseApiResponse = {
                results: [
                    {
                        address_components: [
                            {
                                long_name: '12345',
                                short_name: '12345',
                                types: ['postal_code']
                            }
                        ]
                    }
                ],
                status: 'OK'
            };
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => mockReverseApiResponse
            });
            const coordinates = { latitude: 40.7128, longitude: -74.0060 };
            // First call
            const result1 = await geocodingService.coordinatesToZipCode(coordinates);
            // Second call should use cache
            const result2 = await geocodingService.coordinatesToZipCode(coordinates);
            expect(result1).toBe(result2);
            expect(mockFetch).toHaveBeenCalledTimes(1);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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