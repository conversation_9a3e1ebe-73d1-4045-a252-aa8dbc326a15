{"version": 3, "names": ["cov_w2rjvihid", "actualCoverage", "s", "Errors_1", "require", "validation_1", "constants_1", "GeocodingService", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "f", "cache", "Map", "b", "process", "env", "GOOGLE_PLACES_API_KEY", "baseUrl", "API_CONFIG", "GEOCODING_BASE_URL", "ConfigurationError", "zipCodeToCoordinates", "zipCode", "validateZipCode", "error", "GeocodingError", "cache<PERSON>ey", "cached", "get", "url", "encodeURIComponent", "response", "fetch", "method", "headers", "ok", "ApiError", "statusText", "status", "data", "json", "error_message", "results", "length", "result", "geometry", "location", "coordinates", "latitude", "lat", "longitude", "lng", "set", "Error", "name", "NetworkError", "message", "String", "coordinatesToZipCode", "address_components", "component", "types", "includes", "long_name", "clearCache", "clear", "getCacheSize", "size", "exports"], "sources": ["/Users/<USER>/WebstormProjects/goo/src/services/geocoding.ts"], "sourcesContent": ["import { Coordinates } from '../models/Business';\nimport { GeocodingError, ApiError, NetworkError, ConfigurationError } from '../models/Errors';\nimport { validateZipCode } from '../utils/validation';\nimport { API_CONFIG } from '../constants';\n\n/**\n * Google Geocoding API response interfaces\n */\ninterface GeocodeResult {\n  geometry: {\n    location: {\n      lat: number;\n      lng: number;\n    };\n  };\n  formatted_address: string;\n  address_components?: AddressComponent[];\n}\n\ninterface AddressComponent {\n  long_name: string;\n  short_name: string;\n  types: string[];\n}\n\ninterface GeocodeResponse {\n  results: GeocodeResult[];\n  status: string;\n  error_message?: string;\n}\n\n/**\n * Service for converting between zip codes and coordinates using Google Geocoding API\n */\nexport class GeocodingService {\n  private cache = new Map<string, Coordinates | string>();\n  private readonly apiKey: string;\n  private readonly baseUrl: string;\n\n  constructor(apiKey?: string) {\n    this.apiKey = apiKey || process.env.GOOGLE_PLACES_API_KEY || '';\n    this.baseUrl = API_CONFIG.GEOCODING_BASE_URL;\n\n    if (!this.apiKey) {\n      throw new ConfigurationError('Google Places API key is required', 'GOOGLE_PLACES_API_KEY');\n    }\n  }\n\n  /**\n   * Converts a zip code to coordinates\n   * @param zipCode - The zip code to convert\n   * @returns Promise resolving to coordinates\n   * @throws GeocodingError if conversion fails\n   */\n  async zipCodeToCoordinates(zipCode: string): Promise<Coordinates> {\n    // Validate zip code format\n    try {\n      validateZipCode(zipCode);\n    } catch (error) {\n      throw new GeocodingError(`Invalid zip code format: ${zipCode}`, zipCode);\n    }\n\n    // Check cache first\n    const cacheKey = `zip_${zipCode}`;\n    const cached = this.cache.get(cacheKey);\n    if (cached && typeof cached === 'object') {\n      return cached as Coordinates;\n    }\n\n    try {\n      const url = `${this.baseUrl}/json?address=${encodeURIComponent(zipCode)}&key=${this.apiKey}`;\n      \n      const response = await fetch(url, {\n        method: 'GET',\n        headers: {\n          'Accept': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new ApiError(\n          `Geocoding API request failed: ${response.statusText}`,\n          response.status,\n          'Google Geocoding API'\n        );\n      }\n\n      const data: GeocodeResponse = await response.json();\n\n      if (data.status !== 'OK') {\n        if (data.status === 'ZERO_RESULTS') {\n          throw new GeocodingError(`No results found for zip code: ${zipCode}`, zipCode);\n        }\n        throw new GeocodingError(\n          data.error_message || `Geocoding failed with status: ${data.status}`,\n          zipCode\n        );\n      }\n\n      if (!data.results || data.results.length === 0) {\n        throw new GeocodingError(`No results found for zip code: ${zipCode}`, zipCode);\n      }\n\n      const result = data.results[0];\n      if (!result.geometry || !result.geometry.location) {\n        throw new GeocodingError(`Invalid response format for zip code: ${zipCode}`, zipCode);\n      }\n\n      const coordinates: Coordinates = {\n        latitude: result.geometry.location.lat,\n        longitude: result.geometry.location.lng,\n      };\n\n      // Cache the result\n      this.cache.set(cacheKey, coordinates);\n\n      return coordinates;\n    } catch (error) {\n      if (error instanceof GeocodingError || error instanceof ApiError) {\n        throw error;\n      }\n      \n      if (error instanceof Error) {\n        if (error.name === 'AbortError') {\n          throw new NetworkError('Geocoding request timed out', error);\n        }\n        throw new NetworkError(`Network error during geocoding: ${error.message}`, error);\n      }\n      \n      throw new GeocodingError(`Unknown error during geocoding: ${String(error)}`, zipCode);\n    }\n  }\n\n  /**\n   * Converts coordinates to zip code (reverse geocoding)\n   * @param coordinates - The coordinates to convert\n   * @returns Promise resolving to zip code\n   * @throws GeocodingError if conversion fails\n   */\n  async coordinatesToZipCode(coordinates: Coordinates): Promise<string> {\n    const cacheKey = `coords_${coordinates.latitude}_${coordinates.longitude}`;\n    const cached = this.cache.get(cacheKey);\n    if (cached && typeof cached === 'string') {\n      return cached;\n    }\n\n    try {\n      const url = `${this.baseUrl}/json?latlng=${coordinates.latitude},${coordinates.longitude}&key=${this.apiKey}`;\n      \n      const response = await fetch(url, {\n        method: 'GET',\n        headers: {\n          'Accept': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new ApiError(\n          `Reverse geocoding API request failed: ${response.statusText}`,\n          response.status,\n          'Google Geocoding API'\n        );\n      }\n\n      const data: GeocodeResponse = await response.json();\n\n      if (data.status !== 'OK') {\n        throw new GeocodingError(\n          data.error_message || `Reverse geocoding failed with status: ${data.status}`\n        );\n      }\n\n      if (!data.results || data.results.length === 0) {\n        throw new GeocodingError('No results found for coordinates');\n      }\n\n      // Find postal code in address components\n      for (const result of data.results) {\n        if (result.address_components) {\n          for (const component of result.address_components) {\n            if (component.types.includes('postal_code')) {\n              const zipCode = component.long_name;\n              this.cache.set(cacheKey, zipCode);\n              return zipCode;\n            }\n          }\n        }\n      }\n\n      throw new GeocodingError('No postal code found for coordinates');\n    } catch (error) {\n      if (error instanceof GeocodingError || error instanceof ApiError) {\n        throw error;\n      }\n      \n      if (error instanceof Error) {\n        if (error.name === 'AbortError') {\n          throw new NetworkError('Reverse geocoding request timed out', error);\n        }\n        throw new NetworkError(`Network error during reverse geocoding: ${error.message}`, error);\n      }\n      \n      throw new GeocodingError(`Unknown error during reverse geocoding: ${String(error)}`);\n    }\n  }\n\n  /**\n   * Clears the geocoding cache\n   */\n  clearCache(): void {\n    this.cache.clear();\n  }\n\n  /**\n   * Gets the current cache size\n   * @returns Number of cached entries\n   */\n  getCacheSize(): number {\n    return this.cache.size;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4CM;IAAAA,aAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,aAAA;AAAAA,aAAA,GAAAE,CAAA;;;;;;;AA3CN,MAAAC,QAAA;AAAA;AAAA,CAAAH,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAC,YAAA;AAAA;AAAA,CAAAL,aAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAE,WAAA;AAAA;AAAA,CAAAN,aAAA,GAAAE,CAAA,OAAAE,OAAA;AA4BA;;;AAGA,MAAaG,gBAAgB;EAK3BC,YAAYC,MAAe;IAAA;IAAAT,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAE,CAAA;IAJnB,KAAAS,KAAK,GAAG,IAAIC,GAAG,EAAgC;IAAC;IAAAZ,aAAA,GAAAE,CAAA;IAKtD,IAAI,CAACO,MAAM;IAAG;IAAA,CAAAT,aAAA,GAAAa,CAAA,UAAAJ,MAAM;IAAA;IAAA,CAAAT,aAAA,GAAAa,CAAA,UAAIC,OAAO,CAACC,GAAG,CAACC,qBAAqB;IAAA;IAAA,CAAAhB,aAAA,GAAAa,CAAA,UAAI,EAAE;IAAC;IAAAb,aAAA,GAAAE,CAAA;IAChE,IAAI,CAACe,OAAO,GAAGX,WAAA,CAAAY,UAAU,CAACC,kBAAkB;IAAC;IAAAnB,aAAA,GAAAE,CAAA;IAE7C,IAAI,CAAC,IAAI,CAACO,MAAM,EAAE;MAAA;MAAAT,aAAA,GAAAa,CAAA;MAAAb,aAAA,GAAAE,CAAA;MAChB,MAAM,IAAIC,QAAA,CAAAiB,kBAAkB,CAAC,mCAAmC,EAAE,uBAAuB,CAAC;IAC5F,CAAC;IAAA;IAAA;MAAApB,aAAA,GAAAa,CAAA;IAAA;EACH;EAEA;;;;;;EAMA,MAAMQ,oBAAoBA,CAACC,OAAe;IAAA;IAAAtB,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAE,CAAA;IACxC;IACA,IAAI;MAAA;MAAAF,aAAA,GAAAE,CAAA;MACF,IAAAG,YAAA,CAAAkB,eAAe,EAACD,OAAO,CAAC;IAC1B,CAAC,CAAC,OAAOE,KAAK,EAAE;MAAA;MAAAxB,aAAA,GAAAE,CAAA;MACd,MAAM,IAAIC,QAAA,CAAAsB,cAAc,CAAC,4BAA4BH,OAAO,EAAE,EAAEA,OAAO,CAAC;IAC1E;IAEA;IACA,MAAMI,QAAQ;IAAA;IAAA,CAAA1B,aAAA,GAAAE,CAAA,QAAG,OAAOoB,OAAO,EAAE;IACjC,MAAMK,MAAM;IAAA;IAAA,CAAA3B,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACS,KAAK,CAACiB,GAAG,CAACF,QAAQ,CAAC;IAAC;IAAA1B,aAAA,GAAAE,CAAA;IACxC;IAAI;IAAA,CAAAF,aAAA,GAAAa,CAAA,UAAAc,MAAM;IAAA;IAAA,CAAA3B,aAAA,GAAAa,CAAA,UAAI,OAAOc,MAAM,KAAK,QAAQ,GAAE;MAAA;MAAA3B,aAAA,GAAAa,CAAA;MAAAb,aAAA,GAAAE,CAAA;MACxC,OAAOyB,MAAqB;IAC9B,CAAC;IAAA;IAAA;MAAA3B,aAAA,GAAAa,CAAA;IAAA;IAAAb,aAAA,GAAAE,CAAA;IAED,IAAI;MACF,MAAM2B,GAAG;MAAA;MAAA,CAAA7B,aAAA,GAAAE,CAAA,QAAG,GAAG,IAAI,CAACe,OAAO,iBAAiBa,kBAAkB,CAACR,OAAO,CAAC,QAAQ,IAAI,CAACb,MAAM,EAAE;MAE5F,MAAMsB,QAAQ;MAAA;MAAA,CAAA/B,aAAA,GAAAE,CAAA,QAAG,MAAM8B,KAAK,CAACH,GAAG,EAAE;QAChCI,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,QAAQ,EAAE;;OAEb,CAAC;MAAC;MAAAlC,aAAA,GAAAE,CAAA;MAEH,IAAI,CAAC6B,QAAQ,CAACI,EAAE,EAAE;QAAA;QAAAnC,aAAA,GAAAa,CAAA;QAAAb,aAAA,GAAAE,CAAA;QAChB,MAAM,IAAIC,QAAA,CAAAiC,QAAQ,CAChB,iCAAiCL,QAAQ,CAACM,UAAU,EAAE,EACtDN,QAAQ,CAACO,MAAM,EACf,sBAAsB,CACvB;MACH,CAAC;MAAA;MAAA;QAAAtC,aAAA,GAAAa,CAAA;MAAA;MAED,MAAM0B,IAAI;MAAA;MAAA,CAAAvC,aAAA,GAAAE,CAAA,QAAoB,MAAM6B,QAAQ,CAACS,IAAI,EAAE;MAAC;MAAAxC,aAAA,GAAAE,CAAA;MAEpD,IAAIqC,IAAI,CAACD,MAAM,KAAK,IAAI,EAAE;QAAA;QAAAtC,aAAA,GAAAa,CAAA;QAAAb,aAAA,GAAAE,CAAA;QACxB,IAAIqC,IAAI,CAACD,MAAM,KAAK,cAAc,EAAE;UAAA;UAAAtC,aAAA,GAAAa,CAAA;UAAAb,aAAA,GAAAE,CAAA;UAClC,MAAM,IAAIC,QAAA,CAAAsB,cAAc,CAAC,kCAAkCH,OAAO,EAAE,EAAEA,OAAO,CAAC;QAChF,CAAC;QAAA;QAAA;UAAAtB,aAAA,GAAAa,CAAA;QAAA;QAAAb,aAAA,GAAAE,CAAA;QACD,MAAM,IAAIC,QAAA,CAAAsB,cAAc;QACtB;QAAA,CAAAzB,aAAA,GAAAa,CAAA,UAAA0B,IAAI,CAACE,aAAa;QAAA;QAAA,CAAAzC,aAAA,GAAAa,CAAA,UAAI,iCAAiC0B,IAAI,CAACD,MAAM,EAAE,GACpEhB,OAAO,CACR;MACH,CAAC;MAAA;MAAA;QAAAtB,aAAA,GAAAa,CAAA;MAAA;MAAAb,aAAA,GAAAE,CAAA;MAED;MAAI;MAAA,CAAAF,aAAA,GAAAa,CAAA,WAAC0B,IAAI,CAACG,OAAO;MAAA;MAAA,CAAA1C,aAAA,GAAAa,CAAA,UAAI0B,IAAI,CAACG,OAAO,CAACC,MAAM,KAAK,CAAC,GAAE;QAAA;QAAA3C,aAAA,GAAAa,CAAA;QAAAb,aAAA,GAAAE,CAAA;QAC9C,MAAM,IAAIC,QAAA,CAAAsB,cAAc,CAAC,kCAAkCH,OAAO,EAAE,EAAEA,OAAO,CAAC;MAChF,CAAC;MAAA;MAAA;QAAAtB,aAAA,GAAAa,CAAA;MAAA;MAED,MAAM+B,MAAM;MAAA;MAAA,CAAA5C,aAAA,GAAAE,CAAA,QAAGqC,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC;MAAC;MAAA1C,aAAA,GAAAE,CAAA;MAC/B;MAAI;MAAA,CAAAF,aAAA,GAAAa,CAAA,YAAC+B,MAAM,CAACC,QAAQ;MAAA;MAAA,CAAA7C,aAAA,GAAAa,CAAA,WAAI,CAAC+B,MAAM,CAACC,QAAQ,CAACC,QAAQ,GAAE;QAAA;QAAA9C,aAAA,GAAAa,CAAA;QAAAb,aAAA,GAAAE,CAAA;QACjD,MAAM,IAAIC,QAAA,CAAAsB,cAAc,CAAC,yCAAyCH,OAAO,EAAE,EAAEA,OAAO,CAAC;MACvF,CAAC;MAAA;MAAA;QAAAtB,aAAA,GAAAa,CAAA;MAAA;MAED,MAAMkC,WAAW;MAAA;MAAA,CAAA/C,aAAA,GAAAE,CAAA,QAAgB;QAC/B8C,QAAQ,EAAEJ,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACG,GAAG;QACtCC,SAAS,EAAEN,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACK;OACrC;MAED;MAAA;MAAAnD,aAAA,GAAAE,CAAA;MACA,IAAI,CAACS,KAAK,CAACyC,GAAG,CAAC1B,QAAQ,EAAEqB,WAAW,CAAC;MAAC;MAAA/C,aAAA,GAAAE,CAAA;MAEtC,OAAO6C,WAAW;IACpB,CAAC,CAAC,OAAOvB,KAAK,EAAE;MAAA;MAAAxB,aAAA,GAAAE,CAAA;MACd;MAAI;MAAA,CAAAF,aAAA,GAAAa,CAAA,WAAAW,KAAK,YAAYrB,QAAA,CAAAsB,cAAc;MAAA;MAAA,CAAAzB,aAAA,GAAAa,CAAA,WAAIW,KAAK,YAAYrB,QAAA,CAAAiC,QAAQ,GAAE;QAAA;QAAApC,aAAA,GAAAa,CAAA;QAAAb,aAAA,GAAAE,CAAA;QAChE,MAAMsB,KAAK;MACb,CAAC;MAAA;MAAA;QAAAxB,aAAA,GAAAa,CAAA;MAAA;MAAAb,aAAA,GAAAE,CAAA;MAED,IAAIsB,KAAK,YAAY6B,KAAK,EAAE;QAAA;QAAArD,aAAA,GAAAa,CAAA;QAAAb,aAAA,GAAAE,CAAA;QAC1B,IAAIsB,KAAK,CAAC8B,IAAI,KAAK,YAAY,EAAE;UAAA;UAAAtD,aAAA,GAAAa,CAAA;UAAAb,aAAA,GAAAE,CAAA;UAC/B,MAAM,IAAIC,QAAA,CAAAoD,YAAY,CAAC,6BAA6B,EAAE/B,KAAK,CAAC;QAC9D,CAAC;QAAA;QAAA;UAAAxB,aAAA,GAAAa,CAAA;QAAA;QAAAb,aAAA,GAAAE,CAAA;QACD,MAAM,IAAIC,QAAA,CAAAoD,YAAY,CAAC,mCAAmC/B,KAAK,CAACgC,OAAO,EAAE,EAAEhC,KAAK,CAAC;MACnF,CAAC;MAAA;MAAA;QAAAxB,aAAA,GAAAa,CAAA;MAAA;MAAAb,aAAA,GAAAE,CAAA;MAED,MAAM,IAAIC,QAAA,CAAAsB,cAAc,CAAC,mCAAmCgC,MAAM,CAACjC,KAAK,CAAC,EAAE,EAAEF,OAAO,CAAC;IACvF;EACF;EAEA;;;;;;EAMA,MAAMoC,oBAAoBA,CAACX,WAAwB;IAAA;IAAA/C,aAAA,GAAAU,CAAA;IACjD,MAAMgB,QAAQ;IAAA;IAAA,CAAA1B,aAAA,GAAAE,CAAA,QAAG,UAAU6C,WAAW,CAACC,QAAQ,IAAID,WAAW,CAACG,SAAS,EAAE;IAC1E,MAAMvB,MAAM;IAAA;IAAA,CAAA3B,aAAA,GAAAE,CAAA,QAAG,IAAI,CAACS,KAAK,CAACiB,GAAG,CAACF,QAAQ,CAAC;IAAC;IAAA1B,aAAA,GAAAE,CAAA;IACxC;IAAI;IAAA,CAAAF,aAAA,GAAAa,CAAA,WAAAc,MAAM;IAAA;IAAA,CAAA3B,aAAA,GAAAa,CAAA,WAAI,OAAOc,MAAM,KAAK,QAAQ,GAAE;MAAA;MAAA3B,aAAA,GAAAa,CAAA;MAAAb,aAAA,GAAAE,CAAA;MACxC,OAAOyB,MAAM;IACf,CAAC;IAAA;IAAA;MAAA3B,aAAA,GAAAa,CAAA;IAAA;IAAAb,aAAA,GAAAE,CAAA;IAED,IAAI;MACF,MAAM2B,GAAG;MAAA;MAAA,CAAA7B,aAAA,GAAAE,CAAA,QAAG,GAAG,IAAI,CAACe,OAAO,gBAAgB8B,WAAW,CAACC,QAAQ,IAAID,WAAW,CAACG,SAAS,QAAQ,IAAI,CAACzC,MAAM,EAAE;MAE7G,MAAMsB,QAAQ;MAAA;MAAA,CAAA/B,aAAA,GAAAE,CAAA,QAAG,MAAM8B,KAAK,CAACH,GAAG,EAAE;QAChCI,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,QAAQ,EAAE;;OAEb,CAAC;MAAC;MAAAlC,aAAA,GAAAE,CAAA;MAEH,IAAI,CAAC6B,QAAQ,CAACI,EAAE,EAAE;QAAA;QAAAnC,aAAA,GAAAa,CAAA;QAAAb,aAAA,GAAAE,CAAA;QAChB,MAAM,IAAIC,QAAA,CAAAiC,QAAQ,CAChB,yCAAyCL,QAAQ,CAACM,UAAU,EAAE,EAC9DN,QAAQ,CAACO,MAAM,EACf,sBAAsB,CACvB;MACH,CAAC;MAAA;MAAA;QAAAtC,aAAA,GAAAa,CAAA;MAAA;MAED,MAAM0B,IAAI;MAAA;MAAA,CAAAvC,aAAA,GAAAE,CAAA,QAAoB,MAAM6B,QAAQ,CAACS,IAAI,EAAE;MAAC;MAAAxC,aAAA,GAAAE,CAAA;MAEpD,IAAIqC,IAAI,CAACD,MAAM,KAAK,IAAI,EAAE;QAAA;QAAAtC,aAAA,GAAAa,CAAA;QAAAb,aAAA,GAAAE,CAAA;QACxB,MAAM,IAAIC,QAAA,CAAAsB,cAAc;QACtB;QAAA,CAAAzB,aAAA,GAAAa,CAAA,WAAA0B,IAAI,CAACE,aAAa;QAAA;QAAA,CAAAzC,aAAA,GAAAa,CAAA,WAAI,yCAAyC0B,IAAI,CAACD,MAAM,EAAE,EAC7E;MACH,CAAC;MAAA;MAAA;QAAAtC,aAAA,GAAAa,CAAA;MAAA;MAAAb,aAAA,GAAAE,CAAA;MAED;MAAI;MAAA,CAAAF,aAAA,GAAAa,CAAA,YAAC0B,IAAI,CAACG,OAAO;MAAA;MAAA,CAAA1C,aAAA,GAAAa,CAAA,WAAI0B,IAAI,CAACG,OAAO,CAACC,MAAM,KAAK,CAAC,GAAE;QAAA;QAAA3C,aAAA,GAAAa,CAAA;QAAAb,aAAA,GAAAE,CAAA;QAC9C,MAAM,IAAIC,QAAA,CAAAsB,cAAc,CAAC,kCAAkC,CAAC;MAC9D,CAAC;MAAA;MAAA;QAAAzB,aAAA,GAAAa,CAAA;MAAA;MAED;MAAAb,aAAA,GAAAE,CAAA;MACA,KAAK,MAAM0C,MAAM,IAAIL,IAAI,CAACG,OAAO,EAAE;QAAA;QAAA1C,aAAA,GAAAE,CAAA;QACjC,IAAI0C,MAAM,CAACe,kBAAkB,EAAE;UAAA;UAAA3D,aAAA,GAAAa,CAAA;UAAAb,aAAA,GAAAE,CAAA;UAC7B,KAAK,MAAM0D,SAAS,IAAIhB,MAAM,CAACe,kBAAkB,EAAE;YAAA;YAAA3D,aAAA,GAAAE,CAAA;YACjD,IAAI0D,SAAS,CAACC,KAAK,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAE;cAAA;cAAA9D,aAAA,GAAAa,CAAA;cAC3C,MAAMS,OAAO;cAAA;cAAA,CAAAtB,aAAA,GAAAE,CAAA,QAAG0D,SAAS,CAACG,SAAS;cAAC;cAAA/D,aAAA,GAAAE,CAAA;cACpC,IAAI,CAACS,KAAK,CAACyC,GAAG,CAAC1B,QAAQ,EAAEJ,OAAO,CAAC;cAAC;cAAAtB,aAAA,GAAAE,CAAA;cAClC,OAAOoB,OAAO;YAChB,CAAC;YAAA;YAAA;cAAAtB,aAAA,GAAAa,CAAA;YAAA;UACH;QACF,CAAC;QAAA;QAAA;UAAAb,aAAA,GAAAa,CAAA;QAAA;MACH;MAAC;MAAAb,aAAA,GAAAE,CAAA;MAED,MAAM,IAAIC,QAAA,CAAAsB,cAAc,CAAC,sCAAsC,CAAC;IAClE,CAAC,CAAC,OAAOD,KAAK,EAAE;MAAA;MAAAxB,aAAA,GAAAE,CAAA;MACd;MAAI;MAAA,CAAAF,aAAA,GAAAa,CAAA,WAAAW,KAAK,YAAYrB,QAAA,CAAAsB,cAAc;MAAA;MAAA,CAAAzB,aAAA,GAAAa,CAAA,WAAIW,KAAK,YAAYrB,QAAA,CAAAiC,QAAQ,GAAE;QAAA;QAAApC,aAAA,GAAAa,CAAA;QAAAb,aAAA,GAAAE,CAAA;QAChE,MAAMsB,KAAK;MACb,CAAC;MAAA;MAAA;QAAAxB,aAAA,GAAAa,CAAA;MAAA;MAAAb,aAAA,GAAAE,CAAA;MAED,IAAIsB,KAAK,YAAY6B,KAAK,EAAE;QAAA;QAAArD,aAAA,GAAAa,CAAA;QAAAb,aAAA,GAAAE,CAAA;QAC1B,IAAIsB,KAAK,CAAC8B,IAAI,KAAK,YAAY,EAAE;UAAA;UAAAtD,aAAA,GAAAa,CAAA;UAAAb,aAAA,GAAAE,CAAA;UAC/B,MAAM,IAAIC,QAAA,CAAAoD,YAAY,CAAC,qCAAqC,EAAE/B,KAAK,CAAC;QACtE,CAAC;QAAA;QAAA;UAAAxB,aAAA,GAAAa,CAAA;QAAA;QAAAb,aAAA,GAAAE,CAAA;QACD,MAAM,IAAIC,QAAA,CAAAoD,YAAY,CAAC,2CAA2C/B,KAAK,CAACgC,OAAO,EAAE,EAAEhC,KAAK,CAAC;MAC3F,CAAC;MAAA;MAAA;QAAAxB,aAAA,GAAAa,CAAA;MAAA;MAAAb,aAAA,GAAAE,CAAA;MAED,MAAM,IAAIC,QAAA,CAAAsB,cAAc,CAAC,2CAA2CgC,MAAM,CAACjC,KAAK,CAAC,EAAE,CAAC;IACtF;EACF;EAEA;;;EAGAwC,UAAUA,CAAA;IAAA;IAAAhE,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAE,CAAA;IACR,IAAI,CAACS,KAAK,CAACsD,KAAK,EAAE;EACpB;EAEA;;;;EAIAC,YAAYA,CAAA;IAAA;IAAAlE,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAE,CAAA;IACV,OAAO,IAAI,CAACS,KAAK,CAACwD,IAAI;EACxB;;AACD;AAAAnE,aAAA,GAAAE,CAAA;AA1LDkE,OAAA,CAAA7D,gBAAA,GAAAA,gBAAA", "ignoreList": []}