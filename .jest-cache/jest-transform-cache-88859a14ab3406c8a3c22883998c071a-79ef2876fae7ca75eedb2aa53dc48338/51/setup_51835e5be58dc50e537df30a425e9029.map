{"file": "/Users/<USER>/WebstormProjects/goo/tests/setup.ts", "mappings": ";;AAAA,qCAAmC;AAEnC,yCAAyC;AACzC,OAAO,CAAC,GAAG,CAAC,qBAAqB,GAAG,cAAc,CAAC;AACnD,OAAO,CAAC,GAAG,CAAC,YAAY,GAAG,sCAAsC,CAAC;AAClE,OAAO,CAAC,GAAG,CAAC,eAAe,GAAG,MAAM,CAAC;AACrC,OAAO,CAAC,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC;AAC9B,OAAO,CAAC,GAAG,CAAC,8BAA8B,GAAG,IAAI,CAAC;AAClD,OAAO,CAAC,GAAG,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACzC,OAAO,CAAC,GAAG,CAAC,sBAAsB,GAAG,IAAI,CAAC;AAC1C,OAAO,CAAC,GAAG,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAErC,oBAAoB;AACpB,MAAM,gBAAgB,GAAG;IACvB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;IAClB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;IAClB,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;IACrB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;IAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;IACd,MAAM,EAAE,CAAC;CACV,CAAC;AACF,MAAM,CAAC,YAAY,GAAG,gBAAuB,CAAC;AAE9C,+BAA+B;AAC/B,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;AAEzB,mCAAmC;AACnC,UAAU,CAAC,GAAG,EAAE;IACd,IAAI,CAAC,aAAa,EAAE,CAAC;IACrB,gBAAgB,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;IACrC,gBAAgB,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;IACrC,gBAAgB,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;IACxC,gBAAgB,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;IACnC,gBAAgB,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;IACjC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;IAC3B,MAAM,CAAC,KAAmB,CAAC,SAAS,EAAE,CAAC;AAC1C,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/tests/setup.ts"], "sourcesContent": ["import '@testing-library/jest-dom';\n\n// Mock environment variables for testing\nprocess.env.GOOGLE_PLACES_API_KEY = 'test-api-key';\nprocess.env.API_BASE_URL = 'https://maps.googleapis.com/maps/api';\nprocess.env.REQUEST_TIMEOUT = '5000';\nprocess.env.MAX_RETRIES = '3';\nprocess.env.RATE_LIMIT_REQUESTS_PER_SECOND = '10';\nprocess.env.RATE_LIMIT_BURST_SIZE = '20';\nprocess.env.CACHE_EXPIRATION_HOURS = '24';\nprocess.env.MAX_CACHE_SIZE_MB = '50';\n\n// Mock localStorage\nconst localStorageMock = {\n  getItem: jest.fn(),\n  setItem: jest.fn(),\n  removeItem: jest.fn(),\n  clear: jest.fn(),\n  key: jest.fn(),\n  length: 0,\n};\nglobal.localStorage = localStorageMock as any;\n\n// Mock fetch for HTTP requests\nglobal.fetch = jest.fn();\n\n// Reset all mocks before each test\nbeforeEach(() => {\n  jest.clearAllMocks();\n  localStorageMock.getItem.mockClear();\n  localStorageMock.setItem.mockClear();\n  localStorageMock.removeItem.mockClear();\n  localStorageMock.clear.mockClear();\n  localStorageMock.key.mockClear();\n  localStorageMock.length = 0;\n  (global.fetch as jest.Mock).mockClear();\n});\n"], "version": 3}