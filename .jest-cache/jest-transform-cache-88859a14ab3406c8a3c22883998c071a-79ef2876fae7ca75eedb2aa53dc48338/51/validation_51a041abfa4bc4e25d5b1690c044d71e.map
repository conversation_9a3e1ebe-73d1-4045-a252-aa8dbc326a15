{"file": "/Users/<USER>/WebstormProjects/goo/src/utils/validation.ts", "mappings": ";;;;;AAWA,0CAkBC;AAOD,wCAIC;AAOD,oDAIC;AAOD,kCAgBC;AAOD,sCA+DC;AAOD,sCAcC;AAOD,oDAQC;AAQD,4CAIC;AAUD,sCAIC;AA9MD,6CAAmD;AACnD,4CAAkG;AAClG,yDAAyE;AACzE,0DAAkC;AAClC,wDAAgC;AAEhC;;;;GAIG;AACH,SAAgB,eAAe,CAAC,OAAe;IAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,MAAM,IAAI,wBAAe,CAAC,0BAAc,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;IACxE,CAAC;IAED,sCAAsC;IACtC,IAAI,CAAC,+BAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;QAChD,MAAM,IAAI,wBAAe,CAAC,0BAAc,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;IACxE,CAAC;IAED,4EAA4E;IAC5E,4FAA4F;IAC5F,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACzB,MAAM,OAAO,GAAG,kBAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACzC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,wBAAe,CAAC,0BAAc,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,cAAc,CAAC,MAAc;IAC3C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,MAAM,GAAG,yBAAa,CAAC,UAAU,IAAI,MAAM,GAAG,yBAAa,CAAC,UAAU,EAAE,CAAC;QACxG,MAAM,IAAI,wBAAe,CAAC,0BAAc,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;IACrE,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,oBAAoB,CAAC,YAAoB;IACvD,IAAI,CAAC,YAAY,IAAI,CAAC,0BAAc,CAAC,QAAQ,CAAC,YAAmB,CAAC,EAAE,CAAC;QACnE,MAAM,IAAI,wBAAe,CAAC,0BAAc,CAAC,qBAAqB,EAAE,cAAc,CAAC,CAAC;IAClF,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,WAAW,CAAC,GAAW;IACrC,IAAI,CAAC,GAAG,EAAE,CAAC;QACT,MAAM,IAAI,wBAAe,CAAC,yDAAyD,EAAE,KAAK,CAAC,CAAC;IAC9F,CAAC;IAED,6CAA6C;IAC7C,IAAI,CAAC,mBAAS,CAAC,KAAK,CAAC,GAAG,EAAE;QACxB,SAAS,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;QAC5B,gBAAgB,EAAE,IAAI;QACtB,sBAAsB,EAAE,IAAI;QAC5B,iBAAiB,EAAE,IAAI;QACvB,kBAAkB,EAAE,KAAK;QACzB,4BAA4B,EAAE,KAAK;KACpC,CAAC,EAAE,CAAC;QACH,MAAM,IAAI,wBAAe,CAAC,yDAAyD,EAAE,KAAK,CAAC,CAAC;IAC9F,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,aAAa,CAAC,KAAa;IACzC,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,CAAC,yBAAyB;IACnC,CAAC;IAED,wFAAwF;IACxF,MAAM,iBAAiB,GAAG,2BAA2B,CAAC;IACtD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACnC,MAAM,IAAI,wBAAe,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,2CAA2C;IAC3C,MAAM,cAAc,GAAG,iEAAiE,CAAC;IACzF,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QAC/B,OAAO,CAAC,wBAAwB;IAClC,CAAC;IAED,oDAAoD;IACpD,MAAM,iBAAiB,GAAG,yDAAyD,CAAC;IACpF,IAAI,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QAClC,OAAO,CAAC,yCAAyC;IACnD,CAAC;IAED,wDAAwD;IACxD,IAAI,OAAO,GAAG,KAAK,CAAC;IAEpB,IAAI,CAAC;QACH,wDAAwD;QACxD,MAAM,WAAW,GAAG,IAAA,oCAAgB,EAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAClD,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;YACzC,OAAO,GAAG,IAAI,CAAC;QACjB,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,qCAAqC;IACvC,CAAC;IAED,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,IAAI,CAAC;YACH,iFAAiF;YACjF,MAAM,WAAW,GAAG,IAAA,oCAAgB,EAAC,KAAK,CAAC,CAAC;YAC5C,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;gBACzC,OAAO,GAAG,IAAI,CAAC;YACjB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,qCAAqC;QACvC,CAAC;IACH,CAAC;IAED,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,IAAI,CAAC;YACH,uCAAuC;YACvC,IAAI,IAAA,sCAAkB,EAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC;gBACpC,OAAO,GAAG,IAAI,CAAC;YACjB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,qCAAqC;QACvC,CAAC;IACH,CAAC;IAED,2DAA2D;IAC3D,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,MAAM,IAAI,wBAAe,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,aAAa,CAAC,KAAa;IACzC,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,CAAC,yBAAyB;IACnC,CAAC;IAED,sDAAsD;IACtD,IAAI,CAAC,mBAAS,CAAC,OAAO,CAAC,KAAK,EAAE;QAC5B,qBAAqB,EAAE,KAAK;QAC5B,WAAW,EAAE,IAAI;QACjB,eAAe,EAAE,KAAK;QACtB,0BAA0B,EAAE,IAAI;KACjC,CAAC,EAAE,CAAC;QACH,MAAM,IAAI,wBAAe,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,oBAAoB,CAAC,MAIpC;IACC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAChC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC9B,oBAAoB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AAC5C,CAAC;AAED;;;;;GAKG;AACH,SAAgB,gBAAgB,CAAC,KAAU,EAAE,IAAY;IACvD,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;QAC1D,MAAM,IAAI,wBAAe,CAAC,GAAG,IAAI,cAAc,EAAE,IAAI,CAAC,CAAC;IACzD,CAAC;AACH,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,aAAa,CAAC,KAAa,EAAE,GAAW,EAAE,GAAW,EAAE,SAAiB;IACtF,IAAI,KAAK,GAAG,GAAG,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC;QAC/B,MAAM,IAAI,wBAAe,CAAC,GAAG,SAAS,oBAAoB,GAAG,QAAQ,GAAG,EAAE,EAAE,SAAS,CAAC,CAAC;IACzF,CAAC;AACH,CAAC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/src/utils/validation.ts"], "sourcesContent": ["import { ValidationError } from '../models/Errors';\nimport { VALIDATION_PATTERNS, SEARCH_CONFIG, BUSINESS_TYPES, ERROR_MESSAGES } from '../constants';\nimport { parsePhoneNumber, isValidPhoneNumber } from 'libphonenumber-js';\nimport validator from 'validator';\nimport zipcodes from 'zipcodes';\n\n/**\n * Validates a zip code format using zipcodes package for US zip codes\n * @param zipCode - The zip code to validate\n * @throws ValidationError if zip code is invalid\n */\nexport function validateZipCode(zipCode: string): void {\n  if (!zipCode) {\n    throw new ValidationError(ERROR_MESSAGES.INVALID_ZIP_CODE, 'zipCode');\n  }\n\n  // First check basic format with regex\n  if (!VALIDATION_PATTERNS.ZIP_CODE.test(zipCode)) {\n    throw new ValidationError(ERROR_MESSAGES.INVALID_ZIP_CODE, 'zipCode');\n  }\n\n  // For 5-digit codes, also validate with zipcodes package for real zip codes\n  // For ZIP+4, just use regex validation since zipcodes package may not have all combinations\n  if (zipCode.length === 5) {\n    const zipInfo = zipcodes.lookup(zipCode);\n    if (!zipInfo) {\n      throw new ValidationError(ERROR_MESSAGES.INVALID_ZIP_CODE, 'zipCode');\n    }\n  }\n}\n\n/**\n * Validates a search radius\n * @param radius - The radius to validate (in miles)\n * @throws ValidationError if radius is invalid\n */\nexport function validateRadius(radius: number): void {\n  if (!Number.isInteger(radius) || radius < SEARCH_CONFIG.MIN_RADIUS || radius > SEARCH_CONFIG.MAX_RADIUS) {\n    throw new ValidationError(ERROR_MESSAGES.INVALID_RADIUS, 'radius');\n  }\n}\n\n/**\n * Validates a business type\n * @param businessType - The business type to validate\n * @throws ValidationError if business type is invalid\n */\nexport function validateBusinessType(businessType: string): void {\n  if (!businessType || !BUSINESS_TYPES.includes(businessType as any)) {\n    throw new ValidationError(ERROR_MESSAGES.INVALID_BUSINESS_TYPE, 'businessType');\n  }\n}\n\n/**\n * Validates a URL format using validator.js for comprehensive validation\n * @param url - The URL to validate\n * @throws ValidationError if URL is invalid\n */\nexport function validateUrl(url: string): void {\n  if (!url) {\n    throw new ValidationError('Invalid URL format. Must start with http:// or https://', 'url');\n  }\n\n  // Use validator.js for robust URL validation\n  if (!validator.isURL(url, {\n    protocols: ['http', 'https'],\n    require_protocol: true,\n    require_valid_protocol: true,\n    allow_underscores: true,\n    allow_trailing_dot: false,\n    allow_protocol_relative_urls: false\n  })) {\n    throw new ValidationError('Invalid URL format. Must start with http:// or https://', 'url');\n  }\n}\n\n/**\n * Validates phone number format using libphonenumber-js for international support\n * @param phone - The phone number to validate\n * @throws ValidationError if phone number is invalid\n */\nexport function validatePhone(phone: string): void {\n  if (!phone) {\n    return; // Empty phone is allowed\n  }\n\n  // First try basic format validation - if it doesn't look like a phone number, reject it\n  const basicPhonePattern = /^[\\+]?[\\d\\s\\-\\(\\)\\.]{7,}$/;\n  if (!basicPhonePattern.test(phone)) {\n    throw new ValidationError('Invalid phone number format', 'phone');\n  }\n\n  // For US phone numbers, be more permissive\n  const usPhonePattern = /^[\\+]?1?[\\s\\-\\(\\)]?[\\d]{3}[\\s\\-\\(\\)]?[\\d]{3}[\\s\\-\\(\\)]?[\\d]{4}$/;\n  if (usPhonePattern.test(phone)) {\n    return; // Valid US phone number\n  }\n\n  // Also check for common US formats with parentheses\n  const usPhoneWithParens = /^[\\+]?1?[\\s\\-]?\\([\\d]{3}\\)[\\s\\-]?[\\d]{3}[\\s\\-]?[\\d]{4}$/;\n  if (usPhoneWithParens.test(phone)) {\n    return; // Valid US phone number with parentheses\n  }\n\n  // For international numbers, try more strict validation\n  let isValid = false;\n\n  try {\n    // Try parsing with US country code first for US numbers\n    const phoneNumber = parsePhoneNumber(phone, 'US');\n    if (phoneNumber && phoneNumber.isValid()) {\n      isValid = true;\n    }\n  } catch (error) {\n    // Continue to next validation method\n  }\n\n  if (!isValid) {\n    try {\n      // Try parsing without country code (for international numbers with country code)\n      const phoneNumber = parsePhoneNumber(phone);\n      if (phoneNumber && phoneNumber.isValid()) {\n        isValid = true;\n      }\n    } catch (error) {\n      // Continue to next validation method\n    }\n  }\n\n  if (!isValid) {\n    try {\n      // Try global validation for any format\n      if (isValidPhoneNumber(phone, 'US')) {\n        isValid = true;\n      }\n    } catch (error) {\n      // Continue to next validation method\n    }\n  }\n\n  // If none of the validation methods succeeded, throw error\n  if (!isValid) {\n    throw new ValidationError('Invalid phone number format', 'phone');\n  }\n}\n\n/**\n * Validates email format using validator.js for RFC-compliant validation\n * @param email - The email to validate\n * @throws ValidationError if email is invalid\n */\nexport function validateEmail(email: string): void {\n  if (!email) {\n    return; // Empty email is allowed\n  }\n\n  // Use validator.js for RFC-compliant email validation\n  if (!validator.isEmail(email, {\n    allow_utf8_local_part: false,\n    require_tld: true,\n    allow_ip_domain: false,\n    domain_specific_validation: true\n  })) {\n    throw new ValidationError('Invalid email format', 'email');\n  }\n}\n\n/**\n * Validates search parameters\n * @param params - The search parameters to validate\n * @throws ValidationError if any parameter is invalid\n */\nexport function validateSearchParams(params: {\n  zipCode: string;\n  radius: number;\n  businessType: string;\n}): void {\n  validateZipCode(params.zipCode);\n  validateRadius(params.radius);\n  validateBusinessType(params.businessType);\n}\n\n/**\n * Validates that a required configuration value exists\n * @param value - The configuration value\n * @param name - The name of the configuration\n * @throws ValidationError if value is missing\n */\nexport function validateRequired(value: any, name: string): void {\n  if (value === undefined || value === null || value === '') {\n    throw new ValidationError(`${name} is required`, name);\n  }\n}\n\n/**\n * Validates that a number is within a specified range\n * @param value - The number to validate\n * @param min - Minimum allowed value\n * @param max - Maximum allowed value\n * @param fieldName - Name of the field being validated\n * @throws ValidationError if value is out of range\n */\nexport function validateRange(value: number, min: number, max: number, fieldName: string): void {\n  if (value < min || value > max) {\n    throw new ValidationError(`${fieldName} must be between ${min} and ${max}`, fieldName);\n  }\n}\n"], "version": 3}