ad50d4bb02decd170d3526032e4d76e6
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
require("@testing-library/jest-dom");
// Mock environment variables for testing
process.env.GOOGLE_PLACES_API_KEY = 'test-api-key';
process.env.API_BASE_URL = 'https://maps.googleapis.com/maps/api';
process.env.REQUEST_TIMEOUT = '5000';
process.env.MAX_RETRIES = '3';
process.env.RATE_LIMIT_REQUESTS_PER_SECOND = '10';
process.env.RATE_LIMIT_BURST_SIZE = '20';
process.env.CACHE_EXPIRATION_HOURS = '24';
process.env.MAX_CACHE_SIZE_MB = '50';
// Mock localStorage
const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
    key: jest.fn(),
    length: 0,
};
global.localStorage = localStorageMock;
// Mock fetch for HTTP requests
global.fetch = jest.fn();
// Reset all mocks before each test
beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
    localStorageMock.clear.mockClear();
    localStorageMock.key.mockClear();
    localStorageMock.length = 0;
    global.fetch.mockClear();
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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