d55f545ee3f3458eeaab6f4197acab3e
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateZipCode = validateZipCode;
exports.validateRadius = validateRadius;
exports.validateBusinessType = validateBusinessType;
exports.validateUrl = validateUrl;
exports.validatePhone = validatePhone;
exports.validateEmail = validateEmail;
exports.validateSearchParams = validateSearchParams;
exports.validateRequired = validateRequired;
exports.validateRange = validateRange;
const Errors_1 = require("../models/Errors");
const constants_1 = require("../constants");
const libphonenumber_js_1 = require("libphonenumber-js");
const validator_1 = __importDefault(require("validator"));
const zipcodes_1 = __importDefault(require("zipcodes"));
/**
 * Validates a zip code format using zipcodes package for US zip codes
 * @param zipCode - The zip code to validate
 * @throws ValidationError if zip code is invalid
 */
function validateZipCode(zipCode) {
    if (!zipCode) {
        throw new Errors_1.ValidationError(constants_1.ERROR_MESSAGES.INVALID_ZIP_CODE, 'zipCode');
    }
    // First check basic format with regex
    if (!constants_1.VALIDATION_PATTERNS.ZIP_CODE.test(zipCode)) {
        throw new Errors_1.ValidationError(constants_1.ERROR_MESSAGES.INVALID_ZIP_CODE, 'zipCode');
    }
    // For 5-digit codes, also validate with zipcodes package for real zip codes
    // For ZIP+4, just use regex validation since zipcodes package may not have all combinations
    if (zipCode.length === 5) {
        const zipInfo = zipcodes_1.default.lookup(zipCode);
        if (!zipInfo) {
            throw new Errors_1.ValidationError(constants_1.ERROR_MESSAGES.INVALID_ZIP_CODE, 'zipCode');
        }
    }
}
/**
 * Validates a search radius
 * @param radius - The radius to validate (in miles)
 * @throws ValidationError if radius is invalid
 */
function validateRadius(radius) {
    if (!Number.isInteger(radius) || radius < constants_1.SEARCH_CONFIG.MIN_RADIUS || radius > constants_1.SEARCH_CONFIG.MAX_RADIUS) {
        throw new Errors_1.ValidationError(constants_1.ERROR_MESSAGES.INVALID_RADIUS, 'radius');
    }
}
/**
 * Validates a business type
 * @param businessType - The business type to validate
 * @throws ValidationError if business type is invalid
 */
function validateBusinessType(businessType) {
    if (!businessType || !constants_1.BUSINESS_TYPES.includes(businessType)) {
        throw new Errors_1.ValidationError(constants_1.ERROR_MESSAGES.INVALID_BUSINESS_TYPE, 'businessType');
    }
}
/**
 * Validates a URL format using validator.js for comprehensive validation
 * @param url - The URL to validate
 * @throws ValidationError if URL is invalid
 */
function validateUrl(url) {
    if (!url) {
        throw new Errors_1.ValidationError('Invalid URL format. Must start with http:// or https://', 'url');
    }
    // Use validator.js for robust URL validation
    if (!validator_1.default.isURL(url, {
        protocols: ['http', 'https'],
        require_protocol: true,
        require_valid_protocol: true,
        allow_underscores: true,
        allow_trailing_dot: false,
        allow_protocol_relative_urls: false
    })) {
        throw new Errors_1.ValidationError('Invalid URL format. Must start with http:// or https://', 'url');
    }
}
/**
 * Validates phone number format using libphonenumber-js for international support
 * @param phone - The phone number to validate
 * @throws ValidationError if phone number is invalid
 */
function validatePhone(phone) {
    if (!phone) {
        return; // Empty phone is allowed
    }
    // First try basic format validation - if it doesn't look like a phone number, reject it
    const basicPhonePattern = /^[\+]?[\d\s\-\(\)\.]{7,}$/;
    if (!basicPhonePattern.test(phone)) {
        throw new Errors_1.ValidationError('Invalid phone number format', 'phone');
    }
    // For US phone numbers, be more permissive
    const usPhonePattern = /^[\+]?1?[\s\-\(\)]?[\d]{3}[\s\-\(\)]?[\d]{3}[\s\-\(\)]?[\d]{4}$/;
    if (usPhonePattern.test(phone)) {
        return; // Valid US phone number
    }
    // Also check for common US formats with parentheses
    const usPhoneWithParens = /^[\+]?1?[\s\-]?\([\d]{3}\)[\s\-]?[\d]{3}[\s\-]?[\d]{4}$/;
    if (usPhoneWithParens.test(phone)) {
        return; // Valid US phone number with parentheses
    }
    // For international numbers, try more strict validation
    let isValid = false;
    try {
        // Try parsing with US country code first for US numbers
        const phoneNumber = (0, libphonenumber_js_1.parsePhoneNumber)(phone, 'US');
        if (phoneNumber && phoneNumber.isValid()) {
            isValid = true;
        }
    }
    catch (error) {
        // Continue to next validation method
    }
    if (!isValid) {
        try {
            // Try parsing without country code (for international numbers with country code)
            const phoneNumber = (0, libphonenumber_js_1.parsePhoneNumber)(phone);
            if (phoneNumber && phoneNumber.isValid()) {
                isValid = true;
            }
        }
        catch (error) {
            // Continue to next validation method
        }
    }
    if (!isValid) {
        try {
            // Try global validation for any format
            if ((0, libphonenumber_js_1.isValidPhoneNumber)(phone, 'US')) {
                isValid = true;
            }
        }
        catch (error) {
            // Continue to next validation method
        }
    }
    // If none of the validation methods succeeded, throw error
    if (!isValid) {
        throw new Errors_1.ValidationError('Invalid phone number format', 'phone');
    }
}
/**
 * Validates email format using validator.js for RFC-compliant validation
 * @param email - The email to validate
 * @throws ValidationError if email is invalid
 */
function validateEmail(email) {
    if (!email) {
        return; // Empty email is allowed
    }
    // Use validator.js for RFC-compliant email validation
    if (!validator_1.default.isEmail(email, {
        allow_utf8_local_part: false,
        require_tld: true,
        allow_ip_domain: false,
        domain_specific_validation: true
    })) {
        throw new Errors_1.ValidationError('Invalid email format', 'email');
    }
}
/**
 * Validates search parameters
 * @param params - The search parameters to validate
 * @throws ValidationError if any parameter is invalid
 */
function validateSearchParams(params) {
    validateZipCode(params.zipCode);
    validateRadius(params.radius);
    validateBusinessType(params.businessType);
}
/**
 * Validates that a required configuration value exists
 * @param value - The configuration value
 * @param name - The name of the configuration
 * @throws ValidationError if value is missing
 */
function validateRequired(value, name) {
    if (value === undefined || value === null || value === '') {
        throw new Errors_1.ValidationError(`${name} is required`, name);
    }
}
/**
 * Validates that a number is within a specified range
 * @param value - The number to validate
 * @param min - Minimum allowed value
 * @param max - Maximum allowed value
 * @param fieldName - Name of the field being validated
 * @throws ValidationError if value is out of range
 */
function validateRange(value, min, max, fieldName) {
    if (value < min || value > max) {
        throw new Errors_1.ValidationError(`${fieldName} must be between ${min} and ${max}`, fieldName);
    }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiL1VzZXJzL2tzYWdvby9XZWJzdG9ybVByb2plY3RzL2dvby9zcmMvdXRpbHMvdmFsaWRhdGlvbi50cyIsIm1hcHBpbmdzIjoiOzs7OztBQVdBLDBDQWtCQztBQU9ELHdDQUlDO0FBT0Qsb0RBSUM7QUFPRCxrQ0FnQkM7QUFPRCxzQ0ErREM7QUFPRCxzQ0FjQztBQU9ELG9EQVFDO0FBUUQsNENBSUM7QUFVRCxzQ0FJQztBQTlNRCw2Q0FBbUQ7QUFDbkQsNENBQWtHO0FBQ2xHLHlEQUF5RTtBQUN6RSwwREFBa0M7QUFDbEMsd0RBQWdDO0FBRWhDOzs7O0dBSUc7QUFDSCxTQUFnQixlQUFlLENBQUMsT0FBZTtJQUM3QyxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUM7UUFDYixNQUFNLElBQUksd0JBQWUsQ0FBQywwQkFBYyxDQUFDLGdCQUFnQixFQUFFLFNBQVMsQ0FBQyxDQUFDO0lBQ3hFLENBQUM7SUFFRCxzQ0FBc0M7SUFDdEMsSUFBSSxDQUFDLCtCQUFtQixDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztRQUNoRCxNQUFNLElBQUksd0JBQWUsQ0FBQywwQkFBYyxDQUFDLGdCQUFnQixFQUFFLFNBQVMsQ0FBQyxDQUFDO0lBQ3hFLENBQUM7SUFFRCw0RUFBNEU7SUFDNUUsNEZBQTRGO0lBQzVGLElBQUksT0FBTyxDQUFDLE1BQU0sS0FBSyxDQUFDLEVBQUUsQ0FBQztRQUN6QixNQUFNLE9BQU8sR0FBRyxrQkFBUSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUN6QyxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUM7WUFDYixNQUFNLElBQUksd0JBQWUsQ0FBQywwQkFBYyxDQUFDLGdCQUFnQixFQUFFLFNBQVMsQ0FBQyxDQUFDO1FBQ3hFLENBQUM7SUFDSCxDQUFDO0FBQ0gsQ0FBQztBQUVEOzs7O0dBSUc7QUFDSCxTQUFnQixjQUFjLENBQUMsTUFBYztJQUMzQyxJQUFJLENBQUMsTUFBTSxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsSUFBSSxNQUFNLEdBQUcseUJBQWEsQ0FBQyxVQUFVLElBQUksTUFBTSxHQUFHLHlCQUFhLENBQUMsVUFBVSxFQUFFLENBQUM7UUFDeEcsTUFBTSxJQUFJLHdCQUFlLENBQUMsMEJBQWMsQ0FBQyxjQUFjLEVBQUUsUUFBUSxDQUFDLENBQUM7SUFDckUsQ0FBQztBQUNILENBQUM7QUFFRDs7OztHQUlHO0FBQ0gsU0FBZ0Isb0JBQW9CLENBQUMsWUFBb0I7SUFDdkQsSUFBSSxDQUFDLFlBQVksSUFBSSxDQUFDLDBCQUFjLENBQUMsUUFBUSxDQUFDLFlBQW1CLENBQUMsRUFBRSxDQUFDO1FBQ25FLE1BQU0sSUFBSSx3QkFBZSxDQUFDLDBCQUFjLENBQUMscUJBQXFCLEVBQUUsY0FBYyxDQUFDLENBQUM7SUFDbEYsQ0FBQztBQUNILENBQUM7QUFFRDs7OztHQUlHO0FBQ0gsU0FBZ0IsV0FBVyxDQUFDLEdBQVc7SUFDckMsSUFBSSxDQUFDLEdBQUcsRUFBRSxDQUFDO1FBQ1QsTUFBTSxJQUFJLHdCQUFlLENBQUMseURBQXlELEVBQUUsS0FBSyxDQUFDLENBQUM7SUFDOUYsQ0FBQztJQUVELDZDQUE2QztJQUM3QyxJQUFJLENBQUMsbUJBQVMsQ0FBQyxLQUFLLENBQUMsR0FBRyxFQUFFO1FBQ3hCLFNBQVMsRUFBRSxDQUFDLE1BQU0sRUFBRSxPQUFPLENBQUM7UUFDNUIsZ0JBQWdCLEVBQUUsSUFBSTtRQUN0QixzQkFBc0IsRUFBRSxJQUFJO1FBQzVCLGlCQUFpQixFQUFFLElBQUk7UUFDdkIsa0JBQWtCLEVBQUUsS0FBSztRQUN6Qiw0QkFBNEIsRUFBRSxLQUFLO0tBQ3BDLENBQUMsRUFBRSxDQUFDO1FBQ0gsTUFBTSxJQUFJLHdCQUFlLENBQUMseURBQXlELEVBQUUsS0FBSyxDQUFDLENBQUM7SUFDOUYsQ0FBQztBQUNILENBQUM7QUFFRDs7OztHQUlHO0FBQ0gsU0FBZ0IsYUFBYSxDQUFDLEtBQWE7SUFDekMsSUFBSSxDQUFDLEtBQUssRUFBRSxDQUFDO1FBQ1gsT0FBTyxDQUFDLHlCQUF5QjtJQUNuQyxDQUFDO0lBRUQsd0ZBQXdGO0lBQ3hGLE1BQU0saUJBQWlCLEdBQUcsMkJBQTJCLENBQUM7SUFDdEQsSUFBSSxDQUFDLGlCQUFpQixDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsRUFBRSxDQUFDO1FBQ25DLE1BQU0sSUFBSSx3QkFBZSxDQUFDLDZCQUE2QixFQUFFLE9BQU8sQ0FBQyxDQUFDO0lBQ3BFLENBQUM7SUFFRCwyQ0FBMkM7SUFDM0MsTUFBTSxjQUFjLEdBQUcsaUVBQWlFLENBQUM7SUFDekYsSUFBSSxjQUFjLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxFQUFFLENBQUM7UUFDL0IsT0FBTyxDQUFDLHdCQUF3QjtJQUNsQyxDQUFDO0lBRUQsb0RBQW9EO0lBQ3BELE1BQU0saUJBQWlCLEdBQUcseURBQXlELENBQUM7SUFDcEYsSUFBSSxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLEVBQUUsQ0FBQztRQUNsQyxPQUFPLENBQUMseUNBQXlDO0lBQ25ELENBQUM7SUFFRCx3REFBd0Q7SUFDeEQsSUFBSSxPQUFPLEdBQUcsS0FBSyxDQUFDO0lBRXBCLElBQUksQ0FBQztRQUNILHdEQUF3RDtRQUN4RCxNQUFNLFdBQVcsR0FBRyxJQUFBLG9DQUFnQixFQUFDLEtBQUssRUFBRSxJQUFJLENBQUMsQ0FBQztRQUNsRCxJQUFJLFdBQVcsSUFBSSxXQUFXLENBQUMsT0FBTyxFQUFFLEVBQUUsQ0FBQztZQUN6QyxPQUFPLEdBQUcsSUFBSSxDQUFDO1FBQ2pCLENBQUM7SUFDSCxDQUFDO0lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztRQUNmLHFDQUFxQztJQUN2QyxDQUFDO0lBRUQsSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFDO1FBQ2IsSUFBSSxDQUFDO1lBQ0gsaUZBQWlGO1lBQ2pGLE1BQU0sV0FBVyxHQUFHLElBQUEsb0NBQWdCLEVBQUMsS0FBSyxDQUFDLENBQUM7WUFDNUMsSUFBSSxXQUFXLElBQUksV0FBVyxDQUFDLE9BQU8sRUFBRSxFQUFFLENBQUM7Z0JBQ3pDLE9BQU8sR0FBRyxJQUFJLENBQUM7WUFDakIsQ0FBQztRQUNILENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YscUNBQXFDO1FBQ3ZDLENBQUM7SUFDSCxDQUFDO0lBRUQsSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFDO1FBQ2IsSUFBSSxDQUFDO1lBQ0gsdUNBQXVDO1lBQ3ZDLElBQUksSUFBQSxzQ0FBa0IsRUFBQyxLQUFLLEVBQUUsSUFBSSxDQUFDLEVBQUUsQ0FBQztnQkFDcEMsT0FBTyxHQUFHLElBQUksQ0FBQztZQUNqQixDQUFDO1FBQ0gsQ0FBQztRQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7WUFDZixxQ0FBcUM7UUFDdkMsQ0FBQztJQUNILENBQUM7SUFFRCwyREFBMkQ7SUFDM0QsSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFDO1FBQ2IsTUFBTSxJQUFJLHdCQUFlLENBQUMsNkJBQTZCLEVBQUUsT0FBTyxDQUFDLENBQUM7SUFDcEUsQ0FBQztBQUNILENBQUM7QUFFRDs7OztHQUlHO0FBQ0gsU0FBZ0IsYUFBYSxDQUFDLEtBQWE7SUFDekMsSUFBSSxDQUFDLEtBQUssRUFBRSxDQUFDO1FBQ1gsT0FBTyxDQUFDLHlCQUF5QjtJQUNuQyxDQUFDO0lBRUQsc0RBQXNEO0lBQ3RELElBQUksQ0FBQyxtQkFBUyxDQUFDLE9BQU8sQ0FBQyxLQUFLLEVBQUU7UUFDNUIscUJBQXFCLEVBQUUsS0FBSztRQUM1QixXQUFXLEVBQUUsSUFBSTtRQUNqQixlQUFlLEVBQUUsS0FBSztRQUN0QiwwQkFBMEIsRUFBRSxJQUFJO0tBQ2pDLENBQUMsRUFBRSxDQUFDO1FBQ0gsTUFBTSxJQUFJLHdCQUFlLENBQUMsc0JBQXNCLEVBQUUsT0FBTyxDQUFDLENBQUM7SUFDN0QsQ0FBQztBQUNILENBQUM7QUFFRDs7OztHQUlHO0FBQ0gsU0FBZ0Isb0JBQW9CLENBQUMsTUFJcEM7SUFDQyxlQUFlLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxDQUFDO0lBQ2hDLGNBQWMsQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUM7SUFDOUIsb0JBQW9CLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDO0FBQzVDLENBQUM7QUFFRDs7Ozs7R0FLRztBQUNILFNBQWdCLGdCQUFnQixDQUFDLEtBQVUsRUFBRSxJQUFZO0lBQ3ZELElBQUksS0FBSyxLQUFLLFNBQVMsSUFBSSxLQUFLLEtBQUssSUFBSSxJQUFJLEtBQUssS0FBSyxFQUFFLEVBQUUsQ0FBQztRQUMxRCxNQUFNLElBQUksd0JBQWUsQ0FBQyxHQUFHLElBQUksY0FBYyxFQUFFLElBQUksQ0FBQyxDQUFDO0lBQ3pELENBQUM7QUFDSCxDQUFDO0FBRUQ7Ozs7Ozs7R0FPRztBQUNILFNBQWdCLGFBQWEsQ0FBQyxLQUFhLEVBQUUsR0FBVyxFQUFFLEdBQVcsRUFBRSxTQUFpQjtJQUN0RixJQUFJLEtBQUssR0FBRyxHQUFHLElBQUksS0FBSyxHQUFHLEdBQUcsRUFBRSxDQUFDO1FBQy9CLE1BQU0sSUFBSSx3QkFBZSxDQUFDLEdBQUcsU0FBUyxvQkFBb0IsR0FBRyxRQUFRLEdBQUcsRUFBRSxFQUFFLFNBQVMsQ0FBQyxDQUFDO0lBQ3pGLENBQUM7QUFDSCxDQUFDIiwibmFtZXMiOltdLCJzb3VyY2VzIjpbIi9Vc2Vycy9rc2Fnb28vV2Vic3Rvcm1Qcm9qZWN0cy9nb28vc3JjL3V0aWxzL3ZhbGlkYXRpb24udHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVmFsaWRhdGlvbkVycm9yIH0gZnJvbSAnLi4vbW9kZWxzL0Vycm9ycyc7XG5pbXBvcnQgeyBWQUxJREFUSU9OX1BBVFRFUk5TLCBTRUFSQ0hfQ09ORklHLCBCVVNJTkVTU19UWVBFUywgRVJST1JfTUVTU0FHRVMgfSBmcm9tICcuLi9jb25zdGFudHMnO1xuaW1wb3J0IHsgcGFyc2VQaG9uZU51bWJlciwgaXNWYWxpZFBob25lTnVtYmVyIH0gZnJvbSAnbGlicGhvbmVudW1iZXItanMnO1xuaW1wb3J0IHZhbGlkYXRvciBmcm9tICd2YWxpZGF0b3InO1xuaW1wb3J0IHppcGNvZGVzIGZyb20gJ3ppcGNvZGVzJztcblxuLyoqXG4gKiBWYWxpZGF0ZXMgYSB6aXAgY29kZSBmb3JtYXQgdXNpbmcgemlwY29kZXMgcGFja2FnZSBmb3IgVVMgemlwIGNvZGVzXG4gKiBAcGFyYW0gemlwQ29kZSAtIFRoZSB6aXAgY29kZSB0byB2YWxpZGF0ZVxuICogQHRocm93cyBWYWxpZGF0aW9uRXJyb3IgaWYgemlwIGNvZGUgaXMgaW52YWxpZFxuICovXG5leHBvcnQgZnVuY3Rpb24gdmFsaWRhdGVaaXBDb2RlKHppcENvZGU6IHN0cmluZyk6IHZvaWQge1xuICBpZiAoIXppcENvZGUpIHtcbiAgICB0aHJvdyBuZXcgVmFsaWRhdGlvbkVycm9yKEVSUk9SX01FU1NBR0VTLklOVkFMSURfWklQX0NPREUsICd6aXBDb2RlJyk7XG4gIH1cblxuICAvLyBGaXJzdCBjaGVjayBiYXNpYyBmb3JtYXQgd2l0aCByZWdleFxuICBpZiAoIVZBTElEQVRJT05fUEFUVEVSTlMuWklQX0NPREUudGVzdCh6aXBDb2RlKSkge1xuICAgIHRocm93IG5ldyBWYWxpZGF0aW9uRXJyb3IoRVJST1JfTUVTU0FHRVMuSU5WQUxJRF9aSVBfQ09ERSwgJ3ppcENvZGUnKTtcbiAgfVxuXG4gIC8vIEZvciA1LWRpZ2l0IGNvZGVzLCBhbHNvIHZhbGlkYXRlIHdpdGggemlwY29kZXMgcGFja2FnZSBmb3IgcmVhbCB6aXAgY29kZXNcbiAgLy8gRm9yIFpJUCs0LCBqdXN0IHVzZSByZWdleCB2YWxpZGF0aW9uIHNpbmNlIHppcGNvZGVzIHBhY2thZ2UgbWF5IG5vdCBoYXZlIGFsbCBjb21iaW5hdGlvbnNcbiAgaWYgKHppcENvZGUubGVuZ3RoID09PSA1KSB7XG4gICAgY29uc3QgemlwSW5mbyA9IHppcGNvZGVzLmxvb2t1cCh6aXBDb2RlKTtcbiAgICBpZiAoIXppcEluZm8pIHtcbiAgICAgIHRocm93IG5ldyBWYWxpZGF0aW9uRXJyb3IoRVJST1JfTUVTU0FHRVMuSU5WQUxJRF9aSVBfQ09ERSwgJ3ppcENvZGUnKTtcbiAgICB9XG4gIH1cbn1cblxuLyoqXG4gKiBWYWxpZGF0ZXMgYSBzZWFyY2ggcmFkaXVzXG4gKiBAcGFyYW0gcmFkaXVzIC0gVGhlIHJhZGl1cyB0byB2YWxpZGF0ZSAoaW4gbWlsZXMpXG4gKiBAdGhyb3dzIFZhbGlkYXRpb25FcnJvciBpZiByYWRpdXMgaXMgaW52YWxpZFxuICovXG5leHBvcnQgZnVuY3Rpb24gdmFsaWRhdGVSYWRpdXMocmFkaXVzOiBudW1iZXIpOiB2b2lkIHtcbiAgaWYgKCFOdW1iZXIuaXNJbnRlZ2VyKHJhZGl1cykgfHwgcmFkaXVzIDwgU0VBUkNIX0NPTkZJRy5NSU5fUkFESVVTIHx8IHJhZGl1cyA+IFNFQVJDSF9DT05GSUcuTUFYX1JBRElVUykge1xuICAgIHRocm93IG5ldyBWYWxpZGF0aW9uRXJyb3IoRVJST1JfTUVTU0FHRVMuSU5WQUxJRF9SQURJVVMsICdyYWRpdXMnKTtcbiAgfVxufVxuXG4vKipcbiAqIFZhbGlkYXRlcyBhIGJ1c2luZXNzIHR5cGVcbiAqIEBwYXJhbSBidXNpbmVzc1R5cGUgLSBUaGUgYnVzaW5lc3MgdHlwZSB0byB2YWxpZGF0ZVxuICogQHRocm93cyBWYWxpZGF0aW9uRXJyb3IgaWYgYnVzaW5lc3MgdHlwZSBpcyBpbnZhbGlkXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB2YWxpZGF0ZUJ1c2luZXNzVHlwZShidXNpbmVzc1R5cGU6IHN0cmluZyk6IHZvaWQge1xuICBpZiAoIWJ1c2luZXNzVHlwZSB8fCAhQlVTSU5FU1NfVFlQRVMuaW5jbHVkZXMoYnVzaW5lc3NUeXBlIGFzIGFueSkpIHtcbiAgICB0aHJvdyBuZXcgVmFsaWRhdGlvbkVycm9yKEVSUk9SX01FU1NBR0VTLklOVkFMSURfQlVTSU5FU1NfVFlQRSwgJ2J1c2luZXNzVHlwZScpO1xuICB9XG59XG5cbi8qKlxuICogVmFsaWRhdGVzIGEgVVJMIGZvcm1hdCB1c2luZyB2YWxpZGF0b3IuanMgZm9yIGNvbXByZWhlbnNpdmUgdmFsaWRhdGlvblxuICogQHBhcmFtIHVybCAtIFRoZSBVUkwgdG8gdmFsaWRhdGVcbiAqIEB0aHJvd3MgVmFsaWRhdGlvbkVycm9yIGlmIFVSTCBpcyBpbnZhbGlkXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB2YWxpZGF0ZVVybCh1cmw6IHN0cmluZyk6IHZvaWQge1xuICBpZiAoIXVybCkge1xuICAgIHRocm93IG5ldyBWYWxpZGF0aW9uRXJyb3IoJ0ludmFsaWQgVVJMIGZvcm1hdC4gTXVzdCBzdGFydCB3aXRoIGh0dHA6Ly8gb3IgaHR0cHM6Ly8nLCAndXJsJyk7XG4gIH1cblxuICAvLyBVc2UgdmFsaWRhdG9yLmpzIGZvciByb2J1c3QgVVJMIHZhbGlkYXRpb25cbiAgaWYgKCF2YWxpZGF0b3IuaXNVUkwodXJsLCB7XG4gICAgcHJvdG9jb2xzOiBbJ2h0dHAnLCAnaHR0cHMnXSxcbiAgICByZXF1aXJlX3Byb3RvY29sOiB0cnVlLFxuICAgIHJlcXVpcmVfdmFsaWRfcHJvdG9jb2w6IHRydWUsXG4gICAgYWxsb3dfdW5kZXJzY29yZXM6IHRydWUsXG4gICAgYWxsb3dfdHJhaWxpbmdfZG90OiBmYWxzZSxcbiAgICBhbGxvd19wcm90b2NvbF9yZWxhdGl2ZV91cmxzOiBmYWxzZVxuICB9KSkge1xuICAgIHRocm93IG5ldyBWYWxpZGF0aW9uRXJyb3IoJ0ludmFsaWQgVVJMIGZvcm1hdC4gTXVzdCBzdGFydCB3aXRoIGh0dHA6Ly8gb3IgaHR0cHM6Ly8nLCAndXJsJyk7XG4gIH1cbn1cblxuLyoqXG4gKiBWYWxpZGF0ZXMgcGhvbmUgbnVtYmVyIGZvcm1hdCB1c2luZyBsaWJwaG9uZW51bWJlci1qcyBmb3IgaW50ZXJuYXRpb25hbCBzdXBwb3J0XG4gKiBAcGFyYW0gcGhvbmUgLSBUaGUgcGhvbmUgbnVtYmVyIHRvIHZhbGlkYXRlXG4gKiBAdGhyb3dzIFZhbGlkYXRpb25FcnJvciBpZiBwaG9uZSBudW1iZXIgaXMgaW52YWxpZFxuICovXG5leHBvcnQgZnVuY3Rpb24gdmFsaWRhdGVQaG9uZShwaG9uZTogc3RyaW5nKTogdm9pZCB7XG4gIGlmICghcGhvbmUpIHtcbiAgICByZXR1cm47IC8vIEVtcHR5IHBob25lIGlzIGFsbG93ZWRcbiAgfVxuXG4gIC8vIEZpcnN0IHRyeSBiYXNpYyBmb3JtYXQgdmFsaWRhdGlvbiAtIGlmIGl0IGRvZXNuJ3QgbG9vayBsaWtlIGEgcGhvbmUgbnVtYmVyLCByZWplY3QgaXRcbiAgY29uc3QgYmFzaWNQaG9uZVBhdHRlcm4gPSAvXltcXCtdP1tcXGRcXHNcXC1cXChcXClcXC5dezcsfSQvO1xuICBpZiAoIWJhc2ljUGhvbmVQYXR0ZXJuLnRlc3QocGhvbmUpKSB7XG4gICAgdGhyb3cgbmV3IFZhbGlkYXRpb25FcnJvcignSW52YWxpZCBwaG9uZSBudW1iZXIgZm9ybWF0JywgJ3Bob25lJyk7XG4gIH1cblxuICAvLyBGb3IgVVMgcGhvbmUgbnVtYmVycywgYmUgbW9yZSBwZXJtaXNzaXZlXG4gIGNvbnN0IHVzUGhvbmVQYXR0ZXJuID0gL15bXFwrXT8xP1tcXHNcXC1cXChcXCldP1tcXGRdezN9W1xcc1xcLVxcKFxcKV0/W1xcZF17M31bXFxzXFwtXFwoXFwpXT9bXFxkXXs0fSQvO1xuICBpZiAodXNQaG9uZVBhdHRlcm4udGVzdChwaG9uZSkpIHtcbiAgICByZXR1cm47IC8vIFZhbGlkIFVTIHBob25lIG51bWJlclxuICB9XG5cbiAgLy8gQWxzbyBjaGVjayBmb3IgY29tbW9uIFVTIGZvcm1hdHMgd2l0aCBwYXJlbnRoZXNlc1xuICBjb25zdCB1c1Bob25lV2l0aFBhcmVucyA9IC9eW1xcK10/MT9bXFxzXFwtXT9cXChbXFxkXXszfVxcKVtcXHNcXC1dP1tcXGRdezN9W1xcc1xcLV0/W1xcZF17NH0kLztcbiAgaWYgKHVzUGhvbmVXaXRoUGFyZW5zLnRlc3QocGhvbmUpKSB7XG4gICAgcmV0dXJuOyAvLyBWYWxpZCBVUyBwaG9uZSBudW1iZXIgd2l0aCBwYXJlbnRoZXNlc1xuICB9XG5cbiAgLy8gRm9yIGludGVybmF0aW9uYWwgbnVtYmVycywgdHJ5IG1vcmUgc3RyaWN0IHZhbGlkYXRpb25cbiAgbGV0IGlzVmFsaWQgPSBmYWxzZTtcblxuICB0cnkge1xuICAgIC8vIFRyeSBwYXJzaW5nIHdpdGggVVMgY291bnRyeSBjb2RlIGZpcnN0IGZvciBVUyBudW1iZXJzXG4gICAgY29uc3QgcGhvbmVOdW1iZXIgPSBwYXJzZVBob25lTnVtYmVyKHBob25lLCAnVVMnKTtcbiAgICBpZiAocGhvbmVOdW1iZXIgJiYgcGhvbmVOdW1iZXIuaXNWYWxpZCgpKSB7XG4gICAgICBpc1ZhbGlkID0gdHJ1ZTtcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgLy8gQ29udGludWUgdG8gbmV4dCB2YWxpZGF0aW9uIG1ldGhvZFxuICB9XG5cbiAgaWYgKCFpc1ZhbGlkKSB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIFRyeSBwYXJzaW5nIHdpdGhvdXQgY291bnRyeSBjb2RlIChmb3IgaW50ZXJuYXRpb25hbCBudW1iZXJzIHdpdGggY291bnRyeSBjb2RlKVxuICAgICAgY29uc3QgcGhvbmVOdW1iZXIgPSBwYXJzZVBob25lTnVtYmVyKHBob25lKTtcbiAgICAgIGlmIChwaG9uZU51bWJlciAmJiBwaG9uZU51bWJlci5pc1ZhbGlkKCkpIHtcbiAgICAgICAgaXNWYWxpZCA9IHRydWU7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIC8vIENvbnRpbnVlIHRvIG5leHQgdmFsaWRhdGlvbiBtZXRob2RcbiAgICB9XG4gIH1cblxuICBpZiAoIWlzVmFsaWQpIHtcbiAgICB0cnkge1xuICAgICAgLy8gVHJ5IGdsb2JhbCB2YWxpZGF0aW9uIGZvciBhbnkgZm9ybWF0XG4gICAgICBpZiAoaXNWYWxpZFBob25lTnVtYmVyKHBob25lLCAnVVMnKSkge1xuICAgICAgICBpc1ZhbGlkID0gdHJ1ZTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgLy8gQ29udGludWUgdG8gbmV4dCB2YWxpZGF0aW9uIG1ldGhvZFxuICAgIH1cbiAgfVxuXG4gIC8vIElmIG5vbmUgb2YgdGhlIHZhbGlkYXRpb24gbWV0aG9kcyBzdWNjZWVkZWQsIHRocm93IGVycm9yXG4gIGlmICghaXNWYWxpZCkge1xuICAgIHRocm93IG5ldyBWYWxpZGF0aW9uRXJyb3IoJ0ludmFsaWQgcGhvbmUgbnVtYmVyIGZvcm1hdCcsICdwaG9uZScpO1xuICB9XG59XG5cbi8qKlxuICogVmFsaWRhdGVzIGVtYWlsIGZvcm1hdCB1c2luZyB2YWxpZGF0b3IuanMgZm9yIFJGQy1jb21wbGlhbnQgdmFsaWRhdGlvblxuICogQHBhcmFtIGVtYWlsIC0gVGhlIGVtYWlsIHRvIHZhbGlkYXRlXG4gKiBAdGhyb3dzIFZhbGlkYXRpb25FcnJvciBpZiBlbWFpbCBpcyBpbnZhbGlkXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB2YWxpZGF0ZUVtYWlsKGVtYWlsOiBzdHJpbmcpOiB2b2lkIHtcbiAgaWYgKCFlbWFpbCkge1xuICAgIHJldHVybjsgLy8gRW1wdHkgZW1haWwgaXMgYWxsb3dlZFxuICB9XG5cbiAgLy8gVXNlIHZhbGlkYXRvci5qcyBmb3IgUkZDLWNvbXBsaWFudCBlbWFpbCB2YWxpZGF0aW9uXG4gIGlmICghdmFsaWRhdG9yLmlzRW1haWwoZW1haWwsIHtcbiAgICBhbGxvd191dGY4X2xvY2FsX3BhcnQ6IGZhbHNlLFxuICAgIHJlcXVpcmVfdGxkOiB0cnVlLFxuICAgIGFsbG93X2lwX2RvbWFpbjogZmFsc2UsXG4gICAgZG9tYWluX3NwZWNpZmljX3ZhbGlkYXRpb246IHRydWVcbiAgfSkpIHtcbiAgICB0aHJvdyBuZXcgVmFsaWRhdGlvbkVycm9yKCdJbnZhbGlkIGVtYWlsIGZvcm1hdCcsICdlbWFpbCcpO1xuICB9XG59XG5cbi8qKlxuICogVmFsaWRhdGVzIHNlYXJjaCBwYXJhbWV0ZXJzXG4gKiBAcGFyYW0gcGFyYW1zIC0gVGhlIHNlYXJjaCBwYXJhbWV0ZXJzIHRvIHZhbGlkYXRlXG4gKiBAdGhyb3dzIFZhbGlkYXRpb25FcnJvciBpZiBhbnkgcGFyYW1ldGVyIGlzIGludmFsaWRcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHZhbGlkYXRlU2VhcmNoUGFyYW1zKHBhcmFtczoge1xuICB6aXBDb2RlOiBzdHJpbmc7XG4gIHJhZGl1czogbnVtYmVyO1xuICBidXNpbmVzc1R5cGU6IHN0cmluZztcbn0pOiB2b2lkIHtcbiAgdmFsaWRhdGVaaXBDb2RlKHBhcmFtcy56aXBDb2RlKTtcbiAgdmFsaWRhdGVSYWRpdXMocGFyYW1zLnJhZGl1cyk7XG4gIHZhbGlkYXRlQnVzaW5lc3NUeXBlKHBhcmFtcy5idXNpbmVzc1R5cGUpO1xufVxuXG4vKipcbiAqIFZhbGlkYXRlcyB0aGF0IGEgcmVxdWlyZWQgY29uZmlndXJhdGlvbiB2YWx1ZSBleGlzdHNcbiAqIEBwYXJhbSB2YWx1ZSAtIFRoZSBjb25maWd1cmF0aW9uIHZhbHVlXG4gKiBAcGFyYW0gbmFtZSAtIFRoZSBuYW1lIG9mIHRoZSBjb25maWd1cmF0aW9uXG4gKiBAdGhyb3dzIFZhbGlkYXRpb25FcnJvciBpZiB2YWx1ZSBpcyBtaXNzaW5nXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB2YWxpZGF0ZVJlcXVpcmVkKHZhbHVlOiBhbnksIG5hbWU6IHN0cmluZyk6IHZvaWQge1xuICBpZiAodmFsdWUgPT09IHVuZGVmaW5lZCB8fCB2YWx1ZSA9PT0gbnVsbCB8fCB2YWx1ZSA9PT0gJycpIHtcbiAgICB0aHJvdyBuZXcgVmFsaWRhdGlvbkVycm9yKGAke25hbWV9IGlzIHJlcXVpcmVkYCwgbmFtZSk7XG4gIH1cbn1cblxuLyoqXG4gKiBWYWxpZGF0ZXMgdGhhdCBhIG51bWJlciBpcyB3aXRoaW4gYSBzcGVjaWZpZWQgcmFuZ2VcbiAqIEBwYXJhbSB2YWx1ZSAtIFRoZSBudW1iZXIgdG8gdmFsaWRhdGVcbiAqIEBwYXJhbSBtaW4gLSBNaW5pbXVtIGFsbG93ZWQgdmFsdWVcbiAqIEBwYXJhbSBtYXggLSBNYXhpbXVtIGFsbG93ZWQgdmFsdWVcbiAqIEBwYXJhbSBmaWVsZE5hbWUgLSBOYW1lIG9mIHRoZSBmaWVsZCBiZWluZyB2YWxpZGF0ZWRcbiAqIEB0aHJvd3MgVmFsaWRhdGlvbkVycm9yIGlmIHZhbHVlIGlzIG91dCBvZiByYW5nZVxuICovXG5leHBvcnQgZnVuY3Rpb24gdmFsaWRhdGVSYW5nZSh2YWx1ZTogbnVtYmVyLCBtaW46IG51bWJlciwgbWF4OiBudW1iZXIsIGZpZWxkTmFtZTogc3RyaW5nKTogdm9pZCB7XG4gIGlmICh2YWx1ZSA8IG1pbiB8fCB2YWx1ZSA+IG1heCkge1xuICAgIHRocm93IG5ldyBWYWxpZGF0aW9uRXJyb3IoYCR7ZmllbGROYW1lfSBtdXN0IGJlIGJldHdlZW4gJHttaW59IGFuZCAke21heH1gLCBmaWVsZE5hbWUpO1xuICB9XG59XG4iXSwidmVyc2lvbiI6M30=