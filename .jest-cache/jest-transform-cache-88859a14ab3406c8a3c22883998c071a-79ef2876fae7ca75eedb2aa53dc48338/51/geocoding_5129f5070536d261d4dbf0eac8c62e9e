0af75ab5fccf375c46d46290f74f13ad
"use strict";

/* istanbul ignore next */
function cov_w2rjvihid() {
  var path = "/Users/<USER>/WebstormProjects/goo/src/services/geocoding.ts";
  var hash = "5d8e93b3680271340d026ec258aef8582935d8c6";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/WebstormProjects/goo/src/services/geocoding.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 3,
          column: 34
        }
      },
      "2": {
        start: {
          line: 4,
          column: 17
        },
        end: {
          line: 4,
          column: 44
        }
      },
      "3": {
        start: {
          line: 5,
          column: 21
        },
        end: {
          line: 5,
          column: 51
        }
      },
      "4": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 43
        }
      },
      "5": {
        start: {
          line: 12,
          column: 8
        },
        end: {
          line: 12,
          column: 31
        }
      },
      "6": {
        start: {
          line: 13,
          column: 8
        },
        end: {
          line: 13,
          column: 72
        }
      },
      "7": {
        start: {
          line: 14,
          column: 8
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "8": {
        start: {
          line: 15,
          column: 8
        },
        end: {
          line: 17,
          column: 9
        }
      },
      "9": {
        start: {
          line: 16,
          column: 12
        },
        end: {
          line: 16,
          column: 112
        }
      },
      "10": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 32,
          column: 9
        }
      },
      "11": {
        start: {
          line: 28,
          column: 12
        },
        end: {
          line: 28,
          column: 55
        }
      },
      "12": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 31,
          column: 94
        }
      },
      "13": {
        start: {
          line: 34,
          column: 25
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "14": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 47
        }
      },
      "15": {
        start: {
          line: 36,
          column: 8
        },
        end: {
          line: 38,
          column: 9
        }
      },
      "16": {
        start: {
          line: 37,
          column: 12
        },
        end: {
          line: 37,
          column: 26
        }
      },
      "17": {
        start: {
          line: 39,
          column: 8
        },
        end: {
          line: 83,
          column: 9
        }
      },
      "18": {
        start: {
          line: 40,
          column: 24
        },
        end: {
          line: 40,
          column: 104
        }
      },
      "19": {
        start: {
          line: 41,
          column: 29
        },
        end: {
          line: 46,
          column: 14
        }
      },
      "20": {
        start: {
          line: 47,
          column: 12
        },
        end: {
          line: 49,
          column: 13
        }
      },
      "21": {
        start: {
          line: 48,
          column: 16
        },
        end: {
          line: 48,
          column: 141
        }
      },
      "22": {
        start: {
          line: 50,
          column: 25
        },
        end: {
          line: 50,
          column: 46
        }
      },
      "23": {
        start: {
          line: 51,
          column: 12
        },
        end: {
          line: 56,
          column: 13
        }
      },
      "24": {
        start: {
          line: 52,
          column: 16
        },
        end: {
          line: 54,
          column: 17
        }
      },
      "25": {
        start: {
          line: 53,
          column: 20
        },
        end: {
          line: 53,
          column: 108
        }
      },
      "26": {
        start: {
          line: 55,
          column: 16
        },
        end: {
          line: 55,
          column: 129
        }
      },
      "27": {
        start: {
          line: 57,
          column: 12
        },
        end: {
          line: 59,
          column: 13
        }
      },
      "28": {
        start: {
          line: 58,
          column: 16
        },
        end: {
          line: 58,
          column: 104
        }
      },
      "29": {
        start: {
          line: 60,
          column: 27
        },
        end: {
          line: 60,
          column: 42
        }
      },
      "30": {
        start: {
          line: 61,
          column: 12
        },
        end: {
          line: 63,
          column: 13
        }
      },
      "31": {
        start: {
          line: 62,
          column: 16
        },
        end: {
          line: 62,
          column: 111
        }
      },
      "32": {
        start: {
          line: 64,
          column: 32
        },
        end: {
          line: 67,
          column: 13
        }
      },
      "33": {
        start: {
          line: 69,
          column: 12
        },
        end: {
          line: 69,
          column: 50
        }
      },
      "34": {
        start: {
          line: 70,
          column: 12
        },
        end: {
          line: 70,
          column: 31
        }
      },
      "35": {
        start: {
          line: 73,
          column: 12
        },
        end: {
          line: 75,
          column: 13
        }
      },
      "36": {
        start: {
          line: 74,
          column: 16
        },
        end: {
          line: 74,
          column: 28
        }
      },
      "37": {
        start: {
          line: 76,
          column: 12
        },
        end: {
          line: 81,
          column: 13
        }
      },
      "38": {
        start: {
          line: 77,
          column: 16
        },
        end: {
          line: 79,
          column: 17
        }
      },
      "39": {
        start: {
          line: 78,
          column: 20
        },
        end: {
          line: 78,
          column: 90
        }
      },
      "40": {
        start: {
          line: 80,
          column: 16
        },
        end: {
          line: 80,
          column: 107
        }
      },
      "41": {
        start: {
          line: 82,
          column: 12
        },
        end: {
          line: 82,
          column: 107
        }
      },
      "42": {
        start: {
          line: 92,
          column: 25
        },
        end: {
          line: 92,
          column: 82
        }
      },
      "43": {
        start: {
          line: 93,
          column: 23
        },
        end: {
          line: 93,
          column: 47
        }
      },
      "44": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 96,
          column: 9
        }
      },
      "45": {
        start: {
          line: 95,
          column: 12
        },
        end: {
          line: 95,
          column: 26
        }
      },
      "46": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 140,
          column: 9
        }
      },
      "47": {
        start: {
          line: 98,
          column: 24
        },
        end: {
          line: 98,
          column: 121
        }
      },
      "48": {
        start: {
          line: 99,
          column: 29
        },
        end: {
          line: 104,
          column: 14
        }
      },
      "49": {
        start: {
          line: 105,
          column: 12
        },
        end: {
          line: 107,
          column: 13
        }
      },
      "50": {
        start: {
          line: 106,
          column: 16
        },
        end: {
          line: 106,
          column: 149
        }
      },
      "51": {
        start: {
          line: 108,
          column: 25
        },
        end: {
          line: 108,
          column: 46
        }
      },
      "52": {
        start: {
          line: 109,
          column: 12
        },
        end: {
          line: 111,
          column: 13
        }
      },
      "53": {
        start: {
          line: 110,
          column: 16
        },
        end: {
          line: 110,
          column: 128
        }
      },
      "54": {
        start: {
          line: 112,
          column: 12
        },
        end: {
          line: 114,
          column: 13
        }
      },
      "55": {
        start: {
          line: 113,
          column: 16
        },
        end: {
          line: 113,
          column: 86
        }
      },
      "56": {
        start: {
          line: 116,
          column: 12
        },
        end: {
          line: 126,
          column: 13
        }
      },
      "57": {
        start: {
          line: 117,
          column: 16
        },
        end: {
          line: 125,
          column: 17
        }
      },
      "58": {
        start: {
          line: 118,
          column: 20
        },
        end: {
          line: 124,
          column: 21
        }
      },
      "59": {
        start: {
          line: 119,
          column: 24
        },
        end: {
          line: 123,
          column: 25
        }
      },
      "60": {
        start: {
          line: 120,
          column: 44
        },
        end: {
          line: 120,
          column: 63
        }
      },
      "61": {
        start: {
          line: 121,
          column: 28
        },
        end: {
          line: 121,
          column: 62
        }
      },
      "62": {
        start: {
          line: 122,
          column: 28
        },
        end: {
          line: 122,
          column: 43
        }
      },
      "63": {
        start: {
          line: 127,
          column: 12
        },
        end: {
          line: 127,
          column: 86
        }
      },
      "64": {
        start: {
          line: 130,
          column: 12
        },
        end: {
          line: 132,
          column: 13
        }
      },
      "65": {
        start: {
          line: 131,
          column: 16
        },
        end: {
          line: 131,
          column: 28
        }
      },
      "66": {
        start: {
          line: 133,
          column: 12
        },
        end: {
          line: 138,
          column: 13
        }
      },
      "67": {
        start: {
          line: 134,
          column: 16
        },
        end: {
          line: 136,
          column: 17
        }
      },
      "68": {
        start: {
          line: 135,
          column: 20
        },
        end: {
          line: 135,
          column: 98
        }
      },
      "69": {
        start: {
          line: 137,
          column: 16
        },
        end: {
          line: 137,
          column: 115
        }
      },
      "70": {
        start: {
          line: 139,
          column: 12
        },
        end: {
          line: 139,
          column: 106
        }
      },
      "71": {
        start: {
          line: 146,
          column: 8
        },
        end: {
          line: 146,
          column: 27
        }
      },
      "72": {
        start: {
          line: 153,
          column: 8
        },
        end: {
          line: 153,
          column: 31
        }
      },
      "73": {
        start: {
          line: 156,
          column: 0
        },
        end: {
          line: 156,
          column: 44
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 11,
            column: 4
          },
          end: {
            line: 11,
            column: 5
          }
        },
        loc: {
          start: {
            line: 11,
            column: 24
          },
          end: {
            line: 18,
            column: 5
          }
        },
        line: 11
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 25,
            column: 4
          },
          end: {
            line: 25,
            column: 5
          }
        },
        loc: {
          start: {
            line: 25,
            column: 40
          },
          end: {
            line: 84,
            column: 5
          }
        },
        line: 25
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 91,
            column: 4
          },
          end: {
            line: 91,
            column: 5
          }
        },
        loc: {
          start: {
            line: 91,
            column: 44
          },
          end: {
            line: 141,
            column: 5
          }
        },
        line: 91
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 145,
            column: 4
          },
          end: {
            line: 145,
            column: 5
          }
        },
        loc: {
          start: {
            line: 145,
            column: 17
          },
          end: {
            line: 147,
            column: 5
          }
        },
        line: 145
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 152,
            column: 4
          },
          end: {
            line: 152,
            column: 5
          }
        },
        loc: {
          start: {
            line: 152,
            column: 19
          },
          end: {
            line: 154,
            column: 5
          }
        },
        line: 152
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 13,
            column: 22
          },
          end: {
            line: 13,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 22
          },
          end: {
            line: 13,
            column: 28
          }
        }, {
          start: {
            line: 13,
            column: 32
          },
          end: {
            line: 13,
            column: 65
          }
        }, {
          start: {
            line: 13,
            column: 69
          },
          end: {
            line: 13,
            column: 71
          }
        }],
        line: 13
      },
      "1": {
        loc: {
          start: {
            line: 15,
            column: 8
          },
          end: {
            line: 17,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 15,
            column: 8
          },
          end: {
            line: 17,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 15
      },
      "2": {
        loc: {
          start: {
            line: 36,
            column: 8
          },
          end: {
            line: 38,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 36,
            column: 8
          },
          end: {
            line: 38,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 36
      },
      "3": {
        loc: {
          start: {
            line: 36,
            column: 12
          },
          end: {
            line: 36,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 36,
            column: 12
          },
          end: {
            line: 36,
            column: 18
          }
        }, {
          start: {
            line: 36,
            column: 22
          },
          end: {
            line: 36,
            column: 48
          }
        }],
        line: 36
      },
      "4": {
        loc: {
          start: {
            line: 47,
            column: 12
          },
          end: {
            line: 49,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 47,
            column: 12
          },
          end: {
            line: 49,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 47
      },
      "5": {
        loc: {
          start: {
            line: 51,
            column: 12
          },
          end: {
            line: 56,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 51,
            column: 12
          },
          end: {
            line: 56,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 51
      },
      "6": {
        loc: {
          start: {
            line: 52,
            column: 16
          },
          end: {
            line: 54,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 52,
            column: 16
          },
          end: {
            line: 54,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 52
      },
      "7": {
        loc: {
          start: {
            line: 55,
            column: 50
          },
          end: {
            line: 55,
            column: 118
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 55,
            column: 50
          },
          end: {
            line: 55,
            column: 68
          }
        }, {
          start: {
            line: 55,
            column: 72
          },
          end: {
            line: 55,
            column: 118
          }
        }],
        line: 55
      },
      "8": {
        loc: {
          start: {
            line: 57,
            column: 12
          },
          end: {
            line: 59,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 57,
            column: 12
          },
          end: {
            line: 59,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 57
      },
      "9": {
        loc: {
          start: {
            line: 57,
            column: 16
          },
          end: {
            line: 57,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 57,
            column: 16
          },
          end: {
            line: 57,
            column: 29
          }
        }, {
          start: {
            line: 57,
            column: 33
          },
          end: {
            line: 57,
            column: 58
          }
        }],
        line: 57
      },
      "10": {
        loc: {
          start: {
            line: 61,
            column: 12
          },
          end: {
            line: 63,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 61,
            column: 12
          },
          end: {
            line: 63,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 61
      },
      "11": {
        loc: {
          start: {
            line: 61,
            column: 16
          },
          end: {
            line: 61,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 61,
            column: 16
          },
          end: {
            line: 61,
            column: 32
          }
        }, {
          start: {
            line: 61,
            column: 36
          },
          end: {
            line: 61,
            column: 61
          }
        }],
        line: 61
      },
      "12": {
        loc: {
          start: {
            line: 73,
            column: 12
          },
          end: {
            line: 75,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 73,
            column: 12
          },
          end: {
            line: 75,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 73
      },
      "13": {
        loc: {
          start: {
            line: 73,
            column: 16
          },
          end: {
            line: 73,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 73,
            column: 16
          },
          end: {
            line: 73,
            column: 56
          }
        }, {
          start: {
            line: 73,
            column: 60
          },
          end: {
            line: 73,
            column: 94
          }
        }],
        line: 73
      },
      "14": {
        loc: {
          start: {
            line: 76,
            column: 12
          },
          end: {
            line: 81,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 76,
            column: 12
          },
          end: {
            line: 81,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 76
      },
      "15": {
        loc: {
          start: {
            line: 77,
            column: 16
          },
          end: {
            line: 79,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 16
          },
          end: {
            line: 79,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 77
      },
      "16": {
        loc: {
          start: {
            line: 94,
            column: 8
          },
          end: {
            line: 96,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 94,
            column: 8
          },
          end: {
            line: 96,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 94
      },
      "17": {
        loc: {
          start: {
            line: 94,
            column: 12
          },
          end: {
            line: 94,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 94,
            column: 12
          },
          end: {
            line: 94,
            column: 18
          }
        }, {
          start: {
            line: 94,
            column: 22
          },
          end: {
            line: 94,
            column: 48
          }
        }],
        line: 94
      },
      "18": {
        loc: {
          start: {
            line: 105,
            column: 12
          },
          end: {
            line: 107,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 105,
            column: 12
          },
          end: {
            line: 107,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 105
      },
      "19": {
        loc: {
          start: {
            line: 109,
            column: 12
          },
          end: {
            line: 111,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 109,
            column: 12
          },
          end: {
            line: 111,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 109
      },
      "20": {
        loc: {
          start: {
            line: 110,
            column: 50
          },
          end: {
            line: 110,
            column: 126
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 110,
            column: 50
          },
          end: {
            line: 110,
            column: 68
          }
        }, {
          start: {
            line: 110,
            column: 72
          },
          end: {
            line: 110,
            column: 126
          }
        }],
        line: 110
      },
      "21": {
        loc: {
          start: {
            line: 112,
            column: 12
          },
          end: {
            line: 114,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 112,
            column: 12
          },
          end: {
            line: 114,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 112
      },
      "22": {
        loc: {
          start: {
            line: 112,
            column: 16
          },
          end: {
            line: 112,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 112,
            column: 16
          },
          end: {
            line: 112,
            column: 29
          }
        }, {
          start: {
            line: 112,
            column: 33
          },
          end: {
            line: 112,
            column: 58
          }
        }],
        line: 112
      },
      "23": {
        loc: {
          start: {
            line: 117,
            column: 16
          },
          end: {
            line: 125,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 117,
            column: 16
          },
          end: {
            line: 125,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 117
      },
      "24": {
        loc: {
          start: {
            line: 119,
            column: 24
          },
          end: {
            line: 123,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 119,
            column: 24
          },
          end: {
            line: 123,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 119
      },
      "25": {
        loc: {
          start: {
            line: 130,
            column: 12
          },
          end: {
            line: 132,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 130,
            column: 12
          },
          end: {
            line: 132,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 130
      },
      "26": {
        loc: {
          start: {
            line: 130,
            column: 16
          },
          end: {
            line: 130,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 130,
            column: 16
          },
          end: {
            line: 130,
            column: 56
          }
        }, {
          start: {
            line: 130,
            column: 60
          },
          end: {
            line: 130,
            column: 94
          }
        }],
        line: 130
      },
      "27": {
        loc: {
          start: {
            line: 133,
            column: 12
          },
          end: {
            line: 138,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 133,
            column: 12
          },
          end: {
            line: 138,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 133
      },
      "28": {
        loc: {
          start: {
            line: 134,
            column: 16
          },
          end: {
            line: 136,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 134,
            column: 16
          },
          end: {
            line: 136,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 134
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/WebstormProjects/goo/src/services/geocoding.ts",
      mappings: ";;;AACA,6CAA8F;AAC9F,oDAAsD;AACtD,4CAA0C;AA4B1C;;GAEG;AACH,MAAa,gBAAgB;IAK3B,YAAY,MAAe;QAJnB,UAAK,GAAG,IAAI,GAAG,EAAgC,CAAC;QAKtD,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,EAAE,CAAC;QAChE,IAAI,CAAC,OAAO,GAAG,sBAAU,CAAC,kBAAkB,CAAC;QAE7C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,2BAAkB,CAAC,mCAAmC,EAAE,uBAAuB,CAAC,CAAC;QAC7F,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,oBAAoB,CAAC,OAAe;QACxC,2BAA2B;QAC3B,IAAI,CAAC;YACH,IAAA,4BAAe,EAAC,OAAO,CAAC,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,uBAAc,CAAC,4BAA4B,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;QAC3E,CAAC;QAED,oBAAoB;QACpB,MAAM,QAAQ,GAAG,OAAO,OAAO,EAAE,CAAC;QAClC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YACzC,OAAO,MAAqB,CAAC;QAC/B,CAAC;QAED,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,iBAAiB,kBAAkB,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YAE7F,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;gBAChC,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE;oBACP,QAAQ,EAAE,kBAAkB;iBAC7B;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,iBAAQ,CAChB,iCAAiC,QAAQ,CAAC,UAAU,EAAE,EACtD,QAAQ,CAAC,MAAM,EACf,sBAAsB,CACvB,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,GAAoB,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEpD,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,MAAM,KAAK,cAAc,EAAE,CAAC;oBACnC,MAAM,IAAI,uBAAc,CAAC,kCAAkC,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;gBACjF,CAAC;gBACD,MAAM,IAAI,uBAAc,CACtB,IAAI,CAAC,aAAa,IAAI,iCAAiC,IAAI,CAAC,MAAM,EAAE,EACpE,OAAO,CACR,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/C,MAAM,IAAI,uBAAc,CAAC,kCAAkC,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;YACjF,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC/B,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBAClD,MAAM,IAAI,uBAAc,CAAC,yCAAyC,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;YACxF,CAAC;YAED,MAAM,WAAW,GAAgB;gBAC/B,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG;gBACtC,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG;aACxC,CAAC;YAEF,mBAAmB;YACnB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YAEtC,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,uBAAc,IAAI,KAAK,YAAY,iBAAQ,EAAE,CAAC;gBACjE,MAAM,KAAK,CAAC;YACd,CAAC;YAED,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;oBAChC,MAAM,IAAI,qBAAY,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;gBAC/D,CAAC;gBACD,MAAM,IAAI,qBAAY,CAAC,mCAAmC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;YACpF,CAAC;YAED,MAAM,IAAI,uBAAc,CAAC,mCAAmC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACxF,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,oBAAoB,CAAC,WAAwB;QACjD,MAAM,QAAQ,GAAG,UAAU,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;QAC3E,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YACzC,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,gBAAgB,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,SAAS,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YAE9G,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;gBAChC,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE;oBACP,QAAQ,EAAE,kBAAkB;iBAC7B;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,iBAAQ,CAChB,yCAAyC,QAAQ,CAAC,UAAU,EAAE,EAC9D,QAAQ,CAAC,MAAM,EACf,sBAAsB,CACvB,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,GAAoB,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEpD,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;gBACzB,MAAM,IAAI,uBAAc,CACtB,IAAI,CAAC,aAAa,IAAI,yCAAyC,IAAI,CAAC,MAAM,EAAE,CAC7E,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/C,MAAM,IAAI,uBAAc,CAAC,kCAAkC,CAAC,CAAC;YAC/D,CAAC;YAED,yCAAyC;YACzC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBAClC,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;oBAC9B,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;wBAClD,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;4BAC5C,MAAM,OAAO,GAAG,SAAS,CAAC,SAAS,CAAC;4BACpC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;4BAClC,OAAO,OAAO,CAAC;wBACjB,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,MAAM,IAAI,uBAAc,CAAC,sCAAsC,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,uBAAc,IAAI,KAAK,YAAY,iBAAQ,EAAE,CAAC;gBACjE,MAAM,KAAK,CAAC;YACd,CAAC;YAED,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;oBAChC,MAAM,IAAI,qBAAY,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;gBACvE,CAAC;gBACD,MAAM,IAAI,qBAAY,CAAC,2CAA2C,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;YAC5F,CAAC;YAED,MAAM,IAAI,uBAAc,CAAC,2CAA2C,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACvF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACrB,CAAC;IAED;;;OAGG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;CACF;AA1LD,4CA0LC",
      names: [],
      sources: ["/Users/<USER>/WebstormProjects/goo/src/services/geocoding.ts"],
      sourcesContent: ["import { Coordinates } from '../models/Business';\nimport { GeocodingError, ApiError, NetworkError, ConfigurationError } from '../models/Errors';\nimport { validateZipCode } from '../utils/validation';\nimport { API_CONFIG } from '../constants';\n\n/**\n * Google Geocoding API response interfaces\n */\ninterface GeocodeResult {\n  geometry: {\n    location: {\n      lat: number;\n      lng: number;\n    };\n  };\n  formatted_address: string;\n  address_components?: AddressComponent[];\n}\n\ninterface AddressComponent {\n  long_name: string;\n  short_name: string;\n  types: string[];\n}\n\ninterface GeocodeResponse {\n  results: GeocodeResult[];\n  status: string;\n  error_message?: string;\n}\n\n/**\n * Service for converting between zip codes and coordinates using Google Geocoding API\n */\nexport class GeocodingService {\n  private cache = new Map<string, Coordinates | string>();\n  private readonly apiKey: string;\n  private readonly baseUrl: string;\n\n  constructor(apiKey?: string) {\n    this.apiKey = apiKey || process.env.GOOGLE_PLACES_API_KEY || '';\n    this.baseUrl = API_CONFIG.GEOCODING_BASE_URL;\n\n    if (!this.apiKey) {\n      throw new ConfigurationError('Google Places API key is required', 'GOOGLE_PLACES_API_KEY');\n    }\n  }\n\n  /**\n   * Converts a zip code to coordinates\n   * @param zipCode - The zip code to convert\n   * @returns Promise resolving to coordinates\n   * @throws GeocodingError if conversion fails\n   */\n  async zipCodeToCoordinates(zipCode: string): Promise<Coordinates> {\n    // Validate zip code format\n    try {\n      validateZipCode(zipCode);\n    } catch (error) {\n      throw new GeocodingError(`Invalid zip code format: ${zipCode}`, zipCode);\n    }\n\n    // Check cache first\n    const cacheKey = `zip_${zipCode}`;\n    const cached = this.cache.get(cacheKey);\n    if (cached && typeof cached === 'object') {\n      return cached as Coordinates;\n    }\n\n    try {\n      const url = `${this.baseUrl}/json?address=${encodeURIComponent(zipCode)}&key=${this.apiKey}`;\n      \n      const response = await fetch(url, {\n        method: 'GET',\n        headers: {\n          'Accept': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new ApiError(\n          `Geocoding API request failed: ${response.statusText}`,\n          response.status,\n          'Google Geocoding API'\n        );\n      }\n\n      const data: GeocodeResponse = await response.json();\n\n      if (data.status !== 'OK') {\n        if (data.status === 'ZERO_RESULTS') {\n          throw new GeocodingError(`No results found for zip code: ${zipCode}`, zipCode);\n        }\n        throw new GeocodingError(\n          data.error_message || `Geocoding failed with status: ${data.status}`,\n          zipCode\n        );\n      }\n\n      if (!data.results || data.results.length === 0) {\n        throw new GeocodingError(`No results found for zip code: ${zipCode}`, zipCode);\n      }\n\n      const result = data.results[0];\n      if (!result.geometry || !result.geometry.location) {\n        throw new GeocodingError(`Invalid response format for zip code: ${zipCode}`, zipCode);\n      }\n\n      const coordinates: Coordinates = {\n        latitude: result.geometry.location.lat,\n        longitude: result.geometry.location.lng,\n      };\n\n      // Cache the result\n      this.cache.set(cacheKey, coordinates);\n\n      return coordinates;\n    } catch (error) {\n      if (error instanceof GeocodingError || error instanceof ApiError) {\n        throw error;\n      }\n      \n      if (error instanceof Error) {\n        if (error.name === 'AbortError') {\n          throw new NetworkError('Geocoding request timed out', error);\n        }\n        throw new NetworkError(`Network error during geocoding: ${error.message}`, error);\n      }\n      \n      throw new GeocodingError(`Unknown error during geocoding: ${String(error)}`, zipCode);\n    }\n  }\n\n  /**\n   * Converts coordinates to zip code (reverse geocoding)\n   * @param coordinates - The coordinates to convert\n   * @returns Promise resolving to zip code\n   * @throws GeocodingError if conversion fails\n   */\n  async coordinatesToZipCode(coordinates: Coordinates): Promise<string> {\n    const cacheKey = `coords_${coordinates.latitude}_${coordinates.longitude}`;\n    const cached = this.cache.get(cacheKey);\n    if (cached && typeof cached === 'string') {\n      return cached;\n    }\n\n    try {\n      const url = `${this.baseUrl}/json?latlng=${coordinates.latitude},${coordinates.longitude}&key=${this.apiKey}`;\n      \n      const response = await fetch(url, {\n        method: 'GET',\n        headers: {\n          'Accept': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new ApiError(\n          `Reverse geocoding API request failed: ${response.statusText}`,\n          response.status,\n          'Google Geocoding API'\n        );\n      }\n\n      const data: GeocodeResponse = await response.json();\n\n      if (data.status !== 'OK') {\n        throw new GeocodingError(\n          data.error_message || `Reverse geocoding failed with status: ${data.status}`\n        );\n      }\n\n      if (!data.results || data.results.length === 0) {\n        throw new GeocodingError('No results found for coordinates');\n      }\n\n      // Find postal code in address components\n      for (const result of data.results) {\n        if (result.address_components) {\n          for (const component of result.address_components) {\n            if (component.types.includes('postal_code')) {\n              const zipCode = component.long_name;\n              this.cache.set(cacheKey, zipCode);\n              return zipCode;\n            }\n          }\n        }\n      }\n\n      throw new GeocodingError('No postal code found for coordinates');\n    } catch (error) {\n      if (error instanceof GeocodingError || error instanceof ApiError) {\n        throw error;\n      }\n      \n      if (error instanceof Error) {\n        if (error.name === 'AbortError') {\n          throw new NetworkError('Reverse geocoding request timed out', error);\n        }\n        throw new NetworkError(`Network error during reverse geocoding: ${error.message}`, error);\n      }\n      \n      throw new GeocodingError(`Unknown error during reverse geocoding: ${String(error)}`);\n    }\n  }\n\n  /**\n   * Clears the geocoding cache\n   */\n  clearCache(): void {\n    this.cache.clear();\n  }\n\n  /**\n   * Gets the current cache size\n   * @returns Number of cached entries\n   */\n  getCacheSize(): number {\n    return this.cache.size;\n  }\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5d8e93b3680271340d026ec258aef8582935d8c6"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_w2rjvihid = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_w2rjvihid();
cov_w2rjvihid().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_w2rjvihid().s[1]++;
exports.GeocodingService = void 0;
const Errors_1 =
/* istanbul ignore next */
(cov_w2rjvihid().s[2]++, require("../models/Errors"));
const validation_1 =
/* istanbul ignore next */
(cov_w2rjvihid().s[3]++, require("../utils/validation"));
const constants_1 =
/* istanbul ignore next */
(cov_w2rjvihid().s[4]++, require("../constants"));
/**
 * Service for converting between zip codes and coordinates using Google Geocoding API
 */
class GeocodingService {
  constructor(apiKey) {
    /* istanbul ignore next */
    cov_w2rjvihid().f[0]++;
    cov_w2rjvihid().s[5]++;
    this.cache = new Map();
    /* istanbul ignore next */
    cov_w2rjvihid().s[6]++;
    this.apiKey =
    /* istanbul ignore next */
    (cov_w2rjvihid().b[0][0]++, apiKey) ||
    /* istanbul ignore next */
    (cov_w2rjvihid().b[0][1]++, process.env.GOOGLE_PLACES_API_KEY) ||
    /* istanbul ignore next */
    (cov_w2rjvihid().b[0][2]++, '');
    /* istanbul ignore next */
    cov_w2rjvihid().s[7]++;
    this.baseUrl = constants_1.API_CONFIG.GEOCODING_BASE_URL;
    /* istanbul ignore next */
    cov_w2rjvihid().s[8]++;
    if (!this.apiKey) {
      /* istanbul ignore next */
      cov_w2rjvihid().b[1][0]++;
      cov_w2rjvihid().s[9]++;
      throw new Errors_1.ConfigurationError('Google Places API key is required', 'GOOGLE_PLACES_API_KEY');
    } else
    /* istanbul ignore next */
    {
      cov_w2rjvihid().b[1][1]++;
    }
  }
  /**
   * Converts a zip code to coordinates
   * @param zipCode - The zip code to convert
   * @returns Promise resolving to coordinates
   * @throws GeocodingError if conversion fails
   */
  async zipCodeToCoordinates(zipCode) {
    /* istanbul ignore next */
    cov_w2rjvihid().f[1]++;
    cov_w2rjvihid().s[10]++;
    // Validate zip code format
    try {
      /* istanbul ignore next */
      cov_w2rjvihid().s[11]++;
      (0, validation_1.validateZipCode)(zipCode);
    } catch (error) {
      /* istanbul ignore next */
      cov_w2rjvihid().s[12]++;
      throw new Errors_1.GeocodingError(`Invalid zip code format: ${zipCode}`, zipCode);
    }
    // Check cache first
    const cacheKey =
    /* istanbul ignore next */
    (cov_w2rjvihid().s[13]++, `zip_${zipCode}`);
    const cached =
    /* istanbul ignore next */
    (cov_w2rjvihid().s[14]++, this.cache.get(cacheKey));
    /* istanbul ignore next */
    cov_w2rjvihid().s[15]++;
    if (
    /* istanbul ignore next */
    (cov_w2rjvihid().b[3][0]++, cached) &&
    /* istanbul ignore next */
    (cov_w2rjvihid().b[3][1]++, typeof cached === 'object')) {
      /* istanbul ignore next */
      cov_w2rjvihid().b[2][0]++;
      cov_w2rjvihid().s[16]++;
      return cached;
    } else
    /* istanbul ignore next */
    {
      cov_w2rjvihid().b[2][1]++;
    }
    cov_w2rjvihid().s[17]++;
    try {
      const url =
      /* istanbul ignore next */
      (cov_w2rjvihid().s[18]++, `${this.baseUrl}/json?address=${encodeURIComponent(zipCode)}&key=${this.apiKey}`);
      const response =
      /* istanbul ignore next */
      (cov_w2rjvihid().s[19]++, await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      }));
      /* istanbul ignore next */
      cov_w2rjvihid().s[20]++;
      if (!response.ok) {
        /* istanbul ignore next */
        cov_w2rjvihid().b[4][0]++;
        cov_w2rjvihid().s[21]++;
        throw new Errors_1.ApiError(`Geocoding API request failed: ${response.statusText}`, response.status, 'Google Geocoding API');
      } else
      /* istanbul ignore next */
      {
        cov_w2rjvihid().b[4][1]++;
      }
      const data =
      /* istanbul ignore next */
      (cov_w2rjvihid().s[22]++, await response.json());
      /* istanbul ignore next */
      cov_w2rjvihid().s[23]++;
      if (data.status !== 'OK') {
        /* istanbul ignore next */
        cov_w2rjvihid().b[5][0]++;
        cov_w2rjvihid().s[24]++;
        if (data.status === 'ZERO_RESULTS') {
          /* istanbul ignore next */
          cov_w2rjvihid().b[6][0]++;
          cov_w2rjvihid().s[25]++;
          throw new Errors_1.GeocodingError(`No results found for zip code: ${zipCode}`, zipCode);
        } else
        /* istanbul ignore next */
        {
          cov_w2rjvihid().b[6][1]++;
        }
        cov_w2rjvihid().s[26]++;
        throw new Errors_1.GeocodingError(
        /* istanbul ignore next */
        (cov_w2rjvihid().b[7][0]++, data.error_message) ||
        /* istanbul ignore next */
        (cov_w2rjvihid().b[7][1]++, `Geocoding failed with status: ${data.status}`), zipCode);
      } else
      /* istanbul ignore next */
      {
        cov_w2rjvihid().b[5][1]++;
      }
      cov_w2rjvihid().s[27]++;
      if (
      /* istanbul ignore next */
      (cov_w2rjvihid().b[9][0]++, !data.results) ||
      /* istanbul ignore next */
      (cov_w2rjvihid().b[9][1]++, data.results.length === 0)) {
        /* istanbul ignore next */
        cov_w2rjvihid().b[8][0]++;
        cov_w2rjvihid().s[28]++;
        throw new Errors_1.GeocodingError(`No results found for zip code: ${zipCode}`, zipCode);
      } else
      /* istanbul ignore next */
      {
        cov_w2rjvihid().b[8][1]++;
      }
      const result =
      /* istanbul ignore next */
      (cov_w2rjvihid().s[29]++, data.results[0]);
      /* istanbul ignore next */
      cov_w2rjvihid().s[30]++;
      if (
      /* istanbul ignore next */
      (cov_w2rjvihid().b[11][0]++, !result.geometry) ||
      /* istanbul ignore next */
      (cov_w2rjvihid().b[11][1]++, !result.geometry.location)) {
        /* istanbul ignore next */
        cov_w2rjvihid().b[10][0]++;
        cov_w2rjvihid().s[31]++;
        throw new Errors_1.GeocodingError(`Invalid response format for zip code: ${zipCode}`, zipCode);
      } else
      /* istanbul ignore next */
      {
        cov_w2rjvihid().b[10][1]++;
      }
      const coordinates =
      /* istanbul ignore next */
      (cov_w2rjvihid().s[32]++, {
        latitude: result.geometry.location.lat,
        longitude: result.geometry.location.lng
      });
      // Cache the result
      /* istanbul ignore next */
      cov_w2rjvihid().s[33]++;
      this.cache.set(cacheKey, coordinates);
      /* istanbul ignore next */
      cov_w2rjvihid().s[34]++;
      return coordinates;
    } catch (error) {
      /* istanbul ignore next */
      cov_w2rjvihid().s[35]++;
      if (
      /* istanbul ignore next */
      (cov_w2rjvihid().b[13][0]++, error instanceof Errors_1.GeocodingError) ||
      /* istanbul ignore next */
      (cov_w2rjvihid().b[13][1]++, error instanceof Errors_1.ApiError)) {
        /* istanbul ignore next */
        cov_w2rjvihid().b[12][0]++;
        cov_w2rjvihid().s[36]++;
        throw error;
      } else
      /* istanbul ignore next */
      {
        cov_w2rjvihid().b[12][1]++;
      }
      cov_w2rjvihid().s[37]++;
      if (error instanceof Error) {
        /* istanbul ignore next */
        cov_w2rjvihid().b[14][0]++;
        cov_w2rjvihid().s[38]++;
        if (error.name === 'AbortError') {
          /* istanbul ignore next */
          cov_w2rjvihid().b[15][0]++;
          cov_w2rjvihid().s[39]++;
          throw new Errors_1.NetworkError('Geocoding request timed out', error);
        } else
        /* istanbul ignore next */
        {
          cov_w2rjvihid().b[15][1]++;
        }
        cov_w2rjvihid().s[40]++;
        throw new Errors_1.NetworkError(`Network error during geocoding: ${error.message}`, error);
      } else
      /* istanbul ignore next */
      {
        cov_w2rjvihid().b[14][1]++;
      }
      cov_w2rjvihid().s[41]++;
      throw new Errors_1.GeocodingError(`Unknown error during geocoding: ${String(error)}`, zipCode);
    }
  }
  /**
   * Converts coordinates to zip code (reverse geocoding)
   * @param coordinates - The coordinates to convert
   * @returns Promise resolving to zip code
   * @throws GeocodingError if conversion fails
   */
  async coordinatesToZipCode(coordinates) {
    /* istanbul ignore next */
    cov_w2rjvihid().f[2]++;
    const cacheKey =
    /* istanbul ignore next */
    (cov_w2rjvihid().s[42]++, `coords_${coordinates.latitude}_${coordinates.longitude}`);
    const cached =
    /* istanbul ignore next */
    (cov_w2rjvihid().s[43]++, this.cache.get(cacheKey));
    /* istanbul ignore next */
    cov_w2rjvihid().s[44]++;
    if (
    /* istanbul ignore next */
    (cov_w2rjvihid().b[17][0]++, cached) &&
    /* istanbul ignore next */
    (cov_w2rjvihid().b[17][1]++, typeof cached === 'string')) {
      /* istanbul ignore next */
      cov_w2rjvihid().b[16][0]++;
      cov_w2rjvihid().s[45]++;
      return cached;
    } else
    /* istanbul ignore next */
    {
      cov_w2rjvihid().b[16][1]++;
    }
    cov_w2rjvihid().s[46]++;
    try {
      const url =
      /* istanbul ignore next */
      (cov_w2rjvihid().s[47]++, `${this.baseUrl}/json?latlng=${coordinates.latitude},${coordinates.longitude}&key=${this.apiKey}`);
      const response =
      /* istanbul ignore next */
      (cov_w2rjvihid().s[48]++, await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      }));
      /* istanbul ignore next */
      cov_w2rjvihid().s[49]++;
      if (!response.ok) {
        /* istanbul ignore next */
        cov_w2rjvihid().b[18][0]++;
        cov_w2rjvihid().s[50]++;
        throw new Errors_1.ApiError(`Reverse geocoding API request failed: ${response.statusText}`, response.status, 'Google Geocoding API');
      } else
      /* istanbul ignore next */
      {
        cov_w2rjvihid().b[18][1]++;
      }
      const data =
      /* istanbul ignore next */
      (cov_w2rjvihid().s[51]++, await response.json());
      /* istanbul ignore next */
      cov_w2rjvihid().s[52]++;
      if (data.status !== 'OK') {
        /* istanbul ignore next */
        cov_w2rjvihid().b[19][0]++;
        cov_w2rjvihid().s[53]++;
        throw new Errors_1.GeocodingError(
        /* istanbul ignore next */
        (cov_w2rjvihid().b[20][0]++, data.error_message) ||
        /* istanbul ignore next */
        (cov_w2rjvihid().b[20][1]++, `Reverse geocoding failed with status: ${data.status}`));
      } else
      /* istanbul ignore next */
      {
        cov_w2rjvihid().b[19][1]++;
      }
      cov_w2rjvihid().s[54]++;
      if (
      /* istanbul ignore next */
      (cov_w2rjvihid().b[22][0]++, !data.results) ||
      /* istanbul ignore next */
      (cov_w2rjvihid().b[22][1]++, data.results.length === 0)) {
        /* istanbul ignore next */
        cov_w2rjvihid().b[21][0]++;
        cov_w2rjvihid().s[55]++;
        throw new Errors_1.GeocodingError('No results found for coordinates');
      } else
      /* istanbul ignore next */
      {
        cov_w2rjvihid().b[21][1]++;
      }
      // Find postal code in address components
      cov_w2rjvihid().s[56]++;
      for (const result of data.results) {
        /* istanbul ignore next */
        cov_w2rjvihid().s[57]++;
        if (result.address_components) {
          /* istanbul ignore next */
          cov_w2rjvihid().b[23][0]++;
          cov_w2rjvihid().s[58]++;
          for (const component of result.address_components) {
            /* istanbul ignore next */
            cov_w2rjvihid().s[59]++;
            if (component.types.includes('postal_code')) {
              /* istanbul ignore next */
              cov_w2rjvihid().b[24][0]++;
              const zipCode =
              /* istanbul ignore next */
              (cov_w2rjvihid().s[60]++, component.long_name);
              /* istanbul ignore next */
              cov_w2rjvihid().s[61]++;
              this.cache.set(cacheKey, zipCode);
              /* istanbul ignore next */
              cov_w2rjvihid().s[62]++;
              return zipCode;
            } else
            /* istanbul ignore next */
            {
              cov_w2rjvihid().b[24][1]++;
            }
          }
        } else
        /* istanbul ignore next */
        {
          cov_w2rjvihid().b[23][1]++;
        }
      }
      /* istanbul ignore next */
      cov_w2rjvihid().s[63]++;
      throw new Errors_1.GeocodingError('No postal code found for coordinates');
    } catch (error) {
      /* istanbul ignore next */
      cov_w2rjvihid().s[64]++;
      if (
      /* istanbul ignore next */
      (cov_w2rjvihid().b[26][0]++, error instanceof Errors_1.GeocodingError) ||
      /* istanbul ignore next */
      (cov_w2rjvihid().b[26][1]++, error instanceof Errors_1.ApiError)) {
        /* istanbul ignore next */
        cov_w2rjvihid().b[25][0]++;
        cov_w2rjvihid().s[65]++;
        throw error;
      } else
      /* istanbul ignore next */
      {
        cov_w2rjvihid().b[25][1]++;
      }
      cov_w2rjvihid().s[66]++;
      if (error instanceof Error) {
        /* istanbul ignore next */
        cov_w2rjvihid().b[27][0]++;
        cov_w2rjvihid().s[67]++;
        if (error.name === 'AbortError') {
          /* istanbul ignore next */
          cov_w2rjvihid().b[28][0]++;
          cov_w2rjvihid().s[68]++;
          throw new Errors_1.NetworkError('Reverse geocoding request timed out', error);
        } else
        /* istanbul ignore next */
        {
          cov_w2rjvihid().b[28][1]++;
        }
        cov_w2rjvihid().s[69]++;
        throw new Errors_1.NetworkError(`Network error during reverse geocoding: ${error.message}`, error);
      } else
      /* istanbul ignore next */
      {
        cov_w2rjvihid().b[27][1]++;
      }
      cov_w2rjvihid().s[70]++;
      throw new Errors_1.GeocodingError(`Unknown error during reverse geocoding: ${String(error)}`);
    }
  }
  /**
   * Clears the geocoding cache
   */
  clearCache() {
    /* istanbul ignore next */
    cov_w2rjvihid().f[3]++;
    cov_w2rjvihid().s[71]++;
    this.cache.clear();
  }
  /**
   * Gets the current cache size
   * @returns Number of cached entries
   */
  getCacheSize() {
    /* istanbul ignore next */
    cov_w2rjvihid().f[4]++;
    cov_w2rjvihid().s[72]++;
    return this.cache.size;
  }
}
/* istanbul ignore next */
cov_w2rjvihid().s[73]++;
exports.GeocodingService = GeocodingService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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