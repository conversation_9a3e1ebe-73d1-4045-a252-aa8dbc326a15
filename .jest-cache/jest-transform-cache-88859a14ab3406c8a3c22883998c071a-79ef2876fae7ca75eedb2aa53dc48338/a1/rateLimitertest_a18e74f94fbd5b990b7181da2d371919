7eac5079f2a683ec0d6dc09b0e9fa85e
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const rateLimiter_1 = require("../../src/utils/rateLimiter");
const Errors_1 = require("../../src/models/Errors");
describe('TokenBucketRateLimiter', () => {
    beforeEach(() => {
        jest.useFakeTimers();
    });
    afterEach(() => {
        jest.useRealTimers();
    });
    describe('constructor', () => {
        it('should create rate limiter with correct configuration', () => {
            const rateLimiter = new rateLimiter_1.TokenBucketRateLimiter(10, 20);
            expect(rateLimiter).toBeInstanceOf(rateLimiter_1.TokenBucketRateLimiter);
        });
        it('should throw error for invalid configuration', () => {
            expect(() => new rateLimiter_1.TokenBucketRateLimiter(0, 10)).toThrow();
            expect(() => new rateLimiter_1.TokenBucketRateLimiter(10, 0)).toThrow();
            expect(() => new rateLimiter_1.TokenBucketRateLimiter(-1, 10)).toThrow();
        });
    });
    describe('tryConsume', () => {
        it('should allow requests within rate limit', () => {
            const rateLimiter = new rateLimiter_1.TokenBucketRateLimiter(10, 20);
            // Should allow initial requests up to burst size
            for (let i = 0; i < 20; i++) {
                expect(rateLimiter.tryConsume()).toBe(true);
            }
        });
        it('should reject requests when bucket is empty', () => {
            const rateLimiter = new rateLimiter_1.TokenBucketRateLimiter(10, 5);
            // Consume all tokens
            for (let i = 0; i < 5; i++) {
                expect(rateLimiter.tryConsume()).toBe(true);
            }
            // Next request should be rejected
            expect(rateLimiter.tryConsume()).toBe(false);
        });
        it('should refill tokens over time', () => {
            const rateLimiter = new rateLimiter_1.TokenBucketRateLimiter(10, 5);
            // Consume all tokens
            for (let i = 0; i < 5; i++) {
                rateLimiter.tryConsume();
            }
            expect(rateLimiter.tryConsume()).toBe(false);
            // Advance time by 100ms (should add 1 token at 10 tokens/second)
            jest.advanceTimersByTime(100);
            expect(rateLimiter.tryConsume()).toBe(true);
            expect(rateLimiter.tryConsume()).toBe(false);
        });
        it('should not exceed burst size when refilling', () => {
            const rateLimiter = new rateLimiter_1.TokenBucketRateLimiter(10, 5);
            // Advance time significantly
            jest.advanceTimersByTime(10000);
            // Should still only allow burst size requests
            for (let i = 0; i < 5; i++) {
                expect(rateLimiter.tryConsume()).toBe(true);
            }
            expect(rateLimiter.tryConsume()).toBe(false);
        });
    });
    describe('waitForToken', () => {
        it('should resolve immediately when tokens are available', async () => {
            const rateLimiter = new rateLimiter_1.TokenBucketRateLimiter(10, 5);
            const promise = rateLimiter.waitForToken();
            jest.runAllTimers();
            await expect(promise).resolves.toBeUndefined();
        });
        it('should wait for token refill when bucket is empty', async () => {
            const rateLimiter = new rateLimiter_1.TokenBucketRateLimiter(10, 1);
            // Consume the only token
            rateLimiter.tryConsume();
            const promise = rateLimiter.waitForToken();
            // Should resolve after 100ms (time for 1 token at 10/second)
            jest.advanceTimersByTime(100);
            await expect(promise).resolves.toBeUndefined();
        });
        it('should timeout if waiting too long', async () => {
            const rateLimiter = new rateLimiter_1.TokenBucketRateLimiter(1, 1); // Very slow refill
            // Consume the only token
            rateLimiter.tryConsume();
            const promise = rateLimiter.waitForToken(500); // 500ms timeout
            jest.advanceTimersByTime(600);
            await expect(promise).rejects.toThrow(Errors_1.RateLimitError);
        });
    });
    describe('getAvailableTokens', () => {
        it('should return correct number of available tokens', () => {
            const rateLimiter = new rateLimiter_1.TokenBucketRateLimiter(10, 5);
            expect(rateLimiter.getAvailableTokens()).toBe(5);
            rateLimiter.tryConsume();
            expect(rateLimiter.getAvailableTokens()).toBe(4);
            rateLimiter.tryConsume();
            rateLimiter.tryConsume();
            expect(rateLimiter.getAvailableTokens()).toBe(2);
        });
        it('should update available tokens after refill', () => {
            const rateLimiter = new rateLimiter_1.TokenBucketRateLimiter(10, 2);
            // Consume all tokens
            rateLimiter.tryConsume();
            rateLimiter.tryConsume();
            expect(rateLimiter.getAvailableTokens()).toBe(0);
            // Advance time to refill 1 token
            jest.advanceTimersByTime(100);
            expect(rateLimiter.getAvailableTokens()).toBe(1);
        });
    });
    describe('reset', () => {
        it('should reset bucket to full capacity', () => {
            const rateLimiter = new rateLimiter_1.TokenBucketRateLimiter(10, 5);
            // Consume some tokens
            rateLimiter.tryConsume();
            rateLimiter.tryConsume();
            expect(rateLimiter.getAvailableTokens()).toBe(3);
            rateLimiter.reset();
            expect(rateLimiter.getAvailableTokens()).toBe(5);
        });
    });
    describe('edge cases and error handling', () => {
        it('should handle zero capacity', () => {
            // Constructor parameters are (tokensPerSecond, bucketSize)
            expect(() => new rateLimiter_1.TokenBucketRateLimiter(1, 0)).toThrow('Tokens per second and bucket size must be positive');
        });
        it('should handle zero refill rate', () => {
            // Constructor parameters are (tokensPerSecond, bucketSize)
            expect(() => new rateLimiter_1.TokenBucketRateLimiter(0, 1)).toThrow('Tokens per second and bucket size must be positive');
        });
        it('should handle very small values', () => {
            // Constructor parameters are (tokensPerSecond, bucketSize)
            // With bucket size 0.1, we get 0 tokens initially (Math.floor(0.1) = 0)
            const limiter = new rateLimiter_1.TokenBucketRateLimiter(0.1, 0.1);
            expect(limiter.tryConsume()).toBe(false); // No tokens available initially
            expect(limiter.tryConsume()).toBe(false);
        });
        it('should handle very large capacity', () => {
            const limiter = new rateLimiter_1.TokenBucketRateLimiter(1000000, 1000000);
            // Should be able to consume many tokens initially
            for (let i = 0; i < 1000; i++) {
                expect(limiter.tryConsume()).toBe(true);
            }
        });
        it('should handle fractional refill rates', () => {
            const limiter = new rateLimiter_1.TokenBucketRateLimiter(0.5, 2); // 0.5 tokens per second
            // Consume all tokens
            expect(limiter.tryConsume()).toBe(true);
            expect(limiter.tryConsume()).toBe(true);
            expect(limiter.tryConsume()).toBe(false);
            // Should get 1 token after 2 seconds
            jest.advanceTimersByTime(2000);
            expect(limiter.tryConsume()).toBe(true);
            expect(limiter.tryConsume()).toBe(false);
        });
        it('should handle very long time intervals', () => {
            const limiter = new rateLimiter_1.TokenBucketRateLimiter(10, 5);
            // Consume all tokens
            for (let i = 0; i < 5; i++) {
                expect(limiter.tryConsume()).toBe(true);
            }
            // Advance time by a very long period
            jest.advanceTimersByTime(1000000); // 1000 seconds
            // Should be fully refilled but not exceed capacity
            for (let i = 0; i < 5; i++) {
                expect(limiter.tryConsume()).toBe(true);
            }
            expect(limiter.tryConsume()).toBe(false);
        });
        it('should handle rapid successive calls', () => {
            const limiter = new rateLimiter_1.TokenBucketRateLimiter(10, 5);
            // Rapid consumption
            const results = [];
            for (let i = 0; i < 10; i++) {
                results.push(limiter.tryConsume());
            }
            // First 5 should succeed, rest should fail
            expect(results.slice(0, 5)).toEqual([true, true, true, true, true]);
            expect(results.slice(5)).toEqual([false, false, false, false, false]);
        });
        it('should maintain precision with small time intervals', () => {
            const limiter = new rateLimiter_1.TokenBucketRateLimiter(1000, 1); // 1000 tokens per second
            // Consume the only token
            expect(limiter.tryConsume()).toBe(true);
            expect(limiter.tryConsume()).toBe(false);
            // Advance by 1ms (should add 1 token)
            jest.advanceTimersByTime(1);
            expect(limiter.tryConsume()).toBe(true);
            expect(limiter.tryConsume()).toBe(false);
        });
    });
});
describe('getGlobalRateLimiter', () => {
    beforeEach(() => {
        (0, rateLimiter_1.resetGlobalRateLimiter)();
    });
    it('should return the same instance for same parameters', () => {
        const limiter1 = (0, rateLimiter_1.getGlobalRateLimiter)(10, 20);
        const limiter2 = (0, rateLimiter_1.getGlobalRateLimiter)(10, 20);
        expect(limiter1).toBe(limiter2);
    });
    it('should create new instance for different parameters', () => {
        const limiter1 = (0, rateLimiter_1.getGlobalRateLimiter)(10, 20);
        (0, rateLimiter_1.resetGlobalRateLimiter)(); // Reset between different parameter calls
        const limiter2 = (0, rateLimiter_1.getGlobalRateLimiter)(5, 10);
        expect(limiter1).not.toBe(limiter2);
    });
    it('should handle parameter variations', () => {
        const limiter1 = (0, rateLimiter_1.getGlobalRateLimiter)(10, 20);
        (0, rateLimiter_1.resetGlobalRateLimiter)();
        const limiter2 = (0, rateLimiter_1.getGlobalRateLimiter)(10, 21); // Different burst size
        (0, rateLimiter_1.resetGlobalRateLimiter)();
        const limiter3 = (0, rateLimiter_1.getGlobalRateLimiter)(11, 20); // Different rate
        expect(limiter1).not.toBe(limiter2);
        expect(limiter1).not.toBe(limiter3);
        expect(limiter2).not.toBe(limiter3);
    });
    it('should return TokenBucketRateLimiter instances', () => {
        const limiter = (0, rateLimiter_1.getGlobalRateLimiter)(5, 10);
        expect(limiter).toBeInstanceOf(rateLimiter_1.TokenBucketRateLimiter);
    });
});
describe('getTimeUntilNextToken', () => {
    it('should return 0 when tokens are available', () => {
        const limiter = new rateLimiter_1.TokenBucketRateLimiter(1, 5);
        expect(limiter.getTimeUntilNextToken()).toBe(0);
    });
    it('should return time until next token when no tokens available', () => {
        const limiter = new rateLimiter_1.TokenBucketRateLimiter(1, 1); // 1 token per second, bucket size 1
        // Consume the initial token
        expect(limiter.tryConsume()).toBe(true);
        // Now should return time until next token
        const timeUntilNext = limiter.getTimeUntilNextToken();
        expect(timeUntilNext).toBeGreaterThan(0);
        expect(timeUntilNext).toBeLessThanOrEqual(1000); // Should be within 1 second
    });
    it('should handle edge case when bucket is empty', () => {
        const limiter = new rateLimiter_1.TokenBucketRateLimiter(0.1, 0.1); // Very slow refill
        // Should return time until next token
        const timeUntilNext = limiter.getTimeUntilNextToken();
        expect(timeUntilNext).toBeGreaterThanOrEqual(0);
    });
    it('should calculate correct time based on refill interval', () => {
        const limiter = new rateLimiter_1.TokenBucketRateLimiter(2, 1); // 2 tokens per second, bucket size 1
        // Consume the token
        expect(limiter.tryConsume()).toBe(true);
        // Time until next should be around 500ms (1000ms / 2 tokens per second)
        const timeUntilNext = limiter.getTimeUntilNextToken();
        expect(timeUntilNext).toBeGreaterThan(0);
        expect(timeUntilNext).toBeLessThanOrEqual(500);
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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