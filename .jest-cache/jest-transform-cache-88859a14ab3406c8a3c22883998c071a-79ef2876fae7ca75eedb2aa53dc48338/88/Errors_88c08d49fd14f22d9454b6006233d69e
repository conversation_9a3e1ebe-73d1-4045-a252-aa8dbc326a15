126bbe1b653084399beb28819901032e
"use strict";

/* istanbul ignore next */
function cov_1gv0vrtrp5() {
  var path = "/Users/<USER>/WebstormProjects/goo/src/models/Errors.ts";
  var hash = "7069720d4265bff959a97644facacda513376b43";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/WebstormProjects/goo/src/models/Errors.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 3,
          column: 229
        }
      },
      "2": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 23
        }
      },
      "3": {
        start: {
          line: 10,
          column: 8
        },
        end: {
          line: 10,
          column: 27
        }
      },
      "4": {
        start: {
          line: 11,
          column: 8
        },
        end: {
          line: 11,
          column: 42
        }
      },
      "5": {
        start: {
          line: 13,
          column: 8
        },
        end: {
          line: 15,
          column: 9
        }
      },
      "6": {
        start: {
          line: 14,
          column: 12
        },
        end: {
          line: 14,
          column: 60
        }
      },
      "7": {
        start: {
          line: 18,
          column: 0
        },
        end: {
          line: 18,
          column: 28
        }
      },
      "8": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 24,
          column: 23
        }
      },
      "9": {
        start: {
          line: 25,
          column: 8
        },
        end: {
          line: 25,
          column: 27
        }
      },
      "10": {
        start: {
          line: 26,
          column: 8
        },
        end: {
          line: 26,
          column: 39
        }
      },
      "11": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 30
        }
      },
      "12": {
        start: {
          line: 30,
          column: 0
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "13": {
        start: {
          line: 36,
          column: 8
        },
        end: {
          line: 36,
          column: 30
        }
      },
      "14": {
        start: {
          line: 37,
          column: 8
        },
        end: {
          line: 37,
          column: 31
        }
      },
      "15": {
        start: {
          line: 38,
          column: 8
        },
        end: {
          line: 38,
          column: 32
        }
      },
      "16": {
        start: {
          line: 39,
          column: 8
        },
        end: {
          line: 39,
          column: 37
        }
      },
      "17": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 28
        }
      },
      "18": {
        start: {
          line: 48,
          column: 8
        },
        end: {
          line: 48,
          column: 23
        }
      },
      "19": {
        start: {
          line: 49,
          column: 8
        },
        end: {
          line: 49,
          column: 37
        }
      },
      "20": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 50,
          column: 39
        }
      },
      "21": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 51,
          column: 30
        }
      },
      "22": {
        start: {
          line: 54,
          column: 0
        },
        end: {
          line: 54,
          column: 40
        }
      },
      "23": {
        start: {
          line: 60,
          column: 8
        },
        end: {
          line: 60,
          column: 30
        }
      },
      "24": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 61,
          column: 36
        }
      },
      "25": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 62,
          column: 30
        }
      },
      "26": {
        start: {
          line: 65,
          column: 0
        },
        end: {
          line: 65,
          column: 36
        }
      },
      "27": {
        start: {
          line: 71,
          column: 8
        },
        end: {
          line: 71,
          column: 23
        }
      },
      "28": {
        start: {
          line: 72,
          column: 8
        },
        end: {
          line: 72,
          column: 31
        }
      },
      "29": {
        start: {
          line: 73,
          column: 8
        },
        end: {
          line: 73,
          column: 38
        }
      },
      "30": {
        start: {
          line: 74,
          column: 8
        },
        end: {
          line: 74,
          column: 30
        }
      },
      "31": {
        start: {
          line: 77,
          column: 0
        },
        end: {
          line: 77,
          column: 40
        }
      },
      "32": {
        start: {
          line: 83,
          column: 8
        },
        end: {
          line: 83,
          column: 30
        }
      },
      "33": {
        start: {
          line: 84,
          column: 8
        },
        end: {
          line: 84,
          column: 23
        }
      },
      "34": {
        start: {
          line: 85,
          column: 8
        },
        end: {
          line: 85,
          column: 49
        }
      },
      "35": {
        start: {
          line: 86,
          column: 8
        },
        end: {
          line: 86,
          column: 30
        }
      },
      "36": {
        start: {
          line: 89,
          column: 0
        },
        end: {
          line: 89,
          column: 60
        }
      },
      "37": {
        start: {
          line: 95,
          column: 8
        },
        end: {
          line: 95,
          column: 30
        }
      },
      "38": {
        start: {
          line: 96,
          column: 8
        },
        end: {
          line: 96,
          column: 34
        }
      },
      "39": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 97,
          column: 30
        }
      },
      "40": {
        start: {
          line: 100,
          column: 0
        },
        end: {
          line: 100,
          column: 32
        }
      },
      "41": {
        start: {
          line: 106,
          column: 8
        },
        end: {
          line: 106,
          column: 23
        }
      },
      "42": {
        start: {
          line: 107,
          column: 8
        },
        end: {
          line: 107,
          column: 35
        }
      },
      "43": {
        start: {
          line: 108,
          column: 8
        },
        end: {
          line: 108,
          column: 42
        }
      },
      "44": {
        start: {
          line: 109,
          column: 8
        },
        end: {
          line: 109,
          column: 30
        }
      },
      "45": {
        start: {
          line: 112,
          column: 0
        },
        end: {
          line: 112,
          column: 48
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 8,
            column: 4
          },
          end: {
            line: 8,
            column: 5
          }
        },
        loc: {
          start: {
            line: 8,
            column: 32
          },
          end: {
            line: 16,
            column: 5
          }
        },
        line: 8
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 23,
            column: 4
          },
          end: {
            line: 23,
            column: 5
          }
        },
        loc: {
          start: {
            line: 23,
            column: 32
          },
          end: {
            line: 28,
            column: 5
          }
        },
        line: 23
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 35,
            column: 4
          },
          end: {
            line: 35,
            column: 5
          }
        },
        loc: {
          start: {
            line: 35,
            column: 59
          },
          end: {
            line: 40,
            column: 5
          }
        },
        line: 35
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 47,
            column: 4
          },
          end: {
            line: 47,
            column: 5
          }
        },
        loc: {
          start: {
            line: 47,
            column: 61
          },
          end: {
            line: 52,
            column: 5
          }
        },
        line: 47
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 59,
            column: 4
          },
          end: {
            line: 59,
            column: 5
          }
        },
        loc: {
          start: {
            line: 59,
            column: 32
          },
          end: {
            line: 63,
            column: 5
          }
        },
        line: 59
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 70,
            column: 4
          },
          end: {
            line: 70,
            column: 5
          }
        },
        loc: {
          start: {
            line: 70,
            column: 34
          },
          end: {
            line: 75,
            column: 5
          }
        },
        line: 70
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 82,
            column: 4
          },
          end: {
            line: 82,
            column: 5
          }
        },
        loc: {
          start: {
            line: 82,
            column: 37
          },
          end: {
            line: 87,
            column: 5
          }
        },
        line: 82
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 94,
            column: 4
          },
          end: {
            line: 94,
            column: 5
          }
        },
        loc: {
          start: {
            line: 94,
            column: 32
          },
          end: {
            line: 98,
            column: 5
          }
        },
        line: 94
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 105,
            column: 4
          },
          end: {
            line: 105,
            column: 5
          }
        },
        loc: {
          start: {
            line: 105,
            column: 36
          },
          end: {
            line: 110,
            column: 5
          }
        },
        line: 105
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 13,
            column: 8
          },
          end: {
            line: 15,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 13,
            column: 8
          },
          end: {
            line: 15,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 13
      },
      "1": {
        loc: {
          start: {
            line: 35,
            column: 25
          },
          end: {
            line: 35,
            column: 41
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 35,
            column: 38
          },
          end: {
            line: 35,
            column: 41
          }
        }],
        line: 35
      },
      "2": {
        loc: {
          start: {
            line: 47,
            column: 16
          },
          end: {
            line: 47,
            column: 47
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 47,
            column: 26
          },
          end: {
            line: 47,
            column: 47
          }
        }],
        line: 47
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    b: {
      "0": [0, 0],
      "1": [0],
      "2": [0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/WebstormProjects/goo/src/models/Errors.ts",
      mappings: ";;;AAAA;;GAEG;AACH,MAAsB,QAAS,SAAQ,KAAK;IAI1C,YAAY,OAAe,EAAkB,KAAa;QACxD,KAAK,CAAC,OAAO,CAAC,CAAC;QAD4B,UAAK,GAAL,KAAK,CAAQ;QAExD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAElC,8DAA8D;QAC9D,IAAI,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC5B,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;CACF;AAbD,4BAaC;AAED;;GAEG;AACH,MAAa,eAAgB,SAAQ,QAAQ;IAI3C,YAAY,OAAe,EAAkB,KAAc;QACzD,KAAK,CAAC,OAAO,CAAC,CAAC;QAD4B,UAAK,GAAL,KAAK,CAAS;QAHlD,SAAI,GAAG,kBAAkB,CAAC;QAC1B,eAAU,GAAG,GAAG,CAAC;IAI1B,CAAC;CACF;AAPD,0CAOC;AAED;;GAEG;AACH,MAAa,QAAS,SAAQ,QAAQ;IAIpC,YACE,OAAe,EACf,aAAqB,GAAG,EACR,OAAgB,EAChC,KAAa;QAEb,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAHN,YAAO,GAAP,OAAO,CAAS;QANzB,SAAI,GAAG,WAAW,CAAC;QAU1B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;CACF;AAbD,4BAaC;AAED;;GAEG;AACH,MAAa,cAAe,SAAQ,QAAQ;IAI1C,YACE,UAAkB,qBAAqB,EACvB,UAAmB;QAEnC,KAAK,CAAC,OAAO,CAAC,CAAC;QAFC,eAAU,GAAV,UAAU,CAAS;QAL5B,SAAI,GAAG,kBAAkB,CAAC;QAC1B,eAAU,GAAG,GAAG,CAAC;IAO1B,CAAC;CACF;AAVD,wCAUC;AAED;;GAEG;AACH,MAAa,YAAa,SAAQ,QAAQ;IAIxC,YAAY,OAAe,EAAE,KAAa;QACxC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAJf,SAAI,GAAG,eAAe,CAAC;QACvB,eAAU,GAAG,GAAG,CAAC;IAI1B,CAAC;CACF;AAPD,oCAOC;AAED;;GAEG;AACH,MAAa,cAAe,SAAQ,QAAQ;IAI1C,YAAY,OAAe,EAAkB,OAAgB;QAC3D,KAAK,CAAC,OAAO,CAAC,CAAC;QAD4B,YAAO,GAAP,OAAO,CAAS;QAHpD,SAAI,GAAG,iBAAiB,CAAC;QACzB,eAAU,GAAG,GAAG,CAAC;IAI1B,CAAC;CACF;AAPD,wCAOC;AAED;;GAEG;AACH,MAAa,wBAAyB,SAAQ,QAAQ;IAIpD,YAAY,OAAe,EAAkB,GAAY,EAAE,KAAa;QACtE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QADqB,QAAG,GAAH,GAAG,CAAS;QAHhD,SAAI,GAAG,4BAA4B,CAAC;QACpC,eAAU,GAAG,GAAG,CAAC;IAI1B,CAAC;CACF;AAPD,4DAOC;AAED;;GAEG;AACH,MAAa,UAAW,SAAQ,QAAQ;IAItC,YAAY,OAAe,EAAE,KAAa;QACxC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAJf,SAAI,GAAG,aAAa,CAAC;QACrB,eAAU,GAAG,GAAG,CAAC;IAI1B,CAAC;CACF;AAPD,gCAOC;AAED;;GAEG;AACH,MAAa,kBAAmB,SAAQ,QAAQ;IAI9C,YAAY,OAAe,EAAkB,SAAkB;QAC7D,KAAK,CAAC,OAAO,CAAC,CAAC;QAD4B,cAAS,GAAT,SAAS,CAAS;QAHtD,SAAI,GAAG,qBAAqB,CAAC;QAC7B,eAAU,GAAG,GAAG,CAAC;IAI1B,CAAC;CACF;AAPD,gDAOC",
      names: [],
      sources: ["/Users/<USER>/WebstormProjects/goo/src/models/Errors.ts"],
      sourcesContent: ["/**\n * Base error class for all application errors\n */\nexport abstract class AppError extends Error {\n  abstract readonly code: string;\n  abstract readonly statusCode: number;\n  \n  constructor(message: string, public readonly cause?: Error) {\n    super(message);\n    this.name = this.constructor.name;\n    \n    // Maintains proper stack trace for where our error was thrown\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n  }\n}\n\n/**\n * Error thrown when validation fails\n */\nexport class ValidationError extends AppError {\n  readonly code = 'VALIDATION_ERROR';\n  readonly statusCode = 400;\n  \n  constructor(message: string, public readonly field?: string) {\n    super(message);\n  }\n}\n\n/**\n * Error thrown when API requests fail\n */\nexport class ApiError extends AppError {\n  readonly code = 'API_ERROR';\n  readonly statusCode: number;\n  \n  constructor(\n    message: string,\n    statusCode: number = 500,\n    public readonly apiName?: string,\n    cause?: Error\n  ) {\n    super(message, cause);\n    this.statusCode = statusCode;\n  }\n}\n\n/**\n * Error thrown when rate limits are exceeded\n */\nexport class RateLimitError extends AppError {\n  readonly code = 'RATE_LIMIT_ERROR';\n  readonly statusCode = 429;\n  \n  constructor(\n    message: string = 'Rate limit exceeded',\n    public readonly retryAfter?: number\n  ) {\n    super(message);\n  }\n}\n\n/**\n * Error thrown when network requests fail\n */\nexport class NetworkError extends AppError {\n  readonly code = 'NETWORK_ERROR';\n  readonly statusCode = 503;\n  \n  constructor(message: string, cause?: Error) {\n    super(message, cause);\n  }\n}\n\n/**\n * Error thrown when geocoding fails\n */\nexport class GeocodingError extends AppError {\n  readonly code = 'GEOCODING_ERROR';\n  readonly statusCode = 400;\n  \n  constructor(message: string, public readonly zipCode?: string) {\n    super(message);\n  }\n}\n\n/**\n * Error thrown when website verification fails\n */\nexport class WebsiteVerificationError extends AppError {\n  readonly code = 'WEBSITE_VERIFICATION_ERROR';\n  readonly statusCode = 500;\n  \n  constructor(message: string, public readonly url?: string, cause?: Error) {\n    super(message, cause);\n  }\n}\n\n/**\n * Error thrown when cache operations fail\n */\nexport class CacheError extends AppError {\n  readonly code = 'CACHE_ERROR';\n  readonly statusCode = 500;\n  \n  constructor(message: string, cause?: Error) {\n    super(message, cause);\n  }\n}\n\n/**\n * Error thrown when configuration is invalid\n */\nexport class ConfigurationError extends AppError {\n  readonly code = 'CONFIGURATION_ERROR';\n  readonly statusCode = 500;\n  \n  constructor(message: string, public readonly configKey?: string) {\n    super(message);\n  }\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "7069720d4265bff959a97644facacda513376b43"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1gv0vrtrp5 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1gv0vrtrp5();
cov_1gv0vrtrp5().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1gv0vrtrp5().s[1]++;
exports.ConfigurationError = exports.CacheError = exports.WebsiteVerificationError = exports.GeocodingError = exports.NetworkError = exports.RateLimitError = exports.ApiError = exports.ValidationError = exports.AppError = void 0;
/**
 * Base error class for all application errors
 */
class AppError extends Error {
  constructor(message, cause) {
    /* istanbul ignore next */
    cov_1gv0vrtrp5().f[0]++;
    cov_1gv0vrtrp5().s[2]++;
    super(message);
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[3]++;
    this.cause = cause;
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[4]++;
    this.name = this.constructor.name;
    // Maintains proper stack trace for where our error was thrown
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[5]++;
    if (Error.captureStackTrace) {
      /* istanbul ignore next */
      cov_1gv0vrtrp5().b[0][0]++;
      cov_1gv0vrtrp5().s[6]++;
      Error.captureStackTrace(this, this.constructor);
    } else
    /* istanbul ignore next */
    {
      cov_1gv0vrtrp5().b[0][1]++;
    }
  }
}
/* istanbul ignore next */
cov_1gv0vrtrp5().s[7]++;
exports.AppError = AppError;
/**
 * Error thrown when validation fails
 */
class ValidationError extends AppError {
  constructor(message, field) {
    /* istanbul ignore next */
    cov_1gv0vrtrp5().f[1]++;
    cov_1gv0vrtrp5().s[8]++;
    super(message);
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[9]++;
    this.field = field;
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[10]++;
    this.code = 'VALIDATION_ERROR';
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[11]++;
    this.statusCode = 400;
  }
}
/* istanbul ignore next */
cov_1gv0vrtrp5().s[12]++;
exports.ValidationError = ValidationError;
/**
 * Error thrown when API requests fail
 */
class ApiError extends AppError {
  constructor(message, statusCode =
  /* istanbul ignore next */
  (cov_1gv0vrtrp5().b[1][0]++, 500), apiName, cause) {
    /* istanbul ignore next */
    cov_1gv0vrtrp5().f[2]++;
    cov_1gv0vrtrp5().s[13]++;
    super(message, cause);
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[14]++;
    this.apiName = apiName;
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[15]++;
    this.code = 'API_ERROR';
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[16]++;
    this.statusCode = statusCode;
  }
}
/* istanbul ignore next */
cov_1gv0vrtrp5().s[17]++;
exports.ApiError = ApiError;
/**
 * Error thrown when rate limits are exceeded
 */
class RateLimitError extends AppError {
  constructor(message =
  /* istanbul ignore next */
  (cov_1gv0vrtrp5().b[2][0]++, 'Rate limit exceeded'), retryAfter) {
    /* istanbul ignore next */
    cov_1gv0vrtrp5().f[3]++;
    cov_1gv0vrtrp5().s[18]++;
    super(message);
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[19]++;
    this.retryAfter = retryAfter;
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[20]++;
    this.code = 'RATE_LIMIT_ERROR';
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[21]++;
    this.statusCode = 429;
  }
}
/* istanbul ignore next */
cov_1gv0vrtrp5().s[22]++;
exports.RateLimitError = RateLimitError;
/**
 * Error thrown when network requests fail
 */
class NetworkError extends AppError {
  constructor(message, cause) {
    /* istanbul ignore next */
    cov_1gv0vrtrp5().f[4]++;
    cov_1gv0vrtrp5().s[23]++;
    super(message, cause);
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[24]++;
    this.code = 'NETWORK_ERROR';
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[25]++;
    this.statusCode = 503;
  }
}
/* istanbul ignore next */
cov_1gv0vrtrp5().s[26]++;
exports.NetworkError = NetworkError;
/**
 * Error thrown when geocoding fails
 */
class GeocodingError extends AppError {
  constructor(message, zipCode) {
    /* istanbul ignore next */
    cov_1gv0vrtrp5().f[5]++;
    cov_1gv0vrtrp5().s[27]++;
    super(message);
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[28]++;
    this.zipCode = zipCode;
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[29]++;
    this.code = 'GEOCODING_ERROR';
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[30]++;
    this.statusCode = 400;
  }
}
/* istanbul ignore next */
cov_1gv0vrtrp5().s[31]++;
exports.GeocodingError = GeocodingError;
/**
 * Error thrown when website verification fails
 */
class WebsiteVerificationError extends AppError {
  constructor(message, url, cause) {
    /* istanbul ignore next */
    cov_1gv0vrtrp5().f[6]++;
    cov_1gv0vrtrp5().s[32]++;
    super(message, cause);
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[33]++;
    this.url = url;
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[34]++;
    this.code = 'WEBSITE_VERIFICATION_ERROR';
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[35]++;
    this.statusCode = 500;
  }
}
/* istanbul ignore next */
cov_1gv0vrtrp5().s[36]++;
exports.WebsiteVerificationError = WebsiteVerificationError;
/**
 * Error thrown when cache operations fail
 */
class CacheError extends AppError {
  constructor(message, cause) {
    /* istanbul ignore next */
    cov_1gv0vrtrp5().f[7]++;
    cov_1gv0vrtrp5().s[37]++;
    super(message, cause);
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[38]++;
    this.code = 'CACHE_ERROR';
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[39]++;
    this.statusCode = 500;
  }
}
/* istanbul ignore next */
cov_1gv0vrtrp5().s[40]++;
exports.CacheError = CacheError;
/**
 * Error thrown when configuration is invalid
 */
class ConfigurationError extends AppError {
  constructor(message, configKey) {
    /* istanbul ignore next */
    cov_1gv0vrtrp5().f[8]++;
    cov_1gv0vrtrp5().s[41]++;
    super(message);
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[42]++;
    this.configKey = configKey;
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[43]++;
    this.code = 'CONFIGURATION_ERROR';
    /* istanbul ignore next */
    cov_1gv0vrtrp5().s[44]++;
    this.statusCode = 500;
  }
}
/* istanbul ignore next */
cov_1gv0vrtrp5().s[45]++;
exports.ConfigurationError = ConfigurationError;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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