{"version": 3, "names": ["cov_1gv0vrtrp5", "actualCoverage", "s", "AppError", "Error", "constructor", "message", "cause", "f", "name", "captureStackTrace", "b", "exports", "ValidationError", "field", "code", "statusCode", "ApiError", "apiName", "RateLimitError", "retryAfter", "NetworkError", "GeocodingError", "zipCode", "WebsiteVerificationError", "url", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ConfigurationError", "config<PERSON><PERSON>"], "sources": ["/Users/<USER>/WebstormProjects/goo/src/models/Errors.ts"], "sourcesContent": ["/**\n * Base error class for all application errors\n */\nexport abstract class AppError extends Error {\n  abstract readonly code: string;\n  abstract readonly statusCode: number;\n  \n  constructor(message: string, public readonly cause?: Error) {\n    super(message);\n    this.name = this.constructor.name;\n    \n    // Maintains proper stack trace for where our error was thrown\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n  }\n}\n\n/**\n * Error thrown when validation fails\n */\nexport class ValidationError extends AppError {\n  readonly code = 'VALIDATION_ERROR';\n  readonly statusCode = 400;\n  \n  constructor(message: string, public readonly field?: string) {\n    super(message);\n  }\n}\n\n/**\n * Error thrown when API requests fail\n */\nexport class ApiError extends AppError {\n  readonly code = 'API_ERROR';\n  readonly statusCode: number;\n  \n  constructor(\n    message: string,\n    statusCode: number = 500,\n    public readonly apiName?: string,\n    cause?: Error\n  ) {\n    super(message, cause);\n    this.statusCode = statusCode;\n  }\n}\n\n/**\n * Error thrown when rate limits are exceeded\n */\nexport class RateLimitError extends AppError {\n  readonly code = 'RATE_LIMIT_ERROR';\n  readonly statusCode = 429;\n  \n  constructor(\n    message: string = 'Rate limit exceeded',\n    public readonly retryAfter?: number\n  ) {\n    super(message);\n  }\n}\n\n/**\n * Error thrown when network requests fail\n */\nexport class NetworkError extends AppError {\n  readonly code = 'NETWORK_ERROR';\n  readonly statusCode = 503;\n  \n  constructor(message: string, cause?: Error) {\n    super(message, cause);\n  }\n}\n\n/**\n * Error thrown when geocoding fails\n */\nexport class GeocodingError extends AppError {\n  readonly code = 'GEOCODING_ERROR';\n  readonly statusCode = 400;\n  \n  constructor(message: string, public readonly zipCode?: string) {\n    super(message);\n  }\n}\n\n/**\n * Error thrown when website verification fails\n */\nexport class WebsiteVerificationError extends AppError {\n  readonly code = 'WEBSITE_VERIFICATION_ERROR';\n  readonly statusCode = 500;\n  \n  constructor(message: string, public readonly url?: string, cause?: Error) {\n    super(message, cause);\n  }\n}\n\n/**\n * Error thrown when cache operations fail\n */\nexport class CacheError extends AppError {\n  readonly code = 'CACHE_ERROR';\n  readonly statusCode = 500;\n  \n  constructor(message: string, cause?: Error) {\n    super(message, cause);\n  }\n}\n\n/**\n * Error thrown when configuration is invalid\n */\nexport class ConfigurationError extends AppError {\n  readonly code = 'CONFIGURATION_ERROR';\n  readonly statusCode = 500;\n  \n  constructor(message: string, public readonly configKey?: string) {\n    super(message);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAeG;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;;AAfH;;;AAGA,MAAsBC,QAAS,SAAQC,KAAK;EAI1CC,YAAYC,OAAe,EAAkBC,KAAa;IAAA;IAAAP,cAAA,GAAAQ,CAAA;IAAAR,cAAA,GAAAE,CAAA;IACxD,KAAK,CAACI,OAAO,CAAC;IAAC;IAAAN,cAAA,GAAAE,CAAA;IAD4B,KAAAK,KAAK,GAALA,KAAK;IAAQ;IAAAP,cAAA,GAAAE,CAAA;IAExD,IAAI,CAACO,IAAI,GAAG,IAAI,CAACJ,WAAW,CAACI,IAAI;IAEjC;IAAA;IAAAT,cAAA,GAAAE,CAAA;IACA,IAAIE,KAAK,CAACM,iBAAiB,EAAE;MAAA;MAAAV,cAAA,GAAAW,CAAA;MAAAX,cAAA,GAAAE,CAAA;MAC3BE,KAAK,CAACM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAACL,WAAW,CAAC;IACjD,CAAC;IAAA;IAAA;MAAAL,cAAA,GAAAW,CAAA;IAAA;EACH;;AACD;AAAAX,cAAA,GAAAE,CAAA;AAbDU,OAAA,CAAAT,QAAA,GAAAA,QAAA;AAeA;;;AAGA,MAAaU,eAAgB,SAAQV,QAAQ;EAI3CE,YAAYC,OAAe,EAAkBQ,KAAc;IAAA;IAAAd,cAAA,GAAAQ,CAAA;IAAAR,cAAA,GAAAE,CAAA;IACzD,KAAK,CAACI,OAAO,CAAC;IAAC;IAAAN,cAAA,GAAAE,CAAA;IAD4B,KAAAY,KAAK,GAALA,KAAK;IAAS;IAAAd,cAAA,GAAAE,CAAA;IAHlD,KAAAa,IAAI,GAAG,kBAAkB;IAAC;IAAAf,cAAA,GAAAE,CAAA;IAC1B,KAAAc,UAAU,GAAG,GAAG;EAIzB;;AACD;AAAAhB,cAAA,GAAAE,CAAA;AAPDU,OAAA,CAAAC,eAAA,GAAAA,eAAA;AASA;;;AAGA,MAAaI,QAAS,SAAQd,QAAQ;EAIpCE,YACEC,OAAe,EACfU,UAAA;EAAA;EAAA,CAAAhB,cAAA,GAAAW,CAAA,UAAqB,GAAG,GACRO,OAAgB,EAChCX,KAAa;IAAA;IAAAP,cAAA,GAAAQ,CAAA;IAAAR,cAAA,GAAAE,CAAA;IAEb,KAAK,CAACI,OAAO,EAAEC,KAAK,CAAC;IAAC;IAAAP,cAAA,GAAAE,CAAA;IAHN,KAAAgB,OAAO,GAAPA,OAAO;IAAS;IAAAlB,cAAA,GAAAE,CAAA;IANzB,KAAAa,IAAI,GAAG,WAAW;IAAC;IAAAf,cAAA,GAAAE,CAAA;IAU1B,IAAI,CAACc,UAAU,GAAGA,UAAU;EAC9B;;AACD;AAAAhB,cAAA,GAAAE,CAAA;AAbDU,OAAA,CAAAK,QAAA,GAAAA,QAAA;AAeA;;;AAGA,MAAaE,cAAe,SAAQhB,QAAQ;EAI1CE,YACEC,OAAA;EAAA;EAAA,CAAAN,cAAA,GAAAW,CAAA,UAAkB,qBAAqB,GACvBS,UAAmB;IAAA;IAAApB,cAAA,GAAAQ,CAAA;IAAAR,cAAA,GAAAE,CAAA;IAEnC,KAAK,CAACI,OAAO,CAAC;IAAC;IAAAN,cAAA,GAAAE,CAAA;IAFC,KAAAkB,UAAU,GAAVA,UAAU;IAAS;IAAApB,cAAA,GAAAE,CAAA;IAL5B,KAAAa,IAAI,GAAG,kBAAkB;IAAC;IAAAf,cAAA,GAAAE,CAAA;IAC1B,KAAAc,UAAU,GAAG,GAAG;EAOzB;;AACD;AAAAhB,cAAA,GAAAE,CAAA;AAVDU,OAAA,CAAAO,cAAA,GAAAA,cAAA;AAYA;;;AAGA,MAAaE,YAAa,SAAQlB,QAAQ;EAIxCE,YAAYC,OAAe,EAAEC,KAAa;IAAA;IAAAP,cAAA,GAAAQ,CAAA;IAAAR,cAAA,GAAAE,CAAA;IACxC,KAAK,CAACI,OAAO,EAAEC,KAAK,CAAC;IAAC;IAAAP,cAAA,GAAAE,CAAA;IAJf,KAAAa,IAAI,GAAG,eAAe;IAAC;IAAAf,cAAA,GAAAE,CAAA;IACvB,KAAAc,UAAU,GAAG,GAAG;EAIzB;;AACD;AAAAhB,cAAA,GAAAE,CAAA;AAPDU,OAAA,CAAAS,YAAA,GAAAA,YAAA;AASA;;;AAGA,MAAaC,cAAe,SAAQnB,QAAQ;EAI1CE,YAAYC,OAAe,EAAkBiB,OAAgB;IAAA;IAAAvB,cAAA,GAAAQ,CAAA;IAAAR,cAAA,GAAAE,CAAA;IAC3D,KAAK,CAACI,OAAO,CAAC;IAAC;IAAAN,cAAA,GAAAE,CAAA;IAD4B,KAAAqB,OAAO,GAAPA,OAAO;IAAS;IAAAvB,cAAA,GAAAE,CAAA;IAHpD,KAAAa,IAAI,GAAG,iBAAiB;IAAC;IAAAf,cAAA,GAAAE,CAAA;IACzB,KAAAc,UAAU,GAAG,GAAG;EAIzB;;AACD;AAAAhB,cAAA,GAAAE,CAAA;AAPDU,OAAA,CAAAU,cAAA,GAAAA,cAAA;AASA;;;AAGA,MAAaE,wBAAyB,SAAQrB,QAAQ;EAIpDE,YAAYC,OAAe,EAAkBmB,GAAY,EAAElB,KAAa;IAAA;IAAAP,cAAA,GAAAQ,CAAA;IAAAR,cAAA,GAAAE,CAAA;IACtE,KAAK,CAACI,OAAO,EAAEC,KAAK,CAAC;IAAC;IAAAP,cAAA,GAAAE,CAAA;IADqB,KAAAuB,GAAG,GAAHA,GAAG;IAAS;IAAAzB,cAAA,GAAAE,CAAA;IAHhD,KAAAa,IAAI,GAAG,4BAA4B;IAAC;IAAAf,cAAA,GAAAE,CAAA;IACpC,KAAAc,UAAU,GAAG,GAAG;EAIzB;;AACD;AAAAhB,cAAA,GAAAE,CAAA;AAPDU,OAAA,CAAAY,wBAAA,GAAAA,wBAAA;AASA;;;AAGA,MAAaE,UAAW,SAAQvB,QAAQ;EAItCE,YAAYC,OAAe,EAAEC,KAAa;IAAA;IAAAP,cAAA,GAAAQ,CAAA;IAAAR,cAAA,GAAAE,CAAA;IACxC,KAAK,CAACI,OAAO,EAAEC,KAAK,CAAC;IAAC;IAAAP,cAAA,GAAAE,CAAA;IAJf,KAAAa,IAAI,GAAG,aAAa;IAAC;IAAAf,cAAA,GAAAE,CAAA;IACrB,KAAAc,UAAU,GAAG,GAAG;EAIzB;;AACD;AAAAhB,cAAA,GAAAE,CAAA;AAPDU,OAAA,CAAAc,UAAA,GAAAA,UAAA;AASA;;;AAGA,MAAaC,kBAAmB,SAAQxB,QAAQ;EAI9CE,YAAYC,OAAe,EAAkBsB,SAAkB;IAAA;IAAA5B,cAAA,GAAAQ,CAAA;IAAAR,cAAA,GAAAE,CAAA;IAC7D,KAAK,CAACI,OAAO,CAAC;IAAC;IAAAN,cAAA,GAAAE,CAAA;IAD4B,KAAA0B,SAAS,GAATA,SAAS;IAAS;IAAA5B,cAAA,GAAAE,CAAA;IAHtD,KAAAa,IAAI,GAAG,qBAAqB;IAAC;IAAAf,cAAA,GAAAE,CAAA;IAC7B,KAAAc,UAAU,GAAG,GAAG;EAIzB;;AACD;AAAAhB,cAAA,GAAAE,CAAA;AAPDU,OAAA,CAAAe,kBAAA,GAAAA,kBAAA", "ignoreList": []}