{"file": "/Users/<USER>/WebstormProjects/goo/tests/services/googlePlaces.test.ts", "mappings": ";;AAAA,kEAAsE;AACtE,oDAAiF;AAGjF,sBAAsB;AACtB,MAAM,SAAS,GAAG,MAAM,CAAC,KAA0C,CAAC;AAEpE,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;IACnC,IAAI,aAAkC,CAAC;IAEvC,UAAU,CAAC,GAAG,EAAE;QACd,aAAa,GAAG,IAAI,kCAAmB,EAAE,CAAC;QAC1C,SAAS,CAAC,SAAS,EAAE,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,MAAM,eAAe,GAAgB;YACnC,QAAQ,EAAE,OAAO;YACjB,SAAS,EAAE,CAAC,OAAO;SACpB,CAAC;QAEF,MAAM,eAAe,GAAG;YACtB,OAAO,EAAE;gBACP;oBACE,QAAQ,EAAE,6BAA6B;oBACvC,IAAI,EAAE,iBAAiB;oBACvB,iBAAiB,EAAE,sCAAsC;oBACzD,sBAAsB,EAAE,iBAAiB;oBACzC,OAAO,EAAE,4BAA4B;oBACrC,KAAK,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,eAAe,CAAC;oBAC9C,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE;wBACR,QAAQ,EAAE;4BACR,GAAG,EAAE,OAAO;4BACZ,GAAG,EAAE,CAAC,OAAO;yBACd;qBACF;iBACF;gBACD;oBACE,QAAQ,EAAE,6BAA6B;oBACvC,IAAI,EAAE,oBAAoB;oBAC1B,iBAAiB,EAAE,sCAAsC;oBACzD,KAAK,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC;oBAC7B,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE;wBACR,QAAQ,EAAE;4BACR,GAAG,EAAE,OAAO;4BACZ,GAAG,EAAE,CAAC,OAAO;yBACd;qBACF;iBACF;aACF;YACD,MAAM,EAAE,IAAI;YACZ,eAAe,EAAE,gBAAgB;SAClC,CAAC;QAEF,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,eAAe;aACtB,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,YAAY,CAC7C,eAAe,EACf,IAAI,EACJ,YAAY,CACb,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YACpE,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEpC,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CACpC,MAAM,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,EAC5C,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,KAAK;aACd,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;YACxC,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,aAAa;aACd,CAAC,CAAC;YAEf,MAAM,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;iBAC1E,OAAO,CAAC,OAAO,CAAC,iBAAQ,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,SAAS,CAAC,qBAAqB,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;YAE5D,MAAM,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;iBAC1E,OAAO,CAAC,OAAO,CAAC,qBAAY,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,OAAO,EAAE,EAAE;oBACX,MAAM,EAAE,cAAc;iBACvB,CAAC;aACS,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;YAErF,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,aAAa,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,mBAAmB;aACpB,CAAC,CAAC;YAEf,MAAM,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;iBAC1E,OAAO,CAAC,OAAO,CAAC,iBAAQ,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;iBACvE,OAAO,CAAC,OAAO,EAAE,CAAC;YAErB,MAAM,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;iBAChE,OAAO,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,eAAe;aACtB,CAAC,CAAC;YAEf,MAAM,aAAa,CAAC,YAAY,CAC9B,eAAe,EACf,IAAI,EACJ,YAAY,EACZ;gBACE,QAAQ,EAAE,CAAC;gBACX,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,IAAI;aACd,CACF,CAAC;YAEF,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CACpC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,EACrC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;YACF,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CACpC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,EACrC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;YACF,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CACpC,MAAM,CAAC,gBAAgB,CAAC,cAAc,CAAC,EACvC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,MAAM,WAAW,GAAG,6BAA6B,CAAC;QAClD,MAAM,mBAAmB,GAAG;YAC1B,MAAM,EAAE;gBACN,QAAQ,EAAE,WAAW;gBACrB,IAAI,EAAE,iBAAiB;gBACvB,iBAAiB,EAAE,sCAAsC;gBACzD,sBAAsB,EAAE,iBAAiB;gBACzC,OAAO,EAAE,4BAA4B;gBACrC,KAAK,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,eAAe,CAAC;gBAC9C,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACR,QAAQ,EAAE;wBACR,GAAG,EAAE,OAAO;wBACZ,GAAG,EAAE,CAAC,OAAO;qBACd;iBACF;gBACD,aAAa,EAAE;oBACb,QAAQ,EAAE,IAAI;oBACd,YAAY,EAAE;wBACZ,4BAA4B;wBAC5B,6BAA6B;qBAC9B;iBACF;aACF;YACD,MAAM,EAAE,IAAI;SACb,CAAC;QAEF,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,mBAAmB;aAC1B,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAEhE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAE1D,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CACpC,MAAM,CAAC,gBAAgB,CAAC,cAAc,CAAC,EACvC,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,KAAK;aACd,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,MAAM,EAAE,WAAW;iBACpB,CAAC;aACS,CAAC,CAAC;YAEf,MAAM,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;iBACrD,OAAO,CAAC,OAAO,CAAC,iBAAQ,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,mBAAmB;aAC1B,CAAC,CAAC;YAEf,aAAa;YACb,MAAM,OAAO,GAAG,MAAM,aAAa,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAEjE,+BAA+B;YAC/B,MAAM,OAAO,GAAG,MAAM,aAAa,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAEjE,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACjC,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,MAAM,eAAe,GAAgB;YACnC,QAAQ,EAAE,OAAO;YACjB,SAAS,EAAE,CAAC,OAAO;SACpB,CAAC;QAEF,MAAM,gBAAgB,GAAG;YACvB,OAAO,EAAE;gBACP;oBACE,QAAQ,EAAE,6BAA6B;oBACvC,IAAI,EAAE,cAAc;oBACpB,iBAAiB,EAAE,uCAAuC;oBAC1D,KAAK,EAAE,CAAC,YAAY,EAAE,eAAe,CAAC;oBACtC,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE;wBACR,QAAQ,EAAE;4BACR,GAAG,EAAE,OAAO;4BACZ,GAAG,EAAE,CAAC,OAAO;yBACd;qBACF;iBACF;aACF;YACD,MAAM,EAAE,IAAI;SACb,CAAC;QAEF,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,gBAAgB;aACvB,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,YAAY,CAC7C,+BAA+B,EAC/B,eAAe,EACf,IAAI,CACL,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAEnD,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CACpC,MAAM,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,EAC1C,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,KAAK;aACd,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;YAC1C,MAAM,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;iBAChE,OAAO,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,gBAAgB,GAAG;gBACvB,OAAO,EAAE;oBACP;wBACE,QAAQ,EAAE,6BAA6B;wBACvC,IAAI,EAAE,sBAAsB;wBAC5B,iBAAiB,EAAE,sCAAsC;wBACzD,KAAK,EAAE,CAAC,YAAY,CAAC;wBACrB,QAAQ,EAAE;4BACR,QAAQ,EAAE;gCACR,GAAG,EAAE,OAAO;gCACZ,GAAG,EAAE,CAAC,OAAO;6BACd;yBACF;qBACF;iBACF;gBACD,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,2BAA2B;YAC3B,SAAS,CAAC,SAAS,EAAE,CAAC;YACtB,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,gBAAgB;aACvB,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;YAEjE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAE3D,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CACpC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,EACnD,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,SAAS,CAAC,SAAS,EAAE,CAAC;YACtB,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,MAAM,EAAE,iBAAiB;iBAC1B,CAAC;aACS,CAAC,CAAC;YAEf,MAAM,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;iBACrD,OAAO,CAAC,OAAO,CAAC,iBAAQ,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,+BAA+B;YAC/B,SAAS,CAAC,SAAS,EAAE,CAAC;YACtB,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,MAAM,EAAE;wBACN,QAAQ,EAAE,MAAM;wBAChB,IAAI,EAAE,YAAY;wBAClB,iBAAiB,EAAE,cAAc;wBACjC,QAAQ,EAAE;4BACR,QAAQ,EAAE;gCACR,GAAG,EAAE,OAAO;gCACZ,GAAG,EAAE,CAAC,OAAO;6BACd;yBACF;qBACF;oBACD,MAAM,EAAE,IAAI;iBACb,CAAC;aACS,CAAC,CAAC;YAEf,MAAM,aAAa,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAE3C,cAAc;YACd,aAAa,CAAC,UAAU,EAAE,CAAC;YAE3B,6BAA6B;YAC7B,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,MAAM,EAAE;wBACN,QAAQ,EAAE,MAAM;wBAChB,IAAI,EAAE,YAAY;wBAClB,iBAAiB,EAAE,cAAc;wBACjC,QAAQ,EAAE;4BACR,QAAQ,EAAE;gCACR,GAAG,EAAE,OAAO;gCACZ,GAAG,EAAE,CAAC,OAAO;6BACd;yBACF;qBACF;oBACD,MAAM,EAAE,IAAI;iBACb,CAAC;aACS,CAAC,CAAC;YAEf,MAAM,aAAa,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;YACtD,OAAO,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;YAEzC,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,kCAAmB,EAAE,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;YAErF,kBAAkB;YAClB,IAAI,WAAW,EAAE,CAAC;gBAChB,OAAO,CAAC,GAAG,CAAC,qBAAqB,GAAG,WAAW,CAAC;YAClD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,kCAAmB,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QACjD,MAAM,eAAe,GAAgB;YACnC,QAAQ,EAAE,OAAO;YACjB,SAAS,EAAE,CAAC,OAAO;SACpB,CAAC;QAEF,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,MAAM,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;iBACvE,OAAO,CAAC,OAAO,EAAE,CAAC;YAErB,MAAM,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;iBAC3E,OAAO,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,EAAE,YAAY,EAAE;gBAC3E,QAAQ,EAAE,CAAC,CAAC;aACb,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAEtB,MAAM,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,EAAE,YAAY,EAAE;gBAC3E,QAAQ,EAAE,CAAC;aACZ,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,EAAE,YAAY,EAAE;gBAC3E,QAAQ,EAAE,CAAC,CAAC;aACb,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAEtB,MAAM,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,EAAE,YAAY,EAAE;gBAC3E,QAAQ,EAAE,CAAC;aACZ,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,OAAO,EAAE,EAAE;oBACX,MAAM,EAAE,cAAc;iBACvB,CAAC;aACS,CAAC,CAAC;YAEf,MAAM,aAAa,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,EAAE,YAAY,EAAE;gBACpE,QAAQ,EAAE,CAAC;gBACX,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,OAAO;aACjB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAW,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,MAAM,EAAE,gBAAgB;oBACxB,aAAa,EAAE,iBAAiB;iBACjC,CAAC;aACS,CAAC,CAAC;YAEf,MAAM,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC;gBACtC,QAAQ,EAAE,OAAO;gBACjB,SAAS,EAAE,CAAC,OAAO;aACpB,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,SAAS,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,CAAC;YAEtD,MAAM,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC;gBACtC,QAAQ,EAAE,OAAO;gBACjB,SAAS,EAAE,CAAC,OAAO;aACpB,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,SAAS,CAAC,qBAAqB,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;YAE5D,MAAM,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,OAAO,EAAE;gBAC/C,QAAQ,EAAE,OAAO;gBACjB,SAAS,EAAE,CAAC,OAAO;aACpB,EAAE,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,SAAS,CAAC,qBAAqB,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;YAE5D,MAAM,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;iBACzD,OAAO,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,SAAS,CAAC,qBAAqB,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;YAE5D,MAAM,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;iBAClD,OAAO,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC3C,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,MAAM,EAAE;wBACN,QAAQ,EAAE,MAAM;wBAChB,IAAI,EAAE,YAAY;wBAClB,iBAAiB,EAAE,cAAc;wBACjC,QAAQ,EAAE;4BACR,QAAQ,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE;yBAC1C;qBACF;oBACD,MAAM,EAAE,IAAI;iBACb,CAAC;aACS,CAAC,CAAC;YAEf,MAAM,aAAa,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,mBAAmB,CAAC,CAAC,CAAC;YAEhG,+CAA+C;YAC/C,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CACpC,MAAM,CAAC,gBAAgB,CAAC,4CAA4C,CAAC,EACrE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,kBAAkB,GAAG;gBACzB,OAAO,EAAE;oBACP;wBACE,QAAQ,EAAE,MAAM;wBAChB,IAAI,EAAE,YAAY;wBAClB,+BAA+B;wBAC/B,QAAQ,EAAE;4BACR,QAAQ,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE;yBAC1C;qBACF;iBACF;gBACD,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,kBAAkB;aACzB,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,YAAY,CAAC;gBAC9C,QAAQ,EAAE,OAAO;gBACjB,SAAS,EAAE,CAAC,OAAO;aACpB,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;YAEvB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBAC/B,QAAQ,EAAE,MAAM;gBAChB,IAAI,EAAE,YAAY;gBAClB,iBAAiB,EAAE,EAAE;gBACrB,sBAAsB,EAAE,SAAS;gBACjC,OAAO,EAAE,SAAS;gBAClB,KAAK,EAAE,EAAE;gBACT,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE;oBACR,QAAQ,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE;iBAC1C;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,kBAAkB,GAAG;gBACzB,OAAO,EAAE;oBACP;wBACE,QAAQ,EAAE,MAAM;wBAChB,IAAI,EAAE,YAAY;wBAClB,mBAAmB;qBACpB;iBACF;gBACD,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,kBAAkB;aACzB,CAAC,CAAC;YAEf,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,YAAY,CAAC;gBAC9C,QAAQ,EAAE,OAAO;gBACjB,SAAS,EAAE,CAAC,OAAO;aACpB,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;YAEvB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;gBACjD,GAAG,EAAE,CAAC;gBACN,GAAG,EAAE,CAAC;aACP,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,WAAW,GAAG,aAAa,CAAC,YAAY,EAAE,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE5B,yBAAyB;YACzB,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,MAAM,EAAE;wBACN,QAAQ,EAAE,MAAM;wBAChB,IAAI,EAAE,YAAY;wBAClB,iBAAiB,EAAE,cAAc;wBACjC,QAAQ,EAAE;4BACR,QAAQ,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE;yBAC1C;qBACF;oBACD,MAAM,EAAE,IAAI;iBACb,CAAC;aACS,CAAC,CAAC;YAEf,MAAM,aAAa,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAE5C,MAAM,OAAO,GAAG,aAAa,CAAC,YAAY,EAAE,CAAC;YAC7C,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/tests/services/googlePlaces.test.ts"], "sourcesContent": ["import { GooglePlacesService } from '../../src/services/googlePlaces';\nimport { ApiError, NetworkError, RateLimitError } from '../../src/models/Errors';\nimport { Coordinates, PlaceResult } from '../../src/models/Business';\n\n// Mock fetch globally\nconst mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;\n\ndescribe('GooglePlacesService', () => {\n  let placesService: GooglePlacesService;\n\n  beforeEach(() => {\n    placesService = new GooglePlacesService();\n    mockFetch.mockClear();\n  });\n\n  describe('searchNearby', () => {\n    const mockCoordinates: Coordinates = {\n      latitude: 40.7128,\n      longitude: -74.0060\n    };\n\n    const mockApiResponse = {\n      results: [\n        {\n          place_id: 'ChIJN1t_tDeuEmsRUsoyG83frY4',\n          name: 'Test Restaurant',\n          formatted_address: '123 Main St, New York, NY 10001, USA',\n          formatted_phone_number: '******-123-4567',\n          website: 'https://testrestaurant.com',\n          types: ['restaurant', 'food', 'establishment'],\n          rating: 4.5,\n          geometry: {\n            location: {\n              lat: 40.7128,\n              lng: -74.0060\n            }\n          }\n        },\n        {\n          place_id: 'ChIJN1t_tDeuEmsRUsoyG83frY5',\n          name: 'Another Restaurant',\n          formatted_address: '456 Oak Ave, New York, NY 10002, USA',\n          types: ['restaurant', 'food'],\n          rating: 4.0,\n          geometry: {\n            location: {\n              lat: 40.7130,\n              lng: -74.0062\n            }\n          }\n        }\n      ],\n      status: 'OK',\n      next_page_token: 'next_token_123'\n    };\n\n    it('should search for nearby places successfully', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => mockApiResponse\n      } as Response);\n\n      const result = await placesService.searchNearby(\n        mockCoordinates,\n        1000,\n        'restaurant'\n      );\n\n      expect(result.places).toHaveLength(2);\n      expect(result.places[0].name).toBe('Test Restaurant');\n      expect(result.places[0].website).toBe('https://testrestaurant.com');\n      expect(result.nextPageToken).toBe('next_token_123');\n      expect(result.totalResults).toBe(2);\n\n      expect(mockFetch).toHaveBeenCalledWith(\n        expect.stringContaining('nearbysearch/json'),\n        expect.objectContaining({\n          method: 'GET'\n        })\n      );\n    });\n\n    it('should handle API errors', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: false,\n        status: 400,\n        statusText: 'Bad Request'\n      } as Response);\n\n      await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant'))\n        .rejects.toThrow(ApiError);\n    });\n\n    it('should handle network errors', async () => {\n      mockFetch.mockRejectedValueOnce(new Error('Network error'));\n\n      await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant'))\n        .rejects.toThrow(NetworkError);\n    });\n\n    it('should handle zero results', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => ({\n          results: [],\n          status: 'ZERO_RESULTS'\n        })\n      } as Response);\n\n      const result = await placesService.searchNearby(mockCoordinates, 1000, 'restaurant');\n\n      expect(result.places).toHaveLength(0);\n      expect(result.totalResults).toBe(0);\n      expect(result.nextPageToken).toBeUndefined();\n    });\n\n    it('should handle rate limiting', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: false,\n        status: 429,\n        statusText: 'Too Many Requests'\n      } as Response);\n\n      await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant'))\n        .rejects.toThrow(ApiError);\n    });\n\n    it('should validate search parameters', async () => {\n      await expect(placesService.searchNearby(mockCoordinates, 0, 'restaurant'))\n        .rejects.toThrow();\n\n      await expect(placesService.searchNearby(mockCoordinates, 1000, ''))\n        .rejects.toThrow();\n    });\n\n    it('should include custom parameters in request', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => mockApiResponse\n      } as Response);\n\n      await placesService.searchNearby(\n        mockCoordinates,\n        1000,\n        'restaurant',\n        {\n          minprice: 2,\n          maxprice: 4,\n          opennow: true\n        }\n      );\n\n      expect(mockFetch).toHaveBeenCalledWith(\n        expect.stringContaining('minprice=2'),\n        expect.any(Object)\n      );\n      expect(mockFetch).toHaveBeenCalledWith(\n        expect.stringContaining('maxprice=4'),\n        expect.any(Object)\n      );\n      expect(mockFetch).toHaveBeenCalledWith(\n        expect.stringContaining('opennow=true'),\n        expect.any(Object)\n      );\n    });\n  });\n\n  describe('getPlaceDetails', () => {\n    const mockPlaceId = 'ChIJN1t_tDeuEmsRUsoyG83frY4';\n    const mockDetailsResponse = {\n      result: {\n        place_id: mockPlaceId,\n        name: 'Test Restaurant',\n        formatted_address: '123 Main St, New York, NY 10001, USA',\n        formatted_phone_number: '******-123-4567',\n        website: 'https://testrestaurant.com',\n        types: ['restaurant', 'food', 'establishment'],\n        rating: 4.5,\n        geometry: {\n          location: {\n            lat: 40.7128,\n            lng: -74.0060\n          }\n        },\n        opening_hours: {\n          open_now: true,\n          weekday_text: [\n            'Monday: 9:00 AM – 10:00 PM',\n            'Tuesday: 9:00 AM – 10:00 PM'\n          ]\n        }\n      },\n      status: 'OK'\n    };\n\n    it('should get place details successfully', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => mockDetailsResponse\n      } as Response);\n\n      const result = await placesService.getPlaceDetails(mockPlaceId);\n\n      expect(result.place_id).toBe(mockPlaceId);\n      expect(result.name).toBe('Test Restaurant');\n      expect(result.website).toBe('https://testrestaurant.com');\n\n      expect(mockFetch).toHaveBeenCalledWith(\n        expect.stringContaining('details/json'),\n        expect.objectContaining({\n          method: 'GET'\n        })\n      );\n    });\n\n    it('should handle place not found', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => ({\n          status: 'NOT_FOUND'\n        })\n      } as Response);\n\n      await expect(placesService.getPlaceDetails(mockPlaceId))\n        .rejects.toThrow(ApiError);\n    });\n\n    it('should cache place details', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => mockDetailsResponse\n      } as Response);\n\n      // First call\n      const result1 = await placesService.getPlaceDetails(mockPlaceId);\n      \n      // Second call should use cache\n      const result2 = await placesService.getPlaceDetails(mockPlaceId);\n\n      expect(result1).toEqual(result2);\n      expect(mockFetch).toHaveBeenCalledTimes(1);\n    });\n  });\n\n  describe('searchByText', () => {\n    const mockCoordinates: Coordinates = {\n      latitude: 40.7128,\n      longitude: -74.0060\n    };\n\n    const mockTextResponse = {\n      results: [\n        {\n          place_id: 'ChIJN1t_tDeuEmsRUsoyG83frY4',\n          name: 'Pizza Palace',\n          formatted_address: '789 Broadway, New York, NY 10003, USA',\n          types: ['restaurant', 'meal_delivery'],\n          rating: 4.2,\n          geometry: {\n            location: {\n              lat: 40.7300,\n              lng: -74.0000\n            }\n          }\n        }\n      ],\n      status: 'OK'\n    };\n\n    it('should search by text query successfully', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => mockTextResponse\n      } as Response);\n\n      const result = await placesService.searchByText(\n        'pizza restaurants in New York',\n        mockCoordinates,\n        5000\n      );\n\n      expect(result.places).toHaveLength(1);\n      expect(result.places[0].name).toBe('Pizza Palace');\n\n      expect(mockFetch).toHaveBeenCalledWith(\n        expect.stringContaining('textsearch/json'),\n        expect.objectContaining({\n          method: 'GET'\n        })\n      );\n    });\n\n    it('should validate text query', async () => {\n      await expect(placesService.searchByText('', mockCoordinates, 1000))\n        .rejects.toThrow();\n    });\n  });\n\n  describe('getNextPage', () => {\n    it('should get next page of results', async () => {\n      const nextPageResponse = {\n        results: [\n          {\n            place_id: 'ChIJN1t_tDeuEmsRUsoyG83frY6',\n            name: 'Next Page Restaurant',\n            formatted_address: '999 Next St, New York, NY 10004, USA',\n            types: ['restaurant'],\n            geometry: {\n              location: {\n                lat: 40.7400,\n                lng: -74.0100\n              }\n            }\n          }\n        ],\n        status: 'OK'\n      };\n\n      // Clear any previous mocks\n      mockFetch.mockClear();\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => nextPageResponse\n      } as Response);\n\n      const result = await placesService.getNextPage('next_token_123');\n\n      expect(result.places).toHaveLength(1);\n      expect(result.places[0].name).toBe('Next Page Restaurant');\n\n      expect(mockFetch).toHaveBeenCalledWith(\n        expect.stringContaining('pagetoken=next_token_123'),\n        expect.any(Object)\n      );\n    });\n\n    it('should handle invalid page token', async () => {\n      mockFetch.mockClear();\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => ({\n          status: 'INVALID_REQUEST'\n        })\n      } as Response);\n\n      await expect(placesService.getNextPage('invalid_token'))\n        .rejects.toThrow(ApiError);\n    });\n  });\n\n  describe('clearCache', () => {\n    it('should clear the places cache', async () => {\n      // Add something to cache first\n      mockFetch.mockClear();\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => ({\n          result: {\n            place_id: 'test',\n            name: 'Test Place',\n            formatted_address: 'Test Address',\n            geometry: {\n              location: {\n                lat: 40.7128,\n                lng: -74.0060\n              }\n            }\n          },\n          status: 'OK'\n        })\n      } as Response);\n\n      await placesService.getPlaceDetails('test');\n      expect(mockFetch).toHaveBeenCalledTimes(1);\n\n      // Clear cache\n      placesService.clearCache();\n\n      // Should make API call again\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => ({\n          result: {\n            place_id: 'test',\n            name: 'Test Place',\n            formatted_address: 'Test Address',\n            geometry: {\n              location: {\n                lat: 40.7128,\n                lng: -74.0060\n              }\n            }\n          },\n          status: 'OK'\n        })\n      } as Response);\n\n      await placesService.getPlaceDetails('test');\n      expect(mockFetch).toHaveBeenCalledTimes(2);\n    });\n  });\n\n  describe('constructor error handling', () => {\n    it('should throw ConfigurationError when no API key provided', () => {\n      const originalKey = process.env.GOOGLE_PLACES_API_KEY;\n      delete process.env.GOOGLE_PLACES_API_KEY;\n\n      expect(() => new GooglePlacesService()).toThrow('Google Places API key is required');\n\n      // Restore API key\n      if (originalKey) {\n        process.env.GOOGLE_PLACES_API_KEY = originalKey;\n      }\n    });\n\n    it('should accept API key from constructor parameter', () => {\n      expect(() => new GooglePlacesService('test-api-key')).not.toThrow();\n    });\n  });\n\n  describe('searchNearby parameter validation', () => {\n    const mockCoordinates: Coordinates = {\n      latitude: 40.7128,\n      longitude: -74.0060\n    };\n\n    it('should validate radius range', async () => {\n      await expect(placesService.searchNearby(mockCoordinates, 0, 'restaurant'))\n        .rejects.toThrow();\n\n      await expect(placesService.searchNearby(mockCoordinates, 60000, 'restaurant'))\n        .rejects.toThrow();\n    });\n\n    it('should validate minprice range', async () => {\n      await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant', {\n        minprice: -1\n      })).rejects.toThrow();\n\n      await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant', {\n        minprice: 5\n      })).rejects.toThrow();\n    });\n\n    it('should validate maxprice range', async () => {\n      await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant', {\n        maxprice: -1\n      })).rejects.toThrow();\n\n      await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant', {\n        maxprice: 5\n      })).rejects.toThrow();\n    });\n\n    it('should handle all optional parameters', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => ({\n          results: [],\n          status: 'ZERO_RESULTS'\n        })\n      } as Response);\n\n      await placesService.searchNearby(mockCoordinates, 1000, 'restaurant', {\n        minprice: 1,\n        maxprice: 3,\n        opennow: true,\n        keyword: 'pizza'\n      });\n\n      const callUrl = mockFetch.mock.calls[0][0] as string;\n      expect(callUrl).toContain('minprice=1');\n      expect(callUrl).toContain('maxprice=3');\n      expect(callUrl).toContain('opennow=true');\n      expect(callUrl).toContain('keyword=pizza');\n    });\n  });\n\n  describe('error handling edge cases', () => {\n    it('should handle API status errors', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => ({\n          status: 'REQUEST_DENIED',\n          error_message: 'API key invalid'\n        })\n      } as Response);\n\n      await expect(placesService.searchNearby({\n        latitude: 40.7128,\n        longitude: -74.0060\n      }, 1000, 'restaurant')).rejects.toThrow('API key invalid');\n    });\n\n    it('should handle unknown errors', async () => {\n      mockFetch.mockRejectedValueOnce('unknown error type');\n\n      await expect(placesService.searchNearby({\n        latitude: 40.7128,\n        longitude: -74.0060\n      }, 1000, 'restaurant')).rejects.toThrow('Unknown error during places search');\n    });\n\n    it('should handle text search errors', async () => {\n      mockFetch.mockRejectedValueOnce(new Error('Network error'));\n\n      await expect(placesService.searchByText('pizza', {\n        latitude: 40.7128,\n        longitude: -74.0060\n      }, 1000)).rejects.toThrow('Network error during text search');\n    });\n\n    it('should handle place details errors', async () => {\n      mockFetch.mockRejectedValueOnce(new Error('Network error'));\n\n      await expect(placesService.getPlaceDetails('test-place-id'))\n        .rejects.toThrow('Network error during place details');\n    });\n\n    it('should handle next page errors', async () => {\n      mockFetch.mockRejectedValueOnce(new Error('Network error'));\n\n      await expect(placesService.getNextPage('test-token'))\n        .rejects.toThrow('Network error during next page');\n    });\n  });\n\n  describe('getPlaceDetails with fields', () => {\n    it('should include fields parameter when provided', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => ({\n          result: {\n            place_id: 'test',\n            name: 'Test Place',\n            formatted_address: 'Test Address',\n            geometry: {\n              location: { lat: 40.7128, lng: -74.0060 }\n            }\n          },\n          status: 'OK'\n        })\n      } as Response);\n\n      await placesService.getPlaceDetails('test-place-id', ['name', 'geometry', 'formatted_address']);\n\n      // Fields get URL encoded, so commas become %2C\n      expect(mockFetch).toHaveBeenCalledWith(\n        expect.stringContaining('fields=name%2Cgeometry%2Cformatted_address'),\n        expect.any(Object)\n      );\n    });\n  });\n\n  describe('transformPlaceResult', () => {\n    it('should handle missing optional fields', async () => {\n      const minimalApiResponse = {\n        results: [\n          {\n            place_id: 'test',\n            name: 'Test Place',\n            // Missing most optional fields\n            geometry: {\n              location: { lat: 40.7128, lng: -74.0060 }\n            }\n          }\n        ],\n        status: 'OK'\n      };\n\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => minimalApiResponse\n      } as Response);\n\n      const result = await placesService.searchNearby({\n        latitude: 40.7128,\n        longitude: -74.0060\n      }, 1000, 'restaurant');\n\n      expect(result.places[0]).toEqual({\n        place_id: 'test',\n        name: 'Test Place',\n        formatted_address: '',\n        formatted_phone_number: undefined,\n        website: undefined,\n        types: [],\n        rating: undefined,\n        geometry: {\n          location: { lat: 40.7128, lng: -74.0060 }\n        }\n      });\n    });\n\n    it('should handle missing geometry', async () => {\n      const noGeometryResponse = {\n        results: [\n          {\n            place_id: 'test',\n            name: 'Test Place'\n            // Missing geometry\n          }\n        ],\n        status: 'OK'\n      };\n\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => noGeometryResponse\n      } as Response);\n\n      const result = await placesService.searchNearby({\n        latitude: 40.7128,\n        longitude: -74.0060\n      }, 1000, 'restaurant');\n\n      expect(result.places[0].geometry.location).toEqual({\n        lat: 0,\n        lng: 0\n      });\n    });\n  });\n\n  describe('getCacheSize', () => {\n    it('should return current cache size', async () => {\n      const initialSize = placesService.getCacheSize();\n      expect(initialSize).toBe(0);\n\n      // Add something to cache\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => ({\n          result: {\n            place_id: 'test',\n            name: 'Test Place',\n            formatted_address: 'Test Address',\n            geometry: {\n              location: { lat: 40.7128, lng: -74.0060 }\n            }\n          },\n          status: 'OK'\n        })\n      } as Response);\n\n      await placesService.getPlaceDetails('test');\n\n      const newSize = placesService.getCacheSize();\n      expect(newSize).toBe(1);\n    });\n  });\n});\n"], "version": 3}