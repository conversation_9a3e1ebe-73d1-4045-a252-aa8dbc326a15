a94e921c7e2489a1db04d372b9400839
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const googlePlaces_1 = require("../../src/services/googlePlaces");
const Errors_1 = require("../../src/models/Errors");
// Mock fetch globally
const mockFetch = global.fetch;
describe('GooglePlacesService', () => {
    let placesService;
    beforeEach(() => {
        placesService = new googlePlaces_1.GooglePlacesService();
        mockFetch.mockClear();
    });
    describe('searchNearby', () => {
        const mockCoordinates = {
            latitude: 40.7128,
            longitude: -74.0060
        };
        const mockApiResponse = {
            results: [
                {
                    place_id: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
                    name: 'Test Restaurant',
                    formatted_address: '123 Main St, New York, NY 10001, USA',
                    formatted_phone_number: '******-123-4567',
                    website: 'https://testrestaurant.com',
                    types: ['restaurant', 'food', 'establishment'],
                    rating: 4.5,
                    geometry: {
                        location: {
                            lat: 40.7128,
                            lng: -74.0060
                        }
                    }
                },
                {
                    place_id: 'ChIJN1t_tDeuEmsRUsoyG83frY5',
                    name: 'Another Restaurant',
                    formatted_address: '456 Oak Ave, New York, NY 10002, USA',
                    types: ['restaurant', 'food'],
                    rating: 4.0,
                    geometry: {
                        location: {
                            lat: 40.7130,
                            lng: -74.0062
                        }
                    }
                }
            ],
            status: 'OK',
            next_page_token: 'next_token_123'
        };
        it('should search for nearby places successfully', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => mockApiResponse
            });
            const result = await placesService.searchNearby(mockCoordinates, 1000, 'restaurant');
            expect(result.places).toHaveLength(2);
            expect(result.places[0].name).toBe('Test Restaurant');
            expect(result.places[0].website).toBe('https://testrestaurant.com');
            expect(result.nextPageToken).toBe('next_token_123');
            expect(result.totalResults).toBe(2);
            expect(mockFetch).toHaveBeenCalledWith(expect.stringContaining('nearbysearch/json'), expect.objectContaining({
                method: 'GET'
            }));
        });
        it('should handle API errors', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: false,
                status: 400,
                statusText: 'Bad Request'
            });
            await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant'))
                .rejects.toThrow(Errors_1.ApiError);
        });
        it('should handle network errors', async () => {
            mockFetch.mockRejectedValueOnce(new Error('Network error'));
            await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant'))
                .rejects.toThrow(Errors_1.NetworkError);
        });
        it('should handle zero results', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => ({
                    results: [],
                    status: 'ZERO_RESULTS'
                })
            });
            const result = await placesService.searchNearby(mockCoordinates, 1000, 'restaurant');
            expect(result.places).toHaveLength(0);
            expect(result.totalResults).toBe(0);
            expect(result.nextPageToken).toBeUndefined();
        });
        it('should handle rate limiting', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: false,
                status: 429,
                statusText: 'Too Many Requests'
            });
            await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant'))
                .rejects.toThrow(Errors_1.ApiError);
        });
        it('should validate search parameters', async () => {
            await expect(placesService.searchNearby(mockCoordinates, 0, 'restaurant'))
                .rejects.toThrow();
            await expect(placesService.searchNearby(mockCoordinates, 1000, ''))
                .rejects.toThrow();
        });
        it('should include custom parameters in request', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => mockApiResponse
            });
            await placesService.searchNearby(mockCoordinates, 1000, 'restaurant', {
                minprice: 2,
                maxprice: 4,
                opennow: true
            });
            expect(mockFetch).toHaveBeenCalledWith(expect.stringContaining('minprice=2'), expect.any(Object));
            expect(mockFetch).toHaveBeenCalledWith(expect.stringContaining('maxprice=4'), expect.any(Object));
            expect(mockFetch).toHaveBeenCalledWith(expect.stringContaining('opennow=true'), expect.any(Object));
        });
    });
    describe('getPlaceDetails', () => {
        const mockPlaceId = 'ChIJN1t_tDeuEmsRUsoyG83frY4';
        const mockDetailsResponse = {
            result: {
                place_id: mockPlaceId,
                name: 'Test Restaurant',
                formatted_address: '123 Main St, New York, NY 10001, USA',
                formatted_phone_number: '******-123-4567',
                website: 'https://testrestaurant.com',
                types: ['restaurant', 'food', 'establishment'],
                rating: 4.5,
                geometry: {
                    location: {
                        lat: 40.7128,
                        lng: -74.0060
                    }
                },
                opening_hours: {
                    open_now: true,
                    weekday_text: [
                        'Monday: 9:00 AM – 10:00 PM',
                        'Tuesday: 9:00 AM – 10:00 PM'
                    ]
                }
            },
            status: 'OK'
        };
        it('should get place details successfully', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => mockDetailsResponse
            });
            const result = await placesService.getPlaceDetails(mockPlaceId);
            expect(result.place_id).toBe(mockPlaceId);
            expect(result.name).toBe('Test Restaurant');
            expect(result.website).toBe('https://testrestaurant.com');
            expect(mockFetch).toHaveBeenCalledWith(expect.stringContaining('details/json'), expect.objectContaining({
                method: 'GET'
            }));
        });
        it('should handle place not found', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => ({
                    status: 'NOT_FOUND'
                })
            });
            await expect(placesService.getPlaceDetails(mockPlaceId))
                .rejects.toThrow(Errors_1.ApiError);
        });
        it('should cache place details', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => mockDetailsResponse
            });
            // First call
            const result1 = await placesService.getPlaceDetails(mockPlaceId);
            // Second call should use cache
            const result2 = await placesService.getPlaceDetails(mockPlaceId);
            expect(result1).toEqual(result2);
            expect(mockFetch).toHaveBeenCalledTimes(1);
        });
    });
    describe('searchByText', () => {
        const mockCoordinates = {
            latitude: 40.7128,
            longitude: -74.0060
        };
        const mockTextResponse = {
            results: [
                {
                    place_id: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
                    name: 'Pizza Palace',
                    formatted_address: '789 Broadway, New York, NY 10003, USA',
                    types: ['restaurant', 'meal_delivery'],
                    rating: 4.2,
                    geometry: {
                        location: {
                            lat: 40.7300,
                            lng: -74.0000
                        }
                    }
                }
            ],
            status: 'OK'
        };
        it('should search by text query successfully', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => mockTextResponse
            });
            const result = await placesService.searchByText('pizza restaurants in New York', mockCoordinates, 5000);
            expect(result.places).toHaveLength(1);
            expect(result.places[0].name).toBe('Pizza Palace');
            expect(mockFetch).toHaveBeenCalledWith(expect.stringContaining('textsearch/json'), expect.objectContaining({
                method: 'GET'
            }));
        });
        it('should validate text query', async () => {
            await expect(placesService.searchByText('', mockCoordinates, 1000))
                .rejects.toThrow();
        });
    });
    describe('getNextPage', () => {
        it('should get next page of results', async () => {
            const nextPageResponse = {
                results: [
                    {
                        place_id: 'ChIJN1t_tDeuEmsRUsoyG83frY6',
                        name: 'Next Page Restaurant',
                        formatted_address: '999 Next St, New York, NY 10004, USA',
                        types: ['restaurant'],
                        geometry: {
                            location: {
                                lat: 40.7400,
                                lng: -74.0100
                            }
                        }
                    }
                ],
                status: 'OK'
            };
            // Clear any previous mocks
            mockFetch.mockClear();
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => nextPageResponse
            });
            const result = await placesService.getNextPage('next_token_123');
            expect(result.places).toHaveLength(1);
            expect(result.places[0].name).toBe('Next Page Restaurant');
            expect(mockFetch).toHaveBeenCalledWith(expect.stringContaining('pagetoken=next_token_123'), expect.any(Object));
        });
        it('should handle invalid page token', async () => {
            mockFetch.mockClear();
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => ({
                    status: 'INVALID_REQUEST'
                })
            });
            await expect(placesService.getNextPage('invalid_token'))
                .rejects.toThrow(Errors_1.ApiError);
        });
    });
    describe('clearCache', () => {
        it('should clear the places cache', async () => {
            // Add something to cache first
            mockFetch.mockClear();
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => ({
                    result: {
                        place_id: 'test',
                        name: 'Test Place',
                        formatted_address: 'Test Address',
                        geometry: {
                            location: {
                                lat: 40.7128,
                                lng: -74.0060
                            }
                        }
                    },
                    status: 'OK'
                })
            });
            await placesService.getPlaceDetails('test');
            expect(mockFetch).toHaveBeenCalledTimes(1);
            // Clear cache
            placesService.clearCache();
            // Should make API call again
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => ({
                    result: {
                        place_id: 'test',
                        name: 'Test Place',
                        formatted_address: 'Test Address',
                        geometry: {
                            location: {
                                lat: 40.7128,
                                lng: -74.0060
                            }
                        }
                    },
                    status: 'OK'
                })
            });
            await placesService.getPlaceDetails('test');
            expect(mockFetch).toHaveBeenCalledTimes(2);
        });
    });
    describe('constructor error handling', () => {
        it('should throw ConfigurationError when no API key provided', () => {
            const originalKey = process.env.GOOGLE_PLACES_API_KEY;
            delete process.env.GOOGLE_PLACES_API_KEY;
            expect(() => new googlePlaces_1.GooglePlacesService()).toThrow('Google Places API key is required');
            // Restore API key
            if (originalKey) {
                process.env.GOOGLE_PLACES_API_KEY = originalKey;
            }
        });
        it('should accept API key from constructor parameter', () => {
            expect(() => new googlePlaces_1.GooglePlacesService('test-api-key')).not.toThrow();
        });
    });
    describe('searchNearby parameter validation', () => {
        const mockCoordinates = {
            latitude: 40.7128,
            longitude: -74.0060
        };
        it('should validate radius range', async () => {
            await expect(placesService.searchNearby(mockCoordinates, 0, 'restaurant'))
                .rejects.toThrow();
            await expect(placesService.searchNearby(mockCoordinates, 60000, 'restaurant'))
                .rejects.toThrow();
        });
        it('should validate minprice range', async () => {
            await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant', {
                minprice: -1
            })).rejects.toThrow();
            await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant', {
                minprice: 5
            })).rejects.toThrow();
        });
        it('should validate maxprice range', async () => {
            await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant', {
                maxprice: -1
            })).rejects.toThrow();
            await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant', {
                maxprice: 5
            })).rejects.toThrow();
        });
        it('should handle all optional parameters', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => ({
                    results: [],
                    status: 'ZERO_RESULTS'
                })
            });
            await placesService.searchNearby(mockCoordinates, 1000, 'restaurant', {
                minprice: 1,
                maxprice: 3,
                opennow: true,
                keyword: 'pizza'
            });
            const callUrl = mockFetch.mock.calls[0][0];
            expect(callUrl).toContain('minprice=1');
            expect(callUrl).toContain('maxprice=3');
            expect(callUrl).toContain('opennow=true');
            expect(callUrl).toContain('keyword=pizza');
        });
    });
    describe('error handling edge cases', () => {
        it('should handle API status errors', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => ({
                    status: 'REQUEST_DENIED',
                    error_message: 'API key invalid'
                })
            });
            await expect(placesService.searchNearby({
                latitude: 40.7128,
                longitude: -74.0060
            }, 1000, 'restaurant')).rejects.toThrow('API key invalid');
        });
        it('should handle unknown errors', async () => {
            mockFetch.mockRejectedValueOnce('unknown error type');
            await expect(placesService.searchNearby({
                latitude: 40.7128,
                longitude: -74.0060
            }, 1000, 'restaurant')).rejects.toThrow('Unknown error during places search');
        });
        it('should handle text search errors', async () => {
            mockFetch.mockRejectedValueOnce(new Error('Network error'));
            await expect(placesService.searchByText('pizza', {
                latitude: 40.7128,
                longitude: -74.0060
            }, 1000)).rejects.toThrow('Network error during text search');
        });
        it('should handle place details errors', async () => {
            mockFetch.mockRejectedValueOnce(new Error('Network error'));
            await expect(placesService.getPlaceDetails('test-place-id'))
                .rejects.toThrow('Network error during place details');
        });
        it('should handle next page errors', async () => {
            mockFetch.mockRejectedValueOnce(new Error('Network error'));
            await expect(placesService.getNextPage('test-token'))
                .rejects.toThrow('Network error during next page');
        });
    });
    describe('getPlaceDetails with fields', () => {
        it('should include fields parameter when provided', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => ({
                    result: {
                        place_id: 'test',
                        name: 'Test Place',
                        formatted_address: 'Test Address',
                        geometry: {
                            location: { lat: 40.7128, lng: -74.0060 }
                        }
                    },
                    status: 'OK'
                })
            });
            await placesService.getPlaceDetails('test-place-id', ['name', 'geometry', 'formatted_address']);
            // Fields get URL encoded, so commas become %2C
            expect(mockFetch).toHaveBeenCalledWith(expect.stringContaining('fields=name%2Cgeometry%2Cformatted_address'), expect.any(Object));
        });
    });
    describe('transformPlaceResult', () => {
        it('should handle missing optional fields', async () => {
            const minimalApiResponse = {
                results: [
                    {
                        place_id: 'test',
                        name: 'Test Place',
                        // Missing most optional fields
                        geometry: {
                            location: { lat: 40.7128, lng: -74.0060 }
                        }
                    }
                ],
                status: 'OK'
            };
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => minimalApiResponse
            });
            const result = await placesService.searchNearby({
                latitude: 40.7128,
                longitude: -74.0060
            }, 1000, 'restaurant');
            expect(result.places[0]).toEqual({
                place_id: 'test',
                name: 'Test Place',
                formatted_address: '',
                formatted_phone_number: undefined,
                website: undefined,
                types: [],
                rating: undefined,
                geometry: {
                    location: { lat: 40.7128, lng: -74.0060 }
                }
            });
        });
        it('should handle missing geometry', async () => {
            const noGeometryResponse = {
                results: [
                    {
                        place_id: 'test',
                        name: 'Test Place'
                        // Missing geometry
                    }
                ],
                status: 'OK'
            };
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => noGeometryResponse
            });
            const result = await placesService.searchNearby({
                latitude: 40.7128,
                longitude: -74.0060
            }, 1000, 'restaurant');
            expect(result.places[0].geometry.location).toEqual({
                lat: 0,
                lng: 0
            });
        });
    });
    describe('getCacheSize', () => {
        it('should return current cache size', async () => {
            const initialSize = placesService.getCacheSize();
            expect(initialSize).toBe(0);
            // Add something to cache
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => ({
                    result: {
                        place_id: 'test',
                        name: 'Test Place',
                        formatted_address: 'Test Address',
                        geometry: {
                            location: { lat: 40.7128, lng: -74.0060 }
                        }
                    },
                    status: 'OK'
                })
            });
            await placesService.getPlaceDetails('test');
            const newSize = placesService.getCacheSize();
            expect(newSize).toBe(1);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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