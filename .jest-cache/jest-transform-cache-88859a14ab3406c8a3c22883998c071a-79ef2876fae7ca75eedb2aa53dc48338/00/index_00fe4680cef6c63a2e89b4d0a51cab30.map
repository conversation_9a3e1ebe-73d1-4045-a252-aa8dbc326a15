{"file": "/Users/<USER>/WebstormProjects/goo/src/constants/index.ts", "mappings": ";AAAA;;GAEG;;;AAEH,oBAAoB;AACP,QAAA,UAAU,GAAG;IACxB,sBAAsB,EAAE,4CAA4C;IACpE,kBAAkB,EAAE,8CAA8C;IAClE,eAAe,EAAE,IAAI;IACrB,WAAW,EAAE,CAAC;IACd,gBAAgB,EAAE,IAAI,EAAE,qCAAqC;CACrD,CAAC;AAEX,uBAAuB;AACV,QAAA,aAAa,GAAG;IAC3B,UAAU,EAAE,CAAC;IACb,UAAU,EAAE,EAAE;IACd,cAAc,EAAE,EAAE;IAClB,oBAAoB,EAAE,EAAE;IACxB,iBAAiB,EAAE,IAAI;CACf,CAAC;AAEX,8BAA8B;AACjB,QAAA,iBAAiB,GAAG;IAC/B,mBAAmB,EAAE,EAAE;IACvB,UAAU,EAAE,EAAE;IACd,qBAAqB,EAAE,GAAG,EAAE,eAAe;CACnC,CAAC;AAEX,sBAAsB;AACT,QAAA,YAAY,GAAG;IAC1B,gBAAgB,EAAE,EAAE;IACpB,WAAW,EAAE,EAAE;IACf,UAAU,EAAE,kBAAkB;IAC9B,gBAAgB,EAAE,OAAO,EAAE,yBAAyB;CAC5C,CAAC;AAEX,qCAAqC;AACxB,QAAA,cAAc,GAAG;IAC5B,OAAO,EAAE,IAAI;IACb,aAAa,EAAE,CAAC;IAChB,UAAU,EAAE,uBAAuB;IACnC,kBAAkB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;CAC9D,CAAC;AAEX,sBAAsB;AACT,QAAA,mBAAmB,GAAG;IACjC,QAAQ,EAAE,kBAAkB;IAC5B,KAAK,EAAE,sBAAsB;IAC7B,GAAG,EAAE,gBAAgB;IACrB,KAAK,EAAE,4BAA4B;CAC3B,CAAC;AAEX,2CAA2C;AAC9B,QAAA,cAAc,GAAG;IAC5B,YAAY;IACZ,SAAS;IACT,gBAAgB;IAChB,UAAU;IACV,aAAa;IACb,KAAK;IACL,QAAQ;IACR,MAAM;IACN,KAAK;IACL,cAAc;IACd,eAAe;IACf,YAAY;IACZ,eAAe;IACf,aAAa;IACb,MAAM;IACN,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,UAAU;IACV,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,WAAW;IACX,gBAAgB;IAChB,mBAAmB;IACnB,YAAY;IACZ,SAAS;IACT,kBAAkB;IAClB,QAAQ;IACR,WAAW;IACX,aAAa;IACb,mBAAmB;IACnB,SAAS;IACT,cAAc;IACd,SAAS;IACT,cAAc;IACd,iBAAiB;IACjB,aAAa;IACb,KAAK;IACL,WAAW;IACX,gBAAgB;IAChB,cAAc;IACd,kBAAkB;IAClB,UAAU;IACV,kBAAkB;IAClB,eAAe;IACf,SAAS;IACT,QAAQ;IACR,SAAS;IACT,oBAAoB;IACpB,cAAc;IACd,yBAAyB;IACzB,WAAW;IACX,SAAS;IACT,eAAe;IACf,eAAe;IACf,QAAQ;IACR,cAAc;IACd,eAAe;IACf,gBAAgB;IAChB,QAAQ;IACR,YAAY;IACZ,SAAS;IACT,MAAM;IACN,SAAS;IACT,WAAW;IACX,UAAU;IACV,iBAAiB;IACjB,SAAS;IACT,QAAQ;IACR,aAAa;IACb,gBAAgB;IAChB,oBAAoB;IACpB,YAAY;IACZ,oBAAoB;IACpB,SAAS;IACT,QAAQ;IACR,kBAAkB;IAClB,YAAY;IACZ,eAAe;IACf,KAAK;IACL,SAAS;IACT,SAAS;IACT,OAAO;IACP,gBAAgB;IAChB,aAAa;IACb,WAAW;IACX,YAAY;IACZ,oBAAoB;IACpB,eAAe;IACf,iBAAiB;IACjB,eAAe;IACf,YAAY;IACZ,iBAAiB;IACjB,KAAK;CACG,CAAC;AAEX,iBAAiB;AACJ,QAAA,cAAc,GAAG;IAC5B,gBAAgB,EAAE,gEAAgE;IAClF,cAAc,EAAE,0BAA0B,qBAAa,CAAC,UAAU,QAAQ,qBAAa,CAAC,UAAU,SAAS;IAC3G,qBAAqB,EAAE,8DAA8D;IACrF,eAAe,EAAE,oCAAoC;IACrD,aAAa,EAAE,uDAAuD;IACtE,mBAAmB,EAAE,8CAA8C;IACnE,gBAAgB,EAAE,4CAA4C;IAC9D,2BAA2B,EAAE,yCAAyC;IACtE,WAAW,EAAE,yBAAyB;CAC9B,CAAC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/src/constants/index.ts"], "sourcesContent": ["/**\n * Application constants and configuration values\n */\n\n// API Configuration\nexport const API_CONFIG = {\n  GOOGLE_PLACES_BASE_URL: 'https://maps.googleapis.com/maps/api/place',\n  GEOCODING_BASE_URL: 'https://maps.googleapis.com/maps/api/geocode',\n  REQUEST_TIMEOUT: 5000,\n  MAX_RETRIES: 3,\n  RETRY_DELAY_BASE: 1000, // Base delay for exponential backoff\n} as const;\n\n// Search Configuration\nexport const SEARCH_CONFIG = {\n  MIN_RADIUS: 1,\n  MAX_RADIUS: 50,\n  DEFAULT_RADIUS: 10,\n  MAX_RESULTS_PER_PAGE: 20,\n  MAX_TOTAL_RESULTS: 1000,\n} as const;\n\n// Rate Limiting Configuration\nexport const RATE_LIMIT_CONFIG = {\n  REQUESTS_PER_SECOND: 10,\n  BURST_SIZE: 20,\n  TOKEN_REFILL_INTERVAL: 100, // milliseconds\n} as const;\n\n// Cache Configuration\nexport const CACHE_CONFIG = {\n  EXPIRATION_HOURS: 24,\n  MAX_SIZE_MB: 50,\n  KEY_PREFIX: 'business_search_',\n  CLEANUP_INTERVAL: 3600000, // 1 hour in milliseconds\n} as const;\n\n// Website Verification Configuration\nexport const WEBSITE_CONFIG = {\n  TIMEOUT: 3000,\n  MAX_REDIRECTS: 3,\n  USER_AGENT: 'BusinessSearchBot/1.0',\n  VALID_STATUS_CODES: [200, 201, 202, 203, 204, 301, 302, 303, 307, 308],\n} as const;\n\n// Validation Patterns\nexport const VALIDATION_PATTERNS = {\n  ZIP_CODE: /^\\d{5}(-\\d{4})?$/,\n  PHONE: /^\\+?[\\d\\s\\-\\(\\)\\.]+$/,\n  URL: /^https?:\\/\\/.+/,\n  EMAIL: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n} as const;\n\n// Business Types (Google Places API types)\nexport const BUSINESS_TYPES = [\n  'accounting',\n  'airport',\n  'amusement_park',\n  'aquarium',\n  'art_gallery',\n  'atm',\n  'bakery',\n  'bank',\n  'bar',\n  'beauty_salon',\n  'bicycle_store',\n  'book_store',\n  'bowling_alley',\n  'bus_station',\n  'cafe',\n  'campground',\n  'car_dealer',\n  'car_rental',\n  'car_repair',\n  'car_wash',\n  'casino',\n  'cemetery',\n  'church',\n  'city_hall',\n  'clothing_store',\n  'convenience_store',\n  'courthouse',\n  'dentist',\n  'department_store',\n  'doctor',\n  'drugstore',\n  'electrician',\n  'electronics_store',\n  'embassy',\n  'fire_station',\n  'florist',\n  'funeral_home',\n  'furniture_store',\n  'gas_station',\n  'gym',\n  'hair_care',\n  'hardware_store',\n  'hindu_temple',\n  'home_goods_store',\n  'hospital',\n  'insurance_agency',\n  'jewelry_store',\n  'laundry',\n  'lawyer',\n  'library',\n  'light_rail_station',\n  'liquor_store',\n  'local_government_office',\n  'locksmith',\n  'lodging',\n  'meal_delivery',\n  'meal_takeaway',\n  'mosque',\n  'movie_rental',\n  'movie_theater',\n  'moving_company',\n  'museum',\n  'night_club',\n  'painter',\n  'park',\n  'parking',\n  'pet_store',\n  'pharmacy',\n  'physiotherapist',\n  'plumber',\n  'police',\n  'post_office',\n  'primary_school',\n  'real_estate_agency',\n  'restaurant',\n  'roofing_contractor',\n  'rv_park',\n  'school',\n  'secondary_school',\n  'shoe_store',\n  'shopping_mall',\n  'spa',\n  'stadium',\n  'storage',\n  'store',\n  'subway_station',\n  'supermarket',\n  'synagogue',\n  'taxi_stand',\n  'tourist_attraction',\n  'train_station',\n  'transit_station',\n  'travel_agency',\n  'university',\n  'veterinary_care',\n  'zoo'\n] as const;\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  INVALID_ZIP_CODE: 'Invalid zip code format. Please use 5-digit or 9-digit format.',\n  INVALID_RADIUS: `Radius must be between ${SEARCH_CONFIG.MIN_RADIUS} and ${SEARCH_CONFIG.MAX_RADIUS} miles.`,\n  INVALID_BUSINESS_TYPE: 'Invalid business type. Please select from available options.',\n  API_KEY_MISSING: 'Google Places API key is required.',\n  NETWORK_ERROR: 'Network error occurred. Please check your connection.',\n  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded. Please try again later.',\n  GEOCODING_FAILED: 'Failed to convert zip code to coordinates.',\n  WEBSITE_VERIFICATION_FAILED: 'Failed to verify website accessibility.',\n  CACHE_ERROR: 'Cache operation failed.',\n} as const;\n\nexport type BusinessType = typeof BUSINESS_TYPES[number];\n"], "version": 3}