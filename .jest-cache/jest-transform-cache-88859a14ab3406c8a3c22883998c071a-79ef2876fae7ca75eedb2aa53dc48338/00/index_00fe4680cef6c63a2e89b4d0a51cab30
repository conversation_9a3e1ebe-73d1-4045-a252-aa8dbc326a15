6e3124094bdaf20e76d73f659250bf5a
"use strict";
/**
 * Application constants and configuration values
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ERROR_MESSAGES = exports.BUSINESS_TYPES = exports.VALIDATION_PATTERNS = exports.WEBSITE_CONFIG = exports.CACHE_CONFIG = exports.RATE_LIMIT_CONFIG = exports.SEARCH_CONFIG = exports.API_CONFIG = void 0;
// API Configuration
exports.API_CONFIG = {
    GOOGLE_PLACES_BASE_URL: 'https://maps.googleapis.com/maps/api/place',
    GEOCODING_BASE_URL: 'https://maps.googleapis.com/maps/api/geocode',
    REQUEST_TIMEOUT: 5000,
    MAX_RETRIES: 3,
    RETRY_DELAY_BASE: 1000, // Base delay for exponential backoff
};
// Search Configuration
exports.SEARCH_CONFIG = {
    MIN_RADIUS: 1,
    MAX_RADIUS: 50,
    DEFAULT_RADIUS: 10,
    MAX_RESULTS_PER_PAGE: 20,
    MAX_TOTAL_RESULTS: 1000,
};
// Rate Limiting Configuration
exports.RATE_LIMIT_CONFIG = {
    REQUESTS_PER_SECOND: 10,
    BURST_SIZE: 20,
    TOKEN_REFILL_INTERVAL: 100, // milliseconds
};
// Cache Configuration
exports.CACHE_CONFIG = {
    EXPIRATION_HOURS: 24,
    MAX_SIZE_MB: 50,
    KEY_PREFIX: 'business_search_',
    CLEANUP_INTERVAL: 3600000, // 1 hour in milliseconds
};
// Website Verification Configuration
exports.WEBSITE_CONFIG = {
    TIMEOUT: 3000,
    MAX_REDIRECTS: 3,
    USER_AGENT: 'BusinessSearchBot/1.0',
    VALID_STATUS_CODES: [200, 201, 202, 203, 204, 301, 302, 303, 307, 308],
};
// Validation Patterns
exports.VALIDATION_PATTERNS = {
    ZIP_CODE: /^\d{5}(-\d{4})?$/,
    PHONE: /^\+?[\d\s\-\(\)\.]+$/,
    URL: /^https?:\/\/.+/,
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
};
// Business Types (Google Places API types)
exports.BUSINESS_TYPES = [
    'accounting',
    'airport',
    'amusement_park',
    'aquarium',
    'art_gallery',
    'atm',
    'bakery',
    'bank',
    'bar',
    'beauty_salon',
    'bicycle_store',
    'book_store',
    'bowling_alley',
    'bus_station',
    'cafe',
    'campground',
    'car_dealer',
    'car_rental',
    'car_repair',
    'car_wash',
    'casino',
    'cemetery',
    'church',
    'city_hall',
    'clothing_store',
    'convenience_store',
    'courthouse',
    'dentist',
    'department_store',
    'doctor',
    'drugstore',
    'electrician',
    'electronics_store',
    'embassy',
    'fire_station',
    'florist',
    'funeral_home',
    'furniture_store',
    'gas_station',
    'gym',
    'hair_care',
    'hardware_store',
    'hindu_temple',
    'home_goods_store',
    'hospital',
    'insurance_agency',
    'jewelry_store',
    'laundry',
    'lawyer',
    'library',
    'light_rail_station',
    'liquor_store',
    'local_government_office',
    'locksmith',
    'lodging',
    'meal_delivery',
    'meal_takeaway',
    'mosque',
    'movie_rental',
    'movie_theater',
    'moving_company',
    'museum',
    'night_club',
    'painter',
    'park',
    'parking',
    'pet_store',
    'pharmacy',
    'physiotherapist',
    'plumber',
    'police',
    'post_office',
    'primary_school',
    'real_estate_agency',
    'restaurant',
    'roofing_contractor',
    'rv_park',
    'school',
    'secondary_school',
    'shoe_store',
    'shopping_mall',
    'spa',
    'stadium',
    'storage',
    'store',
    'subway_station',
    'supermarket',
    'synagogue',
    'taxi_stand',
    'tourist_attraction',
    'train_station',
    'transit_station',
    'travel_agency',
    'university',
    'veterinary_care',
    'zoo'
];
// Error Messages
exports.ERROR_MESSAGES = {
    INVALID_ZIP_CODE: 'Invalid zip code format. Please use 5-digit or 9-digit format.',
    INVALID_RADIUS: `Radius must be between ${exports.SEARCH_CONFIG.MIN_RADIUS} and ${exports.SEARCH_CONFIG.MAX_RADIUS} miles.`,
    INVALID_BUSINESS_TYPE: 'Invalid business type. Please select from available options.',
    API_KEY_MISSING: 'Google Places API key is required.',
    NETWORK_ERROR: 'Network error occurred. Please check your connection.',
    RATE_LIMIT_EXCEEDED: 'Rate limit exceeded. Please try again later.',
    GEOCODING_FAILED: 'Failed to convert zip code to coordinates.',
    WEBSITE_VERIFICATION_FAILED: 'Failed to verify website accessibility.',
    CACHE_ERROR: 'Cache operation failed.',
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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