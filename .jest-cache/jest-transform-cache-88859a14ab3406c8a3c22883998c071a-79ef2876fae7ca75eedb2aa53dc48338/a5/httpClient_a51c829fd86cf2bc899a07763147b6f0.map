{"file": "/Users/<USER>/WebstormProjects/goo/src/services/httpClient.ts", "mappings": ";;;AAAA,6CAA0E;AAC1E,4CAA0C;AAkC1C;;GAEG;AACH,MAAa,UAAU;IAMrB,YAAY,UAAkB,sBAAU,CAAC,eAAe,EAAE,aAAqB,sBAAU,CAAC,WAAW;QAL7F,wBAAmB,GAAyB,EAAE,CAAC;QAC/C,yBAAoB,GAA0B,EAAE,CAAC;QAKvD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACH,qBAAqB,CAAC,WAA+B;QACnD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACH,sBAAsB,CAAC,WAAgC;QACrD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,GAAG,CAAU,GAAW,EAAE,SAAiC,EAAE;QACjE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAI,GAAG,EAAE;YAC1C,GAAG,MAAM;YACT,MAAM,EAAE,KAAK;SACd,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,IAAI,CAAU,GAAW,EAAE,IAAU,EAAE,SAAiC,EAAE;QAC9E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAI,GAAG,EAAE;YAC1C,GAAG,MAAM;YACT,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;YAC7C,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,GAAG,MAAM,CAAC,OAAO;aAClB;SACF,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,IAAI,CAAC,GAAW,EAAE,SAAiC,EAAE;QACzD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YACvC,GAAG,MAAM;YACT,MAAM,EAAE,MAAM;SACf,CAAC,CAAC;QAEH,OAAO;YACL,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;YAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,EAAE,EAAE,QAAQ,CAAC,EAAE;SAChB,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,OAAO,CAAU,GAAW,EAAE,MAAqB;QAC/D,IAAI,WAAW,GAAG,EAAE,GAAG,MAAM,EAAE,CAAC;QAEhC,6BAA6B;QAC7B,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACnD,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,CAAC;QACzC,CAAC;QAED,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC;QAC3D,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;QAE7F,IAAI,SAAS,GAAiB,IAAI,CAAC;QAEnC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;YACvD,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAI,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;gBAEtE,8BAA8B;gBAC9B,IAAI,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC;gBACjC,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBACpD,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC;gBAC3C,CAAC;gBAED,OAAO;oBACL,GAAG,QAAQ;oBACX,IAAI,EAAE,YAAY;iBACnB,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAc,CAAC;gBAE3B,gCAAgC;gBAChC,IAAI,KAAK,YAAY,iBAAQ,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC/D,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,IAAI,KAAK,YAAY,uBAAc,EAAE,CAAC;oBACpC,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,kCAAkC;gBAClC,IAAI,OAAO,IAAI,UAAU,EAAE,CAAC;oBAC1B,MAAM;gBACR,CAAC;gBAED,gFAAgF;gBAChF,IAAI,OAAO,GAAG,UAAU,EAAE,CAAC;oBACzB,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;oBAChD,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC1B,CAAC;YACH,CAAC;QACH,CAAC;QAED,qCAAqC;QACrC,MAAM,SAAS,IAAI,IAAI,qBAAY,CAAC,2BAA2B,CAAC,CAAC;IACnE,CAAC;IAED;;;;;;OAMG;IACK,KAAK,CAAC,WAAW,CAAI,GAAW,EAAE,MAAqB,EAAE,OAAe;QAC9E,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC;gBAC1B,YAAY,EAAE,uBAAuB;gBACrC,GAAG,MAAM,CAAC,OAAO;aAClB,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;YACzC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC;YAEhE,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;oBAChC,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,KAAK;oBAC9B,OAAO;oBACP,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,MAAM,EAAE,UAAU,CAAC,MAAM;iBAC1B,CAAC,CAAC;gBAEH,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,OAAO,MAAM,IAAI,CAAC,eAAe,CAAI,QAAQ,EAAE,MAAM,CAAC,CAAC;YACzD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,iBAAQ,IAAI,KAAK,YAAY,uBAAc,EAAE,CAAC;gBACjE,MAAM,KAAK,CAAC;YACd,CAAC;YAED,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;oBAChC,MAAM,IAAI,qBAAY,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;gBACrD,CAAC;gBACD,MAAM,IAAI,qBAAY,CAAC,kBAAkB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;YACnE,CAAC;YAED,MAAM,IAAI,qBAAY,CAAC,0BAA0B,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,eAAe,CAAI,QAAkB,EAAE,MAAqB;QACxE,uBAAuB;QACvB,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;YAC5B,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YACvD,MAAM,iBAAiB,GAAG,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAC5E,MAAM,IAAI,uBAAc,CAAC,qBAAqB,EAAE,iBAAiB,CAAC,CAAC;QACrE,CAAC;QAED,2BAA2B;QAC3B,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,eAAe,CAAC,CAAC;YACrE,MAAM,IAAI,iBAAQ,CAChB,QAAQ,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,UAAU,MAAM,SAAS,EAAE,EAChE,QAAQ,CAAC,MAAM,CAChB,CAAC;QACJ,CAAC;QAED,sBAAsB;QACtB,IAAI,IAAO,CAAC;QACZ,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAEzD,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YAC7B,IAAI,GAAG,IAAW,CAAC;QACrB,CAAC;aAAM,IAAI,WAAW,IAAI,WAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACnE,IAAI,CAAC;gBACH,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC/B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,IAAI,qBAAY,CAAC,qBAAqB,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YAC5F,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC;gBACH,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;YACtC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,IAAI,qBAAY,CAAC,qBAAqB,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YAC5F,CAAC;QACH,CAAC;QAED,OAAO;YACL,IAAI;YACJ,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;YAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,EAAE,EAAE,QAAQ,CAAC,EAAE;SAChB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACK,gBAAgB,CAAC,KAAe;QACtC,uDAAuD;QACvD,OAAO,CAAC,KAAK,CAAC,UAAU,IAAI,GAAG,IAAI,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,CAAC;IACzF,CAAC;IAED;;;;OAIG;IACK,mBAAmB,CAAC,OAAe;QACzC,MAAM,SAAS,GAAG,sBAAU,CAAC,gBAAgB,CAAC;QAC9C,OAAO,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;AA9QD,gCA8QC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/src/services/httpClient.ts"], "sourcesContent": ["import { ApiError, NetworkError, RateLimitError } from '../models/Errors';\nimport { API_CONFIG } from '../constants';\n\n/**\n * HTTP request configuration\n */\nexport interface RequestConfig {\n  method?: string;\n  headers?: Record<string, string>;\n  body?: string;\n  timeout?: number;\n  retries?: number;\n}\n\n/**\n * HTTP response interface\n */\nexport interface HttpResponse<T = any> {\n  data: T;\n  status: number;\n  statusText: string;\n  headers: Headers;\n  ok: boolean;\n}\n\n/**\n * Request interceptor function type\n */\nexport type RequestInterceptor = (config: RequestConfig) => RequestConfig;\n\n/**\n * Response interceptor function type\n */\nexport type ResponseInterceptor = <T>(response: T) => T;\n\n/**\n * HTTP client with retry logic, interceptors, and error handling\n */\nexport class HttpClient {\n  private requestInterceptors: RequestInterceptor[] = [];\n  private responseInterceptors: ResponseInterceptor[] = [];\n  private readonly defaultTimeout: number;\n  private readonly maxRetries: number;\n\n  constructor(timeout: number = API_CONFIG.REQUEST_TIMEOUT, maxRetries: number = API_CONFIG.MAX_RETRIES) {\n    this.defaultTimeout = timeout;\n    this.maxRetries = maxRetries;\n  }\n\n  /**\n   * Adds a request interceptor\n   * @param interceptor - Function to modify request config\n   */\n  addRequestInterceptor(interceptor: RequestInterceptor): void {\n    this.requestInterceptors.push(interceptor);\n  }\n\n  /**\n   * Adds a response interceptor\n   * @param interceptor - Function to modify response data\n   */\n  addResponseInterceptor(interceptor: ResponseInterceptor): void {\n    this.responseInterceptors.push(interceptor);\n  }\n\n  /**\n   * Makes a GET request\n   * @param url - Request URL\n   * @param config - Request configuration\n   * @returns Promise resolving to response data\n   */\n  async get<T = any>(url: string, config: Partial<RequestConfig> = {}): Promise<T> {\n    const response = await this.request<T>(url, {\n      ...config,\n      method: 'GET',\n    });\n    return response.data;\n  }\n\n  /**\n   * Makes a POST request\n   * @param url - Request URL\n   * @param data - Request body data\n   * @param config - Request configuration\n   * @returns Promise resolving to response data\n   */\n  async post<T = any>(url: string, data?: any, config: Partial<RequestConfig> = {}): Promise<T> {\n    const response = await this.request<T>(url, {\n      ...config,\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined,\n      headers: {\n        'Content-Type': 'application/json',\n        ...config.headers,\n      },\n    });\n    return response.data;\n  }\n\n  /**\n   * Makes a HEAD request\n   * @param url - Request URL\n   * @param config - Request configuration\n   * @returns Promise resolving to response (without body)\n   */\n  async head(url: string, config: Partial<RequestConfig> = {}): Promise<Omit<HttpResponse, 'data'>> {\n    const response = await this.request(url, {\n      ...config,\n      method: 'HEAD',\n    });\n\n    return {\n      status: response.status,\n      statusText: response.statusText,\n      headers: response.headers,\n      ok: response.ok,\n    };\n  }\n\n  /**\n   * Makes an HTTP request with retry logic\n   * @param url - Request URL\n   * @param config - Request configuration\n   * @returns Promise resolving to HTTP response\n   */\n  private async request<T = any>(url: string, config: RequestConfig): Promise<HttpResponse<T>> {\n    let finalConfig = { ...config };\n\n    // Apply request interceptors\n    for (const interceptor of this.requestInterceptors) {\n      finalConfig = interceptor(finalConfig);\n    }\n\n    const timeout = finalConfig.timeout || this.defaultTimeout;\n    const maxRetries = finalConfig.retries !== undefined ? finalConfig.retries : this.maxRetries;\n\n    let lastError: Error | null = null;\n\n    for (let attempt = 0; attempt <= maxRetries; attempt++) {\n      try {\n        const response = await this.makeRequest<T>(url, finalConfig, timeout);\n\n        // Apply response interceptors\n        let responseData = response.data;\n        for (const interceptor of this.responseInterceptors) {\n          responseData = interceptor(responseData);\n        }\n\n        return {\n          ...response,\n          data: responseData,\n        };\n      } catch (error) {\n        lastError = error as Error;\n\n        // Don't retry on certain errors\n        if (error instanceof ApiError && !this.isRetryableError(error)) {\n          throw error;\n        }\n\n        if (error instanceof RateLimitError) {\n          throw error;\n        }\n\n        // Don't retry on the last attempt\n        if (attempt >= maxRetries) {\n          break;\n        }\n\n        // Wait before retrying (exponential backoff) - but only if we're going to retry\n        if (attempt < maxRetries) {\n          const delay = this.calculateRetryDelay(attempt);\n          await this.sleep(delay);\n        }\n      }\n    }\n\n    // If we get here, all retries failed\n    throw lastError || new NetworkError('All retry attempts failed');\n  }\n\n  /**\n   * Makes the actual HTTP request\n   * @param url - Request URL\n   * @param config - Request configuration\n   * @param timeout - Request timeout\n   * @returns Promise resolving to HTTP response\n   */\n  private async makeRequest<T>(url: string, config: RequestConfig, timeout: number): Promise<HttpResponse<T>> {\n    try {\n      const headers = new Headers({\n        'User-Agent': 'BusinessSearchApp/1.0',\n        ...config.headers,\n      });\n\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), timeout);\n\n      try {\n        const response = await fetch(url, {\n          method: config.method || 'GET',\n          headers,\n          body: config.body,\n          signal: controller.signal,\n        });\n\n        clearTimeout(timeoutId);\n        return await this.processResponse<T>(response, config);\n      } catch (error) {\n        clearTimeout(timeoutId);\n        throw error;\n      }\n    } catch (error) {\n      if (error instanceof ApiError || error instanceof RateLimitError) {\n        throw error;\n      }\n\n      if (error instanceof Error) {\n        if (error.name === 'AbortError') {\n          throw new NetworkError('Request timed out', error);\n        }\n        throw new NetworkError(`Network error: ${error.message}`, error);\n      }\n\n      throw new NetworkError(`Unknown network error: ${String(error)}`);\n    }\n  }\n\n  /**\n   * Processes the HTTP response\n   * @param response - The fetch response\n   * @param config - Request configuration\n   * @returns Promise resolving to HTTP response\n   */\n  private async processResponse<T>(response: Response, config: RequestConfig): Promise<HttpResponse<T>> {\n    // Handle rate limiting\n    if (response.status === 429) {\n      const retryAfter = response.headers.get('Retry-After');\n      const retryAfterSeconds = retryAfter ? parseInt(retryAfter, 10) : undefined;\n      throw new RateLimitError('Rate limit exceeded', retryAfterSeconds);\n    }\n\n    // Handle other HTTP errors\n    if (!response.ok) {\n      const errorText = await response.text().catch(() => 'Unknown error');\n      throw new ApiError(\n        `HTTP ${response.status}: ${response.statusText} - ${errorText}`,\n        response.status\n      );\n    }\n\n    // Parse response data\n    let data: T;\n    const contentType = response.headers.get('content-type');\n\n    if (config.method === 'HEAD') {\n      data = null as any;\n    } else if (contentType && contentType.includes('application/json')) {\n      try {\n        data = await response.json();\n      } catch (error) {\n        throw new NetworkError('JSON parsing failed', error instanceof Error ? error : undefined);\n      }\n    } else {\n      try {\n        data = await response.text() as any;\n      } catch (error) {\n        throw new NetworkError('Text parsing failed', error instanceof Error ? error : undefined);\n      }\n    }\n\n    return {\n      data,\n      status: response.status,\n      statusText: response.statusText,\n      headers: response.headers,\n      ok: response.ok,\n    };\n  }\n\n  /**\n   * Determines if an error should be retried\n   * @param error - Error to check\n   * @returns True if error should be retried\n   */\n  private isRetryableError(error: ApiError): boolean {\n    // Retry on 5xx server errors and 429 rate limit errors\n    return (error.statusCode >= 500 && error.statusCode < 600) || error.statusCode === 429;\n  }\n\n  /**\n   * Calculates retry delay using exponential backoff\n   * @param attempt - The current attempt number (0-based)\n   * @returns Delay in milliseconds\n   */\n  private calculateRetryDelay(attempt: number): number {\n    const baseDelay = API_CONFIG.RETRY_DELAY_BASE;\n    return baseDelay * Math.pow(2, attempt);\n  }\n\n  /**\n   * Sleeps for the specified duration\n   * @param ms - Duration in milliseconds\n   * @returns Promise that resolves after the delay\n   */\n  private sleep(ms: number): Promise<void> {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n}\n"], "version": 3}