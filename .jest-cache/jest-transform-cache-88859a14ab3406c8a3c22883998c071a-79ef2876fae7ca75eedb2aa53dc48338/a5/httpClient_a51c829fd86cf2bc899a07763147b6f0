fee27fc80a1d5f0e62296995482acc04
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpClient = void 0;
const Errors_1 = require("../models/Errors");
const constants_1 = require("../constants");
/**
 * HTTP client with retry logic, interceptors, and error handling
 */
class HttpClient {
    constructor(timeout = constants_1.API_CONFIG.REQUEST_TIMEOUT, maxRetries = constants_1.API_CONFIG.MAX_RETRIES) {
        this.requestInterceptors = [];
        this.responseInterceptors = [];
        this.defaultTimeout = timeout;
        this.maxRetries = maxRetries;
    }
    /**
     * Adds a request interceptor
     * @param interceptor - Function to modify request config
     */
    addRequestInterceptor(interceptor) {
        this.requestInterceptors.push(interceptor);
    }
    /**
     * Adds a response interceptor
     * @param interceptor - Function to modify response data
     */
    addResponseInterceptor(interceptor) {
        this.responseInterceptors.push(interceptor);
    }
    /**
     * Makes a GET request
     * @param url - Request URL
     * @param config - Request configuration
     * @returns Promise resolving to response data
     */
    async get(url, config = {}) {
        const response = await this.request(url, {
            ...config,
            method: 'GET',
        });
        return response.data;
    }
    /**
     * Makes a POST request
     * @param url - Request URL
     * @param data - Request body data
     * @param config - Request configuration
     * @returns Promise resolving to response data
     */
    async post(url, data, config = {}) {
        const response = await this.request(url, {
            ...config,
            method: 'POST',
            body: data ? JSON.stringify(data) : undefined,
            headers: {
                'Content-Type': 'application/json',
                ...config.headers,
            },
        });
        return response.data;
    }
    /**
     * Makes a HEAD request
     * @param url - Request URL
     * @param config - Request configuration
     * @returns Promise resolving to response (without body)
     */
    async head(url, config = {}) {
        const response = await this.request(url, {
            ...config,
            method: 'HEAD',
        });
        return {
            status: response.status,
            statusText: response.statusText,
            headers: response.headers,
            ok: response.ok,
        };
    }
    /**
     * Makes an HTTP request with retry logic
     * @param url - Request URL
     * @param config - Request configuration
     * @returns Promise resolving to HTTP response
     */
    async request(url, config) {
        let finalConfig = { ...config };
        // Apply request interceptors
        for (const interceptor of this.requestInterceptors) {
            finalConfig = interceptor(finalConfig);
        }
        const timeout = finalConfig.timeout || this.defaultTimeout;
        const maxRetries = finalConfig.retries !== undefined ? finalConfig.retries : this.maxRetries;
        let lastError = null;
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                const response = await this.makeRequest(url, finalConfig, timeout);
                // Apply response interceptors
                let responseData = response.data;
                for (const interceptor of this.responseInterceptors) {
                    responseData = interceptor(responseData);
                }
                return {
                    ...response,
                    data: responseData,
                };
            }
            catch (error) {
                lastError = error;
                // Don't retry on certain errors
                if (error instanceof Errors_1.ApiError && !this.isRetryableError(error)) {
                    throw error;
                }
                if (error instanceof Errors_1.RateLimitError) {
                    throw error;
                }
                // Don't retry on the last attempt
                if (attempt >= maxRetries) {
                    break;
                }
                // Wait before retrying (exponential backoff) - but only if we're going to retry
                if (attempt < maxRetries) {
                    const delay = this.calculateRetryDelay(attempt);
                    await this.sleep(delay);
                }
            }
        }
        // If we get here, all retries failed
        throw lastError || new Errors_1.NetworkError('All retry attempts failed');
    }
    /**
     * Makes the actual HTTP request
     * @param url - Request URL
     * @param config - Request configuration
     * @param timeout - Request timeout
     * @returns Promise resolving to HTTP response
     */
    async makeRequest(url, config, timeout) {
        try {
            const headers = new Headers({
                'User-Agent': 'BusinessSearchApp/1.0',
                ...config.headers,
            });
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeout);
            try {
                const response = await fetch(url, {
                    method: config.method || 'GET',
                    headers,
                    body: config.body,
                    signal: controller.signal,
                });
                clearTimeout(timeoutId);
                return await this.processResponse(response, config);
            }
            catch (error) {
                clearTimeout(timeoutId);
                throw error;
            }
        }
        catch (error) {
            if (error instanceof Errors_1.ApiError || error instanceof Errors_1.RateLimitError) {
                throw error;
            }
            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    throw new Errors_1.NetworkError('Request timed out', error);
                }
                throw new Errors_1.NetworkError(`Network error: ${error.message}`, error);
            }
            throw new Errors_1.NetworkError(`Unknown network error: ${String(error)}`);
        }
    }
    /**
     * Processes the HTTP response
     * @param response - The fetch response
     * @param config - Request configuration
     * @returns Promise resolving to HTTP response
     */
    async processResponse(response, config) {
        // Handle rate limiting
        if (response.status === 429) {
            const retryAfter = response.headers.get('Retry-After');
            const retryAfterSeconds = retryAfter ? parseInt(retryAfter, 10) : undefined;
            throw new Errors_1.RateLimitError('Rate limit exceeded', retryAfterSeconds);
        }
        // Handle other HTTP errors
        if (!response.ok) {
            const errorText = await response.text().catch(() => 'Unknown error');
            throw new Errors_1.ApiError(`HTTP ${response.status}: ${response.statusText} - ${errorText}`, response.status);
        }
        // Parse response data
        let data;
        const contentType = response.headers.get('content-type');
        if (config.method === 'HEAD') {
            data = null;
        }
        else if (contentType && contentType.includes('application/json')) {
            try {
                data = await response.json();
            }
            catch (error) {
                throw new Errors_1.NetworkError('JSON parsing failed', error instanceof Error ? error : undefined);
            }
        }
        else {
            try {
                data = await response.text();
            }
            catch (error) {
                throw new Errors_1.NetworkError('Text parsing failed', error instanceof Error ? error : undefined);
            }
        }
        return {
            data,
            status: response.status,
            statusText: response.statusText,
            headers: response.headers,
            ok: response.ok,
        };
    }
    /**
     * Determines if an error should be retried
     * @param error - Error to check
     * @returns True if error should be retried
     */
    isRetryableError(error) {
        // Retry on 5xx server errors and 429 rate limit errors
        return (error.statusCode >= 500 && error.statusCode < 600) || error.statusCode === 429;
    }
    /**
     * Calculates retry delay using exponential backoff
     * @param attempt - The current attempt number (0-based)
     * @returns Delay in milliseconds
     */
    calculateRetryDelay(attempt) {
        const baseDelay = constants_1.API_CONFIG.RETRY_DELAY_BASE;
        return baseDelay * Math.pow(2, attempt);
    }
    /**
     * Sleeps for the specified duration
     * @param ms - Duration in milliseconds
     * @returns Promise that resolves after the delay
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
exports.HttpClient = HttpClient;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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