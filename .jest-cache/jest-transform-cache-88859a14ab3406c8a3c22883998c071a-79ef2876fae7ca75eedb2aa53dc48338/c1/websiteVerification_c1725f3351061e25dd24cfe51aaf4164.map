{"version": 3, "names": ["cov_1oib1sohgb", "actualCoverage", "s", "Errors_1", "require", "url_1", "constants_1", "WebsiteVerificationService", "constructor", "f", "cache", "Map", "stats", "totalVerified", "successfulVerifications", "failedVerifications", "totalResponseTime", "verifyWebsite", "url", "isValidUrl", "b", "WebsiteVerificationError", "normalizedUrl", "normalizeUrl", "cached", "get", "startTime", "Date", "now", "result", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "WEBSITE_CONFIG", "TIMEOUT", "response", "fetch", "method", "headers", "USER_AGENT", "signal", "redirect", "clearTimeout", "responseTime", "contentType", "isAccessible", "VALID_STATUS_CODES", "includes", "status", "confidence", "calculateConfidence", "accessible", "statusCode", "verifiedAt", "error", "errorMessage", "Error", "name", "message", "set", "verifyMultipleWebsites", "urls", "concurrency", "results", "i", "length", "batch", "slice", "batchPromises", "map", "catch", "batchResults", "Promise", "all", "push", "Math", "max", "min", "getVerificationStats", "successRate", "averageResponseTime", "clearCache", "clear", "getCacheSize", "size", "resetStats", "exports"], "sources": ["/Users/<USER>/WebstormProjects/goo/src/services/websiteVerification.ts"], "sourcesContent": ["import {WebsiteStatus} from '../models/Business';\nimport {WebsiteVerificationError, NetworkError} from '../models/Errors';\nimport {validateUrl} from '../utils/validation';\nimport {normalizeUrl, isValidUrl} from '../utils/url';\nimport {WEBSITE_CONFIG} from '../constants';\n\n/**\n * Website verification result\n */\nexport interface VerificationResult {\n    url: string;\n    status: WebsiteStatus;\n    accessible: boolean;\n    statusCode?: number;\n    responseTime?: number;\n    contentType?: string;\n    confidence: number;\n    error?: string;\n    verifiedAt: Date;\n}\n\n/**\n * Website verification statistics\n */\nexport interface VerificationStats {\n    totalVerified: number;\n    successfulVerifications: number;\n    failedVerifications: number;\n    successRate: number;\n    averageResponseTime: number;\n}\n\n/**\n * Service for verifying website accessibility and status\n */\nexport class WebsiteVerificationService {\n    private cache = new Map<string, VerificationResult>();\n    private stats = {\n        totalVerified: 0,\n        successfulVerifications: 0,\n        failedVerifications: 0,\n        totalResponseTime: 0,\n    };\n\n    /**\n     * Verifies if a website is accessible\n     * @param url - The URL to verify\n     * @returns Promise resolving to verification result\n     */\n    async verifyWebsite(url: string): Promise<VerificationResult> {\n        // Validate and normalize URL\n        if (!isValidUrl(url)) {\n            throw new WebsiteVerificationError(`Invalid URL format: ${url}`, url);\n        }\n\n        const normalizedUrl = normalizeUrl(url);\n\n        // Check cache first\n        const cached = this.cache.get(normalizedUrl);\n        if (cached) {\n            return cached;\n        }\n\n        const startTime = Date.now();\n        let result: VerificationResult;\n\n        try {\n            const controller = new AbortController();\n            const timeoutId = setTimeout(() => controller.abort(), WEBSITE_CONFIG.TIMEOUT);\n\n            const response = await fetch(normalizedUrl, {\n                method: 'HEAD',\n                headers: {\n                    'User-Agent': WEBSITE_CONFIG.USER_AGENT,\n                },\n                signal: controller.signal,\n                redirect: 'manual', // Handle redirects manually to track them\n            });\n\n            clearTimeout(timeoutId);\n            const responseTime = Date.now() - startTime;\n            const contentType = response.headers.get('content-type') || '';\n\n            // Check if the status code indicates success\n            const isAccessible = WEBSITE_CONFIG.VALID_STATUS_CODES.includes(response.status);\n            const confidence = this.calculateConfidence(response.status, contentType, responseTime);\n\n            result = {\n                url: normalizedUrl,\n                status: isAccessible ? 'verified' : 'unverified',\n                accessible: isAccessible,\n                statusCode: response.status,\n                responseTime,\n                contentType,\n                confidence,\n                verifiedAt: new Date(),\n            };\n\n            if (isAccessible) {\n                this.stats.successfulVerifications++;\n            } else {\n                this.stats.failedVerifications++;\n            }\n            this.stats.totalResponseTime += responseTime;\n\n        } catch (error) {\n            const responseTime = Date.now() - startTime;\n            let errorMessage = 'Unknown error';\n\n            if (error instanceof Error) {\n                if (error.name === 'AbortError') {\n                    errorMessage = 'Request timeout';\n                } else {\n                    errorMessage = error.message;\n                }\n            }\n\n            result = {\n                url: normalizedUrl,\n                status: 'unverified',\n                accessible: false,\n                responseTime,\n                confidence: 0.1,\n                error: errorMessage,\n                verifiedAt: new Date(),\n            };\n\n            this.stats.failedVerifications++;\n            this.stats.totalResponseTime += responseTime;\n        }\n\n        this.stats.totalVerified++;\n\n        // Cache the result\n        this.cache.set(normalizedUrl, result);\n\n        return result;\n    }\n\n    /**\n     * Verifies multiple websites concurrently\n     * @param urls - Array of URLs to verify\n     * @param concurrency - Maximum number of concurrent requests\n     * @returns Promise resolving to array of verification results\n     */\n    async verifyMultipleWebsites(\n        urls: string[],\n        concurrency: number = 5\n    ): Promise<VerificationResult[]> {\n        const results: VerificationResult[] = [];\n\n        // Process URLs in batches to respect concurrency limit\n        for (let i = 0; i < urls.length; i += concurrency) {\n            const batch = urls.slice(i, i + concurrency);\n            const batchPromises = batch.map(url =>\n                this.verifyWebsite(url).catch(error => ({\n                    url,\n                    status: 'unverified' as WebsiteStatus,\n                    accessible: false,\n                    confidence: 0,\n                    error: error.message,\n                    verifiedAt: new Date(),\n                }))\n            );\n\n            const batchResults = await Promise.all(batchPromises);\n            results.push(...batchResults);\n        }\n\n        return results;\n    }\n\n    /**\n     * Calculates confidence score for website verification\n     * @param statusCode - HTTP status code\n     * @param contentType - Response content type\n     * @param responseTime - Response time in milliseconds\n     * @returns Confidence score between 0 and 1\n     */\n    calculateConfidence(statusCode: number, contentType: string, responseTime: number): number {\n        let confidence = 0;\n\n        // Base confidence from status code\n        if (statusCode >= 200 && statusCode < 300) {\n            confidence += 0.6; // Success responses\n        } else if (statusCode >= 300 && statusCode < 400) {\n            confidence += 0.4; // Redirects\n        } else if (statusCode >= 400 && statusCode < 500) {\n            confidence += 0.05; // Client errors\n        } else {\n            confidence += 0.05; // Server errors\n        }\n\n        // Bonus for HTML content\n        if (contentType.includes('text/html')) {\n            confidence += 0.2;\n        } else if (contentType.includes('text/')) {\n            confidence += 0.1;\n        }\n\n        // Response time factor (faster is better)\n        if (responseTime < 1000) {\n            confidence += 0.2;\n        } else if (responseTime < 3000) {\n            confidence += 0.1;\n        } else if (responseTime > 5000) {\n            confidence -= 0.1;\n        }\n\n        return Math.max(0, Math.min(1, confidence));\n    }\n\n    /**\n     * Gets verification statistics\n     * @returns Verification statistics\n     */\n    getVerificationStats(): VerificationStats {\n        return {\n            totalVerified: this.stats.totalVerified,\n            successfulVerifications: this.stats.successfulVerifications,\n            failedVerifications: this.stats.failedVerifications,\n            successRate: this.stats.totalVerified > 0\n                ? this.stats.successfulVerifications / this.stats.totalVerified\n                : 0,\n            averageResponseTime: this.stats.totalVerified > 0\n                ? this.stats.totalResponseTime / this.stats.totalVerified\n                : 0,\n        };\n    }\n\n    /**\n     * Clears the verification cache\n     */\n    clearCache(): void {\n        this.cache.clear();\n    }\n\n    /**\n     * Gets the current cache size\n     * @returns Number of cached verification results\n     */\n    getCacheSize(): number {\n        return this.cache.size;\n    }\n\n    /**\n     * Resets verification statistics\n     */\n    resetStats(): void {\n        this.stats = {\n            totalVerified: 0,\n            successfulVerifications: 0,\n            failedVerifications: 0,\n            totalResponseTime: 0,\n        };\n    }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAwCQ;IAAAA,cAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,cAAA;AAAAA,cAAA,GAAAE,CAAA;;;;;;;AAvCR,MAAAC,QAAA;AAAA;AAAA,CAAAH,cAAA,GAAAE,CAAA,OAAAE,OAAA;AAEA,MAAAC,KAAA;AAAA;AAAA,CAAAL,cAAA,GAAAE,CAAA,OAAAE,OAAA;AACA,MAAAE,WAAA;AAAA;AAAA,CAAAN,cAAA,GAAAE,CAAA,OAAAE,OAAA;AA4BA;;;AAGA,MAAaG,0BAA0B;EAAvCC,YAAA;IAAA;IAAAR,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAE,CAAA;IACY,KAAAQ,KAAK,GAAG,IAAIC,GAAG,EAA8B;IAAC;IAAAX,cAAA,GAAAE,CAAA;IAC9C,KAAAU,KAAK,GAAG;MACZC,aAAa,EAAE,CAAC;MAChBC,uBAAuB,EAAE,CAAC;MAC1BC,mBAAmB,EAAE,CAAC;MACtBC,iBAAiB,EAAE;KACtB;EAsNL;EApNI;;;;;EAKA,MAAMC,aAAaA,CAACC,GAAW;IAAA;IAAAlB,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAE,CAAA;IAC3B;IACA,IAAI,CAAC,IAAAG,KAAA,CAAAc,UAAU,EAACD,GAAG,CAAC,EAAE;MAAA;MAAAlB,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAE,CAAA;MAClB,MAAM,IAAIC,QAAA,CAAAkB,wBAAwB,CAAC,uBAAuBH,GAAG,EAAE,EAAEA,GAAG,CAAC;IACzE,CAAC;IAAA;IAAA;MAAAlB,cAAA,GAAAoB,CAAA;IAAA;IAED,MAAME,aAAa;IAAA;IAAA,CAAAtB,cAAA,GAAAE,CAAA,OAAG,IAAAG,KAAA,CAAAkB,YAAY,EAACL,GAAG,CAAC;IAEvC;IACA,MAAMM,MAAM;IAAA;IAAA,CAAAxB,cAAA,GAAAE,CAAA,QAAG,IAAI,CAACQ,KAAK,CAACe,GAAG,CAACH,aAAa,CAAC;IAAC;IAAAtB,cAAA,GAAAE,CAAA;IAC7C,IAAIsB,MAAM,EAAE;MAAA;MAAAxB,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAE,CAAA;MACR,OAAOsB,MAAM;IACjB,CAAC;IAAA;IAAA;MAAAxB,cAAA,GAAAoB,CAAA;IAAA;IAED,MAAMM,SAAS;IAAA;IAAA,CAAA1B,cAAA,GAAAE,CAAA,QAAGyB,IAAI,CAACC,GAAG,EAAE;IAC5B,IAAIC,MAA0B;IAAC;IAAA7B,cAAA,GAAAE,CAAA;IAE/B,IAAI;MACA,MAAM4B,UAAU;MAAA;MAAA,CAAA9B,cAAA,GAAAE,CAAA,QAAG,IAAI6B,eAAe,EAAE;MACxC,MAAMC,SAAS;MAAA;MAAA,CAAAhC,cAAA,GAAAE,CAAA,QAAG+B,UAAU,CAAC,MAAM;QAAA;QAAAjC,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAE,CAAA;QAAA,OAAA4B,UAAU,CAACI,KAAK,EAAE;MAAF,CAAE,EAAE5B,WAAA,CAAA6B,cAAc,CAACC,OAAO,CAAC;MAE9E,MAAMC,QAAQ;MAAA;MAAA,CAAArC,cAAA,GAAAE,CAAA,QAAG,MAAMoC,KAAK,CAAChB,aAAa,EAAE;QACxCiB,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACL,YAAY,EAAElC,WAAA,CAAA6B,cAAc,CAACM;SAChC;QACDC,MAAM,EAAEZ,UAAU,CAACY,MAAM;QACzBC,QAAQ,EAAE,QAAQ,CAAE;OACvB,CAAC;MAAC;MAAA3C,cAAA,GAAAE,CAAA;MAEH0C,YAAY,CAACZ,SAAS,CAAC;MACvB,MAAMa,YAAY;MAAA;MAAA,CAAA7C,cAAA,GAAAE,CAAA,QAAGyB,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;MAC3C,MAAMoB,WAAW;MAAA;MAAA,CAAA9C,cAAA,GAAAE,CAAA;MAAG;MAAA,CAAAF,cAAA,GAAAoB,CAAA,UAAAiB,QAAQ,CAACG,OAAO,CAACf,GAAG,CAAC,cAAc,CAAC;MAAA;MAAA,CAAAzB,cAAA,GAAAoB,CAAA,UAAI,EAAE;MAE9D;MACA,MAAM2B,YAAY;MAAA;MAAA,CAAA/C,cAAA,GAAAE,CAAA,QAAGI,WAAA,CAAA6B,cAAc,CAACa,kBAAkB,CAACC,QAAQ,CAACZ,QAAQ,CAACa,MAAM,CAAC;MAChF,MAAMC,UAAU;MAAA;MAAA,CAAAnD,cAAA,GAAAE,CAAA,QAAG,IAAI,CAACkD,mBAAmB,CAACf,QAAQ,CAACa,MAAM,EAAEJ,WAAW,EAAED,YAAY,CAAC;MAAC;MAAA7C,cAAA,GAAAE,CAAA;MAExF2B,MAAM,GAAG;QACLX,GAAG,EAAEI,aAAa;QAClB4B,MAAM,EAAEH,YAAY;QAAA;QAAA,CAAA/C,cAAA,GAAAoB,CAAA,UAAG,UAAU;QAAA;QAAA,CAAApB,cAAA,GAAAoB,CAAA,UAAG,YAAY;QAChDiC,UAAU,EAAEN,YAAY;QACxBO,UAAU,EAAEjB,QAAQ,CAACa,MAAM;QAC3BL,YAAY;QACZC,WAAW;QACXK,UAAU;QACVI,UAAU,EAAE,IAAI5B,IAAI;OACvB;MAAC;MAAA3B,cAAA,GAAAE,CAAA;MAEF,IAAI6C,YAAY,EAAE;QAAA;QAAA/C,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAE,CAAA;QACd,IAAI,CAACU,KAAK,CAACE,uBAAuB,EAAE;MACxC,CAAC,MAAM;QAAA;QAAAd,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAE,CAAA;QACH,IAAI,CAACU,KAAK,CAACG,mBAAmB,EAAE;MACpC;MAAC;MAAAf,cAAA,GAAAE,CAAA;MACD,IAAI,CAACU,KAAK,CAACI,iBAAiB,IAAI6B,YAAY;IAEhD,CAAC,CAAC,OAAOW,KAAK,EAAE;MACZ,MAAMX,YAAY;MAAA;MAAA,CAAA7C,cAAA,GAAAE,CAAA,QAAGyB,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS;MAC3C,IAAI+B,YAAY;MAAA;MAAA,CAAAzD,cAAA,GAAAE,CAAA,QAAG,eAAe;MAAC;MAAAF,cAAA,GAAAE,CAAA;MAEnC,IAAIsD,KAAK,YAAYE,KAAK,EAAE;QAAA;QAAA1D,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAE,CAAA;QACxB,IAAIsD,KAAK,CAACG,IAAI,KAAK,YAAY,EAAE;UAAA;UAAA3D,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAE,CAAA;UAC7BuD,YAAY,GAAG,iBAAiB;QACpC,CAAC,MAAM;UAAA;UAAAzD,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAE,CAAA;UACHuD,YAAY,GAAGD,KAAK,CAACI,OAAO;QAChC;MACJ,CAAC;MAAA;MAAA;QAAA5D,cAAA,GAAAoB,CAAA;MAAA;MAAApB,cAAA,GAAAE,CAAA;MAED2B,MAAM,GAAG;QACLX,GAAG,EAAEI,aAAa;QAClB4B,MAAM,EAAE,YAAY;QACpBG,UAAU,EAAE,KAAK;QACjBR,YAAY;QACZM,UAAU,EAAE,GAAG;QACfK,KAAK,EAAEC,YAAY;QACnBF,UAAU,EAAE,IAAI5B,IAAI;OACvB;MAAC;MAAA3B,cAAA,GAAAE,CAAA;MAEF,IAAI,CAACU,KAAK,CAACG,mBAAmB,EAAE;MAAC;MAAAf,cAAA,GAAAE,CAAA;MACjC,IAAI,CAACU,KAAK,CAACI,iBAAiB,IAAI6B,YAAY;IAChD;IAAC;IAAA7C,cAAA,GAAAE,CAAA;IAED,IAAI,CAACU,KAAK,CAACC,aAAa,EAAE;IAE1B;IAAA;IAAAb,cAAA,GAAAE,CAAA;IACA,IAAI,CAACQ,KAAK,CAACmD,GAAG,CAACvC,aAAa,EAAEO,MAAM,CAAC;IAAC;IAAA7B,cAAA,GAAAE,CAAA;IAEtC,OAAO2B,MAAM;EACjB;EAEA;;;;;;EAMA,MAAMiC,sBAAsBA,CACxBC,IAAc,EACdC,WAAA;EAAA;EAAA,CAAAhE,cAAA,GAAAoB,CAAA,UAAsB,CAAC;IAAA;IAAApB,cAAA,GAAAS,CAAA;IAEvB,MAAMwD,OAAO;IAAA;IAAA,CAAAjE,cAAA,GAAAE,CAAA,QAAyB,EAAE;IAExC;IAAA;IAAAF,cAAA,GAAAE,CAAA;IACA,KAAK,IAAIgE,CAAC;IAAA;IAAA,CAAAlE,cAAA,GAAAE,CAAA,QAAG,CAAC,GAAEgE,CAAC,GAAGH,IAAI,CAACI,MAAM,EAAED,CAAC,IAAIF,WAAW,EAAE;MAC/C,MAAMI,KAAK;MAAA;MAAA,CAAApE,cAAA,GAAAE,CAAA,QAAG6D,IAAI,CAACM,KAAK,CAACH,CAAC,EAAEA,CAAC,GAAGF,WAAW,CAAC;MAC5C,MAAMM,aAAa;MAAA;MAAA,CAAAtE,cAAA,GAAAE,CAAA,QAAGkE,KAAK,CAACG,GAAG,CAACrD,GAAG,IAC/B;QAAA;QAAAlB,cAAA,GAAAS,CAAA;QAAAT,cAAA,GAAAE,CAAA;QAAA,WAAI,CAACe,aAAa,CAACC,GAAG,CAAC,CAACsD,KAAK,CAAChB,KAAK,IAAK;UAAA;UAAAxD,cAAA,GAAAS,CAAA;UAAAT,cAAA,GAAAE,CAAA;UAAA;YACpCgB,GAAG;YACHgC,MAAM,EAAE,YAA6B;YACrCG,UAAU,EAAE,KAAK;YACjBF,UAAU,EAAE,CAAC;YACbK,KAAK,EAAEA,KAAK,CAACI,OAAO;YACpBL,UAAU,EAAE,IAAI5B,IAAI;WACvB;SAAC,CAAC;MAAD,CAAC,CACN;MAED,MAAM8C,YAAY;MAAA;MAAA,CAAAzE,cAAA,GAAAE,CAAA,QAAG,MAAMwE,OAAO,CAACC,GAAG,CAACL,aAAa,CAAC;MAAC;MAAAtE,cAAA,GAAAE,CAAA;MACtD+D,OAAO,CAACW,IAAI,CAAC,GAAGH,YAAY,CAAC;IACjC;IAAC;IAAAzE,cAAA,GAAAE,CAAA;IAED,OAAO+D,OAAO;EAClB;EAEA;;;;;;;EAOAb,mBAAmBA,CAACE,UAAkB,EAAER,WAAmB,EAAED,YAAoB;IAAA;IAAA7C,cAAA,GAAAS,CAAA;IAC7E,IAAI0C,UAAU;IAAA;IAAA,CAAAnD,cAAA,GAAAE,CAAA,QAAG,CAAC;IAElB;IAAA;IAAAF,cAAA,GAAAE,CAAA;IACA;IAAI;IAAA,CAAAF,cAAA,GAAAoB,CAAA,UAAAkC,UAAU,IAAI,GAAG;IAAA;IAAA,CAAAtD,cAAA,GAAAoB,CAAA,UAAIkC,UAAU,GAAG,GAAG,GAAE;MAAA;MAAAtD,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAE,CAAA;MACvCiD,UAAU,IAAI,GAAG,CAAC,CAAC;IACvB,CAAC,MAAM;MAAA;MAAAnD,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAE,CAAA;MAAA;MAAI;MAAA,CAAAF,cAAA,GAAAoB,CAAA,WAAAkC,UAAU,IAAI,GAAG;MAAA;MAAA,CAAAtD,cAAA,GAAAoB,CAAA,WAAIkC,UAAU,GAAG,GAAG,GAAE;QAAA;QAAAtD,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAE,CAAA;QAC9CiD,UAAU,IAAI,GAAG,CAAC,CAAC;MACvB,CAAC,MAAM;QAAA;QAAAnD,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAE,CAAA;QAAA;QAAI;QAAA,CAAAF,cAAA,GAAAoB,CAAA,WAAAkC,UAAU,IAAI,GAAG;QAAA;QAAA,CAAAtD,cAAA,GAAAoB,CAAA,WAAIkC,UAAU,GAAG,GAAG,GAAE;UAAA;UAAAtD,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAE,CAAA;UAC9CiD,UAAU,IAAI,IAAI,CAAC,CAAC;QACxB,CAAC,MAAM;UAAA;UAAAnD,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAE,CAAA;UACHiD,UAAU,IAAI,IAAI,CAAC,CAAC;QACxB;MAAA;IAAA;IAEA;IAAA;IAAAnD,cAAA,GAAAE,CAAA;IACA,IAAI4C,WAAW,CAACG,QAAQ,CAAC,WAAW,CAAC,EAAE;MAAA;MAAAjD,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAE,CAAA;MACnCiD,UAAU,IAAI,GAAG;IACrB,CAAC,MAAM;MAAA;MAAAnD,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAE,CAAA;MAAA,IAAI4C,WAAW,CAACG,QAAQ,CAAC,OAAO,CAAC,EAAE;QAAA;QAAAjD,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAE,CAAA;QACtCiD,UAAU,IAAI,GAAG;MACrB,CAAC;MAAA;MAAA;QAAAnD,cAAA,GAAAoB,CAAA;MAAA;IAAD;IAEA;IAAA;IAAApB,cAAA,GAAAE,CAAA;IACA,IAAI2C,YAAY,GAAG,IAAI,EAAE;MAAA;MAAA7C,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAE,CAAA;MACrBiD,UAAU,IAAI,GAAG;IACrB,CAAC,MAAM;MAAA;MAAAnD,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAE,CAAA;MAAA,IAAI2C,YAAY,GAAG,IAAI,EAAE;QAAA;QAAA7C,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAE,CAAA;QAC5BiD,UAAU,IAAI,GAAG;MACrB,CAAC,MAAM;QAAA;QAAAnD,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAE,CAAA;QAAA,IAAI2C,YAAY,GAAG,IAAI,EAAE;UAAA;UAAA7C,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAE,CAAA;UAC5BiD,UAAU,IAAI,GAAG;QACrB,CAAC;QAAA;QAAA;UAAAnD,cAAA,GAAAoB,CAAA;QAAA;MAAD;IAAA;IAAC;IAAApB,cAAA,GAAAE,CAAA;IAED,OAAO2E,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE5B,UAAU,CAAC,CAAC;EAC/C;EAEA;;;;EAIA6B,oBAAoBA,CAAA;IAAA;IAAAhF,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAE,CAAA;IAChB,OAAO;MACHW,aAAa,EAAE,IAAI,CAACD,KAAK,CAACC,aAAa;MACvCC,uBAAuB,EAAE,IAAI,CAACF,KAAK,CAACE,uBAAuB;MAC3DC,mBAAmB,EAAE,IAAI,CAACH,KAAK,CAACG,mBAAmB;MACnDkE,WAAW,EAAE,IAAI,CAACrE,KAAK,CAACC,aAAa,GAAG,CAAC;MAAA;MAAA,CAAAb,cAAA,GAAAoB,CAAA,WACnC,IAAI,CAACR,KAAK,CAACE,uBAAuB,GAAG,IAAI,CAACF,KAAK,CAACC,aAAa;MAAA;MAAA,CAAAb,cAAA,GAAAoB,CAAA,WAC7D,CAAC;MACP8D,mBAAmB,EAAE,IAAI,CAACtE,KAAK,CAACC,aAAa,GAAG,CAAC;MAAA;MAAA,CAAAb,cAAA,GAAAoB,CAAA,WAC3C,IAAI,CAACR,KAAK,CAACI,iBAAiB,GAAG,IAAI,CAACJ,KAAK,CAACC,aAAa;MAAA;MAAA,CAAAb,cAAA,GAAAoB,CAAA,WACvD,CAAC;KACV;EACL;EAEA;;;EAGA+D,UAAUA,CAAA;IAAA;IAAAnF,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAE,CAAA;IACN,IAAI,CAACQ,KAAK,CAAC0E,KAAK,EAAE;EACtB;EAEA;;;;EAIAC,YAAYA,CAAA;IAAA;IAAArF,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAE,CAAA;IACR,OAAO,IAAI,CAACQ,KAAK,CAAC4E,IAAI;EAC1B;EAEA;;;EAGAC,UAAUA,CAAA;IAAA;IAAAvF,cAAA,GAAAS,CAAA;IAAAT,cAAA,GAAAE,CAAA;IACN,IAAI,CAACU,KAAK,GAAG;MACTC,aAAa,EAAE,CAAC;MAChBC,uBAAuB,EAAE,CAAC;MAC1BC,mBAAmB,EAAE,CAAC;MACtBC,iBAAiB,EAAE;KACtB;EACL;;AACH;AAAAhB,cAAA,GAAAE,CAAA;AA7NDsF,OAAA,CAAAjF,0BAAA,GAAAA,0BAAA", "ignoreList": []}