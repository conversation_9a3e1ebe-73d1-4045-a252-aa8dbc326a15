13fb157c81c6bce6c516b8413415e0cf
"use strict";

/* istanbul ignore next */
function cov_1oib1sohgb() {
  var path = "/Users/<USER>/WebstormProjects/goo/src/services/websiteVerification.ts";
  var hash = "77bccea3643fa01cdaa8c97d3c69ba6cf81e2d95";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/WebstormProjects/goo/src/services/websiteVerification.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 3,
          column: 44
        }
      },
      "2": {
        start: {
          line: 4,
          column: 17
        },
        end: {
          line: 4,
          column: 44
        }
      },
      "3": {
        start: {
          line: 5,
          column: 14
        },
        end: {
          line: 5,
          column: 37
        }
      },
      "4": {
        start: {
          line: 6,
          column: 20
        },
        end: {
          line: 6,
          column: 43
        }
      },
      "5": {
        start: {
          line: 12,
          column: 8
        },
        end: {
          line: 12,
          column: 31
        }
      },
      "6": {
        start: {
          line: 13,
          column: 8
        },
        end: {
          line: 18,
          column: 10
        }
      },
      "7": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 29,
          column: 9
        }
      },
      "8": {
        start: {
          line: 28,
          column: 12
        },
        end: {
          line: 28,
          column: 91
        }
      },
      "9": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 58
        }
      },
      "10": {
        start: {
          line: 32,
          column: 23
        },
        end: {
          line: 32,
          column: 52
        }
      },
      "11": {
        start: {
          line: 33,
          column: 8
        },
        end: {
          line: 35,
          column: 9
        }
      },
      "12": {
        start: {
          line: 34,
          column: 12
        },
        end: {
          line: 34,
          column: 26
        }
      },
      "13": {
        start: {
          line: 36,
          column: 26
        },
        end: {
          line: 36,
          column: 36
        }
      },
      "14": {
        start: {
          line: 38,
          column: 8
        },
        end: {
          line: 95,
          column: 9
        }
      },
      "15": {
        start: {
          line: 39,
          column: 31
        },
        end: {
          line: 39,
          column: 52
        }
      },
      "16": {
        start: {
          line: 40,
          column: 30
        },
        end: {
          line: 40,
          column: 102
        }
      },
      "17": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 65
        }
      },
      "18": {
        start: {
          line: 41,
          column: 29
        },
        end: {
          line: 48,
          column: 14
        }
      },
      "19": {
        start: {
          line: 49,
          column: 12
        },
        end: {
          line: 49,
          column: 36
        }
      },
      "20": {
        start: {
          line: 50,
          column: 33
        },
        end: {
          line: 50,
          column: 55
        }
      },
      "21": {
        start: {
          line: 51,
          column: 32
        },
        end: {
          line: 51,
          column: 74
        }
      },
      "22": {
        start: {
          line: 53,
          column: 33
        },
        end: {
          line: 53,
          column: 104
        }
      },
      "23": {
        start: {
          line: 54,
          column: 31
        },
        end: {
          line: 54,
          column: 99
        }
      },
      "24": {
        start: {
          line: 55,
          column: 12
        },
        end: {
          line: 64,
          column: 14
        }
      },
      "25": {
        start: {
          line: 65,
          column: 12
        },
        end: {
          line: 70,
          column: 13
        }
      },
      "26": {
        start: {
          line: 66,
          column: 16
        },
        end: {
          line: 66,
          column: 53
        }
      },
      "27": {
        start: {
          line: 69,
          column: 16
        },
        end: {
          line: 69,
          column: 49
        }
      },
      "28": {
        start: {
          line: 71,
          column: 12
        },
        end: {
          line: 71,
          column: 57
        }
      },
      "29": {
        start: {
          line: 74,
          column: 33
        },
        end: {
          line: 74,
          column: 55
        }
      },
      "30": {
        start: {
          line: 75,
          column: 31
        },
        end: {
          line: 75,
          column: 46
        }
      },
      "31": {
        start: {
          line: 76,
          column: 12
        },
        end: {
          line: 83,
          column: 13
        }
      },
      "32": {
        start: {
          line: 77,
          column: 16
        },
        end: {
          line: 82,
          column: 17
        }
      },
      "33": {
        start: {
          line: 78,
          column: 20
        },
        end: {
          line: 78,
          column: 53
        }
      },
      "34": {
        start: {
          line: 81,
          column: 20
        },
        end: {
          line: 81,
          column: 49
        }
      },
      "35": {
        start: {
          line: 84,
          column: 12
        },
        end: {
          line: 92,
          column: 14
        }
      },
      "36": {
        start: {
          line: 93,
          column: 12
        },
        end: {
          line: 93,
          column: 45
        }
      },
      "37": {
        start: {
          line: 94,
          column: 12
        },
        end: {
          line: 94,
          column: 57
        }
      },
      "38": {
        start: {
          line: 96,
          column: 8
        },
        end: {
          line: 96,
          column: 35
        }
      },
      "39": {
        start: {
          line: 98,
          column: 8
        },
        end: {
          line: 98,
          column: 46
        }
      },
      "40": {
        start: {
          line: 99,
          column: 8
        },
        end: {
          line: 99,
          column: 22
        }
      },
      "41": {
        start: {
          line: 108,
          column: 24
        },
        end: {
          line: 108,
          column: 26
        }
      },
      "42": {
        start: {
          line: 110,
          column: 8
        },
        end: {
          line: 122,
          column: 9
        }
      },
      "43": {
        start: {
          line: 110,
          column: 21
        },
        end: {
          line: 110,
          column: 22
        }
      },
      "44": {
        start: {
          line: 111,
          column: 26
        },
        end: {
          line: 111,
          column: 56
        }
      },
      "45": {
        start: {
          line: 112,
          column: 34
        },
        end: {
          line: 119,
          column: 16
        }
      },
      "46": {
        start: {
          line: 112,
          column: 51
        },
        end: {
          line: 119,
          column: 15
        }
      },
      "47": {
        start: {
          line: 112,
          column: 91
        },
        end: {
          line: 119,
          column: 13
        }
      },
      "48": {
        start: {
          line: 120,
          column: 33
        },
        end: {
          line: 120,
          column: 65
        }
      },
      "49": {
        start: {
          line: 121,
          column: 12
        },
        end: {
          line: 121,
          column: 42
        }
      },
      "50": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 123,
          column: 23
        }
      },
      "51": {
        start: {
          line: 133,
          column: 25
        },
        end: {
          line: 133,
          column: 26
        }
      },
      "52": {
        start: {
          line: 135,
          column: 8
        },
        end: {
          line: 146,
          column: 9
        }
      },
      "53": {
        start: {
          line: 136,
          column: 12
        },
        end: {
          line: 136,
          column: 30
        }
      },
      "54": {
        start: {
          line: 138,
          column: 13
        },
        end: {
          line: 146,
          column: 9
        }
      },
      "55": {
        start: {
          line: 139,
          column: 12
        },
        end: {
          line: 139,
          column: 30
        }
      },
      "56": {
        start: {
          line: 141,
          column: 13
        },
        end: {
          line: 146,
          column: 9
        }
      },
      "57": {
        start: {
          line: 142,
          column: 12
        },
        end: {
          line: 142,
          column: 31
        }
      },
      "58": {
        start: {
          line: 145,
          column: 12
        },
        end: {
          line: 145,
          column: 31
        }
      },
      "59": {
        start: {
          line: 148,
          column: 8
        },
        end: {
          line: 153,
          column: 9
        }
      },
      "60": {
        start: {
          line: 149,
          column: 12
        },
        end: {
          line: 149,
          column: 30
        }
      },
      "61": {
        start: {
          line: 151,
          column: 13
        },
        end: {
          line: 153,
          column: 9
        }
      },
      "62": {
        start: {
          line: 152,
          column: 12
        },
        end: {
          line: 152,
          column: 30
        }
      },
      "63": {
        start: {
          line: 155,
          column: 8
        },
        end: {
          line: 163,
          column: 9
        }
      },
      "64": {
        start: {
          line: 156,
          column: 12
        },
        end: {
          line: 156,
          column: 30
        }
      },
      "65": {
        start: {
          line: 158,
          column: 13
        },
        end: {
          line: 163,
          column: 9
        }
      },
      "66": {
        start: {
          line: 159,
          column: 12
        },
        end: {
          line: 159,
          column: 30
        }
      },
      "67": {
        start: {
          line: 161,
          column: 13
        },
        end: {
          line: 163,
          column: 9
        }
      },
      "68": {
        start: {
          line: 162,
          column: 12
        },
        end: {
          line: 162,
          column: 30
        }
      },
      "69": {
        start: {
          line: 164,
          column: 8
        },
        end: {
          line: 164,
          column: 52
        }
      },
      "70": {
        start: {
          line: 171,
          column: 8
        },
        end: {
          line: 181,
          column: 10
        }
      },
      "71": {
        start: {
          line: 187,
          column: 8
        },
        end: {
          line: 187,
          column: 27
        }
      },
      "72": {
        start: {
          line: 194,
          column: 8
        },
        end: {
          line: 194,
          column: 31
        }
      },
      "73": {
        start: {
          line: 200,
          column: 8
        },
        end: {
          line: 205,
          column: 10
        }
      },
      "74": {
        start: {
          line: 208,
          column: 0
        },
        end: {
          line: 208,
          column: 64
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 11,
            column: 4
          },
          end: {
            line: 11,
            column: 5
          }
        },
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 19,
            column: 5
          }
        },
        line: 11
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 25,
            column: 4
          },
          end: {
            line: 25,
            column: 5
          }
        },
        loc: {
          start: {
            line: 25,
            column: 29
          },
          end: {
            line: 100,
            column: 5
          }
        },
        line: 25
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 40,
            column: 41
          },
          end: {
            line: 40,
            column: 42
          }
        },
        loc: {
          start: {
            line: 40,
            column: 47
          },
          end: {
            line: 40,
            column: 65
          }
        },
        line: 40
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 107,
            column: 4
          },
          end: {
            line: 107,
            column: 5
          }
        },
        loc: {
          start: {
            line: 107,
            column: 56
          },
          end: {
            line: 124,
            column: 5
          }
        },
        line: 107
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 112,
            column: 44
          },
          end: {
            line: 112,
            column: 45
          }
        },
        loc: {
          start: {
            line: 112,
            column: 51
          },
          end: {
            line: 119,
            column: 15
          }
        },
        line: 112
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 112,
            column: 81
          },
          end: {
            line: 112,
            column: 82
          }
        },
        loc: {
          start: {
            line: 112,
            column: 91
          },
          end: {
            line: 119,
            column: 13
          }
        },
        line: 112
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 132,
            column: 4
          },
          end: {
            line: 132,
            column: 5
          }
        },
        loc: {
          start: {
            line: 132,
            column: 63
          },
          end: {
            line: 165,
            column: 5
          }
        },
        line: 132
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 170,
            column: 4
          },
          end: {
            line: 170,
            column: 5
          }
        },
        loc: {
          start: {
            line: 170,
            column: 27
          },
          end: {
            line: 182,
            column: 5
          }
        },
        line: 170
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 186,
            column: 4
          },
          end: {
            line: 186,
            column: 5
          }
        },
        loc: {
          start: {
            line: 186,
            column: 17
          },
          end: {
            line: 188,
            column: 5
          }
        },
        line: 186
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 193,
            column: 4
          },
          end: {
            line: 193,
            column: 5
          }
        },
        loc: {
          start: {
            line: 193,
            column: 19
          },
          end: {
            line: 195,
            column: 5
          }
        },
        line: 193
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 199,
            column: 4
          },
          end: {
            line: 199,
            column: 5
          }
        },
        loc: {
          start: {
            line: 199,
            column: 17
          },
          end: {
            line: 206,
            column: 5
          }
        },
        line: 199
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 29,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 29,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "1": {
        loc: {
          start: {
            line: 33,
            column: 8
          },
          end: {
            line: 35,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 8
          },
          end: {
            line: 35,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "2": {
        loc: {
          start: {
            line: 51,
            column: 32
          },
          end: {
            line: 51,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 51,
            column: 32
          },
          end: {
            line: 51,
            column: 68
          }
        }, {
          start: {
            line: 51,
            column: 72
          },
          end: {
            line: 51,
            column: 74
          }
        }],
        line: 51
      },
      "3": {
        loc: {
          start: {
            line: 57,
            column: 24
          },
          end: {
            line: 57,
            column: 64
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 57,
            column: 39
          },
          end: {
            line: 57,
            column: 49
          }
        }, {
          start: {
            line: 57,
            column: 52
          },
          end: {
            line: 57,
            column: 64
          }
        }],
        line: 57
      },
      "4": {
        loc: {
          start: {
            line: 65,
            column: 12
          },
          end: {
            line: 70,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 65,
            column: 12
          },
          end: {
            line: 70,
            column: 13
          }
        }, {
          start: {
            line: 68,
            column: 17
          },
          end: {
            line: 70,
            column: 13
          }
        }],
        line: 65
      },
      "5": {
        loc: {
          start: {
            line: 76,
            column: 12
          },
          end: {
            line: 83,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 76,
            column: 12
          },
          end: {
            line: 83,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 76
      },
      "6": {
        loc: {
          start: {
            line: 77,
            column: 16
          },
          end: {
            line: 82,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 16
          },
          end: {
            line: 82,
            column: 17
          }
        }, {
          start: {
            line: 80,
            column: 21
          },
          end: {
            line: 82,
            column: 17
          }
        }],
        line: 77
      },
      "7": {
        loc: {
          start: {
            line: 107,
            column: 39
          },
          end: {
            line: 107,
            column: 54
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 107,
            column: 53
          },
          end: {
            line: 107,
            column: 54
          }
        }],
        line: 107
      },
      "8": {
        loc: {
          start: {
            line: 135,
            column: 8
          },
          end: {
            line: 146,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 135,
            column: 8
          },
          end: {
            line: 146,
            column: 9
          }
        }, {
          start: {
            line: 138,
            column: 13
          },
          end: {
            line: 146,
            column: 9
          }
        }],
        line: 135
      },
      "9": {
        loc: {
          start: {
            line: 135,
            column: 12
          },
          end: {
            line: 135,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 135,
            column: 12
          },
          end: {
            line: 135,
            column: 29
          }
        }, {
          start: {
            line: 135,
            column: 33
          },
          end: {
            line: 135,
            column: 49
          }
        }],
        line: 135
      },
      "10": {
        loc: {
          start: {
            line: 138,
            column: 13
          },
          end: {
            line: 146,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 138,
            column: 13
          },
          end: {
            line: 146,
            column: 9
          }
        }, {
          start: {
            line: 141,
            column: 13
          },
          end: {
            line: 146,
            column: 9
          }
        }],
        line: 138
      },
      "11": {
        loc: {
          start: {
            line: 138,
            column: 17
          },
          end: {
            line: 138,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 138,
            column: 17
          },
          end: {
            line: 138,
            column: 34
          }
        }, {
          start: {
            line: 138,
            column: 38
          },
          end: {
            line: 138,
            column: 54
          }
        }],
        line: 138
      },
      "12": {
        loc: {
          start: {
            line: 141,
            column: 13
          },
          end: {
            line: 146,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 141,
            column: 13
          },
          end: {
            line: 146,
            column: 9
          }
        }, {
          start: {
            line: 144,
            column: 13
          },
          end: {
            line: 146,
            column: 9
          }
        }],
        line: 141
      },
      "13": {
        loc: {
          start: {
            line: 141,
            column: 17
          },
          end: {
            line: 141,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 141,
            column: 17
          },
          end: {
            line: 141,
            column: 34
          }
        }, {
          start: {
            line: 141,
            column: 38
          },
          end: {
            line: 141,
            column: 54
          }
        }],
        line: 141
      },
      "14": {
        loc: {
          start: {
            line: 148,
            column: 8
          },
          end: {
            line: 153,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 148,
            column: 8
          },
          end: {
            line: 153,
            column: 9
          }
        }, {
          start: {
            line: 151,
            column: 13
          },
          end: {
            line: 153,
            column: 9
          }
        }],
        line: 148
      },
      "15": {
        loc: {
          start: {
            line: 151,
            column: 13
          },
          end: {
            line: 153,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 151,
            column: 13
          },
          end: {
            line: 153,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 151
      },
      "16": {
        loc: {
          start: {
            line: 155,
            column: 8
          },
          end: {
            line: 163,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 155,
            column: 8
          },
          end: {
            line: 163,
            column: 9
          }
        }, {
          start: {
            line: 158,
            column: 13
          },
          end: {
            line: 163,
            column: 9
          }
        }],
        line: 155
      },
      "17": {
        loc: {
          start: {
            line: 158,
            column: 13
          },
          end: {
            line: 163,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 158,
            column: 13
          },
          end: {
            line: 163,
            column: 9
          }
        }, {
          start: {
            line: 161,
            column: 13
          },
          end: {
            line: 163,
            column: 9
          }
        }],
        line: 158
      },
      "18": {
        loc: {
          start: {
            line: 161,
            column: 13
          },
          end: {
            line: 163,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 161,
            column: 13
          },
          end: {
            line: 163,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 161
      },
      "19": {
        loc: {
          start: {
            line: 175,
            column: 25
          },
          end: {
            line: 177,
            column: 19
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 176,
            column: 18
          },
          end: {
            line: 176,
            column: 79
          }
        }, {
          start: {
            line: 177,
            column: 18
          },
          end: {
            line: 177,
            column: 19
          }
        }],
        line: 175
      },
      "20": {
        loc: {
          start: {
            line: 178,
            column: 33
          },
          end: {
            line: 180,
            column: 19
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 179,
            column: 18
          },
          end: {
            line: 179,
            column: 73
          }
        }, {
          start: {
            line: 180,
            column: 18
          },
          end: {
            line: 180,
            column: 19
          }
        }],
        line: 178
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/WebstormProjects/goo/src/services/websiteVerification.ts",
      mappings: ";;;AACA,6CAAwE;AAExE,sCAAsD;AACtD,4CAA4C;AA4B5C;;GAEG;AACH,MAAa,0BAA0B;IAAvC;QACY,UAAK,GAAG,IAAI,GAAG,EAA8B,CAAC;QAC9C,UAAK,GAAG;YACZ,aAAa,EAAE,CAAC;YAChB,uBAAuB,EAAE,CAAC;YAC1B,mBAAmB,EAAE,CAAC;YACtB,iBAAiB,EAAE,CAAC;SACvB,CAAC;IAsNN,CAAC;IApNG;;;;OAIG;IACH,KAAK,CAAC,aAAa,CAAC,GAAW;QAC3B,6BAA6B;QAC7B,IAAI,CAAC,IAAA,gBAAU,EAAC,GAAG,CAAC,EAAE,CAAC;YACnB,MAAM,IAAI,iCAAwB,CAAC,uBAAuB,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,aAAa,GAAG,IAAA,kBAAY,EAAC,GAAG,CAAC,CAAC;QAExC,oBAAoB;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC7C,IAAI,MAAM,EAAE,CAAC;YACT,OAAO,MAAM,CAAC;QAClB,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,MAA0B,CAAC;QAE/B,IAAI,CAAC;YACD,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;YACzC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,0BAAc,CAAC,OAAO,CAAC,CAAC;YAE/E,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,aAAa,EAAE;gBACxC,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACL,YAAY,EAAE,0BAAc,CAAC,UAAU;iBAC1C;gBACD,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,QAAQ,EAAE,QAAQ,EAAE,0CAA0C;aACjE,CAAC,CAAC;YAEH,YAAY,CAAC,SAAS,CAAC,CAAC;YACxB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC5C,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;YAE/D,6CAA6C;YAC7C,MAAM,YAAY,GAAG,0BAAc,CAAC,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACjF,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,MAAM,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;YAExF,MAAM,GAAG;gBACL,GAAG,EAAE,aAAa;gBAClB,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY;gBAChD,UAAU,EAAE,YAAY;gBACxB,UAAU,EAAE,QAAQ,CAAC,MAAM;gBAC3B,YAAY;gBACZ,WAAW;gBACX,UAAU;gBACV,UAAU,EAAE,IAAI,IAAI,EAAE;aACzB,CAAC;YAEF,IAAI,YAAY,EAAE,CAAC;gBACf,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,CAAC;YACzC,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC;YACrC,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,iBAAiB,IAAI,YAAY,CAAC;QAEjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC5C,IAAI,YAAY,GAAG,eAAe,CAAC;YAEnC,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBACzB,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;oBAC9B,YAAY,GAAG,iBAAiB,CAAC;gBACrC,CAAC;qBAAM,CAAC;oBACJ,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;gBACjC,CAAC;YACL,CAAC;YAED,MAAM,GAAG;gBACL,GAAG,EAAE,aAAa;gBAClB,MAAM,EAAE,YAAY;gBACpB,UAAU,EAAE,KAAK;gBACjB,YAAY;gBACZ,UAAU,EAAE,GAAG;gBACf,KAAK,EAAE,YAAY;gBACnB,UAAU,EAAE,IAAI,IAAI,EAAE;aACzB,CAAC;YAEF,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,iBAAiB,IAAI,YAAY,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;QAE3B,mBAAmB;QACnB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QAEtC,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,sBAAsB,CACxB,IAAc,EACd,cAAsB,CAAC;QAEvB,MAAM,OAAO,GAAyB,EAAE,CAAC;QAEzC,uDAAuD;QACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,WAAW,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC;YAC7C,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAClC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACpC,GAAG;gBACH,MAAM,EAAE,YAA6B;gBACrC,UAAU,EAAE,KAAK;gBACjB,UAAU,EAAE,CAAC;gBACb,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,UAAU,EAAE,IAAI,IAAI,EAAE;aACzB,CAAC,CAAC,CACN,CAAC;YAEF,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;QAClC,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;OAMG;IACH,mBAAmB,CAAC,UAAkB,EAAE,WAAmB,EAAE,YAAoB;QAC7E,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,mCAAmC;QACnC,IAAI,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YACxC,UAAU,IAAI,GAAG,CAAC,CAAC,oBAAoB;QAC3C,CAAC;aAAM,IAAI,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YAC/C,UAAU,IAAI,GAAG,CAAC,CAAC,YAAY;QACnC,CAAC;aAAM,IAAI,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YAC/C,UAAU,IAAI,IAAI,CAAC,CAAC,gBAAgB;QACxC,CAAC;aAAM,CAAC;YACJ,UAAU,IAAI,IAAI,CAAC,CAAC,gBAAgB;QACxC,CAAC;QAED,yBAAyB;QACzB,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACpC,UAAU,IAAI,GAAG,CAAC;QACtB,CAAC;aAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACvC,UAAU,IAAI,GAAG,CAAC;QACtB,CAAC;QAED,0CAA0C;QAC1C,IAAI,YAAY,GAAG,IAAI,EAAE,CAAC;YACtB,UAAU,IAAI,GAAG,CAAC;QACtB,CAAC;aAAM,IAAI,YAAY,GAAG,IAAI,EAAE,CAAC;YAC7B,UAAU,IAAI,GAAG,CAAC;QACtB,CAAC;aAAM,IAAI,YAAY,GAAG,IAAI,EAAE,CAAC;YAC7B,UAAU,IAAI,GAAG,CAAC;QACtB,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;IAChD,CAAC;IAED;;;OAGG;IACH,oBAAoB;QAChB,OAAO;YACH,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;YACvC,uBAAuB,EAAE,IAAI,CAAC,KAAK,CAAC,uBAAuB;YAC3D,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB;YACnD,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC;gBACrC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,uBAAuB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa;gBAC/D,CAAC,CAAC,CAAC;YACP,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC;gBAC7C,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa;gBACzD,CAAC,CAAC,CAAC;SACV,CAAC;IACN,CAAC;IAED;;OAEG;IACH,UAAU;QACN,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;IAED;;;OAGG;IACH,YAAY;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,UAAU;QACN,IAAI,CAAC,KAAK,GAAG;YACT,aAAa,EAAE,CAAC;YAChB,uBAAuB,EAAE,CAAC;YAC1B,mBAAmB,EAAE,CAAC;YACtB,iBAAiB,EAAE,CAAC;SACvB,CAAC;IACN,CAAC;CACJ;AA7ND,gEA6NC",
      names: [],
      sources: ["/Users/<USER>/WebstormProjects/goo/src/services/websiteVerification.ts"],
      sourcesContent: ["import {WebsiteStatus} from '../models/Business';\nimport {WebsiteVerificationError, NetworkError} from '../models/Errors';\nimport {validateUrl} from '../utils/validation';\nimport {normalizeUrl, isValidUrl} from '../utils/url';\nimport {WEBSITE_CONFIG} from '../constants';\n\n/**\n * Website verification result\n */\nexport interface VerificationResult {\n    url: string;\n    status: WebsiteStatus;\n    accessible: boolean;\n    statusCode?: number;\n    responseTime?: number;\n    contentType?: string;\n    confidence: number;\n    error?: string;\n    verifiedAt: Date;\n}\n\n/**\n * Website verification statistics\n */\nexport interface VerificationStats {\n    totalVerified: number;\n    successfulVerifications: number;\n    failedVerifications: number;\n    successRate: number;\n    averageResponseTime: number;\n}\n\n/**\n * Service for verifying website accessibility and status\n */\nexport class WebsiteVerificationService {\n    private cache = new Map<string, VerificationResult>();\n    private stats = {\n        totalVerified: 0,\n        successfulVerifications: 0,\n        failedVerifications: 0,\n        totalResponseTime: 0,\n    };\n\n    /**\n     * Verifies if a website is accessible\n     * @param url - The URL to verify\n     * @returns Promise resolving to verification result\n     */\n    async verifyWebsite(url: string): Promise<VerificationResult> {\n        // Validate and normalize URL\n        if (!isValidUrl(url)) {\n            throw new WebsiteVerificationError(`Invalid URL format: ${url}`, url);\n        }\n\n        const normalizedUrl = normalizeUrl(url);\n\n        // Check cache first\n        const cached = this.cache.get(normalizedUrl);\n        if (cached) {\n            return cached;\n        }\n\n        const startTime = Date.now();\n        let result: VerificationResult;\n\n        try {\n            const controller = new AbortController();\n            const timeoutId = setTimeout(() => controller.abort(), WEBSITE_CONFIG.TIMEOUT);\n\n            const response = await fetch(normalizedUrl, {\n                method: 'HEAD',\n                headers: {\n                    'User-Agent': WEBSITE_CONFIG.USER_AGENT,\n                },\n                signal: controller.signal,\n                redirect: 'manual', // Handle redirects manually to track them\n            });\n\n            clearTimeout(timeoutId);\n            const responseTime = Date.now() - startTime;\n            const contentType = response.headers.get('content-type') || '';\n\n            // Check if the status code indicates success\n            const isAccessible = WEBSITE_CONFIG.VALID_STATUS_CODES.includes(response.status);\n            const confidence = this.calculateConfidence(response.status, contentType, responseTime);\n\n            result = {\n                url: normalizedUrl,\n                status: isAccessible ? 'verified' : 'unverified',\n                accessible: isAccessible,\n                statusCode: response.status,\n                responseTime,\n                contentType,\n                confidence,\n                verifiedAt: new Date(),\n            };\n\n            if (isAccessible) {\n                this.stats.successfulVerifications++;\n            } else {\n                this.stats.failedVerifications++;\n            }\n            this.stats.totalResponseTime += responseTime;\n\n        } catch (error) {\n            const responseTime = Date.now() - startTime;\n            let errorMessage = 'Unknown error';\n\n            if (error instanceof Error) {\n                if (error.name === 'AbortError') {\n                    errorMessage = 'Request timeout';\n                } else {\n                    errorMessage = error.message;\n                }\n            }\n\n            result = {\n                url: normalizedUrl,\n                status: 'unverified',\n                accessible: false,\n                responseTime,\n                confidence: 0.1,\n                error: errorMessage,\n                verifiedAt: new Date(),\n            };\n\n            this.stats.failedVerifications++;\n            this.stats.totalResponseTime += responseTime;\n        }\n\n        this.stats.totalVerified++;\n\n        // Cache the result\n        this.cache.set(normalizedUrl, result);\n\n        return result;\n    }\n\n    /**\n     * Verifies multiple websites concurrently\n     * @param urls - Array of URLs to verify\n     * @param concurrency - Maximum number of concurrent requests\n     * @returns Promise resolving to array of verification results\n     */\n    async verifyMultipleWebsites(\n        urls: string[],\n        concurrency: number = 5\n    ): Promise<VerificationResult[]> {\n        const results: VerificationResult[] = [];\n\n        // Process URLs in batches to respect concurrency limit\n        for (let i = 0; i < urls.length; i += concurrency) {\n            const batch = urls.slice(i, i + concurrency);\n            const batchPromises = batch.map(url =>\n                this.verifyWebsite(url).catch(error => ({\n                    url,\n                    status: 'unverified' as WebsiteStatus,\n                    accessible: false,\n                    confidence: 0,\n                    error: error.message,\n                    verifiedAt: new Date(),\n                }))\n            );\n\n            const batchResults = await Promise.all(batchPromises);\n            results.push(...batchResults);\n        }\n\n        return results;\n    }\n\n    /**\n     * Calculates confidence score for website verification\n     * @param statusCode - HTTP status code\n     * @param contentType - Response content type\n     * @param responseTime - Response time in milliseconds\n     * @returns Confidence score between 0 and 1\n     */\n    calculateConfidence(statusCode: number, contentType: string, responseTime: number): number {\n        let confidence = 0;\n\n        // Base confidence from status code\n        if (statusCode >= 200 && statusCode < 300) {\n            confidence += 0.6; // Success responses\n        } else if (statusCode >= 300 && statusCode < 400) {\n            confidence += 0.4; // Redirects\n        } else if (statusCode >= 400 && statusCode < 500) {\n            confidence += 0.05; // Client errors\n        } else {\n            confidence += 0.05; // Server errors\n        }\n\n        // Bonus for HTML content\n        if (contentType.includes('text/html')) {\n            confidence += 0.2;\n        } else if (contentType.includes('text/')) {\n            confidence += 0.1;\n        }\n\n        // Response time factor (faster is better)\n        if (responseTime < 1000) {\n            confidence += 0.2;\n        } else if (responseTime < 3000) {\n            confidence += 0.1;\n        } else if (responseTime > 5000) {\n            confidence -= 0.1;\n        }\n\n        return Math.max(0, Math.min(1, confidence));\n    }\n\n    /**\n     * Gets verification statistics\n     * @returns Verification statistics\n     */\n    getVerificationStats(): VerificationStats {\n        return {\n            totalVerified: this.stats.totalVerified,\n            successfulVerifications: this.stats.successfulVerifications,\n            failedVerifications: this.stats.failedVerifications,\n            successRate: this.stats.totalVerified > 0\n                ? this.stats.successfulVerifications / this.stats.totalVerified\n                : 0,\n            averageResponseTime: this.stats.totalVerified > 0\n                ? this.stats.totalResponseTime / this.stats.totalVerified\n                : 0,\n        };\n    }\n\n    /**\n     * Clears the verification cache\n     */\n    clearCache(): void {\n        this.cache.clear();\n    }\n\n    /**\n     * Gets the current cache size\n     * @returns Number of cached verification results\n     */\n    getCacheSize(): number {\n        return this.cache.size;\n    }\n\n    /**\n     * Resets verification statistics\n     */\n    resetStats(): void {\n        this.stats = {\n            totalVerified: 0,\n            successfulVerifications: 0,\n            failedVerifications: 0,\n            totalResponseTime: 0,\n        };\n    }\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "77bccea3643fa01cdaa8c97d3c69ba6cf81e2d95"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1oib1sohgb = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1oib1sohgb();
cov_1oib1sohgb().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1oib1sohgb().s[1]++;
exports.WebsiteVerificationService = void 0;
const Errors_1 =
/* istanbul ignore next */
(cov_1oib1sohgb().s[2]++, require("../models/Errors"));
const url_1 =
/* istanbul ignore next */
(cov_1oib1sohgb().s[3]++, require("../utils/url"));
const constants_1 =
/* istanbul ignore next */
(cov_1oib1sohgb().s[4]++, require("../constants"));
/**
 * Service for verifying website accessibility and status
 */
class WebsiteVerificationService {
  constructor() {
    /* istanbul ignore next */
    cov_1oib1sohgb().f[0]++;
    cov_1oib1sohgb().s[5]++;
    this.cache = new Map();
    /* istanbul ignore next */
    cov_1oib1sohgb().s[6]++;
    this.stats = {
      totalVerified: 0,
      successfulVerifications: 0,
      failedVerifications: 0,
      totalResponseTime: 0
    };
  }
  /**
   * Verifies if a website is accessible
   * @param url - The URL to verify
   * @returns Promise resolving to verification result
   */
  async verifyWebsite(url) {
    /* istanbul ignore next */
    cov_1oib1sohgb().f[1]++;
    cov_1oib1sohgb().s[7]++;
    // Validate and normalize URL
    if (!(0, url_1.isValidUrl)(url)) {
      /* istanbul ignore next */
      cov_1oib1sohgb().b[0][0]++;
      cov_1oib1sohgb().s[8]++;
      throw new Errors_1.WebsiteVerificationError(`Invalid URL format: ${url}`, url);
    } else
    /* istanbul ignore next */
    {
      cov_1oib1sohgb().b[0][1]++;
    }
    const normalizedUrl =
    /* istanbul ignore next */
    (cov_1oib1sohgb().s[9]++, (0, url_1.normalizeUrl)(url));
    // Check cache first
    const cached =
    /* istanbul ignore next */
    (cov_1oib1sohgb().s[10]++, this.cache.get(normalizedUrl));
    /* istanbul ignore next */
    cov_1oib1sohgb().s[11]++;
    if (cached) {
      /* istanbul ignore next */
      cov_1oib1sohgb().b[1][0]++;
      cov_1oib1sohgb().s[12]++;
      return cached;
    } else
    /* istanbul ignore next */
    {
      cov_1oib1sohgb().b[1][1]++;
    }
    const startTime =
    /* istanbul ignore next */
    (cov_1oib1sohgb().s[13]++, Date.now());
    let result;
    /* istanbul ignore next */
    cov_1oib1sohgb().s[14]++;
    try {
      const controller =
      /* istanbul ignore next */
      (cov_1oib1sohgb().s[15]++, new AbortController());
      const timeoutId =
      /* istanbul ignore next */
      (cov_1oib1sohgb().s[16]++, setTimeout(() => {
        /* istanbul ignore next */
        cov_1oib1sohgb().f[2]++;
        cov_1oib1sohgb().s[17]++;
        return controller.abort();
      }, constants_1.WEBSITE_CONFIG.TIMEOUT));
      const response =
      /* istanbul ignore next */
      (cov_1oib1sohgb().s[18]++, await fetch(normalizedUrl, {
        method: 'HEAD',
        headers: {
          'User-Agent': constants_1.WEBSITE_CONFIG.USER_AGENT
        },
        signal: controller.signal,
        redirect: 'manual' // Handle redirects manually to track them
      }));
      /* istanbul ignore next */
      cov_1oib1sohgb().s[19]++;
      clearTimeout(timeoutId);
      const responseTime =
      /* istanbul ignore next */
      (cov_1oib1sohgb().s[20]++, Date.now() - startTime);
      const contentType =
      /* istanbul ignore next */
      (cov_1oib1sohgb().s[21]++,
      /* istanbul ignore next */
      (cov_1oib1sohgb().b[2][0]++, response.headers.get('content-type')) ||
      /* istanbul ignore next */
      (cov_1oib1sohgb().b[2][1]++, ''));
      // Check if the status code indicates success
      const isAccessible =
      /* istanbul ignore next */
      (cov_1oib1sohgb().s[22]++, constants_1.WEBSITE_CONFIG.VALID_STATUS_CODES.includes(response.status));
      const confidence =
      /* istanbul ignore next */
      (cov_1oib1sohgb().s[23]++, this.calculateConfidence(response.status, contentType, responseTime));
      /* istanbul ignore next */
      cov_1oib1sohgb().s[24]++;
      result = {
        url: normalizedUrl,
        status: isAccessible ?
        /* istanbul ignore next */
        (cov_1oib1sohgb().b[3][0]++, 'verified') :
        /* istanbul ignore next */
        (cov_1oib1sohgb().b[3][1]++, 'unverified'),
        accessible: isAccessible,
        statusCode: response.status,
        responseTime,
        contentType,
        confidence,
        verifiedAt: new Date()
      };
      /* istanbul ignore next */
      cov_1oib1sohgb().s[25]++;
      if (isAccessible) {
        /* istanbul ignore next */
        cov_1oib1sohgb().b[4][0]++;
        cov_1oib1sohgb().s[26]++;
        this.stats.successfulVerifications++;
      } else {
        /* istanbul ignore next */
        cov_1oib1sohgb().b[4][1]++;
        cov_1oib1sohgb().s[27]++;
        this.stats.failedVerifications++;
      }
      /* istanbul ignore next */
      cov_1oib1sohgb().s[28]++;
      this.stats.totalResponseTime += responseTime;
    } catch (error) {
      const responseTime =
      /* istanbul ignore next */
      (cov_1oib1sohgb().s[29]++, Date.now() - startTime);
      let errorMessage =
      /* istanbul ignore next */
      (cov_1oib1sohgb().s[30]++, 'Unknown error');
      /* istanbul ignore next */
      cov_1oib1sohgb().s[31]++;
      if (error instanceof Error) {
        /* istanbul ignore next */
        cov_1oib1sohgb().b[5][0]++;
        cov_1oib1sohgb().s[32]++;
        if (error.name === 'AbortError') {
          /* istanbul ignore next */
          cov_1oib1sohgb().b[6][0]++;
          cov_1oib1sohgb().s[33]++;
          errorMessage = 'Request timeout';
        } else {
          /* istanbul ignore next */
          cov_1oib1sohgb().b[6][1]++;
          cov_1oib1sohgb().s[34]++;
          errorMessage = error.message;
        }
      } else
      /* istanbul ignore next */
      {
        cov_1oib1sohgb().b[5][1]++;
      }
      cov_1oib1sohgb().s[35]++;
      result = {
        url: normalizedUrl,
        status: 'unverified',
        accessible: false,
        responseTime,
        confidence: 0.1,
        error: errorMessage,
        verifiedAt: new Date()
      };
      /* istanbul ignore next */
      cov_1oib1sohgb().s[36]++;
      this.stats.failedVerifications++;
      /* istanbul ignore next */
      cov_1oib1sohgb().s[37]++;
      this.stats.totalResponseTime += responseTime;
    }
    /* istanbul ignore next */
    cov_1oib1sohgb().s[38]++;
    this.stats.totalVerified++;
    // Cache the result
    /* istanbul ignore next */
    cov_1oib1sohgb().s[39]++;
    this.cache.set(normalizedUrl, result);
    /* istanbul ignore next */
    cov_1oib1sohgb().s[40]++;
    return result;
  }
  /**
   * Verifies multiple websites concurrently
   * @param urls - Array of URLs to verify
   * @param concurrency - Maximum number of concurrent requests
   * @returns Promise resolving to array of verification results
   */
  async verifyMultipleWebsites(urls, concurrency =
  /* istanbul ignore next */
  (cov_1oib1sohgb().b[7][0]++, 5)) {
    /* istanbul ignore next */
    cov_1oib1sohgb().f[3]++;
    const results =
    /* istanbul ignore next */
    (cov_1oib1sohgb().s[41]++, []);
    // Process URLs in batches to respect concurrency limit
    /* istanbul ignore next */
    cov_1oib1sohgb().s[42]++;
    for (let i =
    /* istanbul ignore next */
    (cov_1oib1sohgb().s[43]++, 0); i < urls.length; i += concurrency) {
      const batch =
      /* istanbul ignore next */
      (cov_1oib1sohgb().s[44]++, urls.slice(i, i + concurrency));
      const batchPromises =
      /* istanbul ignore next */
      (cov_1oib1sohgb().s[45]++, batch.map(url => {
        /* istanbul ignore next */
        cov_1oib1sohgb().f[4]++;
        cov_1oib1sohgb().s[46]++;
        return this.verifyWebsite(url).catch(error => {
          /* istanbul ignore next */
          cov_1oib1sohgb().f[5]++;
          cov_1oib1sohgb().s[47]++;
          return {
            url,
            status: 'unverified',
            accessible: false,
            confidence: 0,
            error: error.message,
            verifiedAt: new Date()
          };
        });
      }));
      const batchResults =
      /* istanbul ignore next */
      (cov_1oib1sohgb().s[48]++, await Promise.all(batchPromises));
      /* istanbul ignore next */
      cov_1oib1sohgb().s[49]++;
      results.push(...batchResults);
    }
    /* istanbul ignore next */
    cov_1oib1sohgb().s[50]++;
    return results;
  }
  /**
   * Calculates confidence score for website verification
   * @param statusCode - HTTP status code
   * @param contentType - Response content type
   * @param responseTime - Response time in milliseconds
   * @returns Confidence score between 0 and 1
   */
  calculateConfidence(statusCode, contentType, responseTime) {
    /* istanbul ignore next */
    cov_1oib1sohgb().f[6]++;
    let confidence =
    /* istanbul ignore next */
    (cov_1oib1sohgb().s[51]++, 0);
    // Base confidence from status code
    /* istanbul ignore next */
    cov_1oib1sohgb().s[52]++;
    if (
    /* istanbul ignore next */
    (cov_1oib1sohgb().b[9][0]++, statusCode >= 200) &&
    /* istanbul ignore next */
    (cov_1oib1sohgb().b[9][1]++, statusCode < 300)) {
      /* istanbul ignore next */
      cov_1oib1sohgb().b[8][0]++;
      cov_1oib1sohgb().s[53]++;
      confidence += 0.6; // Success responses
    } else {
      /* istanbul ignore next */
      cov_1oib1sohgb().b[8][1]++;
      cov_1oib1sohgb().s[54]++;
      if (
      /* istanbul ignore next */
      (cov_1oib1sohgb().b[11][0]++, statusCode >= 300) &&
      /* istanbul ignore next */
      (cov_1oib1sohgb().b[11][1]++, statusCode < 400)) {
        /* istanbul ignore next */
        cov_1oib1sohgb().b[10][0]++;
        cov_1oib1sohgb().s[55]++;
        confidence += 0.4; // Redirects
      } else {
        /* istanbul ignore next */
        cov_1oib1sohgb().b[10][1]++;
        cov_1oib1sohgb().s[56]++;
        if (
        /* istanbul ignore next */
        (cov_1oib1sohgb().b[13][0]++, statusCode >= 400) &&
        /* istanbul ignore next */
        (cov_1oib1sohgb().b[13][1]++, statusCode < 500)) {
          /* istanbul ignore next */
          cov_1oib1sohgb().b[12][0]++;
          cov_1oib1sohgb().s[57]++;
          confidence += 0.05; // Client errors
        } else {
          /* istanbul ignore next */
          cov_1oib1sohgb().b[12][1]++;
          cov_1oib1sohgb().s[58]++;
          confidence += 0.05; // Server errors
        }
      }
    }
    // Bonus for HTML content
    /* istanbul ignore next */
    cov_1oib1sohgb().s[59]++;
    if (contentType.includes('text/html')) {
      /* istanbul ignore next */
      cov_1oib1sohgb().b[14][0]++;
      cov_1oib1sohgb().s[60]++;
      confidence += 0.2;
    } else {
      /* istanbul ignore next */
      cov_1oib1sohgb().b[14][1]++;
      cov_1oib1sohgb().s[61]++;
      if (contentType.includes('text/')) {
        /* istanbul ignore next */
        cov_1oib1sohgb().b[15][0]++;
        cov_1oib1sohgb().s[62]++;
        confidence += 0.1;
      } else
      /* istanbul ignore next */
      {
        cov_1oib1sohgb().b[15][1]++;
      }
    }
    // Response time factor (faster is better)
    /* istanbul ignore next */
    cov_1oib1sohgb().s[63]++;
    if (responseTime < 1000) {
      /* istanbul ignore next */
      cov_1oib1sohgb().b[16][0]++;
      cov_1oib1sohgb().s[64]++;
      confidence += 0.2;
    } else {
      /* istanbul ignore next */
      cov_1oib1sohgb().b[16][1]++;
      cov_1oib1sohgb().s[65]++;
      if (responseTime < 3000) {
        /* istanbul ignore next */
        cov_1oib1sohgb().b[17][0]++;
        cov_1oib1sohgb().s[66]++;
        confidence += 0.1;
      } else {
        /* istanbul ignore next */
        cov_1oib1sohgb().b[17][1]++;
        cov_1oib1sohgb().s[67]++;
        if (responseTime > 5000) {
          /* istanbul ignore next */
          cov_1oib1sohgb().b[18][0]++;
          cov_1oib1sohgb().s[68]++;
          confidence -= 0.1;
        } else
        /* istanbul ignore next */
        {
          cov_1oib1sohgb().b[18][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_1oib1sohgb().s[69]++;
    return Math.max(0, Math.min(1, confidence));
  }
  /**
   * Gets verification statistics
   * @returns Verification statistics
   */
  getVerificationStats() {
    /* istanbul ignore next */
    cov_1oib1sohgb().f[7]++;
    cov_1oib1sohgb().s[70]++;
    return {
      totalVerified: this.stats.totalVerified,
      successfulVerifications: this.stats.successfulVerifications,
      failedVerifications: this.stats.failedVerifications,
      successRate: this.stats.totalVerified > 0 ?
      /* istanbul ignore next */
      (cov_1oib1sohgb().b[19][0]++, this.stats.successfulVerifications / this.stats.totalVerified) :
      /* istanbul ignore next */
      (cov_1oib1sohgb().b[19][1]++, 0),
      averageResponseTime: this.stats.totalVerified > 0 ?
      /* istanbul ignore next */
      (cov_1oib1sohgb().b[20][0]++, this.stats.totalResponseTime / this.stats.totalVerified) :
      /* istanbul ignore next */
      (cov_1oib1sohgb().b[20][1]++, 0)
    };
  }
  /**
   * Clears the verification cache
   */
  clearCache() {
    /* istanbul ignore next */
    cov_1oib1sohgb().f[8]++;
    cov_1oib1sohgb().s[71]++;
    this.cache.clear();
  }
  /**
   * Gets the current cache size
   * @returns Number of cached verification results
   */
  getCacheSize() {
    /* istanbul ignore next */
    cov_1oib1sohgb().f[9]++;
    cov_1oib1sohgb().s[72]++;
    return this.cache.size;
  }
  /**
   * Resets verification statistics
   */
  resetStats() {
    /* istanbul ignore next */
    cov_1oib1sohgb().f[10]++;
    cov_1oib1sohgb().s[73]++;
    this.stats = {
      totalVerified: 0,
      successfulVerifications: 0,
      failedVerifications: 0,
      totalResponseTime: 0
    };
  }
}
/* istanbul ignore next */
cov_1oib1sohgb().s[74]++;
exports.WebsiteVerificationService = WebsiteVerificationService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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