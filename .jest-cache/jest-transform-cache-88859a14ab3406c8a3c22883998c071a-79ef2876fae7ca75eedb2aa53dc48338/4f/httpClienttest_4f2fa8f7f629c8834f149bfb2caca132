9794f9fb64f55f09397f21ba93b35f04
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const httpClient_1 = require("../../src/services/httpClient");
const Errors_1 = require("../../src/models/Errors");
// Mock fetch globally
const mockFetch = global.fetch;
// Helper function to create complete Response mocks
function createMockResponse(options) {
    return {
        ok: options.ok,
        status: options.status,
        statusText: options.statusText || 'OK',
        headers: options.headers || new Headers(),
        json: options.json || (async () => ({})),
        text: options.text || (async () => ''),
        redirected: false,
        type: 'default',
        url: 'https://api.example.com/test',
        body: null,
        bodyUsed: false,
        bytes: jest.fn(),
        clone: jest.fn(),
        arrayBuffer: jest.fn(),
        blob: jest.fn(),
        formData: jest.fn(),
    };
}
describe('HttpClient', () => {
    let httpClient;
    beforeEach(() => {
        // Use a shorter timeout for testing to avoid Jest timeout issues
        httpClient = new httpClient_1.HttpClient(1000, 3);
        mockFetch.mockClear();
    });
    describe('get', () => {
        it('should make successful GET request', async () => {
            const mockResponse = { data: 'test' };
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                statusText: 'OK',
                headers: new Headers({ 'content-type': 'application/json' }),
                json: async () => mockResponse,
                redirected: false,
                type: 'default',
                url: 'https://api.example.com/test',
                body: null,
                bodyUsed: false,
                bytes: jest.fn(),
                clone: jest.fn(),
                arrayBuffer: jest.fn(),
                blob: jest.fn(),
                formData: jest.fn(),
                text: jest.fn(),
            });
            const result = await httpClient.get('https://api.example.com/test');
            expect(result).toEqual(mockResponse);
            expect(mockFetch).toHaveBeenCalledWith('https://api.example.com/test', expect.objectContaining({
                method: 'GET',
                headers: expect.any(Headers),
            }));
        });
        it('should handle API errors', async () => {
            mockFetch.mockResolvedValueOnce(createMockResponse({
                ok: false,
                status: 400,
                statusText: 'Bad Request',
                text: async () => 'Error details',
            }));
            await expect(httpClient.get('https://api.example.com/test'))
                .rejects.toThrow(Errors_1.ApiError);
        });
        it('should handle network errors', async () => {
            // Mock the sleep method to make tests faster
            const sleepSpy = jest.spyOn(httpClient, 'sleep').mockResolvedValue(undefined);
            mockFetch.mockRejectedValueOnce(new Error('Network error'));
            await expect(httpClient.get('https://api.example.com/test'))
                .rejects.toThrow(Errors_1.NetworkError);
            sleepSpy.mockRestore();
        });
        it('should handle rate limiting', async () => {
            mockFetch.mockResolvedValueOnce(createMockResponse({
                ok: false,
                status: 429,
                statusText: 'Too Many Requests',
                headers: new Headers({ 'Retry-After': '60' }),
            }));
            await expect(httpClient.get('https://api.example.com/test'))
                .rejects.toThrow(Errors_1.RateLimitError);
        });
        it('should retry on transient errors', async () => {
            // Mock the sleep method to make tests faster
            const sleepSpy = jest.spyOn(httpClient, 'sleep').mockResolvedValue(undefined);
            // First call fails with 500 error (retryable)
            mockFetch.mockResolvedValueOnce(createMockResponse({
                ok: false,
                status: 500,
                statusText: 'Internal Server Error',
                text: async () => 'Server error',
            }));
            // Second call succeeds
            const mockResponse = { data: 'test' };
            mockFetch.mockResolvedValueOnce(createMockResponse({
                ok: true,
                status: 200,
                headers: new Headers({ 'content-type': 'application/json' }),
                json: async () => mockResponse,
            }));
            // Execute the request
            const result = await httpClient.get('https://api.example.com/test');
            expect(result).toEqual(mockResponse);
            expect(mockFetch).toHaveBeenCalledTimes(2);
            sleepSpy.mockRestore();
        });
        it('should handle timeout errors', async () => {
            // Mock the sleep method to make tests faster
            const sleepSpy = jest.spyOn(httpClient, 'sleep').mockResolvedValue(undefined);
            const abortError = new Error('The operation was aborted');
            abortError.name = 'AbortError';
            mockFetch.mockRejectedValueOnce(abortError);
            await expect(httpClient.get('https://api.example.com/test'))
                .rejects.toThrow(Errors_1.NetworkError);
            sleepSpy.mockRestore();
        });
        it('should handle different content types', async () => {
            // Test JSON response
            mockFetch.mockResolvedValueOnce(createMockResponse({
                ok: true,
                status: 200,
                headers: new Headers({ 'content-type': 'application/json' }),
                json: async () => ({ message: 'success' }),
            }));
            const jsonResult = await httpClient.get('https://api.example.com/json');
            expect(jsonResult).toEqual({ message: 'success' });
            // Test text response
            mockFetch.mockResolvedValueOnce(createMockResponse({
                ok: true,
                status: 200,
                headers: new Headers({ 'content-type': 'text/plain' }),
                text: async () => 'plain text',
            }));
            const textResult = await httpClient.get('https://api.example.com/text');
            expect(textResult).toBe('plain text');
        });
        it('should add custom headers', async () => {
            const mockResponse = { data: 'test' };
            mockFetch.mockResolvedValueOnce(createMockResponse({
                ok: true,
                status: 200,
                headers: new Headers({ 'content-type': 'application/json' }),
                json: async () => mockResponse,
            }));
            await httpClient.get('https://api.example.com/test', {
                headers: { 'Custom-Header': 'value' }
            });
            expect(mockFetch).toHaveBeenCalledWith('https://api.example.com/test', expect.objectContaining({
                method: 'GET',
                headers: expect.any(Headers),
            }));
            // Check that the headers were properly set
            const callArgs = mockFetch.mock.calls[0];
            if (callArgs && callArgs[1]) {
                const headers = callArgs[1].headers;
                expect(headers.get('User-Agent')).toBe('BusinessSearchApp/1.0');
                expect(headers.get('Custom-Header')).toBe('value');
            }
        });
    });
    describe('post', () => {
        it('should make successful POST request with data', async () => {
            const mockResponse = { success: true };
            const postData = { name: 'test' };
            mockFetch.mockResolvedValueOnce(createMockResponse({
                ok: true,
                status: 201,
                headers: new Headers({ 'content-type': 'application/json' }),
                json: async () => mockResponse,
            }));
            const result = await httpClient.post('https://api.example.com/test', postData);
            expect(result).toEqual(mockResponse);
            expect(mockFetch).toHaveBeenCalledWith('https://api.example.com/test', expect.objectContaining({
                method: 'POST',
                body: JSON.stringify(postData),
                headers: expect.any(Headers),
            }));
            // Check that the headers were properly set
            const callArgs = mockFetch.mock.calls[0];
            if (callArgs && callArgs[1]) {
                const headers = callArgs[1].headers;
                expect(headers.get('User-Agent')).toBe('BusinessSearchApp/1.0');
                expect(headers.get('Content-Type')).toBe('application/json');
            }
        });
    });
    describe('head', () => {
        it('should make successful HEAD request', async () => {
            mockFetch.mockResolvedValueOnce(createMockResponse({
                ok: true,
                status: 200,
                headers: new Headers({ 'Content-Type': 'text/html' }),
            }));
            const result = await httpClient.head('https://example.com');
            expect(result.ok).toBe(true);
            expect(result.status).toBe(200);
            expect(mockFetch).toHaveBeenCalledWith('https://example.com', expect.objectContaining({
                method: 'HEAD'
            }));
        });
        it('should handle redirects in HEAD requests', async () => {
            mockFetch.mockResolvedValueOnce(createMockResponse({
                ok: true,
                status: 301,
                headers: new Headers({ 'Location': 'https://example.com/new' }),
            }));
            const result = await httpClient.head('https://example.com');
            expect(result.status).toBe(301);
        });
    });
    describe('retry logic', () => {
        it('should not retry on non-retryable errors', async () => {
            mockFetch.mockResolvedValueOnce(createMockResponse({
                ok: false,
                status: 400,
                statusText: 'Bad Request',
                text: async () => 'Bad request',
            }));
            await expect(httpClient.get('https://api.example.com/test'))
                .rejects.toThrow(Errors_1.ApiError);
            expect(mockFetch).toHaveBeenCalledTimes(1);
        });
        it('should exhaust all retries before failing', async () => {
            // Mock the sleep method to make tests faster
            const sleepSpy = jest.spyOn(httpClient, 'sleep').mockResolvedValue(undefined);
            // All calls fail with retryable errors
            mockFetch.mockResolvedValue(createMockResponse({
                ok: false,
                status: 500,
                statusText: 'Internal Server Error',
                text: async () => 'Server error',
            }));
            await expect(httpClient.get('https://api.example.com/test'))
                .rejects.toThrow(Errors_1.ApiError);
            // Should try initial request + 3 retries = 4 total
            expect(mockFetch).toHaveBeenCalledTimes(4);
            // Should have called sleep 3 times (for retries)
            expect(sleepSpy).toHaveBeenCalledTimes(3);
            sleepSpy.mockRestore();
        });
        it('should use exponential backoff for retries', async () => {
            // Mock the sleep method to track delays
            const sleepSpy = jest.spyOn(httpClient, 'sleep').mockResolvedValue(undefined);
            mockFetch.mockResolvedValue(createMockResponse({
                ok: false,
                status: 500,
                statusText: 'Internal Server Error',
                text: async () => 'Server error',
            }));
            await expect(httpClient.get('https://api.example.com/test'))
                .rejects.toThrow(Errors_1.ApiError);
            // Should have made 4 attempts (initial + 3 retries)
            expect(mockFetch).toHaveBeenCalledTimes(4);
            // Should have called sleep 3 times (for retries)
            expect(sleepSpy).toHaveBeenCalledTimes(3);
            sleepSpy.mockRestore();
        });
    });
    describe('request interceptors', () => {
        it('should apply request interceptors', async () => {
            const interceptor = jest.fn((config) => ({
                ...config,
                headers: { ...config.headers, 'X-Custom': 'intercepted' }
            }));
            httpClient.addRequestInterceptor(interceptor);
            mockFetch.mockResolvedValueOnce(createMockResponse({
                ok: true,
                status: 200,
                headers: new Headers({ 'content-type': 'application/json' }),
                json: async () => ({}),
            }));
            await httpClient.get('https://api.example.com/test');
            expect(interceptor).toHaveBeenCalled();
            expect(mockFetch).toHaveBeenCalledWith('https://api.example.com/test', expect.objectContaining({
                method: 'GET',
                headers: expect.any(Headers),
            }));
            // Check that the interceptor was applied
            const callArgs = mockFetch.mock.calls[0];
            if (callArgs && callArgs[1]) {
                const headers = callArgs[1].headers;
                expect(headers.get('User-Agent')).toBe('BusinessSearchApp/1.0');
                expect(headers.get('X-Custom')).toBe('intercepted');
            }
        });
    });
    describe('response interceptors', () => {
        it('should apply response interceptors', async () => {
            const interceptor = jest.fn((response) => ({
                ...response,
                intercepted: true
            }));
            httpClient.addResponseInterceptor(interceptor);
            mockFetch.mockResolvedValueOnce(createMockResponse({
                ok: true,
                status: 200,
                headers: new Headers({ 'content-type': 'application/json' }),
                json: async () => ({ data: 'test' }),
            }));
            const result = await httpClient.get('https://api.example.com/test');
            expect(interceptor).toHaveBeenCalled();
            expect(result).toEqual({
                data: 'test',
                intercepted: true
            });
        });
    });
    describe('constructor options', () => {
        it('should accept custom timeout and retries', () => {
            const customClient = new httpClient_1.HttpClient(10000, 5);
            expect(customClient).toBeInstanceOf(httpClient_1.HttpClient);
        });
        it('should use default values when not provided', () => {
            const defaultClient = new httpClient_1.HttpClient();
            expect(defaultClient).toBeInstanceOf(httpClient_1.HttpClient);
        });
    });
    describe('error classification', () => {
        it('should classify 4xx errors as non-retryable', async () => {
            mockFetch.mockResolvedValueOnce(createMockResponse({
                ok: false,
                status: 400,
                statusText: 'Bad Request',
                text: async () => 'Bad request',
            }));
            await expect(httpClient.get('https://api.example.com/test'))
                .rejects.toThrow(Errors_1.ApiError);
            // Should not retry 4xx errors
            expect(mockFetch).toHaveBeenCalledTimes(1);
        });
        it('should classify 5xx errors as retryable', async () => {
            // Mock the sleep method to make tests faster
            const sleepSpy = jest.spyOn(httpClient, 'sleep').mockResolvedValue(undefined);
            mockFetch.mockResolvedValue(createMockResponse({
                ok: false,
                status: 503,
                statusText: 'Service Unavailable',
                text: async () => 'Service unavailable',
            }));
            await expect(httpClient.get('https://api.example.com/test'))
                .rejects.toThrow(Errors_1.ApiError);
            // Should retry 5xx errors
            expect(mockFetch).toHaveBeenCalledTimes(4);
            // Should have called sleep 3 times (for retries)
            expect(sleepSpy).toHaveBeenCalledTimes(3);
            sleepSpy.mockRestore();
        });
    });
    describe('request configuration', () => {
        it('should handle custom headers in requests', async () => {
            mockFetch.mockResolvedValueOnce(createMockResponse({
                ok: true,
                status: 200,
                headers: new Headers({ 'content-type': 'application/json' }),
                json: async () => ({ success: true }),
            }));
            await httpClient.get('https://api.example.com/test', {
                headers: { 'Authorization': 'Bearer token' }
            });
            expect(mockFetch).toHaveBeenCalledWith('https://api.example.com/test', expect.objectContaining({
                method: 'GET',
                headers: expect.any(Headers),
            }));
            // Check that the headers were properly set
            const callArgs = mockFetch.mock.calls[0];
            if (callArgs && callArgs[1]) {
                const headers = callArgs[1].headers;
                expect(headers.get('User-Agent')).toBe('BusinessSearchApp/1.0');
                expect(headers.get('Authorization')).toBe('Bearer token');
            }
        });
        it('should handle custom timeout', async () => {
            mockFetch.mockResolvedValueOnce(createMockResponse({
                ok: true,
                status: 200,
                headers: new Headers({ 'content-type': 'application/json' }),
                json: async () => ({ success: true }),
            }));
            await httpClient.get('https://api.example.com/test', {
                timeout: 15000
            });
            expect(mockFetch).toHaveBeenCalled();
        });
        it('should handle custom retry count', async () => {
            // Mock the sleep method to make tests faster
            const sleepSpy = jest.spyOn(httpClient, 'sleep').mockResolvedValue(undefined);
            mockFetch.mockResolvedValue(createMockResponse({
                ok: false,
                status: 500,
                statusText: 'Internal Server Error',
                text: async () => 'Server error',
            }));
            await expect(httpClient.get('https://api.example.com/test', {
                retries: 1
            })).rejects.toThrow(Errors_1.ApiError);
            // Should try initial request + 1 retry = 2 total
            expect(mockFetch).toHaveBeenCalledTimes(2);
            sleepSpy.mockRestore();
        });
    });
    describe('edge cases', () => {
        it('should handle fetch throwing non-Error objects', async () => {
            // Mock the sleep method to make tests faster
            const sleepSpy = jest.spyOn(httpClient, 'sleep').mockResolvedValue(undefined);
            mockFetch.mockRejectedValue('string error');
            await expect(httpClient.get('https://api.example.com/test'))
                .rejects.toThrow(Errors_1.NetworkError);
            sleepSpy.mockRestore();
        });
        it('should handle response.text() throwing error', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: false,
                status: 400,
                statusText: 'Bad Request',
                headers: new Headers(),
                text: async () => { throw new Error('Text parsing failed'); },
                redirected: false,
                type: 'default',
                url: 'https://api.example.com/test',
                body: null,
                bodyUsed: false,
                bytes: jest.fn(),
                clone: jest.fn(),
                arrayBuffer: jest.fn(),
                blob: jest.fn(),
                formData: jest.fn(),
                json: jest.fn(),
            });
            await expect(httpClient.get('https://api.example.com/test'))
                .rejects.toThrow(Errors_1.ApiError);
        });
        it('should handle response.json() throwing error', async () => {
            // Mock the sleep method to make tests faster
            const sleepSpy = jest.spyOn(httpClient, 'sleep').mockResolvedValue(undefined);
            mockFetch.mockResolvedValue({
                ok: true,
                status: 200,
                statusText: 'OK',
                headers: new Headers({ 'content-type': 'application/json' }),
                json: async () => { throw new Error('JSON parsing failed'); },
                redirected: false,
                type: 'default',
                url: 'https://api.example.com/test',
                body: null,
                bodyUsed: false,
                bytes: jest.fn(),
                clone: jest.fn(),
                arrayBuffer: jest.fn(),
                blob: jest.fn(),
                formData: jest.fn(),
                text: jest.fn(),
            });
            await expect(httpClient.get('https://api.example.com/test'))
                .rejects.toThrow(Errors_1.NetworkError);
            sleepSpy.mockRestore();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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