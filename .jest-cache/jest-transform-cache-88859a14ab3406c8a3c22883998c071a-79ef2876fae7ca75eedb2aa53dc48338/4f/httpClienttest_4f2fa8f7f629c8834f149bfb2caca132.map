{"file": "/Users/<USER>/WebstormProjects/goo/tests/services/httpClient.test.ts", "mappings": ";;AAAA,8DAA2D;AAC3D,oDAAiF;AAEjF,sBAAsB;AACtB,MAAM,SAAS,GAAG,MAAM,CAAC,KAA0C,CAAC;AAEpE,oDAAoD;AACpD,SAAS,kBAAkB,CAAC,OAO3B;IACC,OAAO;QACL,EAAE,EAAE,OAAO,CAAC,EAAE;QACd,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,IAAI;QACtC,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI,OAAO,EAAE;QACzC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QACxC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,CAAC;QACtC,UAAU,EAAE,KAAK;QACjB,IAAI,EAAE,SAAS;QACf,GAAG,EAAE,8BAA8B;QACnC,IAAI,EAAE,IAAI;QACV,QAAQ,EAAE,KAAK;QACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;QACtB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;QACf,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;KACG,CAAC;AAC3B,CAAC;AAED,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;IAC1B,IAAI,UAAsB,CAAC;IAE3B,UAAU,CAAC,GAAG,EAAE;QACd,iEAAiE;QACjE,UAAU,GAAG,IAAI,uBAAU,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACrC,SAAS,CAAC,SAAS,EAAE,CAAC;IACxB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE;QACnB,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,YAAY,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;YACtC,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,cAAc,EAAE,kBAAkB,EAAE,CAAC;gBAC5D,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,YAAY;gBAC9B,UAAU,EAAE,KAAK;gBACjB,IAAI,EAAE,SAAS;gBACf,GAAG,EAAE,8BAA8B;gBACnC,IAAI,EAAE,IAAI;gBACV,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;gBAChB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;gBAChB,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;gBACtB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;gBACf,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;gBACnB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;aACO,CAAC,CAAC;YAE1B,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAEpE,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACrC,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CACpC,8BAA8B,EAC9B,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;aAC7B,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;YACxC,SAAS,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;gBACjD,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,aAAa;gBACzB,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,eAAe;aAClC,CAAC,CAAC,CAAC;YAEJ,MAAM,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;iBACzD,OAAO,CAAC,OAAO,CAAC,iBAAQ,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAiB,EAAE,OAAO,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAErF,SAAS,CAAC,qBAAqB,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;YAE5D,MAAM,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;iBACzD,OAAO,CAAC,OAAO,CAAC,qBAAY,CAAC,CAAC;YAEjC,QAAQ,CAAC,WAAW,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,SAAS,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;gBACjD,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,mBAAmB;gBAC/B,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC;aAC9C,CAAC,CAAC,CAAC;YAEJ,MAAM,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;iBACzD,OAAO,CAAC,OAAO,CAAC,uBAAc,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAiB,EAAE,OAAO,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAErF,8CAA8C;YAC9C,SAAS,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;gBACjD,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,uBAAuB;gBACnC,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,cAAc;aACjC,CAAC,CAAC,CAAC;YAEJ,uBAAuB;YACvB,MAAM,YAAY,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;YACtC,SAAS,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;gBACjD,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,cAAc,EAAE,kBAAkB,EAAE,CAAC;gBAC5D,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,YAAY;aAC/B,CAAC,CAAC,CAAC;YAEJ,sBAAsB;YACtB,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAEpE,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACrC,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAE3C,QAAQ,CAAC,WAAW,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAiB,EAAE,OAAO,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAErF,MAAM,UAAU,GAAG,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC1D,UAAU,CAAC,IAAI,GAAG,YAAY,CAAC;YAC/B,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;YAE5C,MAAM,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;iBACzD,OAAO,CAAC,OAAO,CAAC,qBAAY,CAAC,CAAC;YAEjC,QAAQ,CAAC,WAAW,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,qBAAqB;YACrB,SAAS,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;gBACjD,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,cAAc,EAAE,kBAAkB,EAAE,CAAC;gBAC5D,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;aAC3C,CAAC,CAAC,CAAC;YAEJ,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YACxE,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;YAEnD,qBAAqB;YACrB,SAAS,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;gBACjD,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,cAAc,EAAE,YAAY,EAAE,CAAC;gBACtD,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,YAAY;aAC/B,CAAC,CAAC,CAAC;YAEJ,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YACxE,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;YACzC,MAAM,YAAY,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;YACtC,SAAS,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;gBACjD,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,cAAc,EAAE,kBAAkB,EAAE,CAAC;gBAC5D,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,YAAY;aAC/B,CAAC,CAAC,CAAC;YAEJ,MAAM,UAAU,CAAC,GAAG,CAAC,8BAA8B,EAAE;gBACnD,OAAO,EAAE,EAAE,eAAe,EAAE,OAAO,EAAE;aACtC,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CACpC,8BAA8B,EAC9B,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;aAC7B,CAAC,CACH,CAAC;YAEF,2CAA2C;YAC3C,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACzC,IAAI,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5B,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAkB,CAAC;gBAC/C,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;gBAChE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE;QACpB,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,YAAY,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;YACvC,MAAM,QAAQ,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;YAElC,SAAS,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;gBACjD,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,cAAc,EAAE,kBAAkB,EAAE,CAAC;gBAC5D,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,YAAY;aAC/B,CAAC,CAAC,CAAC;YAEJ,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,8BAA8B,EAAE,QAAQ,CAAC,CAAC;YAE/E,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACrC,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CACpC,8BAA8B,EAC9B,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;gBAC9B,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;aAC7B,CAAC,CACH,CAAC;YAEF,2CAA2C;YAC3C,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACzC,IAAI,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5B,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAkB,CAAC;gBAC/C,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;gBAChE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE;QACpB,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,SAAS,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;gBACjD,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,CAAC;aACtD,CAAC,CAAC,CAAC;YAEJ,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAE5D,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAChC,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CACpC,qBAAqB,EACrB,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,MAAM;aACf,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,SAAS,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;gBACjD,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,UAAU,EAAE,yBAAyB,EAAE,CAAC;aAChE,CAAC,CAAC,CAAC;YAEJ,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAE5D,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,SAAS,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;gBACjD,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,aAAa;gBACzB,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,aAAa;aAChC,CAAC,CAAC,CAAC;YAEJ,MAAM,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;iBACzD,OAAO,CAAC,OAAO,CAAC,iBAAQ,CAAC,CAAC;YAE7B,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAiB,EAAE,OAAO,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAErF,uCAAuC;YACvC,SAAS,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;gBAC7C,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,uBAAuB;gBACnC,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,cAAc;aACjC,CAAC,CAAC,CAAC;YAEJ,MAAM,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;iBACzD,OAAO,CAAC,OAAO,CAAC,iBAAQ,CAAC,CAAC;YAE7B,mDAAmD;YACnD,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAE3C,iDAAiD;YACjD,MAAM,CAAC,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAE1C,QAAQ,CAAC,WAAW,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,wCAAwC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAiB,EAAE,OAAO,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAErF,SAAS,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;gBAC7C,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,uBAAuB;gBACnC,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,cAAc;aACjC,CAAC,CAAC,CAAC;YAEJ,MAAM,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;iBACzD,OAAO,CAAC,OAAO,CAAC,iBAAQ,CAAC,CAAC;YAE7B,oDAAoD;YACpD,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAE3C,iDAAiD;YACjD,MAAM,CAAC,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAE1C,QAAQ,CAAC,WAAW,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;gBACvC,GAAG,MAAM;gBACT,OAAO,EAAE,EAAE,GAAG,MAAM,CAAC,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE;aAC1D,CAAC,CAAC,CAAC;YAEJ,UAAU,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC;YAE9C,SAAS,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;gBACjD,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,cAAc,EAAE,kBAAkB,EAAE,CAAC;gBAC5D,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;aACvB,CAAC,CAAC,CAAC;YAEJ,MAAM,UAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAErD,MAAM,CAAC,WAAW,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACvC,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CACpC,8BAA8B,EAC9B,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;aAC7B,CAAC,CACH,CAAC;YAEF,yCAAyC;YACzC,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACzC,IAAI,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5B,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAkB,CAAC;gBAC/C,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;gBAChE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACtD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;gBACzC,GAAG,QAAQ;gBACX,WAAW,EAAE,IAAI;aAClB,CAAC,CAAC,CAAC;YAEJ,UAAU,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;YAE/C,SAAS,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;gBACjD,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,cAAc,EAAE,kBAAkB,EAAE,CAAC;gBAC5D,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;aACrC,CAAC,CAAC,CAAC;YAEJ,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAEpE,MAAM,CAAC,WAAW,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,IAAI,EAAE,MAAM;gBACZ,WAAW,EAAE,IAAI;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,YAAY,GAAG,IAAI,uBAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC9C,MAAM,CAAC,YAAY,CAAC,CAAC,cAAc,CAAC,uBAAU,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,aAAa,GAAG,IAAI,uBAAU,EAAE,CAAC;YACvC,MAAM,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,uBAAU,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,SAAS,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;gBACjD,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,aAAa;gBACzB,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,aAAa;aAChC,CAAC,CAAC,CAAC;YAEJ,MAAM,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;iBACzD,OAAO,CAAC,OAAO,CAAC,iBAAQ,CAAC,CAAC;YAE7B,8BAA8B;YAC9B,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAiB,EAAE,OAAO,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAErF,SAAS,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;gBAC7C,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,qBAAqB;gBACjC,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,qBAAqB;aACxC,CAAC,CAAC,CAAC;YAEJ,MAAM,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;iBACzD,OAAO,CAAC,OAAO,CAAC,iBAAQ,CAAC,CAAC;YAE7B,0BAA0B;YAC1B,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAE3C,iDAAiD;YACjD,MAAM,CAAC,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAE1C,QAAQ,CAAC,WAAW,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,SAAS,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;gBACjD,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,cAAc,EAAE,kBAAkB,EAAE,CAAC;gBAC5D,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;aACtC,CAAC,CAAC,CAAC;YAEJ,MAAM,UAAU,CAAC,GAAG,CAAC,8BAA8B,EAAE;gBACnD,OAAO,EAAE,EAAE,eAAe,EAAE,cAAc,EAAE;aAC7C,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CACpC,8BAA8B,EAC9B,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;aAC7B,CAAC,CACH,CAAC;YAEF,2CAA2C;YAC3C,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACzC,IAAI,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5B,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAkB,CAAC;gBAC/C,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;gBAChE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,SAAS,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;gBACjD,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,cAAc,EAAE,kBAAkB,EAAE,CAAC;gBAC5D,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;aACtC,CAAC,CAAC,CAAC;YAEJ,MAAM,UAAU,CAAC,GAAG,CAAC,8BAA8B,EAAE;gBACnD,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,CAAC,gBAAgB,EAAE,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAiB,EAAE,OAAO,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAErF,SAAS,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;gBAC7C,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,uBAAuB;gBACnC,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,cAAc;aACjC,CAAC,CAAC,CAAC;YAEJ,MAAM,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,8BAA8B,EAAE;gBAC1D,OAAO,EAAE,CAAC;aACX,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAQ,CAAC,CAAC;YAE9B,iDAAiD;YACjD,MAAM,CAAC,SAAS,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAE3C,QAAQ,CAAC,WAAW,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAiB,EAAE,OAAO,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAErF,SAAS,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAE5C,MAAM,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;iBACzD,OAAO,CAAC,OAAO,CAAC,qBAAY,CAAC,CAAC;YAEjC,QAAQ,CAAC,WAAW,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,SAAS,CAAC,qBAAqB,CAAC;gBAC9B,EAAE,EAAE,KAAK;gBACT,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,aAAa;gBACzB,OAAO,EAAE,IAAI,OAAO,EAAE;gBACtB,IAAI,EAAE,KAAK,IAAI,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;gBAC7D,UAAU,EAAE,KAAK;gBACjB,IAAI,EAAE,SAAS;gBACf,GAAG,EAAE,8BAA8B;gBACnC,IAAI,EAAE,IAAI;gBACV,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;gBAChB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;gBAChB,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;gBACtB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;gBACf,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;gBACnB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;aACO,CAAC,CAAC;YAE1B,MAAM,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;iBACzD,OAAO,CAAC,OAAO,CAAC,iBAAQ,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAiB,EAAE,OAAO,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAErF,SAAS,CAAC,iBAAiB,CAAC;gBAC1B,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,GAAG;gBACX,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE,IAAI,OAAO,CAAC,EAAE,cAAc,EAAE,kBAAkB,EAAE,CAAC;gBAC5D,IAAI,EAAE,KAAK,IAAI,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;gBAC7D,UAAU,EAAE,KAAK;gBACjB,IAAI,EAAE,SAAS;gBACf,GAAG,EAAE,8BAA8B;gBACnC,IAAI,EAAE,IAAI;gBACV,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;gBAChB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;gBAChB,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;gBACtB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;gBACf,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;gBACnB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;aACO,CAAC,CAAC;YAE1B,MAAM,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;iBACzD,OAAO,CAAC,OAAO,CAAC,qBAAY,CAAC,CAAC;YAEjC,QAAQ,CAAC,WAAW,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/tests/services/httpClient.test.ts"], "sourcesContent": ["import { HttpClient } from '../../src/services/httpClient';\nimport { ApiError, NetworkError, RateLimitError } from '../../src/models/Errors';\n\n// Mock fetch globally\nconst mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;\n\n// Helper function to create complete Response mocks\nfunction createMockResponse(options: {\n  ok: boolean;\n  status: number;\n  statusText?: string;\n  headers?: Headers;\n  json?: () => Promise<any>;\n  text?: () => Promise<string>;\n}): Response {\n  return {\n    ok: options.ok,\n    status: options.status,\n    statusText: options.statusText || 'OK',\n    headers: options.headers || new Headers(),\n    json: options.json || (async () => ({})),\n    text: options.text || (async () => ''),\n    redirected: false,\n    type: 'default',\n    url: 'https://api.example.com/test',\n    body: null,\n    bodyUsed: false,\n    bytes: jest.fn(),\n    clone: jest.fn(),\n    arrayBuffer: jest.fn(),\n    blob: jest.fn(),\n    formData: jest.fn(),\n  } as unknown as Response;\n}\n\ndescribe('HttpClient', () => {\n  let httpClient: HttpClient;\n\n  beforeEach(() => {\n    // Use a shorter timeout for testing to avoid Jest timeout issues\n    httpClient = new HttpClient(1000, 3);\n    mockFetch.mockClear();\n  });\n\n  describe('get', () => {\n    it('should make successful GET request', async () => {\n      const mockResponse = { data: 'test' };\n      mockFetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        statusText: 'OK',\n        headers: new Headers({ 'content-type': 'application/json' }),\n        json: async () => mockResponse,\n        redirected: false,\n        type: 'default',\n        url: 'https://api.example.com/test',\n        body: null,\n        bodyUsed: false,\n        bytes: jest.fn(),\n        clone: jest.fn(),\n        arrayBuffer: jest.fn(),\n        blob: jest.fn(),\n        formData: jest.fn(),\n        text: jest.fn(),\n      } as unknown as Response);\n\n      const result = await httpClient.get('https://api.example.com/test');\n\n      expect(result).toEqual(mockResponse);\n      expect(mockFetch).toHaveBeenCalledWith(\n        'https://api.example.com/test',\n        expect.objectContaining({\n          method: 'GET',\n          headers: expect.any(Headers),\n        })\n      );\n    });\n\n    it('should handle API errors', async () => {\n      mockFetch.mockResolvedValueOnce(createMockResponse({\n        ok: false,\n        status: 400,\n        statusText: 'Bad Request',\n        text: async () => 'Error details',\n      }));\n\n      await expect(httpClient.get('https://api.example.com/test'))\n        .rejects.toThrow(ApiError);\n    });\n\n    it('should handle network errors', async () => {\n      // Mock the sleep method to make tests faster\n      const sleepSpy = jest.spyOn(httpClient as any, 'sleep').mockResolvedValue(undefined);\n      \n      mockFetch.mockRejectedValueOnce(new Error('Network error'));\n\n      await expect(httpClient.get('https://api.example.com/test'))\n        .rejects.toThrow(NetworkError);\n        \n      sleepSpy.mockRestore();\n    });\n\n    it('should handle rate limiting', async () => {\n      mockFetch.mockResolvedValueOnce(createMockResponse({\n        ok: false,\n        status: 429,\n        statusText: 'Too Many Requests',\n        headers: new Headers({ 'Retry-After': '60' }),\n      }));\n\n      await expect(httpClient.get('https://api.example.com/test'))\n        .rejects.toThrow(RateLimitError);\n    });\n\n    it('should retry on transient errors', async () => {\n      // Mock the sleep method to make tests faster\n      const sleepSpy = jest.spyOn(httpClient as any, 'sleep').mockResolvedValue(undefined);\n      \n      // First call fails with 500 error (retryable)\n      mockFetch.mockResolvedValueOnce(createMockResponse({\n        ok: false,\n        status: 500,\n        statusText: 'Internal Server Error',\n        text: async () => 'Server error',\n      }));\n\n      // Second call succeeds\n      const mockResponse = { data: 'test' };\n      mockFetch.mockResolvedValueOnce(createMockResponse({\n        ok: true,\n        status: 200,\n        headers: new Headers({ 'content-type': 'application/json' }),\n        json: async () => mockResponse,\n      }));\n\n      // Execute the request\n      const result = await httpClient.get('https://api.example.com/test');\n\n      expect(result).toEqual(mockResponse);\n      expect(mockFetch).toHaveBeenCalledTimes(2);\n      \n      sleepSpy.mockRestore();\n    });\n\n    it('should handle timeout errors', async () => {\n      // Mock the sleep method to make tests faster\n      const sleepSpy = jest.spyOn(httpClient as any, 'sleep').mockResolvedValue(undefined);\n      \n      const abortError = new Error('The operation was aborted');\n      abortError.name = 'AbortError';\n      mockFetch.mockRejectedValueOnce(abortError);\n\n      await expect(httpClient.get('https://api.example.com/test'))\n        .rejects.toThrow(NetworkError);\n        \n      sleepSpy.mockRestore();\n    });\n\n    it('should handle different content types', async () => {\n      // Test JSON response\n      mockFetch.mockResolvedValueOnce(createMockResponse({\n        ok: true,\n        status: 200,\n        headers: new Headers({ 'content-type': 'application/json' }),\n        json: async () => ({ message: 'success' }),\n      }));\n\n      const jsonResult = await httpClient.get('https://api.example.com/json');\n      expect(jsonResult).toEqual({ message: 'success' });\n\n      // Test text response\n      mockFetch.mockResolvedValueOnce(createMockResponse({\n        ok: true,\n        status: 200,\n        headers: new Headers({ 'content-type': 'text/plain' }),\n        text: async () => 'plain text',\n      }));\n\n      const textResult = await httpClient.get('https://api.example.com/text');\n      expect(textResult).toBe('plain text');\n    });\n\n    it('should add custom headers', async () => {\n      const mockResponse = { data: 'test' };\n      mockFetch.mockResolvedValueOnce(createMockResponse({\n        ok: true,\n        status: 200,\n        headers: new Headers({ 'content-type': 'application/json' }),\n        json: async () => mockResponse,\n      }));\n\n      await httpClient.get('https://api.example.com/test', {\n        headers: { 'Custom-Header': 'value' }\n      });\n\n      expect(mockFetch).toHaveBeenCalledWith(\n        'https://api.example.com/test',\n        expect.objectContaining({\n          method: 'GET',\n          headers: expect.any(Headers),\n        })\n      );\n      \n      // Check that the headers were properly set\n      const callArgs = mockFetch.mock.calls[0];\n      if (callArgs && callArgs[1]) {\n        const headers = callArgs[1].headers as Headers;\n        expect(headers.get('User-Agent')).toBe('BusinessSearchApp/1.0');\n        expect(headers.get('Custom-Header')).toBe('value');\n      }\n    });\n  });\n\n  describe('post', () => {\n    it('should make successful POST request with data', async () => {\n      const mockResponse = { success: true };\n      const postData = { name: 'test' };\n      \n      mockFetch.mockResolvedValueOnce(createMockResponse({\n        ok: true,\n        status: 201,\n        headers: new Headers({ 'content-type': 'application/json' }),\n        json: async () => mockResponse,\n      }));\n\n      const result = await httpClient.post('https://api.example.com/test', postData);\n\n      expect(result).toEqual(mockResponse);\n      expect(mockFetch).toHaveBeenCalledWith(\n        'https://api.example.com/test',\n        expect.objectContaining({\n          method: 'POST',\n          body: JSON.stringify(postData),\n          headers: expect.any(Headers),\n        })\n      );\n      \n      // Check that the headers were properly set\n      const callArgs = mockFetch.mock.calls[0];\n      if (callArgs && callArgs[1]) {\n        const headers = callArgs[1].headers as Headers;\n        expect(headers.get('User-Agent')).toBe('BusinessSearchApp/1.0');\n        expect(headers.get('Content-Type')).toBe('application/json');\n      }\n    });\n  });\n\n  describe('head', () => {\n    it('should make successful HEAD request', async () => {\n      mockFetch.mockResolvedValueOnce(createMockResponse({\n        ok: true,\n        status: 200,\n        headers: new Headers({ 'Content-Type': 'text/html' }),\n      }));\n\n      const result = await httpClient.head('https://example.com');\n\n      expect(result.ok).toBe(true);\n      expect(result.status).toBe(200);\n      expect(mockFetch).toHaveBeenCalledWith(\n        'https://example.com',\n        expect.objectContaining({\n          method: 'HEAD'\n        })\n      );\n    });\n\n    it('should handle redirects in HEAD requests', async () => {\n      mockFetch.mockResolvedValueOnce(createMockResponse({\n        ok: true,\n        status: 301,\n        headers: new Headers({ 'Location': 'https://example.com/new' }),\n      }));\n\n      const result = await httpClient.head('https://example.com');\n\n      expect(result.status).toBe(301);\n    });\n  });\n\n  describe('retry logic', () => {\n    it('should not retry on non-retryable errors', async () => {\n      mockFetch.mockResolvedValueOnce(createMockResponse({\n        ok: false,\n        status: 400,\n        statusText: 'Bad Request',\n        text: async () => 'Bad request',\n      }));\n\n      await expect(httpClient.get('https://api.example.com/test'))\n        .rejects.toThrow(ApiError);\n\n      expect(mockFetch).toHaveBeenCalledTimes(1);\n    });\n\n    it('should exhaust all retries before failing', async () => {\n      // Mock the sleep method to make tests faster\n      const sleepSpy = jest.spyOn(httpClient as any, 'sleep').mockResolvedValue(undefined);\n      \n      // All calls fail with retryable errors\n      mockFetch.mockResolvedValue(createMockResponse({\n        ok: false,\n        status: 500,\n        statusText: 'Internal Server Error',\n        text: async () => 'Server error',\n      }));\n\n      await expect(httpClient.get('https://api.example.com/test'))\n        .rejects.toThrow(ApiError);\n\n      // Should try initial request + 3 retries = 4 total\n      expect(mockFetch).toHaveBeenCalledTimes(4);\n      \n      // Should have called sleep 3 times (for retries)\n      expect(sleepSpy).toHaveBeenCalledTimes(3);\n      \n      sleepSpy.mockRestore();\n    });\n\n    it('should use exponential backoff for retries', async () => {\n      // Mock the sleep method to track delays\n      const sleepSpy = jest.spyOn(httpClient as any, 'sleep').mockResolvedValue(undefined);\n\n      mockFetch.mockResolvedValue(createMockResponse({\n        ok: false,\n        status: 500,\n        statusText: 'Internal Server Error',\n        text: async () => 'Server error',\n      }));\n\n      await expect(httpClient.get('https://api.example.com/test'))\n        .rejects.toThrow(ApiError);\n\n      // Should have made 4 attempts (initial + 3 retries)\n      expect(mockFetch).toHaveBeenCalledTimes(4);\n\n      // Should have called sleep 3 times (for retries)\n      expect(sleepSpy).toHaveBeenCalledTimes(3);\n\n      sleepSpy.mockRestore();\n    });\n  });\n\n  describe('request interceptors', () => {\n    it('should apply request interceptors', async () => {\n      const interceptor = jest.fn((config) => ({\n        ...config,\n        headers: { ...config.headers, 'X-Custom': 'intercepted' }\n      }));\n\n      httpClient.addRequestInterceptor(interceptor);\n\n      mockFetch.mockResolvedValueOnce(createMockResponse({\n        ok: true,\n        status: 200,\n        headers: new Headers({ 'content-type': 'application/json' }),\n        json: async () => ({}),\n      }));\n\n      await httpClient.get('https://api.example.com/test');\n\n      expect(interceptor).toHaveBeenCalled();\n      expect(mockFetch).toHaveBeenCalledWith(\n        'https://api.example.com/test',\n        expect.objectContaining({\n          method: 'GET',\n          headers: expect.any(Headers),\n        })\n      );\n      \n      // Check that the interceptor was applied\n      const callArgs = mockFetch.mock.calls[0];\n      if (callArgs && callArgs[1]) {\n        const headers = callArgs[1].headers as Headers;\n        expect(headers.get('User-Agent')).toBe('BusinessSearchApp/1.0');\n        expect(headers.get('X-Custom')).toBe('intercepted');\n      }\n    });\n  });\n\n  describe('response interceptors', () => {\n    it('should apply response interceptors', async () => {\n      const interceptor = jest.fn((response) => ({\n        ...response,\n        intercepted: true\n      }));\n\n      httpClient.addResponseInterceptor(interceptor);\n\n      mockFetch.mockResolvedValueOnce(createMockResponse({\n        ok: true,\n        status: 200,\n        headers: new Headers({ 'content-type': 'application/json' }),\n        json: async () => ({ data: 'test' }),\n      }));\n\n      const result = await httpClient.get('https://api.example.com/test');\n\n      expect(interceptor).toHaveBeenCalled();\n      expect(result).toEqual({\n        data: 'test',\n        intercepted: true\n      });\n    });\n  });\n\n  describe('constructor options', () => {\n    it('should accept custom timeout and retries', () => {\n      const customClient = new HttpClient(10000, 5);\n      expect(customClient).toBeInstanceOf(HttpClient);\n    });\n\n    it('should use default values when not provided', () => {\n      const defaultClient = new HttpClient();\n      expect(defaultClient).toBeInstanceOf(HttpClient);\n    });\n  });\n\n  describe('error classification', () => {\n    it('should classify 4xx errors as non-retryable', async () => {\n      mockFetch.mockResolvedValueOnce(createMockResponse({\n        ok: false,\n        status: 400,\n        statusText: 'Bad Request',\n        text: async () => 'Bad request',\n      }));\n\n      await expect(httpClient.get('https://api.example.com/test'))\n        .rejects.toThrow(ApiError);\n\n      // Should not retry 4xx errors\n      expect(mockFetch).toHaveBeenCalledTimes(1);\n    });\n\n    it('should classify 5xx errors as retryable', async () => {\n      // Mock the sleep method to make tests faster\n      const sleepSpy = jest.spyOn(httpClient as any, 'sleep').mockResolvedValue(undefined);\n      \n      mockFetch.mockResolvedValue(createMockResponse({\n        ok: false,\n        status: 503,\n        statusText: 'Service Unavailable',\n        text: async () => 'Service unavailable',\n      }));\n\n      await expect(httpClient.get('https://api.example.com/test'))\n        .rejects.toThrow(ApiError);\n\n      // Should retry 5xx errors\n      expect(mockFetch).toHaveBeenCalledTimes(4);\n      \n      // Should have called sleep 3 times (for retries)\n      expect(sleepSpy).toHaveBeenCalledTimes(3);\n      \n      sleepSpy.mockRestore();\n    });\n  });\n\n  describe('request configuration', () => {\n    it('should handle custom headers in requests', async () => {\n      mockFetch.mockResolvedValueOnce(createMockResponse({\n        ok: true,\n        status: 200,\n        headers: new Headers({ 'content-type': 'application/json' }),\n        json: async () => ({ success: true }),\n      }));\n\n      await httpClient.get('https://api.example.com/test', {\n        headers: { 'Authorization': 'Bearer token' }\n      });\n\n      expect(mockFetch).toHaveBeenCalledWith(\n        'https://api.example.com/test',\n        expect.objectContaining({\n          method: 'GET',\n          headers: expect.any(Headers),\n        })\n      );\n      \n      // Check that the headers were properly set\n      const callArgs = mockFetch.mock.calls[0];\n      if (callArgs && callArgs[1]) {\n        const headers = callArgs[1].headers as Headers;\n        expect(headers.get('User-Agent')).toBe('BusinessSearchApp/1.0');\n        expect(headers.get('Authorization')).toBe('Bearer token');\n      }\n    });\n\n    it('should handle custom timeout', async () => {\n      mockFetch.mockResolvedValueOnce(createMockResponse({\n        ok: true,\n        status: 200,\n        headers: new Headers({ 'content-type': 'application/json' }),\n        json: async () => ({ success: true }),\n      }));\n\n      await httpClient.get('https://api.example.com/test', {\n        timeout: 15000\n      });\n\n      expect(mockFetch).toHaveBeenCalled();\n    });\n\n    it('should handle custom retry count', async () => {\n      // Mock the sleep method to make tests faster\n      const sleepSpy = jest.spyOn(httpClient as any, 'sleep').mockResolvedValue(undefined);\n      \n      mockFetch.mockResolvedValue(createMockResponse({\n        ok: false,\n        status: 500,\n        statusText: 'Internal Server Error',\n        text: async () => 'Server error',\n      }));\n\n      await expect(httpClient.get('https://api.example.com/test', {\n        retries: 1\n      })).rejects.toThrow(ApiError);\n\n      // Should try initial request + 1 retry = 2 total\n      expect(mockFetch).toHaveBeenCalledTimes(2);\n      \n      sleepSpy.mockRestore();\n    });\n  });\n\n  describe('edge cases', () => {\n    it('should handle fetch throwing non-Error objects', async () => {\n      // Mock the sleep method to make tests faster\n      const sleepSpy = jest.spyOn(httpClient as any, 'sleep').mockResolvedValue(undefined);\n      \n      mockFetch.mockRejectedValue('string error');\n\n      await expect(httpClient.get('https://api.example.com/test'))\n        .rejects.toThrow(NetworkError);\n        \n      sleepSpy.mockRestore();\n    });\n\n    it('should handle response.text() throwing error', async () => {\n      mockFetch.mockResolvedValueOnce({\n        ok: false,\n        status: 400,\n        statusText: 'Bad Request',\n        headers: new Headers(),\n        text: async () => { throw new Error('Text parsing failed'); },\n        redirected: false,\n        type: 'default',\n        url: 'https://api.example.com/test',\n        body: null,\n        bodyUsed: false,\n        bytes: jest.fn(),\n        clone: jest.fn(),\n        arrayBuffer: jest.fn(),\n        blob: jest.fn(),\n        formData: jest.fn(),\n        json: jest.fn(),\n      } as unknown as Response);\n\n      await expect(httpClient.get('https://api.example.com/test'))\n        .rejects.toThrow(ApiError);\n    });\n\n    it('should handle response.json() throwing error', async () => {\n      // Mock the sleep method to make tests faster\n      const sleepSpy = jest.spyOn(httpClient as any, 'sleep').mockResolvedValue(undefined);\n      \n      mockFetch.mockResolvedValue({\n        ok: true,\n        status: 200,\n        statusText: 'OK',\n        headers: new Headers({ 'content-type': 'application/json' }),\n        json: async () => { throw new Error('JSON parsing failed'); },\n        redirected: false,\n        type: 'default',\n        url: 'https://api.example.com/test',\n        body: null,\n        bodyUsed: false,\n        bytes: jest.fn(),\n        clone: jest.fn(),\n        arrayBuffer: jest.fn(),\n        blob: jest.fn(),\n        formData: jest.fn(),\n        text: jest.fn(),\n      } as unknown as Response);\n\n      await expect(httpClient.get('https://api.example.com/test'))\n        .rejects.toThrow(NetworkError);\n        \n      sleepSpy.mockRestore();\n    });\n  });\n});\n"], "version": 3}