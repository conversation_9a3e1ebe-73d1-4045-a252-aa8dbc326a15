{"file": "/Users/<USER>/WebstormProjects/goo/tests/utils/url.test.ts", "mappings": ";;AAAA,6CAQ6B;AAE7B,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;IACzB,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,sEAAsE,EAAE,GAAG,EAAE;YAC9E,MAAM,CAAC,IAAA,kBAAY,EAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAChE,MAAM,CAAC,IAAA,kBAAY,EAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACxE,MAAM,CAAC,IAAA,kBAAY,EAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACzE,MAAM,CAAC,IAAA,kBAAY,EAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,IAAA,kBAAY,EAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACxE,MAAM,CAAC,IAAA,kBAAY,EAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,CAAC,IAAA,kBAAY,EAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAC1E,MAAM,CAAC,IAAA,kBAAY,EAAC,2BAA2B,CAAC,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,IAAA,kBAAY,EAAC,yBAAyB,CAAC,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YACxF,MAAM,CAAC,IAAA,kBAAY,EAAC,sCAAsC,CAAC,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC5G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,CAAC,IAAA,kBAAY,EAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAClC,MAAM,CAAC,IAAA,kBAAY,EAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,IAAA,gBAAU,EAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,IAAA,gBAAU,EAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,IAAA,gBAAU,EAAC,oCAAoC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpE,MAAM,CAAC,IAAA,gBAAU,EAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,IAAA,gBAAU,EAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAA,gBAAU,EAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,IAAA,gBAAU,EAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,IAAA,gBAAU,EAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,IAAA,gBAAU,EAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,CAAC,IAAA,mBAAa,EAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACjE,MAAM,CAAC,IAAA,mBAAa,EAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAClE,MAAM,CAAC,IAAA,mBAAa,EAAC,oCAAoC,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAC1F,MAAM,CAAC,IAAA,mBAAa,EAAC,+BAA+B,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,CAAC,IAAA,mBAAa,EAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAA,mBAAa,EAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,CAAC,IAAA,iBAAW,EAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC/D,MAAM,CAAC,IAAA,iBAAW,EAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,IAAA,iBAAW,EAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACvE,MAAM,CAAC,IAAA,iBAAW,EAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,IAAA,iBAAW,EAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjC,MAAM,CAAC,IAAA,iBAAW,EAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,IAAA,cAAQ,EAAC,uCAAuC,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACtF,MAAM,CAAC,IAAA,cAAQ,EAAC,gDAAgD,CAAC,CAAC,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAC5G,MAAM,CAAC,IAAA,cAAQ,EAAC,0CAA0C,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,CAAC,IAAA,cAAQ,EAAC,mCAAmC,CAAC,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QACnG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,IAAA,cAAQ,EAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACpE,MAAM,CAAC,IAAA,cAAQ,EAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,IAAA,cAAQ,EAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAChD,MAAM,CAAC,IAAA,cAAQ,EAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,kBAAkB,GAAG,qKAAqK,CAAC;YACjM,+EAA+E;YAC/E,MAAM,CAAC,IAAA,cAAQ,EAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,QAAQ,GAAG,8EAA8E,CAAC;YAChG,MAAM,CAAC,IAAA,cAAQ,EAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,CAAC,IAAA,cAAQ,EAAC,gDAAgD,CAAC,CAAC,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC3G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,CAAC,IAAA,cAAQ,EAAC,4CAA4C,CAAC,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAClG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,CAAC,IAAA,cAAQ,EAAC,2DAA2D,CAAC,CAAC,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAChI,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,IAAA,gBAAU,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAA,gBAAU,EAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAA,gBAAU,EAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,IAAA,gBAAU,EAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,yDAAyD;YACzD,MAAM,CAAC,IAAA,gBAAU,EAAC,sCAAsC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtE,MAAM,CAAC,IAAA,gBAAU,EAAC,oDAAoD,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,IAAA,gBAAU,EAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,IAAA,gBAAU,EAAC,gCAAgC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;QAC9E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,IAAA,gBAAU,EAAC,+BAA+B,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,gFAAgF;YAChF,MAAM,CAAC,IAAA,kBAAY,EAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC9E,sFAAsF;YACtF,MAAM,CAAC,IAAA,kBAAY,EAAC,yBAAyB,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC5E,MAAM,CAAC,IAAA,kBAAY,EAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACzE,MAAM,CAAC,IAAA,kBAAY,EAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,IAAA,mBAAa,EAAC,yBAAyB,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACzE,MAAM,CAAC,IAAA,mBAAa,EAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACtE,MAAM,CAAC,IAAA,mBAAa,EAAC,+BAA+B,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,CAAC,IAAA,gBAAU,EAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,IAAA,gBAAU,EAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,CAAC,IAAA,gBAAU,EAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,IAAA,mBAAa,EAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,IAAA,kBAAY,EAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,CAAC,IAAA,cAAQ,EAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACrE,MAAM,CAAC,IAAA,cAAQ,EAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,IAAA,cAAQ,EAAC,oDAAoD,CAAC,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAC7G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,sCAAsC;YACtC,MAAM,CAAC,IAAA,kBAAY,EAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,oDAAoD;YACpD,MAAM,CAAC,IAAA,kBAAY,EAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACzE,MAAM,CAAC,IAAA,kBAAY,EAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,CAAC,IAAA,gBAAU,EAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,IAAA,mBAAa,EAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,CAAC,IAAA,gBAAU,EAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAA,gBAAU,EAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,IAAA,gBAAU,EAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,IAAA,gBAAU,EAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,IAAA,gBAAU,EAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,kDAAkD;YAClD,gEAAgE;YAChE,MAAM,CAAC,IAAA,kBAAY,EAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAExF,mDAAmD;YACnD,MAAM,CAAC,IAAA,kBAAY,EAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,mDAAmD;YACnD,MAAM,CAAC,IAAA,uBAAiB,EAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrE,MAAM,CAAC,IAAA,uBAAiB,EAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvE,MAAM,CAAC,IAAA,uBAAiB,EAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,8BAA8B;YAC9B,MAAM,CAAC,IAAA,6BAAuB,EAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAA,6BAAuB,EAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElD,wGAAwG;YACxG,MAAM,CAAC,IAAA,6BAAuB,EAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,sBAAsB;YAC9F,MAAM,CAAC,IAAA,6BAAuB,EAAC,4BAA4B,CAAC,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC,CAAC,sBAAsB;YAEhI,uBAAuB;YACvB,MAAM,CAAC,IAAA,6BAAuB,EAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC3E,MAAM,CAAC,IAAA,6BAAuB,EAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,CAAC,IAAA,uBAAiB,EAAC,wBAAwB,EAAE,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtF,MAAM,CAAC,IAAA,uBAAiB,EAAC,8BAA8B,EAAE,yBAAyB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChG,MAAM,CAAC,IAAA,uBAAiB,EAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,0DAA0D;YAC1D,MAAM,CAAC,IAAA,kBAAY,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChD,qEAAqE;YACrE,MAAM,CAAC,IAAA,kBAAY,EAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,sDAAsD;YACtD,MAAM,CAAC,IAAA,mBAAa,EAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAA,gBAAU,EAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAA,iBAAW,EAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/tests/utils/url.test.ts"], "sourcesContent": ["import {\n  normalizeUrl,\n  isValidUrl,\n  extractDomain,\n  addProtocol,\n  cleanUrl,\n  areUrlsEquivalent,\n  validateAndNormalizeUrl\n} from '../../src/utils/url';\n\ndescribe('URL Utils', () => {\n  describe('normalizeUrl', () => {\n    it('should normalize URLs by adding protocol and removing trailing slash', () => {\n      expect(normalizeUrl('example.com')).toBe('https://example.com');\n      expect(normalizeUrl('www.example.com')).toBe('https://www.example.com');\n      expect(normalizeUrl('https://example.com/')).toBe('https://example.com');\n      expect(normalizeUrl('http://example.com/')).toBe('http://example.com');\n    });\n\n    it('should preserve existing protocols', () => {\n      expect(normalizeUrl('https://example.com')).toBe('https://example.com');\n      expect(normalizeUrl('http://example.com')).toBe('http://example.com');\n    });\n\n    it('should handle URLs with paths', () => {\n      expect(normalizeUrl('example.com/path')).toBe('https://example.com/path');\n      expect(normalizeUrl('https://example.com/path/')).toBe('https://example.com/path');\n    });\n\n    it('should handle URLs with query parameters', () => {\n      expect(normalizeUrl('example.com?param=value')).toBe('https://example.com?param=value');\n      expect(normalizeUrl('https://example.com/path?param=value')).toBe('https://example.com/path?param=value');\n    });\n\n    it('should return empty string for invalid input', () => {\n      expect(normalizeUrl('')).toBe('');\n      expect(normalizeUrl('   ')).toBe('');\n    });\n  });\n\n  describe('isValidUrl', () => {\n    it('should return true for valid URLs', () => {\n      expect(isValidUrl('https://example.com')).toBe(true);\n      expect(isValidUrl('http://test.org')).toBe(true);\n      expect(isValidUrl('https://subdomain.example.com/path')).toBe(true);\n      expect(isValidUrl('https://example.com:8080')).toBe(true);\n    });\n\n    it('should return false for invalid URLs', () => {\n      expect(isValidUrl('example.com')).toBe(false);\n      expect(isValidUrl('ftp://example.com')).toBe(false);\n      expect(isValidUrl('')).toBe(false);\n      expect(isValidUrl('not-a-url')).toBe(false);\n      expect(isValidUrl('javascript:alert(1)')).toBe(false);\n    });\n  });\n\n  describe('extractDomain', () => {\n    it('should extract domain from URLs', () => {\n      expect(extractDomain('https://example.com')).toBe('example.com');\n      expect(extractDomain('http://www.test.org')).toBe('www.test.org');\n      expect(extractDomain('https://subdomain.example.com/path')).toBe('subdomain.example.com');\n      expect(extractDomain('https://example.com:8080/path')).toBe('example.com');\n    });\n\n    it('should return empty string for invalid URLs', () => {\n      expect(extractDomain('invalid-url')).toBe('');\n      expect(extractDomain('')).toBe('');\n    });\n  });\n\n  describe('addProtocol', () => {\n    it('should add https protocol to URLs without protocol', () => {\n      expect(addProtocol('example.com')).toBe('https://example.com');\n      expect(addProtocol('www.example.com')).toBe('https://www.example.com');\n    });\n\n    it('should preserve existing protocols', () => {\n      expect(addProtocol('https://example.com')).toBe('https://example.com');\n      expect(addProtocol('http://example.com')).toBe('http://example.com');\n    });\n\n    it('should handle empty or invalid input', () => {\n      expect(addProtocol('')).toBe('');\n      expect(addProtocol('   ')).toBe('');\n    });\n  });\n\n  describe('cleanUrl', () => {\n    it('should remove common tracking parameters', () => {\n      expect(cleanUrl('https://example.com?utm_source=google')).toBe('https://example.com');\n      expect(cleanUrl('https://example.com?utm_medium=cpc&param=value')).toBe('https://example.com/?param=value');\n      expect(cleanUrl('https://example.com?fbclid=123&gclid=456')).toBe('https://example.com');\n    });\n\n    it('should preserve important parameters', () => {\n      expect(cleanUrl('https://example.com?id=123&page=2')).toBe('https://example.com/?id=123&page=2');\n    });\n\n    it('should handle URLs without parameters', () => {\n      expect(cleanUrl('https://example.com')).toBe('https://example.com');\n      expect(cleanUrl('https://example.com/path')).toBe('https://example.com/path');\n    });\n\n    it('should handle malformed URLs gracefully', () => {\n      expect(cleanUrl('not-a-url')).toBe('not-a-url');\n      expect(cleanUrl('')).toBe('');\n    });\n\n    it('should handle all tracking parameters', () => {\n      const urlWithAllTracking = 'https://example.com?utm_source=google&utm_medium=cpc&utm_campaign=test&utm_term=keyword&utm_content=ad&fbclid=123&gclid=456&msclkid=789&ref=twitter&source=facebook';\n      // Note: ref and source are not in the tracking parameters list, so they remain\n      expect(cleanUrl(urlWithAllTracking)).toBe('https://example.com/?ref=twitter&source=facebook');\n    });\n\n    it('should handle mixed tracking and non-tracking parameters', () => {\n      const mixedUrl = 'https://example.com?id=123&utm_source=google&page=2&fbclid=456&category=tech';\n      expect(cleanUrl(mixedUrl)).toBe('https://example.com/?id=123&page=2&category=tech');\n    });\n\n    it('should preserve hash fragments', () => {\n      expect(cleanUrl('https://example.com?utm_source=google#section1')).toBe('https://example.com/#section1');\n    });\n\n    it('should handle URLs with ports', () => {\n      expect(cleanUrl('https://example.com:8080?utm_source=google')).toBe('https://example.com:8080');\n    });\n\n    it('should handle URLs with paths', () => {\n      expect(cleanUrl('https://example.com/path/to/page?utm_source=google&id=123')).toBe('https://example.com/path/to/page?id=123');\n    });\n  });\n\n  describe('edge cases and error handling', () => {\n    it('should handle malformed URLs in isValidUrl', () => {\n      expect(isValidUrl('http://')).toBe(false);\n      expect(isValidUrl('https://')).toBe(false);\n      expect(isValidUrl('ftp://example.com')).toBe(false);\n      expect(isValidUrl('javascript:alert(1)')).toBe(false);\n    });\n\n    it('should handle special characters in URLs', () => {\n      // URLs with spaces are actually valid in modern browsers\n      expect(isValidUrl('https://example.com/path with spaces')).toBe(true);\n      expect(isValidUrl('https://example.com/path%20with%20encoded%20spaces')).toBe(true);\n    });\n\n    it('should handle international domain names', () => {\n      expect(isValidUrl('https://例え.テスト')).toBe(true);\n      expect(isValidUrl('https://xn--r8jz45g.xn--zckzah')).toBe(true); // Punycode\n    });\n\n    it('should handle URLs with authentication', () => {\n      expect(isValidUrl('https://user:<EMAIL>')).toBe(true);\n    });\n\n    it('should handle normalizeUrl edge cases', () => {\n      // normalizeUrl checks for lowercase protocols, so uppercase gets https:// added\n      expect(normalizeUrl('HTTP://EXAMPLE.COM')).toBe('https://HTTP://EXAMPLE.COM');\n      // normalizeUrl removes trailing slash for root path, which also removes default ports\n      expect(normalizeUrl('https://example.com:443')).toBe('https://example.com');\n      expect(normalizeUrl('http://example.com:80')).toBe('http://example.com');\n      expect(normalizeUrl('https://example.com:8080')).toBe('https://example.com:8080');\n    });\n\n    it('should handle extractDomain edge cases', () => {\n      expect(extractDomain('https://sub.example.com')).toBe('sub.example.com');\n      expect(extractDomain('https://example.com:8080')).toBe('example.com');\n      expect(extractDomain('https://user:<EMAIL>')).toBe('example.com');\n    });\n\n    it('should handle URLs with unusual but valid schemes', () => {\n      expect(isValidUrl('https://example.com')).toBe(true);\n      expect(isValidUrl('http://example.com')).toBe(true);\n    });\n\n    it('should handle empty and null inputs', () => {\n      expect(isValidUrl('')).toBe(false);\n      expect(extractDomain('')).toBe('');\n      expect(normalizeUrl('')).toBe('');\n    });\n\n    it('should handle URLs with query parameters only', () => {\n      expect(cleanUrl('https://example.com?')).toBe('https://example.com');\n      expect(cleanUrl('https://example.com?&')).toBe('https://example.com');\n    });\n\n    it('should handle URLs with encoded characters', () => {\n      expect(cleanUrl('https://example.com?utm_source=google%20ads&id=123')).toBe('https://example.com/?id=123');\n    });\n\n    it('should handle case sensitivity in domains', () => {\n      // normalizeUrl preserves case in URLs\n      expect(normalizeUrl('https://EXAMPLE.COM/PATH')).toBe('https://EXAMPLE.COM/PATH');\n    });\n\n    it('should handle trailing slashes consistently', () => {\n      // normalizeUrl removes trailing slash for root path\n      expect(normalizeUrl('https://example.com/')).toBe('https://example.com');\n      expect(normalizeUrl('https://example.com')).toBe('https://example.com');\n    });\n\n    it('should handle IPv4 addresses', () => {\n      expect(isValidUrl('https://***********')).toBe(true);\n      expect(extractDomain('https://***********:8080')).toBe('***********');\n    });\n\n    it('should handle IPv6 addresses', () => {\n      expect(isValidUrl('https://[::1]')).toBe(true);\n      expect(isValidUrl('https://[2001:db8::1]')).toBe(true);\n    });\n\n    it('should handle localhost variations', () => {\n      expect(isValidUrl('http://localhost')).toBe(true);\n      expect(isValidUrl('http://localhost:3000')).toBe(true);\n      expect(isValidUrl('https://127.0.0.1')).toBe(true);\n    });\n\n    it('should handle URL parsing failures in normalizeUrl', () => {\n      // Test with a URL that would cause parsing issues\n      // The normalizeUrl function will try to add protocol if missing\n      expect(normalizeUrl('invalid://malformed/url/')).toBe('https://invalid//malformed/url');\n\n      // Test with malformed URLs that get protocol added\n      expect(normalizeUrl('malformed-url')).toBe('https://malformed-url');\n    });\n\n    it('should handle errors in areUrlsEquivalent', () => {\n      // Test with invalid URLs that cause parsing errors\n      expect(areUrlsEquivalent('not-a-url', 'also-not-a-url')).toBe(false);\n      expect(areUrlsEquivalent('http://valid.com', 'not-a-url')).toBe(false);\n      expect(areUrlsEquivalent('', '')).toBe(false);\n    });\n\n    it('should handle validateAndNormalizeUrl edge cases', () => {\n      // Test with empty/null inputs\n      expect(validateAndNormalizeUrl('')).toBe(null);\n      expect(validateAndNormalizeUrl('   ')).toBe(null);\n\n      // Test with URLs that get protocol added (validateAndNormalizeUrl adds https:// to domain-like strings)\n      expect(validateAndNormalizeUrl('not-a-url')).toBe('https://not-a-url'); // Gets protocol added\n      expect(validateAndNormalizeUrl('ftp://invalid-protocol.com')).toBe('https://ftp://invalid-protocol.com'); // Gets protocol added\n\n      // Test with valid URLs\n      expect(validateAndNormalizeUrl('example.com')).toBe('https://example.com');\n      expect(validateAndNormalizeUrl('http://example.com')).toBe('http://example.com');\n    });\n\n    it('should handle areUrlsEquivalent with www normalization', () => {\n      expect(areUrlsEquivalent('http://www.example.com', 'https://example.com')).toBe(true);\n      expect(areUrlsEquivalent('https://www.example.com/path', 'http://example.com/path')).toBe(true);\n      expect(areUrlsEquivalent('www.example.com', 'example.com')).toBe(true);\n    });\n\n    it('should handle normalizeUrl with very short URLs', () => {\n      // Test URLs that are too short for trailing slash removal\n      expect(normalizeUrl('http://')).toBe('http://');\n      // This gets protocol added since it doesn't look like a complete URL\n      expect(normalizeUrl('https:/')).toBe('https://https');\n    });\n\n    it('should handle URL parsing edge cases', () => {\n      // Test malformed URLs that might cause parsing issues\n      expect(extractDomain('://malformed')).toBe('');\n      expect(isValidUrl('://malformed')).toBe(false);\n      expect(addProtocol('://malformed')).toBe('https://://malformed');\n    });\n  });\n});\n"], "version": 3}