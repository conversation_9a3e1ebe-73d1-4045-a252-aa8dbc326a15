a6abb51f59150fff8f39d9f6fc814d59
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const url_1 = require("../../src/utils/url");
describe('URL Utils', () => {
    describe('normalizeUrl', () => {
        it('should normalize URLs by adding protocol and removing trailing slash', () => {
            expect((0, url_1.normalizeUrl)('example.com')).toBe('https://example.com');
            expect((0, url_1.normalizeUrl)('www.example.com')).toBe('https://www.example.com');
            expect((0, url_1.normalizeUrl)('https://example.com/')).toBe('https://example.com');
            expect((0, url_1.normalizeUrl)('http://example.com/')).toBe('http://example.com');
        });
        it('should preserve existing protocols', () => {
            expect((0, url_1.normalizeUrl)('https://example.com')).toBe('https://example.com');
            expect((0, url_1.normalizeUrl)('http://example.com')).toBe('http://example.com');
        });
        it('should handle URLs with paths', () => {
            expect((0, url_1.normalizeUrl)('example.com/path')).toBe('https://example.com/path');
            expect((0, url_1.normalizeUrl)('https://example.com/path/')).toBe('https://example.com/path');
        });
        it('should handle URLs with query parameters', () => {
            expect((0, url_1.normalizeUrl)('example.com?param=value')).toBe('https://example.com?param=value');
            expect((0, url_1.normalizeUrl)('https://example.com/path?param=value')).toBe('https://example.com/path?param=value');
        });
        it('should return empty string for invalid input', () => {
            expect((0, url_1.normalizeUrl)('')).toBe('');
            expect((0, url_1.normalizeUrl)('   ')).toBe('');
        });
    });
    describe('isValidUrl', () => {
        it('should return true for valid URLs', () => {
            expect((0, url_1.isValidUrl)('https://example.com')).toBe(true);
            expect((0, url_1.isValidUrl)('http://test.org')).toBe(true);
            expect((0, url_1.isValidUrl)('https://subdomain.example.com/path')).toBe(true);
            expect((0, url_1.isValidUrl)('https://example.com:8080')).toBe(true);
        });
        it('should return false for invalid URLs', () => {
            expect((0, url_1.isValidUrl)('example.com')).toBe(false);
            expect((0, url_1.isValidUrl)('ftp://example.com')).toBe(false);
            expect((0, url_1.isValidUrl)('')).toBe(false);
            expect((0, url_1.isValidUrl)('not-a-url')).toBe(false);
            expect((0, url_1.isValidUrl)('javascript:alert(1)')).toBe(false);
        });
    });
    describe('extractDomain', () => {
        it('should extract domain from URLs', () => {
            expect((0, url_1.extractDomain)('https://example.com')).toBe('example.com');
            expect((0, url_1.extractDomain)('http://www.test.org')).toBe('www.test.org');
            expect((0, url_1.extractDomain)('https://subdomain.example.com/path')).toBe('subdomain.example.com');
            expect((0, url_1.extractDomain)('https://example.com:8080/path')).toBe('example.com');
        });
        it('should return empty string for invalid URLs', () => {
            expect((0, url_1.extractDomain)('invalid-url')).toBe('');
            expect((0, url_1.extractDomain)('')).toBe('');
        });
    });
    describe('addProtocol', () => {
        it('should add https protocol to URLs without protocol', () => {
            expect((0, url_1.addProtocol)('example.com')).toBe('https://example.com');
            expect((0, url_1.addProtocol)('www.example.com')).toBe('https://www.example.com');
        });
        it('should preserve existing protocols', () => {
            expect((0, url_1.addProtocol)('https://example.com')).toBe('https://example.com');
            expect((0, url_1.addProtocol)('http://example.com')).toBe('http://example.com');
        });
        it('should handle empty or invalid input', () => {
            expect((0, url_1.addProtocol)('')).toBe('');
            expect((0, url_1.addProtocol)('   ')).toBe('');
        });
    });
    describe('cleanUrl', () => {
        it('should remove common tracking parameters', () => {
            expect((0, url_1.cleanUrl)('https://example.com?utm_source=google')).toBe('https://example.com');
            expect((0, url_1.cleanUrl)('https://example.com?utm_medium=cpc&param=value')).toBe('https://example.com/?param=value');
            expect((0, url_1.cleanUrl)('https://example.com?fbclid=123&gclid=456')).toBe('https://example.com');
        });
        it('should preserve important parameters', () => {
            expect((0, url_1.cleanUrl)('https://example.com?id=123&page=2')).toBe('https://example.com/?id=123&page=2');
        });
        it('should handle URLs without parameters', () => {
            expect((0, url_1.cleanUrl)('https://example.com')).toBe('https://example.com');
            expect((0, url_1.cleanUrl)('https://example.com/path')).toBe('https://example.com/path');
        });
        it('should handle malformed URLs gracefully', () => {
            expect((0, url_1.cleanUrl)('not-a-url')).toBe('not-a-url');
            expect((0, url_1.cleanUrl)('')).toBe('');
        });
        it('should handle all tracking parameters', () => {
            const urlWithAllTracking = 'https://example.com?utm_source=google&utm_medium=cpc&utm_campaign=test&utm_term=keyword&utm_content=ad&fbclid=123&gclid=456&msclkid=789&ref=twitter&source=facebook';
            // Note: ref and source are not in the tracking parameters list, so they remain
            expect((0, url_1.cleanUrl)(urlWithAllTracking)).toBe('https://example.com/?ref=twitter&source=facebook');
        });
        it('should handle mixed tracking and non-tracking parameters', () => {
            const mixedUrl = 'https://example.com?id=123&utm_source=google&page=2&fbclid=456&category=tech';
            expect((0, url_1.cleanUrl)(mixedUrl)).toBe('https://example.com/?id=123&page=2&category=tech');
        });
        it('should preserve hash fragments', () => {
            expect((0, url_1.cleanUrl)('https://example.com?utm_source=google#section1')).toBe('https://example.com/#section1');
        });
        it('should handle URLs with ports', () => {
            expect((0, url_1.cleanUrl)('https://example.com:8080?utm_source=google')).toBe('https://example.com:8080');
        });
        it('should handle URLs with paths', () => {
            expect((0, url_1.cleanUrl)('https://example.com/path/to/page?utm_source=google&id=123')).toBe('https://example.com/path/to/page?id=123');
        });
    });
    describe('edge cases and error handling', () => {
        it('should handle malformed URLs in isValidUrl', () => {
            expect((0, url_1.isValidUrl)('http://')).toBe(false);
            expect((0, url_1.isValidUrl)('https://')).toBe(false);
            expect((0, url_1.isValidUrl)('ftp://example.com')).toBe(false);
            expect((0, url_1.isValidUrl)('javascript:alert(1)')).toBe(false);
        });
        it('should handle special characters in URLs', () => {
            // URLs with spaces are actually valid in modern browsers
            expect((0, url_1.isValidUrl)('https://example.com/path with spaces')).toBe(true);
            expect((0, url_1.isValidUrl)('https://example.com/path%20with%20encoded%20spaces')).toBe(true);
        });
        it('should handle international domain names', () => {
            expect((0, url_1.isValidUrl)('https://例え.テスト')).toBe(true);
            expect((0, url_1.isValidUrl)('https://xn--r8jz45g.xn--zckzah')).toBe(true); // Punycode
        });
        it('should handle URLs with authentication', () => {
            expect((0, url_1.isValidUrl)('https://user:<EMAIL>')).toBe(true);
        });
        it('should handle normalizeUrl edge cases', () => {
            // normalizeUrl checks for lowercase protocols, so uppercase gets https:// added
            expect((0, url_1.normalizeUrl)('HTTP://EXAMPLE.COM')).toBe('https://HTTP://EXAMPLE.COM');
            // normalizeUrl removes trailing slash for root path, which also removes default ports
            expect((0, url_1.normalizeUrl)('https://example.com:443')).toBe('https://example.com');
            expect((0, url_1.normalizeUrl)('http://example.com:80')).toBe('http://example.com');
            expect((0, url_1.normalizeUrl)('https://example.com:8080')).toBe('https://example.com:8080');
        });
        it('should handle extractDomain edge cases', () => {
            expect((0, url_1.extractDomain)('https://sub.example.com')).toBe('sub.example.com');
            expect((0, url_1.extractDomain)('https://example.com:8080')).toBe('example.com');
            expect((0, url_1.extractDomain)('https://user:<EMAIL>')).toBe('example.com');
        });
        it('should handle URLs with unusual but valid schemes', () => {
            expect((0, url_1.isValidUrl)('https://example.com')).toBe(true);
            expect((0, url_1.isValidUrl)('http://example.com')).toBe(true);
        });
        it('should handle empty and null inputs', () => {
            expect((0, url_1.isValidUrl)('')).toBe(false);
            expect((0, url_1.extractDomain)('')).toBe('');
            expect((0, url_1.normalizeUrl)('')).toBe('');
        });
        it('should handle URLs with query parameters only', () => {
            expect((0, url_1.cleanUrl)('https://example.com?')).toBe('https://example.com');
            expect((0, url_1.cleanUrl)('https://example.com?&')).toBe('https://example.com');
        });
        it('should handle URLs with encoded characters', () => {
            expect((0, url_1.cleanUrl)('https://example.com?utm_source=google%20ads&id=123')).toBe('https://example.com/?id=123');
        });
        it('should handle case sensitivity in domains', () => {
            // normalizeUrl preserves case in URLs
            expect((0, url_1.normalizeUrl)('https://EXAMPLE.COM/PATH')).toBe('https://EXAMPLE.COM/PATH');
        });
        it('should handle trailing slashes consistently', () => {
            // normalizeUrl removes trailing slash for root path
            expect((0, url_1.normalizeUrl)('https://example.com/')).toBe('https://example.com');
            expect((0, url_1.normalizeUrl)('https://example.com')).toBe('https://example.com');
        });
        it('should handle IPv4 addresses', () => {
            expect((0, url_1.isValidUrl)('https://***********')).toBe(true);
            expect((0, url_1.extractDomain)('https://***********:8080')).toBe('***********');
        });
        it('should handle IPv6 addresses', () => {
            expect((0, url_1.isValidUrl)('https://[::1]')).toBe(true);
            expect((0, url_1.isValidUrl)('https://[2001:db8::1]')).toBe(true);
        });
        it('should handle localhost variations', () => {
            expect((0, url_1.isValidUrl)('http://localhost')).toBe(true);
            expect((0, url_1.isValidUrl)('http://localhost:3000')).toBe(true);
            expect((0, url_1.isValidUrl)('https://127.0.0.1')).toBe(true);
        });
        it('should handle URL parsing failures in normalizeUrl', () => {
            // Test with a URL that would cause parsing issues
            // The normalizeUrl function will try to add protocol if missing
            expect((0, url_1.normalizeUrl)('invalid://malformed/url/')).toBe('https://invalid//malformed/url');
            // Test with malformed URLs that get protocol added
            expect((0, url_1.normalizeUrl)('malformed-url')).toBe('https://malformed-url');
        });
        it('should handle errors in areUrlsEquivalent', () => {
            // Test with invalid URLs that cause parsing errors
            expect((0, url_1.areUrlsEquivalent)('not-a-url', 'also-not-a-url')).toBe(false);
            expect((0, url_1.areUrlsEquivalent)('http://valid.com', 'not-a-url')).toBe(false);
            expect((0, url_1.areUrlsEquivalent)('', '')).toBe(false);
        });
        it('should handle validateAndNormalizeUrl edge cases', () => {
            // Test with empty/null inputs
            expect((0, url_1.validateAndNormalizeUrl)('')).toBe(null);
            expect((0, url_1.validateAndNormalizeUrl)('   ')).toBe(null);
            // Test with URLs that get protocol added (validateAndNormalizeUrl adds https:// to domain-like strings)
            expect((0, url_1.validateAndNormalizeUrl)('not-a-url')).toBe('https://not-a-url'); // Gets protocol added
            expect((0, url_1.validateAndNormalizeUrl)('ftp://invalid-protocol.com')).toBe('https://ftp://invalid-protocol.com'); // Gets protocol added
            // Test with valid URLs
            expect((0, url_1.validateAndNormalizeUrl)('example.com')).toBe('https://example.com');
            expect((0, url_1.validateAndNormalizeUrl)('http://example.com')).toBe('http://example.com');
        });
        it('should handle areUrlsEquivalent with www normalization', () => {
            expect((0, url_1.areUrlsEquivalent)('http://www.example.com', 'https://example.com')).toBe(true);
            expect((0, url_1.areUrlsEquivalent)('https://www.example.com/path', 'http://example.com/path')).toBe(true);
            expect((0, url_1.areUrlsEquivalent)('www.example.com', 'example.com')).toBe(true);
        });
        it('should handle normalizeUrl with very short URLs', () => {
            // Test URLs that are too short for trailing slash removal
            expect((0, url_1.normalizeUrl)('http://')).toBe('http://');
            // This gets protocol added since it doesn't look like a complete URL
            expect((0, url_1.normalizeUrl)('https:/')).toBe('https://https');
        });
        it('should handle URL parsing edge cases', () => {
            // Test malformed URLs that might cause parsing issues
            expect((0, url_1.extractDomain)('://malformed')).toBe('');
            expect((0, url_1.isValidUrl)('://malformed')).toBe(false);
            expect((0, url_1.addProtocol)('://malformed')).toBe('https://://malformed');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************