{"file": "/Users/<USER>/WebstormProjects/goo/src/utils/url.ts", "mappings": ";AAAA;;GAEG;;AAOH,oCA+BC;AAOD,gCAWC;AAOD,sCAWC;AAOD,kCAYC;AAOD,4BA8CC;AAQD,8CAaC;AAOD,0DAOC;AAnLD;;;;GAIG;AACH,SAAgB,YAAY,CAAC,GAAW;IACtC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;QACxB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,IAAI,UAAU,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;IAE5B,0BAA0B;IAC1B,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;QAC5E,UAAU,GAAG,WAAW,UAAU,EAAE,CAAC;IACvC,CAAC;IAED,+CAA+C;IAC/C,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;QACnC,IAAI,MAAM,CAAC,QAAQ,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,IAAI,MAAM,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;YAC1E,0DAA0D;YAC1D,UAAU,GAAG,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,IAAI,EAAE,CAAC;QACpD,CAAC;aAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,QAAQ,KAAK,GAAG,EAAE,CAAC;YACpE,4CAA4C;YAC5C,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/C,UAAU,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QACjC,CAAC;IACH,CAAC;IAAC,MAAM,CAAC;QACP,+DAA+D;QAC/D,IAAI,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtD,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;;;GAIG;AACH,SAAgB,UAAU,CAAC,GAAW;IACpC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;QACxB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5B,OAAO,MAAM,CAAC,QAAQ,KAAK,OAAO,IAAI,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAAC;IACrE,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,aAAa,CAAC,GAAW;IACvC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;QACxB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5B,OAAO,MAAM,CAAC,QAAQ,CAAC;IACzB,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,WAAW,CAAC,GAAW;IACrC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;QACxB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;IAE3B,IAAI,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;QACpE,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,OAAO,WAAW,OAAO,EAAE,CAAC;AAC9B,CAAC;AAED;;;;GAIG;AACH,SAAgB,QAAQ,CAAC,GAAW;IAClC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;QACxB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5B,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAElD,wCAAwC;QACxC,MAAM,cAAc,GAAG;YACrB,YAAY;YACZ,YAAY;YACZ,cAAc;YACd,UAAU;YACV,aAAa;YACb,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,OAAO;YACP,SAAS;YACT,KAAK;YACL,KAAK;YACL,QAAQ;YACR,QAAQ;SACT,CAAC;QAEF,6BAA6B;QAC7B,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC7B,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,cAAc;QACd,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAElC,yEAAyE;QACzE,IAAI,MAAM,CAAC,QAAQ,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,IAAI,MAAM,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;YAC1E,OAAO,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,IAAI,EAAE,CAAC;QAC9C,CAAC;QAED,sDAAsD;QACtD,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC;IAC3B,CAAC;IAAC,MAAM,CAAC;QACP,wCAAwC;QACxC,OAAO,GAAG,CAAC;IACb,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,SAAgB,iBAAiB,CAAC,IAAY,EAAE,IAAY;IAC1D,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,CAAC,GAAW,EAAE,EAAE;YAChC,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1C,mCAAmC;YACnC,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YACvD,OAAO,GAAG,QAAQ,GAAG,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QACvE,CAAC,CAAC;QAEF,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,uBAAuB,CAAC,GAAW;IACjD,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,UAAU,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;IACrC,OAAO,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;AACpD,CAAC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/src/utils/url.ts"], "sourcesContent": ["/**\n * URL utility functions for normalizing, validating, and cleaning URLs\n */\n\n/**\n * Normalizes a URL by adding protocol and removing trailing slash\n * @param url - The URL to normalize\n * @returns Normalized URL\n */\nexport function normalizeUrl(url: string): string {\n  if (!url || !url.trim()) {\n    return '';\n  }\n\n  let normalized = url.trim();\n\n  // Add protocol if missing\n  if (!normalized.startsWith('http://') && !normalized.startsWith('https://')) {\n    normalized = `https://${normalized}`;\n  }\n\n  // Remove trailing slash (except for root path)\n  try {\n    const urlObj = new URL(normalized);\n    if (urlObj.pathname === '/' && urlObj.search === '' && urlObj.hash === '') {\n      // For root path with no query/hash, remove trailing slash\n      normalized = `${urlObj.protocol}//${urlObj.host}`;\n    } else if (urlObj.pathname.endsWith('/') && urlObj.pathname !== '/') {\n      // For non-root paths, remove trailing slash\n      urlObj.pathname = urlObj.pathname.slice(0, -1);\n      normalized = urlObj.toString();\n    }\n  } catch {\n    // If URL parsing fails, just remove trailing slash from string\n    if (normalized.endsWith('/') && normalized.length > 8) {\n      normalized = normalized.slice(0, -1);\n    }\n  }\n\n  return normalized;\n}\n\n/**\n * Validates if a string is a valid HTTP/HTTPS URL\n * @param url - The URL to validate\n * @returns True if valid, false otherwise\n */\nexport function isValidUrl(url: string): boolean {\n  if (!url || !url.trim()) {\n    return false;\n  }\n\n  try {\n    const urlObj = new URL(url);\n    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Extracts the domain from a URL\n * @param url - The URL to extract domain from\n * @returns Domain name or empty string if invalid\n */\nexport function extractDomain(url: string): string {\n  if (!url || !url.trim()) {\n    return '';\n  }\n\n  try {\n    const urlObj = new URL(url);\n    return urlObj.hostname;\n  } catch {\n    return '';\n  }\n}\n\n/**\n * Adds HTTPS protocol to a URL if no protocol is present\n * @param url - The URL to add protocol to\n * @returns URL with protocol\n */\nexport function addProtocol(url: string): string {\n  if (!url || !url.trim()) {\n    return '';\n  }\n\n  const trimmed = url.trim();\n  \n  if (trimmed.startsWith('http://') || trimmed.startsWith('https://')) {\n    return trimmed;\n  }\n\n  return `https://${trimmed}`;\n}\n\n/**\n * Removes tracking parameters and cleans up URL\n * @param url - The URL to clean\n * @returns Cleaned URL\n */\nexport function cleanUrl(url: string): string {\n  if (!url || !url.trim()) {\n    return '';\n  }\n\n  try {\n    const urlObj = new URL(url);\n    const params = new URLSearchParams(urlObj.search);\n\n    // List of tracking parameters to remove\n    const trackingParams = [\n      'utm_source',\n      'utm_medium',\n      'utm_campaign',\n      'utm_term',\n      'utm_content',\n      'fbclid',\n      'gclid',\n      'gclsrc',\n      'dclid',\n      'msclkid',\n      '_ga',\n      '_gl',\n      'mc_cid',\n      'mc_eid'\n    ];\n\n    // Remove tracking parameters\n    trackingParams.forEach(param => {\n      params.delete(param);\n    });\n\n    // Rebuild URL\n    urlObj.search = params.toString();\n\n    // Remove trailing slash for root path only if no query parameters remain\n    if (urlObj.pathname === '/' && urlObj.search === '' && urlObj.hash === '') {\n      return `${urlObj.protocol}//${urlObj.host}`;\n    }\n\n    // For root path with query parameters, keep the slash\n    return urlObj.toString();\n  } catch {\n    // If URL parsing fails, return original\n    return url;\n  }\n}\n\n/**\n * Checks if two URLs point to the same resource (ignoring protocol and www)\n * @param url1 - First URL\n * @param url2 - Second URL\n * @returns True if URLs are equivalent\n */\nexport function areUrlsEquivalent(url1: string, url2: string): boolean {\n  try {\n    const normalize = (url: string) => {\n      const urlObj = new URL(normalizeUrl(url));\n      // Remove www prefix for comparison\n      const hostname = urlObj.hostname.replace(/^www\\./, '');\n      return `${hostname}${urlObj.pathname}${urlObj.search}${urlObj.hash}`;\n    };\n\n    return normalize(url1) === normalize(url2);\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Validates and normalizes a website URL\n * @param url - The URL to validate and normalize\n * @returns Normalized URL or null if invalid\n */\nexport function validateAndNormalizeUrl(url: string): string | null {\n  if (!url || !url.trim()) {\n    return null;\n  }\n\n  const normalized = normalizeUrl(url);\n  return isValidUrl(normalized) ? normalized : null;\n}\n"], "version": 3}