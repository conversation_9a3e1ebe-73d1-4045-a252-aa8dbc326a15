fed2aac0f487ed532cd4d1059ad1f97c
"use strict";
/**
 * URL utility functions for normalizing, validating, and cleaning URLs
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.normalizeUrl = normalizeUrl;
exports.isValidUrl = isValidUrl;
exports.extractDomain = extractDomain;
exports.addProtocol = addProtocol;
exports.cleanUrl = cleanUrl;
exports.areUrlsEquivalent = areUrlsEquivalent;
exports.validateAndNormalizeUrl = validateAndNormalizeUrl;
/**
 * Normalizes a URL by adding protocol and removing trailing slash
 * @param url - The URL to normalize
 * @returns Normalized URL
 */
function normalizeUrl(url) {
    if (!url || !url.trim()) {
        return '';
    }
    let normalized = url.trim();
    // Add protocol if missing
    if (!normalized.startsWith('http://') && !normalized.startsWith('https://')) {
        normalized = `https://${normalized}`;
    }
    // Remove trailing slash (except for root path)
    try {
        const urlObj = new URL(normalized);
        if (urlObj.pathname === '/' && urlObj.search === '' && urlObj.hash === '') {
            // For root path with no query/hash, remove trailing slash
            normalized = `${urlObj.protocol}//${urlObj.host}`;
        }
        else if (urlObj.pathname.endsWith('/') && urlObj.pathname !== '/') {
            // For non-root paths, remove trailing slash
            urlObj.pathname = urlObj.pathname.slice(0, -1);
            normalized = urlObj.toString();
        }
    }
    catch {
        // If URL parsing fails, just remove trailing slash from string
        if (normalized.endsWith('/') && normalized.length > 8) {
            normalized = normalized.slice(0, -1);
        }
    }
    return normalized;
}
/**
 * Validates if a string is a valid HTTP/HTTPS URL
 * @param url - The URL to validate
 * @returns True if valid, false otherwise
 */
function isValidUrl(url) {
    if (!url || !url.trim()) {
        return false;
    }
    try {
        const urlObj = new URL(url);
        return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    }
    catch {
        return false;
    }
}
/**
 * Extracts the domain from a URL
 * @param url - The URL to extract domain from
 * @returns Domain name or empty string if invalid
 */
function extractDomain(url) {
    if (!url || !url.trim()) {
        return '';
    }
    try {
        const urlObj = new URL(url);
        return urlObj.hostname;
    }
    catch {
        return '';
    }
}
/**
 * Adds HTTPS protocol to a URL if no protocol is present
 * @param url - The URL to add protocol to
 * @returns URL with protocol
 */
function addProtocol(url) {
    if (!url || !url.trim()) {
        return '';
    }
    const trimmed = url.trim();
    if (trimmed.startsWith('http://') || trimmed.startsWith('https://')) {
        return trimmed;
    }
    return `https://${trimmed}`;
}
/**
 * Removes tracking parameters and cleans up URL
 * @param url - The URL to clean
 * @returns Cleaned URL
 */
function cleanUrl(url) {
    if (!url || !url.trim()) {
        return '';
    }
    try {
        const urlObj = new URL(url);
        const params = new URLSearchParams(urlObj.search);
        // List of tracking parameters to remove
        const trackingParams = [
            'utm_source',
            'utm_medium',
            'utm_campaign',
            'utm_term',
            'utm_content',
            'fbclid',
            'gclid',
            'gclsrc',
            'dclid',
            'msclkid',
            '_ga',
            '_gl',
            'mc_cid',
            'mc_eid'
        ];
        // Remove tracking parameters
        trackingParams.forEach(param => {
            params.delete(param);
        });
        // Rebuild URL
        urlObj.search = params.toString();
        // Remove trailing slash for root path only if no query parameters remain
        if (urlObj.pathname === '/' && urlObj.search === '' && urlObj.hash === '') {
            return `${urlObj.protocol}//${urlObj.host}`;
        }
        // For root path with query parameters, keep the slash
        return urlObj.toString();
    }
    catch {
        // If URL parsing fails, return original
        return url;
    }
}
/**
 * Checks if two URLs point to the same resource (ignoring protocol and www)
 * @param url1 - First URL
 * @param url2 - Second URL
 * @returns True if URLs are equivalent
 */
function areUrlsEquivalent(url1, url2) {
    try {
        const normalize = (url) => {
            const urlObj = new URL(normalizeUrl(url));
            // Remove www prefix for comparison
            const hostname = urlObj.hostname.replace(/^www\./, '');
            return `${hostname}${urlObj.pathname}${urlObj.search}${urlObj.hash}`;
        };
        return normalize(url1) === normalize(url2);
    }
    catch {
        return false;
    }
}
/**
 * Validates and normalizes a website URL
 * @param url - The URL to validate and normalize
 * @returns Normalized URL or null if invalid
 */
function validateAndNormalizeUrl(url) {
    if (!url || !url.trim()) {
        return null;
    }
    const normalized = normalizeUrl(url);
    return isValidUrl(normalized) ? normalized : null;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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