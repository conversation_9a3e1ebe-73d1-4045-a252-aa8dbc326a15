fe2a81d555eb575804c5ef3a4579215f
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Mock the services
jest.mock('../src/services/geocoding');
jest.mock('../src/services/businessSearch');
jest.mock('../src/services/googlePlaces');
jest.mock('../src/services/websiteVerification');
jest.mock('../src/services/dataManager');
const BusinessSearchApp_1 = require("../src/BusinessSearchApp");
const Errors_1 = require("../src/models/Errors");
const businessSearch_1 = require("../src/services/businessSearch");
const dataManager_1 = require("../src/services/dataManager");
describe('BusinessSearchApp', () => {
    let app;
    let mockBusinessSearchService;
    let mockDataManager;
    let mockSearchResult;
    let mockSearchRequest;
    beforeEach(() => {
        // Reset mocks
        jest.clearAllMocks();
        // Create mock search result
        mockSearchResult = {
            searchParams: {
                zipCode: '10001',
                radius: 5,
                businessType: 'restaurant',
                timestamp: new Date('2023-06-01')
            },
            results: {
                withWebsites: [],
                withoutWebsites: []
            },
            statistics: {
                totalFound: 0,
                withWebsiteCount: 0,
                withoutWebsiteCount: 0,
                websiteAdoptionRate: 0,
                searchDuration: 1000
            }
        };
        mockSearchRequest = {
            zipCode: '10001',
            radius: 5,
            businessType: 'restaurant'
        };
        // Setup mock business search service
        mockBusinessSearchService = {
            searchBusinesses: jest.fn().mockResolvedValue(mockSearchResult),
            getSearchHistory: jest.fn().mockReturnValue([]),
            clearSearchHistory: jest.fn(),
            getSearchStatistics: jest.fn().mockReturnValue({
                totalSearches: 0,
                averageResultsPerSearch: 0,
                averageWebsiteAdoptionRate: 0,
                averageSearchDuration: 0
            }),
            reset: jest.fn()
        };
        // Setup mock data manager
        mockDataManager = {
            saveSearchResult: jest.fn().mockReturnValue('test-key'),
            getSearchResult: jest.fn().mockReturnValue(null),
            getAllSearchResults: jest.fn().mockReturnValue([]),
            deleteSearchResult: jest.fn(),
            clearAllData: jest.fn(),
            getStorageInfo: jest.fn().mockReturnValue({
                totalEntries: 0,
                totalSizeBytes: 0,
                oldestEntry: null,
                newestEntry: null
            }),
            cleanupExpiredData: jest.fn().mockReturnValue(0),
            exportData: jest.fn().mockReturnValue('{"version":"1.0","exportDate":"2023-06-01","searchResults":[]}'),
            importData: jest.fn().mockReturnValue(0),
            getCacheSize: jest.fn().mockReturnValue(0)
        };
        // Mock the constructor to inject our mocks
        businessSearch_1.BusinessSearchService.mockImplementation(() => mockBusinessSearchService);
        dataManager_1.DataManager.mockImplementation(() => mockDataManager);
        app = new BusinessSearchApp_1.BusinessSearchApp();
    });
    describe('searchBusinesses', () => {
        it('should perform a complete business search', async () => {
            const searchRequest = {
                zipCode: '10001',
                radius: 10,
                businessType: 'restaurant',
            };
            // With mocked services, this should succeed and return a result
            const result = await app.searchBusinesses(searchRequest);
            expect(result).toHaveProperty('searchParams');
            expect(result).toHaveProperty('results');
            expect(result).toHaveProperty('statistics');
        });
        it('should validate search parameters', async () => {
            const invalidRequest = {
                zipCode: 'invalid',
                radius: 10,
                businessType: 'restaurant',
            };
            await expect(app.searchBusinesses(invalidRequest)).rejects.toThrow(Errors_1.ValidationError);
        });
        it('should validate radius range', async () => {
            const invalidRequest = {
                zipCode: '10001',
                radius: 0,
                businessType: 'restaurant',
            };
            await expect(app.searchBusinesses(invalidRequest)).rejects.toThrow(Errors_1.ValidationError);
        });
        it('should validate business type', async () => {
            const invalidRequest = {
                zipCode: '10001',
                radius: 10,
                businessType: 'invalid_type',
            };
            await expect(app.searchBusinesses(invalidRequest)).rejects.toThrow(Errors_1.ValidationError);
        });
    });
    describe('getSearchHistory', () => {
        it('should return search history', () => {
            const history = app.getSearchHistory();
            expect(Array.isArray(history)).toBe(true);
        });
    });
    describe('clearSearchHistory', () => {
        it('should clear search history', () => {
            app.clearSearchHistory();
            const history = app.getSearchHistory();
            expect(history).toHaveLength(0);
        });
    });
    describe('getSearchStatistics', () => {
        it('should return search statistics', () => {
            const stats = app.getSearchStatistics();
            expect(stats).toHaveProperty('totalSearches');
            expect(stats).toHaveProperty('averageResultsPerSearch');
            expect(stats).toHaveProperty('averageWebsiteAdoptionRate');
            expect(stats).toHaveProperty('averageSearchDuration');
        });
    });
    describe('exportData', () => {
        it('should export search data', () => {
            const exported = app.exportData();
            expect(typeof exported).toBe('string');
            const parsed = JSON.parse(exported);
            expect(parsed).toHaveProperty('version');
            expect(parsed).toHaveProperty('exportDate');
            expect(parsed).toHaveProperty('searchResults');
        });
    });
    describe('importData', () => {
        it('should import search data', () => {
            const exportData = {
                version: '1.0',
                exportDate: new Date().toISOString(),
                searchResults: [],
            };
            const importedCount = app.importData(JSON.stringify(exportData));
            expect(importedCount).toBe(0);
        });
        it('should handle invalid import data', () => {
            // Override the mock to throw for invalid JSON
            mockDataManager.importData.mockImplementation((jsonData) => {
                if (jsonData === 'invalid-json') {
                    throw new Errors_1.CacheError('Failed to import data: Unexpected token i in JSON at position 0');
                }
                return 0;
            });
            expect(() => app.importData('invalid-json')).toThrow(Errors_1.CacheError);
        });
    });
    describe('getStorageInfo', () => {
        it('should return storage information', () => {
            const info = app.getStorageInfo();
            expect(info).toHaveProperty('totalEntries');
            expect(info).toHaveProperty('totalSizeBytes');
            expect(info).toHaveProperty('oldestEntry');
            expect(info).toHaveProperty('newestEntry');
        });
    });
    describe('clearAllData', () => {
        it('should clear all application data', () => {
            app.clearAllData();
            const history = app.getSearchHistory();
            const stats = app.getSearchStatistics();
            expect(history).toHaveLength(0);
            expect(stats.totalSearches).toBe(0);
        });
    });
    describe('configuration', () => {
        it('should allow custom configuration', () => {
            const customApp = new BusinessSearchApp_1.BusinessSearchApp({
                cacheExpirationHours: 48,
                maxConcurrentRequests: 10,
                requestTimeoutMs: 10000,
            });
            expect(customApp).toBeInstanceOf(BusinessSearchApp_1.BusinessSearchApp);
        });
        it('should use default configuration when none provided', () => {
            const defaultApp = new BusinessSearchApp_1.BusinessSearchApp();
            expect(defaultApp).toBeInstanceOf(BusinessSearchApp_1.BusinessSearchApp);
        });
    });
    describe('error handling', () => {
        it('should handle missing API key gracefully', () => {
            // Remove API key from environment
            const originalKey = process.env.GOOGLE_PLACES_API_KEY;
            delete process.env.GOOGLE_PLACES_API_KEY;
            expect(() => new BusinessSearchApp_1.BusinessSearchApp()).toThrow();
            // Restore API key
            if (originalKey) {
                process.env.GOOGLE_PLACES_API_KEY = originalKey;
            }
        });
        it('should handle API key from config', () => {
            // Remove API key from environment
            const originalKey = process.env.GOOGLE_PLACES_API_KEY;
            delete process.env.GOOGLE_PLACES_API_KEY;
            expect(() => new BusinessSearchApp_1.BusinessSearchApp({ apiKey: 'test-key' })).not.toThrow();
            // Restore API key
            if (originalKey) {
                process.env.GOOGLE_PLACES_API_KEY = originalKey;
            }
        });
    });
    describe('service integration', () => {
        it('should initialize all required services', () => {
            expect(app).toHaveProperty('searchBusinesses');
            expect(app).toHaveProperty('getSearchHistory');
            expect(app).toHaveProperty('getSearchStatistics');
            expect(app).toHaveProperty('exportData');
            expect(app).toHaveProperty('importData');
        });
    });
    describe('cleanupExpiredData', () => {
        it('should cleanup expired data', () => {
            const removedCount = app.cleanupExpiredData();
            expect(typeof removedCount).toBe('number');
            expect(removedCount).toBeGreaterThanOrEqual(0);
        });
    });
    describe('getConfig', () => {
        it('should return readonly configuration', () => {
            const config = app.getConfig();
            expect(config).toHaveProperty('apiKey');
            expect(config).toHaveProperty('cacheExpirationHours');
            expect(config).toHaveProperty('maxConcurrentRequests');
            expect(config).toHaveProperty('requestTimeoutMs');
            expect(config).toHaveProperty('rateLimitRequestsPerSecond');
            expect(config).toHaveProperty('rateLimitBurstSize');
        });
        it('should return default values when not specified', () => {
            const config = app.getConfig();
            expect(config.cacheExpirationHours).toBe(24);
            expect(config.maxConcurrentRequests).toBe(5);
            expect(config.requestTimeoutMs).toBe(5000);
        });
    });
    describe('getHealthStatus', () => {
        it('should return health status for all services', async () => {
            const health = await app.getHealthStatus();
            expect(health).toHaveProperty('geocoding');
            expect(health).toHaveProperty('places');
            expect(health).toHaveProperty('websiteVerification');
            expect(health).toHaveProperty('storage');
            expect(typeof health.geocoding).toBe('boolean');
            expect(typeof health.places).toBe('boolean');
            expect(typeof health.websiteVerification).toBe('boolean');
            expect(typeof health.storage).toBe('boolean');
        });
        it('should handle service failures gracefully', async () => {
            // This test will likely fail due to missing API key, but should not throw
            const health = await app.getHealthStatus();
            expect(health.websiteVerification).toBe(true); // This should always be true
        });
    });
    describe('search history deduplication', () => {
        it('should deduplicate search results', () => {
            // This tests the private deduplicateSearchResults method indirectly
            const history1 = app.getSearchHistory();
            const history2 = app.getSearchHistory();
            // Should return the same results
            expect(history1).toEqual(history2);
        });
    });
    describe('advanced configuration', () => {
        it('should accept all configuration options', () => {
            const customApp = new BusinessSearchApp_1.BusinessSearchApp({
                apiKey: 'test-key',
                cacheExpirationHours: 48,
                maxConcurrentRequests: 10,
                requestTimeoutMs: 10000,
                rateLimitRequestsPerSecond: 5,
                rateLimitBurstSize: 15,
            });
            const config = customApp.getConfig();
            expect(config.cacheExpirationHours).toBe(48);
            expect(config.maxConcurrentRequests).toBe(10);
            expect(config.requestTimeoutMs).toBe(10000);
            expect(config.rateLimitRequestsPerSecond).toBe(5);
            expect(config.rateLimitBurstSize).toBe(15);
        });
    });
    describe('data persistence integration', () => {
        it('should save search results automatically', async () => {
            // Mock a successful search that would save data
            const searchRequest = {
                zipCode: '10001',
                radius: 5,
                businessType: 'restaurant',
            };
            // This will fail due to missing API key, but tests the flow
            try {
                await app.searchBusinesses(searchRequest);
            }
            catch (error) {
                // Expected to fail without API key
                expect(error).toBeDefined();
            }
        });
        it('should integrate search history from multiple sources', () => {
            const history = app.getSearchHistory();
            expect(Array.isArray(history)).toBe(true);
            // Should combine memory and persistent storage
            // Even if empty, should not throw
        });
    });
    describe('cache management', () => {
        it('should clear all caches when clearing data', () => {
            // This tests that clearAllData calls all service cache clears
            expect(() => app.clearAllData()).not.toThrow();
        });
    });
    describe('error handling and edge cases', () => {
        it('should handle dataManager save errors in searchBusinesses', async () => {
            // Mock the business search service to return a result
            const mockBusinessSearchService = {
                searchBusinesses: jest.fn().mockResolvedValue(mockSearchResult)
            };
            // Replace the service in the app
            app.businessSearchService = mockBusinessSearchService;
            // Mock dataManager to throw error on save
            const originalSave = app['dataManager'].saveSearchResult;
            app['dataManager'].saveSearchResult = jest.fn().mockImplementation(() => {
                throw new Error('Storage full');
            });
            // Should still return result even if save fails
            const result = await app.searchBusinesses(mockSearchRequest);
            expect(result).toEqual(mockSearchResult);
            // Restore original method
            app['dataManager'].saveSearchResult = originalSave;
        });
        it('should handle deduplication in getSearchHistory', () => {
            const duplicateResult = { ...mockSearchResult };
            // Mock the business search service
            const mockBusinessSearchService = {
                getSearchHistory: jest.fn().mockReturnValue([mockSearchResult])
            };
            app.businessSearchService = mockBusinessSearchService;
            // Mock dataManager
            app['dataManager'].getAllSearchResults = jest.fn().mockReturnValue([duplicateResult]);
            const history = app.getSearchHistory();
            // Should only return one result (deduplicated)
            expect(history).toHaveLength(1);
            expect(history[0]).toEqual(mockSearchResult);
        });
        it('should handle successful geocoding health check', async () => {
            // Mock the geocoding service directly on the app instance
            const mockGeocodingService = {
                zipCodeToCoordinates: jest.fn().mockResolvedValue({ lat: 40.7128, lng: -74.0060 })
            };
            app.geocodingService = mockGeocodingService;
            const status = await app.getHealthStatus();
            expect(status.geocoding).toBe(true);
        });
        it('should handle storage health check errors', async () => {
            // Mock dataManager to throw error on save
            const originalSave = app['dataManager'].saveSearchResult;
            app['dataManager'].saveSearchResult = jest.fn().mockImplementation(() => {
                throw new Error('Storage error');
            });
            const status = await app.getHealthStatus();
            expect(status.storage).toBe(false);
            // Restore original method
            app['dataManager'].saveSearchResult = originalSave;
        });
        it('should handle search history sorting with multiple results', () => {
            const olderResult = {
                ...mockSearchResult,
                searchParams: {
                    ...mockSearchResult.searchParams,
                    timestamp: new Date('2023-01-01')
                }
            };
            const newerResult = {
                ...mockSearchResult,
                searchParams: {
                    ...mockSearchResult.searchParams,
                    timestamp: new Date('2023-12-31')
                }
            };
            // Mock the business search service
            const mockBusinessSearchService = {
                getSearchHistory: jest.fn().mockReturnValue([olderResult])
            };
            app.businessSearchService = mockBusinessSearchService;
            app['dataManager'].getAllSearchResults = jest.fn().mockReturnValue([newerResult]);
            const history = app.getSearchHistory();
            // Should be sorted newest first
            expect(history[0].searchParams.timestamp.getTime()).toBeGreaterThan(history[1].searchParams.timestamp.getTime());
        });
        it('should handle deduplication edge cases', () => {
            const result1 = { ...mockSearchResult };
            const result2 = {
                ...mockSearchResult,
                searchParams: {
                    ...mockSearchResult.searchParams,
                    zipCode: '90210' // Different zip code
                }
            };
            const result3 = { ...mockSearchResult }; // Exact duplicate of result1
            // Mock the business search service
            const mockBusinessSearchService = {
                getSearchHistory: jest.fn().mockReturnValue([result1, result2])
            };
            app.businessSearchService = mockBusinessSearchService;
            app['dataManager'].getAllSearchResults = jest.fn().mockReturnValue([result3]);
            const history = app.getSearchHistory();
            // Should have 2 unique results (result1 and result2, with result3 deduplicated)
            expect(history).toHaveLength(2);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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