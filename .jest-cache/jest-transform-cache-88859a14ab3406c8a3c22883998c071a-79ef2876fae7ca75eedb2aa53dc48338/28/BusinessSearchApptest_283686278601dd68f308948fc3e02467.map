{"file": "/Users/<USER>/WebstormProjects/goo/tests/BusinessSearchApp.test.ts", "mappings": ";;AASA,oBAAoB;AACpB,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;AACvC,IAAI,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;AAC5C,IAAI,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;AAC1C,IAAI,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;AACjD,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;AAdzC,gEAA6D;AAE7D,iDAAmE;AAEnE,mEAAuE;AAGvE,6DAA0D;AAS1D,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;IACjC,IAAI,GAAsB,CAAC;IAC3B,IAAI,yBAA6D,CAAC;IAClE,IAAI,eAAyC,CAAC;IAC9C,IAAI,gBAA8B,CAAC;IACnC,IAAI,iBAAsB,CAAC;IAE3B,UAAU,CAAC,GAAG,EAAE;QACd,cAAc;QACd,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,4BAA4B;QAC5B,gBAAgB,GAAG;YACjB,YAAY,EAAE;gBACZ,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,CAAC;gBACT,YAAY,EAAE,YAAY;gBAC1B,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;aAClC;YACD,OAAO,EAAE;gBACP,YAAY,EAAE,EAAE;gBAChB,eAAe,EAAE,EAAE;aACpB;YACD,UAAU,EAAE;gBACV,UAAU,EAAE,CAAC;gBACb,gBAAgB,EAAE,CAAC;gBACnB,mBAAmB,EAAE,CAAC;gBACtB,mBAAmB,EAAE,CAAC;gBACtB,cAAc,EAAE,IAAI;aACrB;SACF,CAAC;QAEF,iBAAiB,GAAG;YAClB,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,CAAC;YACT,YAAY,EAAE,YAAY;SAC3B,CAAC;QAEF,qCAAqC;QACrC,yBAAyB,GAAG;YAC1B,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;YAC/D,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC;YAC/C,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;YAC7B,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC;gBAC7C,aAAa,EAAE,CAAC;gBAChB,uBAAuB,EAAE,CAAC;gBAC1B,0BAA0B,EAAE,CAAC;gBAC7B,qBAAqB,EAAE,CAAC;aACzB,CAAC;YACF,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;SACV,CAAC;QAET,0BAA0B;QAC1B,eAAe,GAAG;YAChB,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,UAAU,CAAC;YACvD,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;YAChD,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC;YAClD,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;YAC7B,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;YACvB,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC;gBACxC,YAAY,EAAE,CAAC;gBACf,cAAc,EAAE,CAAC;gBACjB,WAAW,EAAE,IAAI;gBACjB,WAAW,EAAE,IAAI;aAClB,CAAC;YACF,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;YAChD,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,gEAAgE,CAAC;YACvG,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;YACxC,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;SACpC,CAAC;QAET,2CAA2C;QAC1C,sCAAwE,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,yBAAyB,CAAC,CAAC;QAC7H,yBAAoD,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,eAAe,CAAC,CAAC;QAEhG,GAAG,GAAG,IAAI,qCAAiB,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,aAAa,GAAG;gBACpB,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,EAAE;gBACV,YAAY,EAAE,YAAY;aAC3B,CAAC;YAEF,gEAAgE;YAChE,MAAM,MAAM,GAAG,MAAM,GAAG,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YACzD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,cAAc,GAAG;gBACrB,OAAO,EAAE,SAAS;gBAClB,MAAM,EAAE,EAAE;gBACV,YAAY,EAAE,YAAY;aAC3B,CAAC;YAEF,MAAM,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,wBAAe,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,MAAM,cAAc,GAAG;gBACrB,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,CAAC;gBACT,YAAY,EAAE,YAAY;aAC3B,CAAC;YAEF,MAAM,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,wBAAe,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,MAAM,cAAc,GAAG;gBACrB,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,EAAE;gBACV,YAAY,EAAE,cAAc;aAC7B,CAAC;YAEF,MAAM,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,wBAAe,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,OAAO,GAAG,GAAG,CAAC,gBAAgB,EAAE,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,GAAG,CAAC,kBAAkB,EAAE,CAAC;YACzB,MAAM,OAAO,GAAG,GAAG,CAAC,gBAAgB,EAAE,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,KAAK,GAAG,GAAG,CAAC,mBAAmB,EAAE,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,yBAAyB,CAAC,CAAC;YACxD,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,4BAA4B,CAAC,CAAC;YAC3D,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC;YAClC,MAAM,CAAC,OAAO,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEvC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,UAAU,GAAG;gBACjB,OAAO,EAAE,KAAK;gBACd,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACpC,aAAa,EAAE,EAAE;aAClB,CAAC;YAEF,MAAM,aAAa,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YACjE,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,8CAA8C;YAC9C,eAAe,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,QAAgB,EAAE,EAAE;gBACjE,IAAI,QAAQ,KAAK,cAAc,EAAE,CAAC;oBAChC,MAAM,IAAI,mBAAU,CAAC,iEAAiE,CAAC,CAAC;gBAC1F,CAAC;gBACD,OAAO,CAAC,CAAC;YACX,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAU,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,IAAI,GAAG,GAAG,CAAC,cAAc,EAAE,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,GAAG,CAAC,YAAY,EAAE,CAAC;YACnB,MAAM,OAAO,GAAG,GAAG,CAAC,gBAAgB,EAAE,CAAC;YACvC,MAAM,KAAK,GAAG,GAAG,CAAC,mBAAmB,EAAE,CAAC;YAExC,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,SAAS,GAAG,IAAI,qCAAiB,CAAC;gBACtC,oBAAoB,EAAE,EAAE;gBACxB,qBAAqB,EAAE,EAAE;gBACzB,gBAAgB,EAAE,KAAK;aACxB,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,qCAAiB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,UAAU,GAAG,IAAI,qCAAiB,EAAE,CAAC;YAC3C,MAAM,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,qCAAiB,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,kCAAkC;YAClC,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;YACtD,OAAO,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;YAEzC,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,qCAAiB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;YAEhD,kBAAkB;YAClB,IAAI,WAAW,EAAE,CAAC;gBAChB,OAAO,CAAC,GAAG,CAAC,qBAAqB,GAAG,WAAW,CAAC;YAClD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,kCAAkC;YAClC,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;YACtD,OAAO,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;YAEzC,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,qCAAiB,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAE1E,kBAAkB;YAClB,IAAI,WAAW,EAAE,CAAC;gBAChB,OAAO,CAAC,GAAG,CAAC,qBAAqB,GAAG,WAAW,CAAC;YAClD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;YAC/C,MAAM,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;YAC/C,MAAM,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;YAClD,MAAM,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,YAAY,GAAG,GAAG,CAAC,kBAAkB,EAAE,CAAC;YAC9C,MAAM,CAAC,OAAO,YAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,YAAY,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;QACzB,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,EAAE,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,4BAA4B,CAAC,CAAC;YAC5D,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,EAAE,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,MAAM,GAAG,MAAM,GAAG,CAAC,eAAe,EAAE,CAAC;YAE3C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAEzC,MAAM,CAAC,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAO,MAAM,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,0EAA0E;YAC1E,MAAM,MAAM,GAAG,MAAM,GAAG,CAAC,eAAe,EAAE,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,6BAA6B;QAC9E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,oEAAoE;YACpE,MAAM,QAAQ,GAAG,GAAG,CAAC,gBAAgB,EAAE,CAAC;YACxC,MAAM,QAAQ,GAAG,GAAG,CAAC,gBAAgB,EAAE,CAAC;YAExC,iCAAiC;YACjC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,SAAS,GAAG,IAAI,qCAAiB,CAAC;gBACtC,MAAM,EAAE,UAAU;gBAClB,oBAAoB,EAAE,EAAE;gBACxB,qBAAqB,EAAE,EAAE;gBACzB,gBAAgB,EAAE,KAAK;gBACvB,0BAA0B,EAAE,CAAC;gBAC7B,kBAAkB,EAAE,EAAE;aACvB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,gDAAgD;YAChD,MAAM,aAAa,GAAG;gBACpB,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,CAAC;gBACT,YAAY,EAAE,YAAY;aAC3B,CAAC;YAEF,4DAA4D;YAC5D,IAAI,CAAC;gBACH,MAAM,GAAG,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAC5C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,mCAAmC;gBACnC,MAAM,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,OAAO,GAAG,GAAG,CAAC,gBAAgB,EAAE,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE1C,+CAA+C;YAC/C,kCAAkC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,8DAA8D;YAC9D,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,2DAA2D,EAAE,KAAK,IAAI,EAAE;YACzE,sDAAsD;YACtD,MAAM,yBAAyB,GAAG;gBAChC,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;aAChE,CAAC;YAEF,iCAAiC;YAChC,GAAW,CAAC,qBAAqB,GAAG,yBAAyB,CAAC;YAE/D,0CAA0C;YAC1C,MAAM,YAAY,GAAG,GAAG,CAAC,aAAa,CAAC,CAAC,gBAAgB,CAAC;YACzD,GAAG,CAAC,aAAa,CAAC,CAAC,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,GAAG,EAAE;gBACtE,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;YAEH,gDAAgD;YAChD,MAAM,MAAM,GAAG,MAAM,GAAG,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAEzC,0BAA0B;YAC1B,GAAG,CAAC,aAAa,CAAC,CAAC,gBAAgB,GAAG,YAAY,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,eAAe,GAAG,EAAE,GAAG,gBAAgB,EAAE,CAAC;YAEhD,mCAAmC;YACnC,MAAM,yBAAyB,GAAG;gBAChC,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,gBAAgB,CAAC,CAAC;aAChE,CAAC;YACD,GAAW,CAAC,qBAAqB,GAAG,yBAAyB,CAAC;YAE/D,mBAAmB;YACnB,GAAG,CAAC,aAAa,CAAC,CAAC,mBAAmB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC;YAEtF,MAAM,OAAO,GAAG,GAAG,CAAC,gBAAgB,EAAE,CAAC;YAEvC,+CAA+C;YAC/C,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,0DAA0D;YAC1D,MAAM,oBAAoB,GAAG;gBAC3B,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;aACnF,CAAC;YACD,GAAW,CAAC,gBAAgB,GAAG,oBAAoB,CAAC;YAErD,MAAM,MAAM,GAAG,MAAM,GAAG,CAAC,eAAe,EAAE,CAAC;YAE3C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,0CAA0C;YAC1C,MAAM,YAAY,GAAG,GAAG,CAAC,aAAa,CAAC,CAAC,gBAAgB,CAAC;YACzD,GAAG,CAAC,aAAa,CAAC,CAAC,gBAAgB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,GAAG,EAAE;gBACtE,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,GAAG,CAAC,eAAe,EAAE,CAAC;YAE3C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEnC,0BAA0B;YAC1B,GAAG,CAAC,aAAa,CAAC,CAAC,gBAAgB,GAAG,YAAY,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,WAAW,GAAG;gBAClB,GAAG,gBAAgB;gBACnB,YAAY,EAAE;oBACZ,GAAG,gBAAgB,CAAC,YAAY;oBAChC,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;iBAClC;aACF,CAAC;YAEF,MAAM,WAAW,GAAG;gBAClB,GAAG,gBAAgB;gBACnB,YAAY,EAAE;oBACZ,GAAG,gBAAgB,CAAC,YAAY;oBAChC,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;iBAClC;aACF,CAAC;YAEF,mCAAmC;YACnC,MAAM,yBAAyB,GAAG;gBAChC,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,CAAC;aAC3D,CAAC;YACD,GAAW,CAAC,qBAAqB,GAAG,yBAAyB,CAAC;YAE/D,GAAG,CAAC,aAAa,CAAC,CAAC,mBAAmB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;YAElF,MAAM,OAAO,GAAG,GAAG,CAAC,gBAAgB,EAAE,CAAC;YAEvC,gCAAgC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,eAAe,CACjE,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,CAC5C,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,OAAO,GAAG,EAAE,GAAG,gBAAgB,EAAE,CAAC;YACxC,MAAM,OAAO,GAAG;gBACd,GAAG,gBAAgB;gBACnB,YAAY,EAAE;oBACZ,GAAG,gBAAgB,CAAC,YAAY;oBAChC,OAAO,EAAE,OAAO,CAAC,qBAAqB;iBACvC;aACF,CAAC;YACF,MAAM,OAAO,GAAG,EAAE,GAAG,gBAAgB,EAAE,CAAC,CAAC,6BAA6B;YAEtE,mCAAmC;YACnC,MAAM,yBAAyB,GAAG;gBAChC,gBAAgB,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;aAChE,CAAC;YACD,GAAW,CAAC,qBAAqB,GAAG,yBAAyB,CAAC;YAE/D,GAAG,CAAC,aAAa,CAAC,CAAC,mBAAmB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YAE9E,MAAM,OAAO,GAAG,GAAG,CAAC,gBAAgB,EAAE,CAAC;YAEvC,gFAAgF;YAChF,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/tests/BusinessSearchApp.test.ts"], "sourcesContent": ["import { BusinessSearchApp } from '../src/BusinessSearchApp';\nimport { SearchResult } from '../src/models/Business';\nimport { ValidationError, CacheError } from '../src/models/Errors';\nimport { GeocodingService } from '../src/services/geocoding';\nimport { BusinessSearchService } from '../src/services/businessSearch';\nimport { GooglePlacesService } from '../src/services/googlePlaces';\nimport { WebsiteVerificationService } from '../src/services/websiteVerification';\nimport { DataManager } from '../src/services/dataManager';\n\n// Mock the services\njest.mock('../src/services/geocoding');\njest.mock('../src/services/businessSearch');\njest.mock('../src/services/googlePlaces');\njest.mock('../src/services/websiteVerification');\njest.mock('../src/services/dataManager');\n\ndescribe('BusinessSearchApp', () => {\n  let app: BusinessSearchApp;\n  let mockBusinessSearchService: jest.Mocked<BusinessSearchService>;\n  let mockDataManager: jest.Mocked<DataManager>;\n  let mockSearchResult: SearchResult;\n  let mockSearchRequest: any;\n\n  beforeEach(() => {\n    // Reset mocks\n    jest.clearAllMocks();\n\n    // Create mock search result\n    mockSearchResult = {\n      searchParams: {\n        zipCode: '10001',\n        radius: 5,\n        businessType: 'restaurant',\n        timestamp: new Date('2023-06-01')\n      },\n      results: {\n        withWebsites: [],\n        withoutWebsites: []\n      },\n      statistics: {\n        totalFound: 0,\n        withWebsiteCount: 0,\n        withoutWebsiteCount: 0,\n        websiteAdoptionRate: 0,\n        searchDuration: 1000\n      }\n    };\n\n    mockSearchRequest = {\n      zipCode: '10001',\n      radius: 5,\n      businessType: 'restaurant'\n    };\n\n    // Setup mock business search service\n    mockBusinessSearchService = {\n      searchBusinesses: jest.fn().mockResolvedValue(mockSearchResult),\n      getSearchHistory: jest.fn().mockReturnValue([]),\n      clearSearchHistory: jest.fn(),\n      getSearchStatistics: jest.fn().mockReturnValue({\n        totalSearches: 0,\n        averageResultsPerSearch: 0,\n        averageWebsiteAdoptionRate: 0,\n        averageSearchDuration: 0\n      }),\n      reset: jest.fn()\n    } as any;\n\n    // Setup mock data manager\n    mockDataManager = {\n      saveSearchResult: jest.fn().mockReturnValue('test-key'),\n      getSearchResult: jest.fn().mockReturnValue(null),\n      getAllSearchResults: jest.fn().mockReturnValue([]),\n      deleteSearchResult: jest.fn(),\n      clearAllData: jest.fn(),\n      getStorageInfo: jest.fn().mockReturnValue({\n        totalEntries: 0,\n        totalSizeBytes: 0,\n        oldestEntry: null,\n        newestEntry: null\n      }),\n      cleanupExpiredData: jest.fn().mockReturnValue(0),\n      exportData: jest.fn().mockReturnValue('{\"version\":\"1.0\",\"exportDate\":\"2023-06-01\",\"searchResults\":[]}'),\n      importData: jest.fn().mockReturnValue(0),\n      getCacheSize: jest.fn().mockReturnValue(0)\n    } as any;\n\n    // Mock the constructor to inject our mocks\n    (BusinessSearchService as jest.MockedClass<typeof BusinessSearchService>).mockImplementation(() => mockBusinessSearchService);\n    (DataManager as jest.MockedClass<typeof DataManager>).mockImplementation(() => mockDataManager);\n\n    app = new BusinessSearchApp();\n  });\n\n  describe('searchBusinesses', () => {\n    it('should perform a complete business search', async () => {\n      const searchRequest = {\n        zipCode: '10001',\n        radius: 10,\n        businessType: 'restaurant',\n      };\n\n      // With mocked services, this should succeed and return a result\n      const result = await app.searchBusinesses(searchRequest);\n      expect(result).toHaveProperty('searchParams');\n      expect(result).toHaveProperty('results');\n      expect(result).toHaveProperty('statistics');\n    });\n\n    it('should validate search parameters', async () => {\n      const invalidRequest = {\n        zipCode: 'invalid',\n        radius: 10,\n        businessType: 'restaurant',\n      };\n\n      await expect(app.searchBusinesses(invalidRequest)).rejects.toThrow(ValidationError);\n    });\n\n    it('should validate radius range', async () => {\n      const invalidRequest = {\n        zipCode: '10001',\n        radius: 0,\n        businessType: 'restaurant',\n      };\n\n      await expect(app.searchBusinesses(invalidRequest)).rejects.toThrow(ValidationError);\n    });\n\n    it('should validate business type', async () => {\n      const invalidRequest = {\n        zipCode: '10001',\n        radius: 10,\n        businessType: 'invalid_type',\n      };\n\n      await expect(app.searchBusinesses(invalidRequest)).rejects.toThrow(ValidationError);\n    });\n  });\n\n  describe('getSearchHistory', () => {\n    it('should return search history', () => {\n      const history = app.getSearchHistory();\n      expect(Array.isArray(history)).toBe(true);\n    });\n  });\n\n  describe('clearSearchHistory', () => {\n    it('should clear search history', () => {\n      app.clearSearchHistory();\n      const history = app.getSearchHistory();\n      expect(history).toHaveLength(0);\n    });\n  });\n\n  describe('getSearchStatistics', () => {\n    it('should return search statistics', () => {\n      const stats = app.getSearchStatistics();\n      expect(stats).toHaveProperty('totalSearches');\n      expect(stats).toHaveProperty('averageResultsPerSearch');\n      expect(stats).toHaveProperty('averageWebsiteAdoptionRate');\n      expect(stats).toHaveProperty('averageSearchDuration');\n    });\n  });\n\n  describe('exportData', () => {\n    it('should export search data', () => {\n      const exported = app.exportData();\n      expect(typeof exported).toBe('string');\n      \n      const parsed = JSON.parse(exported);\n      expect(parsed).toHaveProperty('version');\n      expect(parsed).toHaveProperty('exportDate');\n      expect(parsed).toHaveProperty('searchResults');\n    });\n  });\n\n  describe('importData', () => {\n    it('should import search data', () => {\n      const exportData = {\n        version: '1.0',\n        exportDate: new Date().toISOString(),\n        searchResults: [],\n      };\n\n      const importedCount = app.importData(JSON.stringify(exportData));\n      expect(importedCount).toBe(0);\n    });\n\n    it('should handle invalid import data', () => {\n      // Override the mock to throw for invalid JSON\n      mockDataManager.importData.mockImplementation((jsonData: string) => {\n        if (jsonData === 'invalid-json') {\n          throw new CacheError('Failed to import data: Unexpected token i in JSON at position 0');\n        }\n        return 0;\n      });\n\n      expect(() => app.importData('invalid-json')).toThrow(CacheError);\n    });\n  });\n\n  describe('getStorageInfo', () => {\n    it('should return storage information', () => {\n      const info = app.getStorageInfo();\n      expect(info).toHaveProperty('totalEntries');\n      expect(info).toHaveProperty('totalSizeBytes');\n      expect(info).toHaveProperty('oldestEntry');\n      expect(info).toHaveProperty('newestEntry');\n    });\n  });\n\n  describe('clearAllData', () => {\n    it('should clear all application data', () => {\n      app.clearAllData();\n      const history = app.getSearchHistory();\n      const stats = app.getSearchStatistics();\n      \n      expect(history).toHaveLength(0);\n      expect(stats.totalSearches).toBe(0);\n    });\n  });\n\n  describe('configuration', () => {\n    it('should allow custom configuration', () => {\n      const customApp = new BusinessSearchApp({\n        cacheExpirationHours: 48,\n        maxConcurrentRequests: 10,\n        requestTimeoutMs: 10000,\n      });\n\n      expect(customApp).toBeInstanceOf(BusinessSearchApp);\n    });\n\n    it('should use default configuration when none provided', () => {\n      const defaultApp = new BusinessSearchApp();\n      expect(defaultApp).toBeInstanceOf(BusinessSearchApp);\n    });\n  });\n\n  describe('error handling', () => {\n    it('should handle missing API key gracefully', () => {\n      // Remove API key from environment\n      const originalKey = process.env.GOOGLE_PLACES_API_KEY;\n      delete process.env.GOOGLE_PLACES_API_KEY;\n\n      expect(() => new BusinessSearchApp()).toThrow();\n\n      // Restore API key\n      if (originalKey) {\n        process.env.GOOGLE_PLACES_API_KEY = originalKey;\n      }\n    });\n\n    it('should handle API key from config', () => {\n      // Remove API key from environment\n      const originalKey = process.env.GOOGLE_PLACES_API_KEY;\n      delete process.env.GOOGLE_PLACES_API_KEY;\n\n      expect(() => new BusinessSearchApp({ apiKey: 'test-key' })).not.toThrow();\n\n      // Restore API key\n      if (originalKey) {\n        process.env.GOOGLE_PLACES_API_KEY = originalKey;\n      }\n    });\n  });\n\n  describe('service integration', () => {\n    it('should initialize all required services', () => {\n      expect(app).toHaveProperty('searchBusinesses');\n      expect(app).toHaveProperty('getSearchHistory');\n      expect(app).toHaveProperty('getSearchStatistics');\n      expect(app).toHaveProperty('exportData');\n      expect(app).toHaveProperty('importData');\n    });\n  });\n\n  describe('cleanupExpiredData', () => {\n    it('should cleanup expired data', () => {\n      const removedCount = app.cleanupExpiredData();\n      expect(typeof removedCount).toBe('number');\n      expect(removedCount).toBeGreaterThanOrEqual(0);\n    });\n  });\n\n  describe('getConfig', () => {\n    it('should return readonly configuration', () => {\n      const config = app.getConfig();\n      expect(config).toHaveProperty('apiKey');\n      expect(config).toHaveProperty('cacheExpirationHours');\n      expect(config).toHaveProperty('maxConcurrentRequests');\n      expect(config).toHaveProperty('requestTimeoutMs');\n      expect(config).toHaveProperty('rateLimitRequestsPerSecond');\n      expect(config).toHaveProperty('rateLimitBurstSize');\n    });\n\n    it('should return default values when not specified', () => {\n      const config = app.getConfig();\n      expect(config.cacheExpirationHours).toBe(24);\n      expect(config.maxConcurrentRequests).toBe(5);\n      expect(config.requestTimeoutMs).toBe(5000);\n    });\n  });\n\n  describe('getHealthStatus', () => {\n    it('should return health status for all services', async () => {\n      const health = await app.getHealthStatus();\n\n      expect(health).toHaveProperty('geocoding');\n      expect(health).toHaveProperty('places');\n      expect(health).toHaveProperty('websiteVerification');\n      expect(health).toHaveProperty('storage');\n\n      expect(typeof health.geocoding).toBe('boolean');\n      expect(typeof health.places).toBe('boolean');\n      expect(typeof health.websiteVerification).toBe('boolean');\n      expect(typeof health.storage).toBe('boolean');\n    });\n\n    it('should handle service failures gracefully', async () => {\n      // This test will likely fail due to missing API key, but should not throw\n      const health = await app.getHealthStatus();\n      expect(health.websiteVerification).toBe(true); // This should always be true\n    });\n  });\n\n  describe('search history deduplication', () => {\n    it('should deduplicate search results', () => {\n      // This tests the private deduplicateSearchResults method indirectly\n      const history1 = app.getSearchHistory();\n      const history2 = app.getSearchHistory();\n\n      // Should return the same results\n      expect(history1).toEqual(history2);\n    });\n  });\n\n  describe('advanced configuration', () => {\n    it('should accept all configuration options', () => {\n      const customApp = new BusinessSearchApp({\n        apiKey: 'test-key',\n        cacheExpirationHours: 48,\n        maxConcurrentRequests: 10,\n        requestTimeoutMs: 10000,\n        rateLimitRequestsPerSecond: 5,\n        rateLimitBurstSize: 15,\n      });\n\n      const config = customApp.getConfig();\n      expect(config.cacheExpirationHours).toBe(48);\n      expect(config.maxConcurrentRequests).toBe(10);\n      expect(config.requestTimeoutMs).toBe(10000);\n      expect(config.rateLimitRequestsPerSecond).toBe(5);\n      expect(config.rateLimitBurstSize).toBe(15);\n    });\n  });\n\n  describe('data persistence integration', () => {\n    it('should save search results automatically', async () => {\n      // Mock a successful search that would save data\n      const searchRequest = {\n        zipCode: '10001',\n        radius: 5,\n        businessType: 'restaurant',\n      };\n\n      // This will fail due to missing API key, but tests the flow\n      try {\n        await app.searchBusinesses(searchRequest);\n      } catch (error) {\n        // Expected to fail without API key\n        expect(error).toBeDefined();\n      }\n    });\n\n    it('should integrate search history from multiple sources', () => {\n      const history = app.getSearchHistory();\n      expect(Array.isArray(history)).toBe(true);\n\n      // Should combine memory and persistent storage\n      // Even if empty, should not throw\n    });\n  });\n\n  describe('cache management', () => {\n    it('should clear all caches when clearing data', () => {\n      // This tests that clearAllData calls all service cache clears\n      expect(() => app.clearAllData()).not.toThrow();\n    });\n  });\n\n  describe('error handling and edge cases', () => {\n    it('should handle dataManager save errors in searchBusinesses', async () => {\n      // Mock the business search service to return a result\n      const mockBusinessSearchService = {\n        searchBusinesses: jest.fn().mockResolvedValue(mockSearchResult)\n      };\n\n      // Replace the service in the app\n      (app as any).businessSearchService = mockBusinessSearchService;\n\n      // Mock dataManager to throw error on save\n      const originalSave = app['dataManager'].saveSearchResult;\n      app['dataManager'].saveSearchResult = jest.fn().mockImplementation(() => {\n        throw new Error('Storage full');\n      });\n\n      // Should still return result even if save fails\n      const result = await app.searchBusinesses(mockSearchRequest);\n      expect(result).toEqual(mockSearchResult);\n\n      // Restore original method\n      app['dataManager'].saveSearchResult = originalSave;\n    });\n\n    it('should handle deduplication in getSearchHistory', () => {\n      const duplicateResult = { ...mockSearchResult };\n\n      // Mock the business search service\n      const mockBusinessSearchService = {\n        getSearchHistory: jest.fn().mockReturnValue([mockSearchResult])\n      };\n      (app as any).businessSearchService = mockBusinessSearchService;\n\n      // Mock dataManager\n      app['dataManager'].getAllSearchResults = jest.fn().mockReturnValue([duplicateResult]);\n\n      const history = app.getSearchHistory();\n\n      // Should only return one result (deduplicated)\n      expect(history).toHaveLength(1);\n      expect(history[0]).toEqual(mockSearchResult);\n    });\n\n    it('should handle successful geocoding health check', async () => {\n      // Mock the geocoding service directly on the app instance\n      const mockGeocodingService = {\n        zipCodeToCoordinates: jest.fn().mockResolvedValue({ lat: 40.7128, lng: -74.0060 })\n      };\n      (app as any).geocodingService = mockGeocodingService;\n\n      const status = await app.getHealthStatus();\n\n      expect(status.geocoding).toBe(true);\n    });\n\n    it('should handle storage health check errors', async () => {\n      // Mock dataManager to throw error on save\n      const originalSave = app['dataManager'].saveSearchResult;\n      app['dataManager'].saveSearchResult = jest.fn().mockImplementation(() => {\n        throw new Error('Storage error');\n      });\n\n      const status = await app.getHealthStatus();\n\n      expect(status.storage).toBe(false);\n\n      // Restore original method\n      app['dataManager'].saveSearchResult = originalSave;\n    });\n\n    it('should handle search history sorting with multiple results', () => {\n      const olderResult = {\n        ...mockSearchResult,\n        searchParams: {\n          ...mockSearchResult.searchParams,\n          timestamp: new Date('2023-01-01')\n        }\n      };\n\n      const newerResult = {\n        ...mockSearchResult,\n        searchParams: {\n          ...mockSearchResult.searchParams,\n          timestamp: new Date('2023-12-31')\n        }\n      };\n\n      // Mock the business search service\n      const mockBusinessSearchService = {\n        getSearchHistory: jest.fn().mockReturnValue([olderResult])\n      };\n      (app as any).businessSearchService = mockBusinessSearchService;\n\n      app['dataManager'].getAllSearchResults = jest.fn().mockReturnValue([newerResult]);\n\n      const history = app.getSearchHistory();\n\n      // Should be sorted newest first\n      expect(history[0].searchParams.timestamp.getTime()).toBeGreaterThan(\n        history[1].searchParams.timestamp.getTime()\n      );\n    });\n\n    it('should handle deduplication edge cases', () => {\n      const result1 = { ...mockSearchResult };\n      const result2 = {\n        ...mockSearchResult,\n        searchParams: {\n          ...mockSearchResult.searchParams,\n          zipCode: '90210' // Different zip code\n        }\n      };\n      const result3 = { ...mockSearchResult }; // Exact duplicate of result1\n\n      // Mock the business search service\n      const mockBusinessSearchService = {\n        getSearchHistory: jest.fn().mockReturnValue([result1, result2])\n      };\n      (app as any).businessSearchService = mockBusinessSearchService;\n\n      app['dataManager'].getAllSearchResults = jest.fn().mockReturnValue([result3]);\n\n      const history = app.getSearchHistory();\n\n      // Should have 2 unique results (result1 and result2, with result3 deduplicated)\n      expect(history).toHaveLength(2);\n    });\n  });\n});\n"], "version": 3}