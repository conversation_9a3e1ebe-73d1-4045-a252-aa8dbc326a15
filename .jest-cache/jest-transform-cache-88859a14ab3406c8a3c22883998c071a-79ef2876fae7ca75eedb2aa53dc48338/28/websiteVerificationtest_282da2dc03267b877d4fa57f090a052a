9a6d3e103f07fa6e9398e4a081cf99b4
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const websiteVerification_1 = require("../../src/services/websiteVerification");
const Errors_1 = require("../../src/models/Errors");
// Mock fetch globally
const mockFetch = global.fetch;
describe('WebsiteVerificationService', () => {
    let websiteService;
    beforeEach(() => {
        websiteService = new websiteVerification_1.WebsiteVerificationService();
        mockFetch.mockClear();
    });
    describe('verifyWebsite', () => {
        it('should verify accessible website', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Headers({ 'content-type': 'text/html' }),
            });
            const result = await websiteService.verifyWebsite('https://example.com');
            expect(result.status).toBe('verified');
            expect(result.accessible).toBe(true);
            expect(result.statusCode).toBe(200);
            expect(result.confidence).toBeGreaterThan(0.8);
        });
        it('should handle redirects as verified', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 301,
                headers: new Headers({ 'location': 'https://example.com/new' }),
            });
            const result = await websiteService.verifyWebsite('https://example.com');
            expect(result.status).toBe('verified');
            expect(result.accessible).toBe(true);
            expect(result.statusCode).toBe(301);
        });
        it('should handle 404 as unverified', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: false,
                status: 404,
                statusText: 'Not Found',
                headers: new Headers(),
            });
            const result = await websiteService.verifyWebsite('https://example.com');
            expect(result.status).toBe('unverified');
            expect(result.accessible).toBe(false);
            expect(result.statusCode).toBe(404);
            expect(result.confidence).toBeLessThan(0.5);
        });
        it('should handle network errors', async () => {
            mockFetch.mockRejectedValueOnce(new Error('Network error'));
            const result = await websiteService.verifyWebsite('https://example.com');
            expect(result.status).toBe('unverified');
            expect(result.accessible).toBe(false);
            expect(result.error).toContain('Network error');
        });
        it('should validate URL format', async () => {
            await expect(websiteService.verifyWebsite('invalid-url'))
                .rejects.toThrow(Errors_1.WebsiteVerificationError);
            expect(mockFetch).not.toHaveBeenCalled();
        });
        it('should handle timeout', async () => {
            const abortError = new Error('The operation was aborted');
            abortError.name = 'AbortError';
            mockFetch.mockRejectedValueOnce(abortError);
            const result = await websiteService.verifyWebsite('https://example.com');
            expect(result.status).toBe('unverified');
            expect(result.accessible).toBe(false);
            expect(result.error).toContain('timeout');
        });
        it('should cache verification results', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Headers(),
            });
            // First call
            const result1 = await websiteService.verifyWebsite('https://example.com');
            // Second call should use cache
            const result2 = await websiteService.verifyWebsite('https://example.com');
            expect(result1).toEqual(result2);
            expect(mockFetch).toHaveBeenCalledTimes(1);
        });
    });
    describe('verifyMultipleWebsites', () => {
        it('should verify multiple websites concurrently', async () => {
            mockFetch
                .mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Headers(),
            })
                .mockResolvedValueOnce({
                ok: false,
                status: 404,
                statusText: 'Not Found',
            });
            const urls = ['https://example1.com', 'https://example2.com'];
            const results = await websiteService.verifyMultipleWebsites(urls);
            expect(results).toHaveLength(2);
            expect(results[0].status).toBe('verified');
            expect(results[1].status).toBe('unverified');
            expect(mockFetch).toHaveBeenCalledTimes(2);
        });
        it('should handle mixed results', async () => {
            mockFetch
                .mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Headers(),
            })
                .mockRejectedValueOnce(new Error('Network error'));
            const urls = ['https://good.com', 'https://bad.com'];
            const results = await websiteService.verifyMultipleWebsites(urls);
            expect(results).toHaveLength(2);
            expect(results[0].status).toBe('verified');
            expect(results[1].status).toBe('unverified');
        });
        it('should respect concurrency limit', async () => {
            const urls = Array.from({ length: 10 }, (_, i) => `https://example${i}.com`);
            mockFetch.mockResolvedValue({
                ok: true,
                status: 200,
                headers: new Headers(),
            });
            const startTime = Date.now();
            await websiteService.verifyMultipleWebsites(urls, 3); // Limit to 3 concurrent
            const endTime = Date.now();
            // With concurrency limit, it should take longer than if all were parallel
            expect(mockFetch).toHaveBeenCalledTimes(10);
        });
    });
    describe('getVerificationStats', () => {
        it('should return verification statistics', async () => {
            mockFetch
                .mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Headers(),
            })
                .mockResolvedValueOnce({
                ok: false,
                status: 404,
                statusText: 'Not Found',
            });
            await websiteService.verifyWebsite('https://good.com');
            await websiteService.verifyWebsite('https://bad.com');
            const stats = websiteService.getVerificationStats();
            expect(stats.totalVerified).toBe(2);
            expect(stats.successfulVerifications).toBe(1);
            expect(stats.failedVerifications).toBe(1);
            expect(stats.successRate).toBe(0.5);
        });
    });
    describe('clearCache', () => {
        it('should clear verification cache', async () => {
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Headers(),
            });
            await websiteService.verifyWebsite('https://example.com');
            expect(mockFetch).toHaveBeenCalledTimes(1);
            websiteService.clearCache();
            mockFetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Headers(),
            });
            await websiteService.verifyWebsite('https://example.com');
            expect(mockFetch).toHaveBeenCalledTimes(2);
        });
    });
    describe('calculateConfidence', () => {
        it('should calculate high confidence for successful responses', () => {
            const confidence = websiteService.calculateConfidence(200, 'text/html', 500);
            expect(confidence).toBeGreaterThan(0.8);
        });
        it('should calculate low confidence for error responses', () => {
            const confidence = websiteService.calculateConfidence(404, '', 0);
            expect(confidence).toBeLessThan(0.3);
        });
        it('should consider response time in confidence calculation', () => {
            const fastConfidence = websiteService.calculateConfidence(200, 'text/html', 100);
            const slowConfidence = websiteService.calculateConfidence(200, 'text/html', 5000);
            expect(fastConfidence).toBeGreaterThan(slowConfidence);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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