{"version": 3, "names": ["cov_155q57m7zi", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "branchMap", "s", "f", "b", "inputSourceMap", "file", "mappings", "names", "sources", "sourcesContent", "version", "_coverageSchema", "coverage", "actualCoverage", "exports", "API_CONFIG", "GOOGLE_PLACES_BASE_URL", "GEOCODING_BASE_URL", "REQUEST_TIMEOUT", "MAX_RETRIES", "RETRY_DELAY_BASE", "SEARCH_CONFIG", "MIN_RADIUS", "MAX_RADIUS", "DEFAULT_RADIUS", "MAX_RESULTS_PER_PAGE", "MAX_TOTAL_RESULTS", "RATE_LIMIT_CONFIG", "REQUESTS_PER_SECOND", "BURST_SIZE", "TOKEN_REFILL_INTERVAL", "CACHE_CONFIG", "EXPIRATION_HOURS", "MAX_SIZE_MB", "KEY_PREFIX", "CLEANUP_INTERVAL", "WEBSITE_CONFIG", "TIMEOUT", "MAX_REDIRECTS", "USER_AGENT", "VALID_STATUS_CODES", "VALIDATION_PATTERNS", "ZIP_CODE", "PHONE", "URL", "EMAIL", "BUSINESS_TYPES", "ERROR_MESSAGES", "INVALID_ZIP_CODE", "INVALID_RADIUS", "INVALID_BUSINESS_TYPE", "API_KEY_MISSING", "NETWORK_ERROR", "RATE_LIMIT_EXCEEDED", "GEOCODING_FAILED", "WEBSITE_VERIFICATION_FAILED", "CACHE_ERROR"], "sources": ["/Users/<USER>/WebstormProjects/goo/src/constants/index.ts"], "sourcesContent": ["/**\n * Application constants and configuration values\n */\n\n// API Configuration\nexport const API_CONFIG = {\n  GOOGLE_PLACES_BASE_URL: 'https://maps.googleapis.com/maps/api/place',\n  GEOCODING_BASE_URL: 'https://maps.googleapis.com/maps/api/geocode',\n  REQUEST_TIMEOUT: 5000,\n  MAX_RETRIES: 3,\n  RETRY_DELAY_BASE: 1000, // Base delay for exponential backoff\n} as const;\n\n// Search Configuration\nexport const SEARCH_CONFIG = {\n  MIN_RADIUS: 1,\n  MAX_RADIUS: 50,\n  DEFAULT_RADIUS: 10,\n  MAX_RESULTS_PER_PAGE: 20,\n  MAX_TOTAL_RESULTS: 1000,\n} as const;\n\n// Rate Limiting Configuration\nexport const RATE_LIMIT_CONFIG = {\n  REQUESTS_PER_SECOND: 10,\n  BURST_SIZE: 20,\n  TOKEN_REFILL_INTERVAL: 100, // milliseconds\n} as const;\n\n// Cache Configuration\nexport const CACHE_CONFIG = {\n  EXPIRATION_HOURS: 24,\n  MAX_SIZE_MB: 50,\n  KEY_PREFIX: 'business_search_',\n  CLEANUP_INTERVAL: 3600000, // 1 hour in milliseconds\n} as const;\n\n// Website Verification Configuration\nexport const WEBSITE_CONFIG = {\n  TIMEOUT: 3000,\n  MAX_REDIRECTS: 3,\n  USER_AGENT: 'BusinessSearchBot/1.0',\n  VALID_STATUS_CODES: [200, 201, 202, 203, 204, 301, 302, 303, 307, 308],\n} as const;\n\n// Validation Patterns\nexport const VALIDATION_PATTERNS = {\n  ZIP_CODE: /^\\d{5}(-\\d{4})?$/,\n  PHONE: /^\\+?[\\d\\s\\-\\(\\)\\.]+$/,\n  URL: /^https?:\\/\\/.+/,\n  EMAIL: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n} as const;\n\n// Business Types (Google Places API types)\nexport const BUSINESS_TYPES = [\n  'accounting',\n  'airport',\n  'amusement_park',\n  'aquarium',\n  'art_gallery',\n  'atm',\n  'bakery',\n  'bank',\n  'bar',\n  'beauty_salon',\n  'bicycle_store',\n  'book_store',\n  'bowling_alley',\n  'bus_station',\n  'cafe',\n  'campground',\n  'car_dealer',\n  'car_rental',\n  'car_repair',\n  'car_wash',\n  'casino',\n  'cemetery',\n  'church',\n  'city_hall',\n  'clothing_store',\n  'convenience_store',\n  'courthouse',\n  'dentist',\n  'department_store',\n  'doctor',\n  'drugstore',\n  'electrician',\n  'electronics_store',\n  'embassy',\n  'fire_station',\n  'florist',\n  'funeral_home',\n  'furniture_store',\n  'gas_station',\n  'gym',\n  'hair_care',\n  'hardware_store',\n  'hindu_temple',\n  'home_goods_store',\n  'hospital',\n  'insurance_agency',\n  'jewelry_store',\n  'laundry',\n  'lawyer',\n  'library',\n  'light_rail_station',\n  'liquor_store',\n  'local_government_office',\n  'locksmith',\n  'lodging',\n  'meal_delivery',\n  'meal_takeaway',\n  'mosque',\n  'movie_rental',\n  'movie_theater',\n  'moving_company',\n  'museum',\n  'night_club',\n  'painter',\n  'park',\n  'parking',\n  'pet_store',\n  'pharmacy',\n  'physiotherapist',\n  'plumber',\n  'police',\n  'post_office',\n  'primary_school',\n  'real_estate_agency',\n  'restaurant',\n  'roofing_contractor',\n  'rv_park',\n  'school',\n  'secondary_school',\n  'shoe_store',\n  'shopping_mall',\n  'spa',\n  'stadium',\n  'storage',\n  'store',\n  'subway_station',\n  'supermarket',\n  'synagogue',\n  'taxi_stand',\n  'tourist_attraction',\n  'train_station',\n  'transit_station',\n  'travel_agency',\n  'university',\n  'veterinary_care',\n  'zoo'\n] as const;\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  INVALID_ZIP_CODE: 'Invalid zip code format. Please use 5-digit or 9-digit format.',\n  INVALID_RADIUS: `Radius must be between ${SEARCH_CONFIG.MIN_RADIUS} and ${SEARCH_CONFIG.MAX_RADIUS} miles.`,\n  INVALID_BUSINESS_TYPE: 'Invalid business type. Please select from available options.',\n  API_KEY_MISSING: 'Google Places API key is required.',\n  NETWORK_ERROR: 'Network error occurred. Please check your connection.',\n  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded. Please try again later.',\n  GEOCODING_FAILED: 'Failed to convert zip code to coordinates.',\n  WEBSITE_VERIFICATION_FAILED: 'Failed to verify website accessibility.',\n  CACHE_ERROR: 'Cache operation failed.',\n} as const;\n\nexport type BusinessType = typeof BUSINESS_TYPES[number];\n"], "mappings": ";;AAAA;;;AAAA;AAAA,SAAAA,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;IAAAC,SAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;IAAAC,CAAA;IAAAC,cAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,KAAA;MAAAC,OAAA;MAAAC,cAAA;MAAAC,OAAA;IAAA;IAAAC,eAAA;IAAAtB,IAAA;EAAA;EAAA,IAAAuB,QAAA,GAAAtB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAoB,QAAA,CAAAxB,IAAA,KAAAwB,QAAA,CAAAxB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAuB,QAAA,CAAAxB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAoB,cAAA,GAAAD,QAAA,CAAAxB,IAAA;EAAA;IAca;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAA0B,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAA1B,cAAA;AAAAA,cAAA,GAAAc,CAAA;;;;;;;AAVb;AAAA;AAAAd,cAAA,GAAAc,CAAA;AACaa,OAAA,CAAAC,UAAU,GAAG;EACxBC,sBAAsB,EAAE,4CAA4C;EACpEC,kBAAkB,EAAE,8CAA8C;EAClEC,eAAe,EAAE,IAAI;EACrBC,WAAW,EAAE,CAAC;EACdC,gBAAgB,EAAE,IAAI,CAAE;CAChB;AAEV;AAAA;AAAAjC,cAAA,GAAAc,CAAA;AACaa,OAAA,CAAAO,aAAa,GAAG;EAC3BC,UAAU,EAAE,CAAC;EACbC,UAAU,EAAE,EAAE;EACdC,cAAc,EAAE,EAAE;EAClBC,oBAAoB,EAAE,EAAE;EACxBC,iBAAiB,EAAE;CACX;AAEV;AAAA;AAAAvC,cAAA,GAAAc,CAAA;AACaa,OAAA,CAAAa,iBAAiB,GAAG;EAC/BC,mBAAmB,EAAE,EAAE;EACvBC,UAAU,EAAE,EAAE;EACdC,qBAAqB,EAAE,GAAG,CAAE;CACpB;AAEV;AAAA;AAAA3C,cAAA,GAAAc,CAAA;AACaa,OAAA,CAAAiB,YAAY,GAAG;EAC1BC,gBAAgB,EAAE,EAAE;EACpBC,WAAW,EAAE,EAAE;EACfC,UAAU,EAAE,kBAAkB;EAC9BC,gBAAgB,EAAE,OAAO,CAAE;CACnB;AAEV;AAAA;AAAAhD,cAAA,GAAAc,CAAA;AACaa,OAAA,CAAAsB,cAAc,GAAG;EAC5BC,OAAO,EAAE,IAAI;EACbC,aAAa,EAAE,CAAC;EAChBC,UAAU,EAAE,uBAAuB;EACnCC,kBAAkB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;CAC7D;AAEV;AAAA;AAAArD,cAAA,GAAAc,CAAA;AACaa,OAAA,CAAA2B,mBAAmB,GAAG;EACjCC,QAAQ,EAAE,kBAAkB;EAC5BC,KAAK,EAAE,sBAAsB;EAC7BC,GAAG,EAAE,gBAAgB;EACrBC,KAAK,EAAE;CACC;AAEV;AAAA;AAAA1D,cAAA,GAAAc,CAAA;AACaa,OAAA,CAAAgC,cAAc,GAAG,CAC5B,YAAY,EACZ,SAAS,EACT,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,KAAK,EACL,QAAQ,EACR,MAAM,EACN,KAAK,EACL,cAAc,EACd,eAAe,EACf,YAAY,EACZ,eAAe,EACf,aAAa,EACb,MAAM,EACN,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,UAAU,EACV,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,WAAW,EACX,gBAAgB,EAChB,mBAAmB,EACnB,YAAY,EACZ,SAAS,EACT,kBAAkB,EAClB,QAAQ,EACR,WAAW,EACX,aAAa,EACb,mBAAmB,EACnB,SAAS,EACT,cAAc,EACd,SAAS,EACT,cAAc,EACd,iBAAiB,EACjB,aAAa,EACb,KAAK,EACL,WAAW,EACX,gBAAgB,EAChB,cAAc,EACd,kBAAkB,EAClB,UAAU,EACV,kBAAkB,EAClB,eAAe,EACf,SAAS,EACT,QAAQ,EACR,SAAS,EACT,oBAAoB,EACpB,cAAc,EACd,yBAAyB,EACzB,WAAW,EACX,SAAS,EACT,eAAe,EACf,eAAe,EACf,QAAQ,EACR,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,QAAQ,EACR,YAAY,EACZ,SAAS,EACT,MAAM,EACN,SAAS,EACT,WAAW,EACX,UAAU,EACV,iBAAiB,EACjB,SAAS,EACT,QAAQ,EACR,aAAa,EACb,gBAAgB,EAChB,oBAAoB,EACpB,YAAY,EACZ,oBAAoB,EACpB,SAAS,EACT,QAAQ,EACR,kBAAkB,EAClB,YAAY,EACZ,eAAe,EACf,KAAK,EACL,SAAS,EACT,SAAS,EACT,OAAO,EACP,gBAAgB,EAChB,aAAa,EACb,WAAW,EACX,YAAY,EACZ,oBAAoB,EACpB,eAAe,EACf,iBAAiB,EACjB,eAAe,EACf,YAAY,EACZ,iBAAiB,EACjB,KAAK,CACG;AAEV;AAAA;AAAA3D,cAAA,GAAAc,CAAA;AACaa,OAAA,CAAAiC,cAAc,GAAG;EAC5BC,gBAAgB,EAAE,gEAAgE;EAClFC,cAAc,EAAE,0BAA0BnC,OAAA,CAAAO,aAAa,CAACC,UAAU,QAAQR,OAAA,CAAAO,aAAa,CAACE,UAAU,SAAS;EAC3G2B,qBAAqB,EAAE,8DAA8D;EACrFC,eAAe,EAAE,oCAAoC;EACrDC,aAAa,EAAE,uDAAuD;EACtEC,mBAAmB,EAAE,8CAA8C;EACnEC,gBAAgB,EAAE,4CAA4C;EAC9DC,2BAA2B,EAAE,yCAAyC;EACtEC,WAAW,EAAE;CACL", "ignoreList": []}