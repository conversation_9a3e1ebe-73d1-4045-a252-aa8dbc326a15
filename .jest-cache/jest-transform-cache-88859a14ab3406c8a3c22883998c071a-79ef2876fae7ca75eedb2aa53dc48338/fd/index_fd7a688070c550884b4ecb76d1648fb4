76be7e976de4ba55e9d36d2469a97326
"use strict";

/**
 * Application constants and configuration values
 */
/* istanbul ignore next */
function cov_155q57m7zi() {
  var path = "/Users/<USER>/WebstormProjects/goo/src/constants/index.ts";
  var hash = "f284cb621d7dc90b6f7005b02d6eb4ae67af3e54";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/WebstormProjects/goo/src/constants/index.ts",
    statementMap: {
      "0": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "1": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 208
        }
      },
      "2": {
        start: {
          line: 8,
          column: 0
        },
        end: {
          line: 14,
          column: 2
        }
      },
      "3": {
        start: {
          line: 16,
          column: 0
        },
        end: {
          line: 22,
          column: 2
        }
      },
      "4": {
        start: {
          line: 24,
          column: 0
        },
        end: {
          line: 28,
          column: 2
        }
      },
      "5": {
        start: {
          line: 30,
          column: 0
        },
        end: {
          line: 35,
          column: 2
        }
      },
      "6": {
        start: {
          line: 37,
          column: 0
        },
        end: {
          line: 42,
          column: 2
        }
      },
      "7": {
        start: {
          line: 44,
          column: 0
        },
        end: {
          line: 49,
          column: 2
        }
      },
      "8": {
        start: {
          line: 51,
          column: 0
        },
        end: {
          line: 148,
          column: 2
        }
      },
      "9": {
        start: {
          line: 150,
          column: 0
        },
        end: {
          line: 160,
          column: 2
        }
      }
    },
    fnMap: {},
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    f: {},
    b: {},
    inputSourceMap: {
      file: "/Users/<USER>/WebstormProjects/goo/src/constants/index.ts",
      mappings: ";AAAA;;GAEG;;;AAEH,oBAAoB;AACP,QAAA,UAAU,GAAG;IACxB,sBAAsB,EAAE,4CAA4C;IACpE,kBAAkB,EAAE,8CAA8C;IAClE,eAAe,EAAE,IAAI;IACrB,WAAW,EAAE,CAAC;IACd,gBAAgB,EAAE,IAAI,EAAE,qCAAqC;CACrD,CAAC;AAEX,uBAAuB;AACV,QAAA,aAAa,GAAG;IAC3B,UAAU,EAAE,CAAC;IACb,UAAU,EAAE,EAAE;IACd,cAAc,EAAE,EAAE;IAClB,oBAAoB,EAAE,EAAE;IACxB,iBAAiB,EAAE,IAAI;CACf,CAAC;AAEX,8BAA8B;AACjB,QAAA,iBAAiB,GAAG;IAC/B,mBAAmB,EAAE,EAAE;IACvB,UAAU,EAAE,EAAE;IACd,qBAAqB,EAAE,GAAG,EAAE,eAAe;CACnC,CAAC;AAEX,sBAAsB;AACT,QAAA,YAAY,GAAG;IAC1B,gBAAgB,EAAE,EAAE;IACpB,WAAW,EAAE,EAAE;IACf,UAAU,EAAE,kBAAkB;IAC9B,gBAAgB,EAAE,OAAO,EAAE,yBAAyB;CAC5C,CAAC;AAEX,qCAAqC;AACxB,QAAA,cAAc,GAAG;IAC5B,OAAO,EAAE,IAAI;IACb,aAAa,EAAE,CAAC;IAChB,UAAU,EAAE,uBAAuB;IACnC,kBAAkB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;CAC9D,CAAC;AAEX,sBAAsB;AACT,QAAA,mBAAmB,GAAG;IACjC,QAAQ,EAAE,kBAAkB;IAC5B,KAAK,EAAE,sBAAsB;IAC7B,GAAG,EAAE,gBAAgB;IACrB,KAAK,EAAE,4BAA4B;CAC3B,CAAC;AAEX,2CAA2C;AAC9B,QAAA,cAAc,GAAG;IAC5B,YAAY;IACZ,SAAS;IACT,gBAAgB;IAChB,UAAU;IACV,aAAa;IACb,KAAK;IACL,QAAQ;IACR,MAAM;IACN,KAAK;IACL,cAAc;IACd,eAAe;IACf,YAAY;IACZ,eAAe;IACf,aAAa;IACb,MAAM;IACN,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,UAAU;IACV,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,WAAW;IACX,gBAAgB;IAChB,mBAAmB;IACnB,YAAY;IACZ,SAAS;IACT,kBAAkB;IAClB,QAAQ;IACR,WAAW;IACX,aAAa;IACb,mBAAmB;IACnB,SAAS;IACT,cAAc;IACd,SAAS;IACT,cAAc;IACd,iBAAiB;IACjB,aAAa;IACb,KAAK;IACL,WAAW;IACX,gBAAgB;IAChB,cAAc;IACd,kBAAkB;IAClB,UAAU;IACV,kBAAkB;IAClB,eAAe;IACf,SAAS;IACT,QAAQ;IACR,SAAS;IACT,oBAAoB;IACpB,cAAc;IACd,yBAAyB;IACzB,WAAW;IACX,SAAS;IACT,eAAe;IACf,eAAe;IACf,QAAQ;IACR,cAAc;IACd,eAAe;IACf,gBAAgB;IAChB,QAAQ;IACR,YAAY;IACZ,SAAS;IACT,MAAM;IACN,SAAS;IACT,WAAW;IACX,UAAU;IACV,iBAAiB;IACjB,SAAS;IACT,QAAQ;IACR,aAAa;IACb,gBAAgB;IAChB,oBAAoB;IACpB,YAAY;IACZ,oBAAoB;IACpB,SAAS;IACT,QAAQ;IACR,kBAAkB;IAClB,YAAY;IACZ,eAAe;IACf,KAAK;IACL,SAAS;IACT,SAAS;IACT,OAAO;IACP,gBAAgB;IAChB,aAAa;IACb,WAAW;IACX,YAAY;IACZ,oBAAoB;IACpB,eAAe;IACf,iBAAiB;IACjB,eAAe;IACf,YAAY;IACZ,iBAAiB;IACjB,KAAK;CACG,CAAC;AAEX,iBAAiB;AACJ,QAAA,cAAc,GAAG;IAC5B,gBAAgB,EAAE,gEAAgE;IAClF,cAAc,EAAE,0BAA0B,qBAAa,CAAC,UAAU,QAAQ,qBAAa,CAAC,UAAU,SAAS;IAC3G,qBAAqB,EAAE,8DAA8D;IACrF,eAAe,EAAE,oCAAoC;IACrD,aAAa,EAAE,uDAAuD;IACtE,mBAAmB,EAAE,8CAA8C;IACnE,gBAAgB,EAAE,4CAA4C;IAC9D,2BAA2B,EAAE,yCAAyC;IACtE,WAAW,EAAE,yBAAyB;CAC9B,CAAC",
      names: [],
      sources: ["/Users/<USER>/WebstormProjects/goo/src/constants/index.ts"],
      sourcesContent: ["/**\n * Application constants and configuration values\n */\n\n// API Configuration\nexport const API_CONFIG = {\n  GOOGLE_PLACES_BASE_URL: 'https://maps.googleapis.com/maps/api/place',\n  GEOCODING_BASE_URL: 'https://maps.googleapis.com/maps/api/geocode',\n  REQUEST_TIMEOUT: 5000,\n  MAX_RETRIES: 3,\n  RETRY_DELAY_BASE: 1000, // Base delay for exponential backoff\n} as const;\n\n// Search Configuration\nexport const SEARCH_CONFIG = {\n  MIN_RADIUS: 1,\n  MAX_RADIUS: 50,\n  DEFAULT_RADIUS: 10,\n  MAX_RESULTS_PER_PAGE: 20,\n  MAX_TOTAL_RESULTS: 1000,\n} as const;\n\n// Rate Limiting Configuration\nexport const RATE_LIMIT_CONFIG = {\n  REQUESTS_PER_SECOND: 10,\n  BURST_SIZE: 20,\n  TOKEN_REFILL_INTERVAL: 100, // milliseconds\n} as const;\n\n// Cache Configuration\nexport const CACHE_CONFIG = {\n  EXPIRATION_HOURS: 24,\n  MAX_SIZE_MB: 50,\n  KEY_PREFIX: 'business_search_',\n  CLEANUP_INTERVAL: 3600000, // 1 hour in milliseconds\n} as const;\n\n// Website Verification Configuration\nexport const WEBSITE_CONFIG = {\n  TIMEOUT: 3000,\n  MAX_REDIRECTS: 3,\n  USER_AGENT: 'BusinessSearchBot/1.0',\n  VALID_STATUS_CODES: [200, 201, 202, 203, 204, 301, 302, 303, 307, 308],\n} as const;\n\n// Validation Patterns\nexport const VALIDATION_PATTERNS = {\n  ZIP_CODE: /^\\d{5}(-\\d{4})?$/,\n  PHONE: /^\\+?[\\d\\s\\-\\(\\)\\.]+$/,\n  URL: /^https?:\\/\\/.+/,\n  EMAIL: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n} as const;\n\n// Business Types (Google Places API types)\nexport const BUSINESS_TYPES = [\n  'accounting',\n  'airport',\n  'amusement_park',\n  'aquarium',\n  'art_gallery',\n  'atm',\n  'bakery',\n  'bank',\n  'bar',\n  'beauty_salon',\n  'bicycle_store',\n  'book_store',\n  'bowling_alley',\n  'bus_station',\n  'cafe',\n  'campground',\n  'car_dealer',\n  'car_rental',\n  'car_repair',\n  'car_wash',\n  'casino',\n  'cemetery',\n  'church',\n  'city_hall',\n  'clothing_store',\n  'convenience_store',\n  'courthouse',\n  'dentist',\n  'department_store',\n  'doctor',\n  'drugstore',\n  'electrician',\n  'electronics_store',\n  'embassy',\n  'fire_station',\n  'florist',\n  'funeral_home',\n  'furniture_store',\n  'gas_station',\n  'gym',\n  'hair_care',\n  'hardware_store',\n  'hindu_temple',\n  'home_goods_store',\n  'hospital',\n  'insurance_agency',\n  'jewelry_store',\n  'laundry',\n  'lawyer',\n  'library',\n  'light_rail_station',\n  'liquor_store',\n  'local_government_office',\n  'locksmith',\n  'lodging',\n  'meal_delivery',\n  'meal_takeaway',\n  'mosque',\n  'movie_rental',\n  'movie_theater',\n  'moving_company',\n  'museum',\n  'night_club',\n  'painter',\n  'park',\n  'parking',\n  'pet_store',\n  'pharmacy',\n  'physiotherapist',\n  'plumber',\n  'police',\n  'post_office',\n  'primary_school',\n  'real_estate_agency',\n  'restaurant',\n  'roofing_contractor',\n  'rv_park',\n  'school',\n  'secondary_school',\n  'shoe_store',\n  'shopping_mall',\n  'spa',\n  'stadium',\n  'storage',\n  'store',\n  'subway_station',\n  'supermarket',\n  'synagogue',\n  'taxi_stand',\n  'tourist_attraction',\n  'train_station',\n  'transit_station',\n  'travel_agency',\n  'university',\n  'veterinary_care',\n  'zoo'\n] as const;\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  INVALID_ZIP_CODE: 'Invalid zip code format. Please use 5-digit or 9-digit format.',\n  INVALID_RADIUS: `Radius must be between ${SEARCH_CONFIG.MIN_RADIUS} and ${SEARCH_CONFIG.MAX_RADIUS} miles.`,\n  INVALID_BUSINESS_TYPE: 'Invalid business type. Please select from available options.',\n  API_KEY_MISSING: 'Google Places API key is required.',\n  NETWORK_ERROR: 'Network error occurred. Please check your connection.',\n  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded. Please try again later.',\n  GEOCODING_FAILED: 'Failed to convert zip code to coordinates.',\n  WEBSITE_VERIFICATION_FAILED: 'Failed to verify website accessibility.',\n  CACHE_ERROR: 'Cache operation failed.',\n} as const;\n\nexport type BusinessType = typeof BUSINESS_TYPES[number];\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "f284cb621d7dc90b6f7005b02d6eb4ae67af3e54"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_155q57m7zi = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_155q57m7zi();
cov_155q57m7zi().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_155q57m7zi().s[1]++;
exports.ERROR_MESSAGES = exports.BUSINESS_TYPES = exports.VALIDATION_PATTERNS = exports.WEBSITE_CONFIG = exports.CACHE_CONFIG = exports.RATE_LIMIT_CONFIG = exports.SEARCH_CONFIG = exports.API_CONFIG = void 0;
// API Configuration
/* istanbul ignore next */
cov_155q57m7zi().s[2]++;
exports.API_CONFIG = {
  GOOGLE_PLACES_BASE_URL: 'https://maps.googleapis.com/maps/api/place',
  GEOCODING_BASE_URL: 'https://maps.googleapis.com/maps/api/geocode',
  REQUEST_TIMEOUT: 5000,
  MAX_RETRIES: 3,
  RETRY_DELAY_BASE: 1000 // Base delay for exponential backoff
};
// Search Configuration
/* istanbul ignore next */
cov_155q57m7zi().s[3]++;
exports.SEARCH_CONFIG = {
  MIN_RADIUS: 1,
  MAX_RADIUS: 50,
  DEFAULT_RADIUS: 10,
  MAX_RESULTS_PER_PAGE: 20,
  MAX_TOTAL_RESULTS: 1000
};
// Rate Limiting Configuration
/* istanbul ignore next */
cov_155q57m7zi().s[4]++;
exports.RATE_LIMIT_CONFIG = {
  REQUESTS_PER_SECOND: 10,
  BURST_SIZE: 20,
  TOKEN_REFILL_INTERVAL: 100 // milliseconds
};
// Cache Configuration
/* istanbul ignore next */
cov_155q57m7zi().s[5]++;
exports.CACHE_CONFIG = {
  EXPIRATION_HOURS: 24,
  MAX_SIZE_MB: 50,
  KEY_PREFIX: 'business_search_',
  CLEANUP_INTERVAL: 3600000 // 1 hour in milliseconds
};
// Website Verification Configuration
/* istanbul ignore next */
cov_155q57m7zi().s[6]++;
exports.WEBSITE_CONFIG = {
  TIMEOUT: 3000,
  MAX_REDIRECTS: 3,
  USER_AGENT: 'BusinessSearchBot/1.0',
  VALID_STATUS_CODES: [200, 201, 202, 203, 204, 301, 302, 303, 307, 308]
};
// Validation Patterns
/* istanbul ignore next */
cov_155q57m7zi().s[7]++;
exports.VALIDATION_PATTERNS = {
  ZIP_CODE: /^\d{5}(-\d{4})?$/,
  PHONE: /^\+?[\d\s\-\(\)\.]+$/,
  URL: /^https?:\/\/.+/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
};
// Business Types (Google Places API types)
/* istanbul ignore next */
cov_155q57m7zi().s[8]++;
exports.BUSINESS_TYPES = ['accounting', 'airport', 'amusement_park', 'aquarium', 'art_gallery', 'atm', 'bakery', 'bank', 'bar', 'beauty_salon', 'bicycle_store', 'book_store', 'bowling_alley', 'bus_station', 'cafe', 'campground', 'car_dealer', 'car_rental', 'car_repair', 'car_wash', 'casino', 'cemetery', 'church', 'city_hall', 'clothing_store', 'convenience_store', 'courthouse', 'dentist', 'department_store', 'doctor', 'drugstore', 'electrician', 'electronics_store', 'embassy', 'fire_station', 'florist', 'funeral_home', 'furniture_store', 'gas_station', 'gym', 'hair_care', 'hardware_store', 'hindu_temple', 'home_goods_store', 'hospital', 'insurance_agency', 'jewelry_store', 'laundry', 'lawyer', 'library', 'light_rail_station', 'liquor_store', 'local_government_office', 'locksmith', 'lodging', 'meal_delivery', 'meal_takeaway', 'mosque', 'movie_rental', 'movie_theater', 'moving_company', 'museum', 'night_club', 'painter', 'park', 'parking', 'pet_store', 'pharmacy', 'physiotherapist', 'plumber', 'police', 'post_office', 'primary_school', 'real_estate_agency', 'restaurant', 'roofing_contractor', 'rv_park', 'school', 'secondary_school', 'shoe_store', 'shopping_mall', 'spa', 'stadium', 'storage', 'store', 'subway_station', 'supermarket', 'synagogue', 'taxi_stand', 'tourist_attraction', 'train_station', 'transit_station', 'travel_agency', 'university', 'veterinary_care', 'zoo'];
// Error Messages
/* istanbul ignore next */
cov_155q57m7zi().s[9]++;
exports.ERROR_MESSAGES = {
  INVALID_ZIP_CODE: 'Invalid zip code format. Please use 5-digit or 9-digit format.',
  INVALID_RADIUS: `Radius must be between ${exports.SEARCH_CONFIG.MIN_RADIUS} and ${exports.SEARCH_CONFIG.MAX_RADIUS} miles.`,
  INVALID_BUSINESS_TYPE: 'Invalid business type. Please select from available options.',
  API_KEY_MISSING: 'Google Places API key is required.',
  NETWORK_ERROR: 'Network error occurred. Please check your connection.',
  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded. Please try again later.',
  GEOCODING_FAILED: 'Failed to convert zip code to coordinates.',
  WEBSITE_VERIFICATION_FAILED: 'Failed to verify website accessibility.',
  CACHE_ERROR: 'Cache operation failed.'
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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