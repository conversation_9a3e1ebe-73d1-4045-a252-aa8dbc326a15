c36df15355f5f40df16cf3f60f2683ba
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Mock the services
jest.mock('../../src/services/geocoding');
jest.mock('../../src/services/googlePlaces');
jest.mock('../../src/services/websiteVerification');
const businessSearch_1 = require("../../src/services/businessSearch");
const geocoding_1 = require("../../src/services/geocoding");
const googlePlaces_1 = require("../../src/services/googlePlaces");
const websiteVerification_1 = require("../../src/services/websiteVerification");
const Errors_1 = require("../../src/models/Errors");
const MockedGeocodingService = geocoding_1.GeocodingService;
const MockedGooglePlacesService = googlePlaces_1.GooglePlacesService;
const MockedWebsiteVerificationService = websiteVerification_1.WebsiteVerificationService;
describe('BusinessSearchService', () => {
    let businessSearchService;
    let mockGeocodingService;
    let mockGooglePlacesService;
    let mockWebsiteVerificationService;
    beforeEach(() => {
        // Clear all mocks
        jest.clearAllMocks();
        // Create mock instances
        mockGeocodingService = new MockedGeocodingService();
        mockGooglePlacesService = new MockedGooglePlacesService();
        mockWebsiteVerificationService = new MockedWebsiteVerificationService();
        businessSearchService = new businessSearch_1.BusinessSearchService(mockGeocodingService, mockGooglePlacesService, mockWebsiteVerificationService);
    });
    describe('searchBusinesses', () => {
        const mockCoordinates = {
            latitude: 40.7128,
            longitude: -74.0060
        };
        const mockPlacesResult = {
            places: [
                {
                    place_id: '1',
                    name: 'Restaurant A',
                    formatted_address: '123 Main St, New York, NY',
                    website: 'https://restaurant-a.com',
                    types: ['restaurant'],
                    rating: 4.5,
                    geometry: {
                        location: { lat: 40.7128, lng: -74.0060 }
                    }
                },
                {
                    place_id: '2',
                    name: 'Restaurant B',
                    formatted_address: '456 Oak Ave, New York, NY',
                    types: ['restaurant'],
                    rating: 4.0,
                    geometry: {
                        location: { lat: 40.7130, lng: -74.0062 }
                    }
                }
            ],
            totalResults: 2,
            nextPageToken: undefined
        };
        const mockVerificationResults = [
            {
                url: 'https://restaurant-a.com',
                status: 'verified',
                accessible: true,
                confidence: 0.9,
                verifiedAt: new Date()
            }
        ];
        it('should search businesses successfully', async () => {
            // Setup mocks
            mockGeocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);
            mockGooglePlacesService.searchNearby.mockResolvedValue(mockPlacesResult);
            mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue(mockVerificationResults);
            const result = await businessSearchService.searchBusinesses({
                zipCode: '10001',
                radius: 10,
                businessType: 'restaurant'
            });
            expect(result.results.withWebsites).toHaveLength(1);
            expect(result.results.withoutWebsites).toHaveLength(1);
            expect(result.statistics.totalFound).toBe(2);
            expect(result.statistics.withWebsiteCount).toBe(1);
            expect(result.statistics.withoutWebsiteCount).toBe(1);
            expect(result.statistics.websiteAdoptionRate).toBe(0.5);
            // Verify service calls
            expect(mockGeocodingService.zipCodeToCoordinates).toHaveBeenCalledWith('10001');
            expect(mockGooglePlacesService.searchNearby).toHaveBeenCalledWith(mockCoordinates, 16093.4, // 10 miles in meters
            'restaurant', {});
            expect(mockWebsiteVerificationService.verifyMultipleWebsites).toHaveBeenCalledWith(['https://restaurant-a.com'], 5);
        });
        it('should handle businesses without websites', async () => {
            const placesWithoutWebsites = {
                places: [
                    {
                        place_id: '1',
                        name: 'Restaurant A',
                        formatted_address: '123 Main St, New York, NY',
                        types: ['restaurant'],
                        rating: 4.5,
                        geometry: {
                            location: { lat: 40.7128, lng: -74.0060 }
                        }
                    }
                ],
                totalResults: 1,
                nextPageToken: undefined
            };
            mockGeocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);
            mockGooglePlacesService.searchNearby.mockResolvedValue(placesWithoutWebsites);
            mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue([]);
            const result = await businessSearchService.searchBusinesses({
                zipCode: '10001',
                radius: 10,
                businessType: 'restaurant'
            });
            expect(result.results.withWebsites).toHaveLength(0);
            expect(result.results.withoutWebsites).toHaveLength(1);
            expect(result.statistics.websiteAdoptionRate).toBe(0);
        });
        it('should validate search parameters', async () => {
            await expect(businessSearchService.searchBusinesses({
                zipCode: 'invalid',
                radius: 10,
                businessType: 'restaurant'
            })).rejects.toThrow(Errors_1.ValidationError);
            await expect(businessSearchService.searchBusinesses({
                zipCode: '10001',
                radius: 0,
                businessType: 'restaurant'
            })).rejects.toThrow(Errors_1.ValidationError);
            await expect(businessSearchService.searchBusinesses({
                zipCode: '10001',
                radius: 10,
                businessType: 'invalid_type'
            })).rejects.toThrow(Errors_1.ValidationError);
        });
        it('should handle geocoding errors gracefully', async () => {
            mockGeocodingService.zipCodeToCoordinates.mockRejectedValue(new Error('Geocoding failed'));
            await expect(businessSearchService.searchBusinesses({
                zipCode: '10001',
                radius: 10,
                businessType: 'restaurant'
            })).rejects.toThrow('Geocoding failed');
        });
        it('should calculate distances correctly', async () => {
            mockGeocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);
            mockGooglePlacesService.searchNearby.mockResolvedValue(mockPlacesResult);
            mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue([]);
            const result = await businessSearchService.searchBusinesses({
                zipCode: '10001',
                radius: 10,
                businessType: 'restaurant'
            });
            // Check that distances are calculated
            expect(result.results.withoutWebsites[0].distance).toBeGreaterThanOrEqual(0);
            expect(result.results.withoutWebsites[1].distance).toBeGreaterThanOrEqual(0);
        });
        it('should sort results by distance', async () => {
            const placesWithDifferentDistances = {
                places: [
                    {
                        place_id: '1',
                        name: 'Far Restaurant',
                        formatted_address: '123 Main St, New York, NY',
                        types: ['restaurant'],
                        geometry: {
                            location: { lat: 40.8000, lng: -74.1000 } // Further away
                        }
                    },
                    {
                        place_id: '2',
                        name: 'Near Restaurant',
                        formatted_address: '456 Oak Ave, New York, NY',
                        types: ['restaurant'],
                        geometry: {
                            location: { lat: 40.7129, lng: -74.0061 } // Closer
                        }
                    }
                ],
                totalResults: 2,
                nextPageToken: undefined
            };
            mockGeocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);
            mockGooglePlacesService.searchNearby.mockResolvedValue(placesWithDifferentDistances);
            mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue([]);
            const result = await businessSearchService.searchBusinesses({
                zipCode: '10001',
                radius: 10,
                businessType: 'restaurant'
            });
            // Results should be sorted by distance (closest first)
            expect(result.results.withoutWebsites[0].name).toBe('Near Restaurant');
            expect(result.results.withoutWebsites[1].name).toBe('Far Restaurant');
            expect(result.results.withoutWebsites[0].distance).toBeLessThan(result.results.withoutWebsites[1].distance);
        });
        it('should handle website verification failures gracefully', async () => {
            mockGeocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);
            mockGooglePlacesService.searchNearby.mockResolvedValue(mockPlacesResult);
            mockWebsiteVerificationService.verifyMultipleWebsites.mockRejectedValue(new Error('Verification failed'));
            // Should still return results even if website verification fails
            const result = await businessSearchService.searchBusinesses({
                zipCode: '10001',
                radius: 10,
                businessType: 'restaurant'
            });
            expect(result.results.withWebsites).toHaveLength(0);
            expect(result.results.withoutWebsites).toHaveLength(2);
            expect(result.statistics.totalFound).toBe(2);
        });
    });
    describe('getSearchHistory', () => {
        it('should return search history', () => {
            const history = businessSearchService.getSearchHistory();
            expect(Array.isArray(history)).toBe(true);
        });
    });
    describe('clearSearchHistory', () => {
        it('should clear search history', () => {
            businessSearchService.clearSearchHistory();
            const history = businessSearchService.getSearchHistory();
            expect(history).toHaveLength(0);
        });
    });
    describe('getSearchStatistics', () => {
        it('should return search statistics', () => {
            const stats = businessSearchService.getSearchStatistics();
            expect(stats).toHaveProperty('totalSearches');
            expect(stats).toHaveProperty('averageResultsPerSearch');
            expect(stats).toHaveProperty('averageWebsiteAdoptionRate');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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