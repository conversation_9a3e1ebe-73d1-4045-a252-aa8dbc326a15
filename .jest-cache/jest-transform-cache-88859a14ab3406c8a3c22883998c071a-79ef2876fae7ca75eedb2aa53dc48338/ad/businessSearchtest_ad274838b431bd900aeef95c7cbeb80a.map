{"file": "/Users/<USER>/WebstormProjects/goo/tests/services/businessSearch.test.ts", "mappings": ";;AAOA,oBAAoB;AACpB,IAAI,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;AAC1C,IAAI,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;AAC7C,IAAI,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;AAVpD,sEAA0E;AAC1E,4DAAgE;AAChE,kEAAsE;AACtE,gFAAoF;AAEpF,oDAA0D;AAO1D,MAAM,sBAAsB,GAAG,4BAA6D,CAAC;AAC7F,MAAM,yBAAyB,GAAG,kCAAmE,CAAC;AACtG,MAAM,gCAAgC,GAAG,gDAAiF,CAAC;AAE3H,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;IACrC,IAAI,qBAA4C,CAAC;IACjD,IAAI,oBAAmD,CAAC;IACxD,IAAI,uBAAyD,CAAC;IAC9D,IAAI,8BAAuE,CAAC;IAE5E,UAAU,CAAC,GAAG,EAAE;QACd,kBAAkB;QAClB,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,wBAAwB;QACxB,oBAAoB,GAAG,IAAI,sBAAsB,EAAmC,CAAC;QACrF,uBAAuB,GAAG,IAAI,yBAAyB,EAAsC,CAAC;QAC9F,8BAA8B,GAAG,IAAI,gCAAgC,EAA6C,CAAC;QAEnH,qBAAqB,GAAG,IAAI,sCAAqB,CAC/C,oBAAoB,EACpB,uBAAuB,EACvB,8BAA8B,CAC/B,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,MAAM,eAAe,GAAgB;YACnC,QAAQ,EAAE,OAAO;YACjB,SAAS,EAAE,CAAC,OAAO;SACpB,CAAC;QAEF,MAAM,gBAAgB,GAAG;YACvB,MAAM,EAAE;gBACN;oBACE,QAAQ,EAAE,GAAG;oBACb,IAAI,EAAE,cAAc;oBACpB,iBAAiB,EAAE,2BAA2B;oBAC9C,OAAO,EAAE,0BAA0B;oBACnC,KAAK,EAAE,CAAC,YAAY,CAAC;oBACrB,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE;wBACR,QAAQ,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE;qBAC1C;iBACF;gBACD;oBACE,QAAQ,EAAE,GAAG;oBACb,IAAI,EAAE,cAAc;oBACpB,iBAAiB,EAAE,2BAA2B;oBAC9C,KAAK,EAAE,CAAC,YAAY,CAAC;oBACrB,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE;wBACR,QAAQ,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE;qBAC1C;iBACF;aACF;YACD,YAAY,EAAE,CAAC;YACf,aAAa,EAAE,SAAS;SACzB,CAAC;QAEF,MAAM,uBAAuB,GAAG;YAC9B;gBACE,GAAG,EAAE,0BAA0B;gBAC/B,MAAM,EAAE,UAAmB;gBAC3B,UAAU,EAAE,IAAI;gBAChB,UAAU,EAAE,GAAG;gBACf,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB;SACF,CAAC;QAEF,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,cAAc;YACd,oBAAoB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAC7E,uBAAuB,CAAC,YAAY,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YACzE,8BAA8B,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,CAAC;YAEjG,MAAM,MAAM,GAAG,MAAM,qBAAqB,CAAC,gBAAgB,CAAC;gBAC1D,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,EAAE;gBACV,YAAY,EAAE,YAAY;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAExD,uBAAuB;YACvB,MAAM,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAChF,MAAM,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAC/D,eAAe,EACf,OAAO,EAAE,qBAAqB;YAC9B,YAAY,EACZ,EAAE,CACH,CAAC;YACF,MAAM,CAAC,8BAA8B,CAAC,sBAAsB,CAAC,CAAC,oBAAoB,CAChF,CAAC,0BAA0B,CAAC,EAC5B,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,qBAAqB,GAAG;gBAC5B,MAAM,EAAE;oBACN;wBACE,QAAQ,EAAE,GAAG;wBACb,IAAI,EAAE,cAAc;wBACpB,iBAAiB,EAAE,2BAA2B;wBAC9C,KAAK,EAAE,CAAC,YAAY,CAAC;wBACrB,MAAM,EAAE,GAAG;wBACX,QAAQ,EAAE;4BACR,QAAQ,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE;yBAC1C;qBACF;iBACF;gBACD,YAAY,EAAE,CAAC;gBACf,aAAa,EAAE,SAAS;aACzB,CAAC;YAEF,oBAAoB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAC7E,uBAAuB,CAAC,YAAY,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;YAC9E,8BAA8B,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAE5E,MAAM,MAAM,GAAG,MAAM,qBAAqB,CAAC,gBAAgB,CAAC;gBAC1D,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,EAAE;gBACV,YAAY,EAAE,YAAY;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC;gBAClD,OAAO,EAAE,SAAS;gBAClB,MAAM,EAAE,EAAE;gBACV,YAAY,EAAE,YAAY;aAC3B,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,wBAAe,CAAC,CAAC;YAErC,MAAM,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC;gBAClD,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,CAAC;gBACT,YAAY,EAAE,YAAY;aAC3B,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,wBAAe,CAAC,CAAC;YAErC,MAAM,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC;gBAClD,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,EAAE;gBACV,YAAY,EAAE,cAAc;aAC7B,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,wBAAe,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,oBAAoB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAE3F,MAAM,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC;gBAClD,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,EAAE;gBACV,YAAY,EAAE,YAAY;aAC3B,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,oBAAoB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAC7E,uBAAuB,CAAC,YAAY,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YACzE,8BAA8B,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAE5E,MAAM,MAAM,GAAG,MAAM,qBAAqB,CAAC,gBAAgB,CAAC;gBAC1D,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,EAAE;gBACV,YAAY,EAAE,YAAY;aAC3B,CAAC,CAAC;YAEH,sCAAsC;YACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC7E,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,4BAA4B,GAAG;gBACnC,MAAM,EAAE;oBACN;wBACE,QAAQ,EAAE,GAAG;wBACb,IAAI,EAAE,gBAAgB;wBACtB,iBAAiB,EAAE,2BAA2B;wBAC9C,KAAK,EAAE,CAAC,YAAY,CAAC;wBACrB,QAAQ,EAAE;4BACR,QAAQ,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,eAAe;yBAC1D;qBACF;oBACD;wBACE,QAAQ,EAAE,GAAG;wBACb,IAAI,EAAE,iBAAiB;wBACvB,iBAAiB,EAAE,2BAA2B;wBAC9C,KAAK,EAAE,CAAC,YAAY,CAAC;wBACrB,QAAQ,EAAE;4BACR,QAAQ,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,SAAS;yBACpD;qBACF;iBACF;gBACD,YAAY,EAAE,CAAC;gBACf,aAAa,EAAE,SAAS;aACzB,CAAC;YAEF,oBAAoB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAC7E,uBAAuB,CAAC,YAAY,CAAC,iBAAiB,CAAC,4BAA4B,CAAC,CAAC;YACrF,8BAA8B,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAE5E,MAAM,MAAM,GAAG,MAAM,qBAAqB,CAAC,gBAAgB,CAAC;gBAC1D,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,EAAE;gBACV,YAAY,EAAE,YAAY;aAC3B,CAAC,CAAC;YAEH,uDAAuD;YACvD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACvE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACtE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QAC9G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,oBAAoB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAC7E,uBAAuB,CAAC,YAAY,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YACzE,8BAA8B,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC;YAE1G,iEAAiE;YACjE,MAAM,MAAM,GAAG,MAAM,qBAAqB,CAAC,gBAAgB,CAAC;gBAC1D,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,EAAE;gBACV,YAAY,EAAE,YAAY;aAC3B,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,OAAO,GAAG,qBAAqB,CAAC,gBAAgB,EAAE,CAAC;YACzD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,qBAAqB,CAAC,kBAAkB,EAAE,CAAC;YAC3C,MAAM,OAAO,GAAG,qBAAqB,CAAC,gBAAgB,EAAE,CAAC;YACzD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,KAAK,GAAG,qBAAqB,CAAC,mBAAmB,EAAE,CAAC;YAC1D,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,yBAAyB,CAAC,CAAC;YACxD,MAAM,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,4BAA4B,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/tests/services/businessSearch.test.ts"], "sourcesContent": ["import { BusinessSearchService } from '../../src/services/businessSearch';\nimport { GeocodingService } from '../../src/services/geocoding';\nimport { GooglePlacesService } from '../../src/services/googlePlaces';\nimport { WebsiteVerificationService } from '../../src/services/websiteVerification';\nimport { Business, SearchResult, Coordinates } from '../../src/models/Business';\nimport { ValidationError } from '../../src/models/Errors';\n\n// Mock the services\njest.mock('../../src/services/geocoding');\njest.mock('../../src/services/googlePlaces');\njest.mock('../../src/services/websiteVerification');\n\nconst MockedGeocodingService = GeocodingService as jest.MockedClass<typeof GeocodingService>;\nconst MockedGooglePlacesService = GooglePlacesService as jest.MockedClass<typeof GooglePlacesService>;\nconst MockedWebsiteVerificationService = WebsiteVerificationService as jest.MockedClass<typeof WebsiteVerificationService>;\n\ndescribe('BusinessSearchService', () => {\n  let businessSearchService: BusinessSearchService;\n  let mockGeocodingService: jest.Mocked<GeocodingService>;\n  let mockGooglePlacesService: jest.Mocked<GooglePlacesService>;\n  let mockWebsiteVerificationService: jest.Mocked<WebsiteVerificationService>;\n\n  beforeEach(() => {\n    // Clear all mocks\n    jest.clearAllMocks();\n\n    // Create mock instances\n    mockGeocodingService = new MockedGeocodingService() as jest.Mocked<GeocodingService>;\n    mockGooglePlacesService = new MockedGooglePlacesService() as jest.Mocked<GooglePlacesService>;\n    mockWebsiteVerificationService = new MockedWebsiteVerificationService() as jest.Mocked<WebsiteVerificationService>;\n\n    businessSearchService = new BusinessSearchService(\n      mockGeocodingService,\n      mockGooglePlacesService,\n      mockWebsiteVerificationService\n    );\n  });\n\n  describe('searchBusinesses', () => {\n    const mockCoordinates: Coordinates = {\n      latitude: 40.7128,\n      longitude: -74.0060\n    };\n\n    const mockPlacesResult = {\n      places: [\n        {\n          place_id: '1',\n          name: 'Restaurant A',\n          formatted_address: '123 Main St, New York, NY',\n          website: 'https://restaurant-a.com',\n          types: ['restaurant'],\n          rating: 4.5,\n          geometry: {\n            location: { lat: 40.7128, lng: -74.0060 }\n          }\n        },\n        {\n          place_id: '2',\n          name: 'Restaurant B',\n          formatted_address: '456 Oak Ave, New York, NY',\n          types: ['restaurant'],\n          rating: 4.0,\n          geometry: {\n            location: { lat: 40.7130, lng: -74.0062 }\n          }\n        }\n      ],\n      totalResults: 2,\n      nextPageToken: undefined\n    };\n\n    const mockVerificationResults = [\n      {\n        url: 'https://restaurant-a.com',\n        status: 'verified' as const,\n        accessible: true,\n        confidence: 0.9,\n        verifiedAt: new Date()\n      }\n    ];\n\n    it('should search businesses successfully', async () => {\n      // Setup mocks\n      mockGeocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);\n      mockGooglePlacesService.searchNearby.mockResolvedValue(mockPlacesResult);\n      mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue(mockVerificationResults);\n\n      const result = await businessSearchService.searchBusinesses({\n        zipCode: '10001',\n        radius: 10,\n        businessType: 'restaurant'\n      });\n\n      expect(result.results.withWebsites).toHaveLength(1);\n      expect(result.results.withoutWebsites).toHaveLength(1);\n      expect(result.statistics.totalFound).toBe(2);\n      expect(result.statistics.withWebsiteCount).toBe(1);\n      expect(result.statistics.withoutWebsiteCount).toBe(1);\n      expect(result.statistics.websiteAdoptionRate).toBe(0.5);\n\n      // Verify service calls\n      expect(mockGeocodingService.zipCodeToCoordinates).toHaveBeenCalledWith('10001');\n      expect(mockGooglePlacesService.searchNearby).toHaveBeenCalledWith(\n        mockCoordinates,\n        16093.4, // 10 miles in meters\n        'restaurant',\n        {}\n      );\n      expect(mockWebsiteVerificationService.verifyMultipleWebsites).toHaveBeenCalledWith(\n        ['https://restaurant-a.com'],\n        5\n      );\n    });\n\n    it('should handle businesses without websites', async () => {\n      const placesWithoutWebsites = {\n        places: [\n          {\n            place_id: '1',\n            name: 'Restaurant A',\n            formatted_address: '123 Main St, New York, NY',\n            types: ['restaurant'],\n            rating: 4.5,\n            geometry: {\n              location: { lat: 40.7128, lng: -74.0060 }\n            }\n          }\n        ],\n        totalResults: 1,\n        nextPageToken: undefined\n      };\n\n      mockGeocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);\n      mockGooglePlacesService.searchNearby.mockResolvedValue(placesWithoutWebsites);\n      mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue([]);\n\n      const result = await businessSearchService.searchBusinesses({\n        zipCode: '10001',\n        radius: 10,\n        businessType: 'restaurant'\n      });\n\n      expect(result.results.withWebsites).toHaveLength(0);\n      expect(result.results.withoutWebsites).toHaveLength(1);\n      expect(result.statistics.websiteAdoptionRate).toBe(0);\n    });\n\n    it('should validate search parameters', async () => {\n      await expect(businessSearchService.searchBusinesses({\n        zipCode: 'invalid',\n        radius: 10,\n        businessType: 'restaurant'\n      })).rejects.toThrow(ValidationError);\n\n      await expect(businessSearchService.searchBusinesses({\n        zipCode: '10001',\n        radius: 0,\n        businessType: 'restaurant'\n      })).rejects.toThrow(ValidationError);\n\n      await expect(businessSearchService.searchBusinesses({\n        zipCode: '10001',\n        radius: 10,\n        businessType: 'invalid_type'\n      })).rejects.toThrow(ValidationError);\n    });\n\n    it('should handle geocoding errors gracefully', async () => {\n      mockGeocodingService.zipCodeToCoordinates.mockRejectedValue(new Error('Geocoding failed'));\n\n      await expect(businessSearchService.searchBusinesses({\n        zipCode: '10001',\n        radius: 10,\n        businessType: 'restaurant'\n      })).rejects.toThrow('Geocoding failed');\n    });\n\n    it('should calculate distances correctly', async () => {\n      mockGeocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);\n      mockGooglePlacesService.searchNearby.mockResolvedValue(mockPlacesResult);\n      mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue([]);\n\n      const result = await businessSearchService.searchBusinesses({\n        zipCode: '10001',\n        radius: 10,\n        businessType: 'restaurant'\n      });\n\n      // Check that distances are calculated\n      expect(result.results.withoutWebsites[0].distance).toBeGreaterThanOrEqual(0);\n      expect(result.results.withoutWebsites[1].distance).toBeGreaterThanOrEqual(0);\n    });\n\n    it('should sort results by distance', async () => {\n      const placesWithDifferentDistances = {\n        places: [\n          {\n            place_id: '1',\n            name: 'Far Restaurant',\n            formatted_address: '123 Main St, New York, NY',\n            types: ['restaurant'],\n            geometry: {\n              location: { lat: 40.8000, lng: -74.1000 } // Further away\n            }\n          },\n          {\n            place_id: '2',\n            name: 'Near Restaurant',\n            formatted_address: '456 Oak Ave, New York, NY',\n            types: ['restaurant'],\n            geometry: {\n              location: { lat: 40.7129, lng: -74.0061 } // Closer\n            }\n          }\n        ],\n        totalResults: 2,\n        nextPageToken: undefined\n      };\n\n      mockGeocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);\n      mockGooglePlacesService.searchNearby.mockResolvedValue(placesWithDifferentDistances);\n      mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue([]);\n\n      const result = await businessSearchService.searchBusinesses({\n        zipCode: '10001',\n        radius: 10,\n        businessType: 'restaurant'\n      });\n\n      // Results should be sorted by distance (closest first)\n      expect(result.results.withoutWebsites[0].name).toBe('Near Restaurant');\n      expect(result.results.withoutWebsites[1].name).toBe('Far Restaurant');\n      expect(result.results.withoutWebsites[0].distance).toBeLessThan(result.results.withoutWebsites[1].distance);\n    });\n\n    it('should handle website verification failures gracefully', async () => {\n      mockGeocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);\n      mockGooglePlacesService.searchNearby.mockResolvedValue(mockPlacesResult);\n      mockWebsiteVerificationService.verifyMultipleWebsites.mockRejectedValue(new Error('Verification failed'));\n\n      // Should still return results even if website verification fails\n      const result = await businessSearchService.searchBusinesses({\n        zipCode: '10001',\n        radius: 10,\n        businessType: 'restaurant'\n      });\n\n      expect(result.results.withWebsites).toHaveLength(0);\n      expect(result.results.withoutWebsites).toHaveLength(2);\n      expect(result.statistics.totalFound).toBe(2);\n    });\n  });\n\n  describe('getSearchHistory', () => {\n    it('should return search history', () => {\n      const history = businessSearchService.getSearchHistory();\n      expect(Array.isArray(history)).toBe(true);\n    });\n  });\n\n  describe('clearSearchHistory', () => {\n    it('should clear search history', () => {\n      businessSearchService.clearSearchHistory();\n      const history = businessSearchService.getSearchHistory();\n      expect(history).toHaveLength(0);\n    });\n  });\n\n  describe('getSearchStatistics', () => {\n    it('should return search statistics', () => {\n      const stats = businessSearchService.getSearchStatistics();\n      expect(stats).toHaveProperty('totalSearches');\n      expect(stats).toHaveProperty('averageResultsPerSearch');\n      expect(stats).toHaveProperty('averageWebsiteAdoptionRate');\n    });\n  });\n});\n"], "version": 3}