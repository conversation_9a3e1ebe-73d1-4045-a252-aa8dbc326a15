376270165ac906e050c275181965a4ed
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GooglePlacesService = void 0;
const Errors_1 = require("../models/Errors");
const validation_1 = require("../utils/validation");
const constants_1 = require("../constants");
/**
 * Service for interacting with Google Places API
 */
class GooglePlacesService {
    constructor(apiKey) {
        this.cache = new Map();
        this.apiKey = apiKey || process.env.GOOGLE_PLACES_API_KEY || '';
        this.baseUrl = constants_1.API_CONFIG.GOOGLE_PLACES_BASE_URL;
        if (!this.apiKey) {
            throw new Errors_1.ConfigurationError('Google Places API key is required', 'GOOGLE_PLACES_API_KEY');
        }
    }
    /**
     * Searches for places near a location
     * @param coordinates - Center point for search
     * @param radius - Search radius in meters
     * @param type - Type of place to search for
     * @param params - Additional search parameters
     * @returns Promise resolving to search results
     */
    async searchNearby(coordinates, radius, type, params = {}) {
        // Validate parameters
        (0, validation_1.validateRequired)(type, 'type');
        (0, validation_1.validateRange)(radius, 1, 50000, 'radius');
        if (params.minprice !== undefined) {
            (0, validation_1.validateRange)(params.minprice, 0, 4, 'minprice');
        }
        if (params.maxprice !== undefined) {
            (0, validation_1.validateRange)(params.maxprice, 0, 4, 'maxprice');
        }
        const searchParams = new URLSearchParams({
            location: `${coordinates.latitude},${coordinates.longitude}`,
            radius: radius.toString(),
            type: type,
            key: this.apiKey,
        });
        // Add optional parameters
        if (params.minprice !== undefined) {
            searchParams.append('minprice', params.minprice.toString());
        }
        if (params.maxprice !== undefined) {
            searchParams.append('maxprice', params.maxprice.toString());
        }
        if (params.opennow !== undefined) {
            searchParams.append('opennow', params.opennow.toString());
        }
        if (params.keyword) {
            searchParams.append('keyword', params.keyword);
        }
        const url = `${this.baseUrl}/nearbysearch/json?${searchParams.toString()}`;
        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                },
            });
            if (!response.ok) {
                throw new Errors_1.ApiError(`Places API request failed: ${response.statusText}`, response.status, 'Google Places API');
            }
            const data = await response.json();
            if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
                throw new Errors_1.ApiError(data.error_message || `Places API error: ${data.status}`, response.status, 'Google Places API');
            }
            const places = data.results.map(this.transformPlaceResult);
            return {
                places,
                nextPageToken: data.next_page_token,
                totalResults: places.length,
                searchParams: {
                    coordinates,
                    radius,
                    type,
                    ...params,
                },
            };
        }
        catch (error) {
            if (error instanceof Errors_1.ApiError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new Errors_1.NetworkError(`Network error during places search: ${error.message}`, error);
            }
            throw new Errors_1.NetworkError(`Unknown error during places search: ${String(error)}`);
        }
    }
    /**
     * Searches for places using text query
     * @param query - Text search query
     * @param location - Optional location bias
     * @param radius - Optional search radius in meters
     * @returns Promise resolving to search results
     */
    async searchByText(query, location, radius) {
        (0, validation_1.validateRequired)(query, 'query');
        const searchParams = new URLSearchParams({
            query: query,
            key: this.apiKey,
        });
        if (location) {
            searchParams.append('location', `${location.latitude},${location.longitude}`);
        }
        if (radius) {
            searchParams.append('radius', radius.toString());
        }
        const url = `${this.baseUrl}/textsearch/json?${searchParams.toString()}`;
        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                },
            });
            if (!response.ok) {
                throw new Errors_1.ApiError(`Places text search failed: ${response.statusText}`, response.status, 'Google Places API');
            }
            const data = await response.json();
            if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
                throw new Errors_1.ApiError(data.error_message || `Places text search error: ${data.status}`, response.status, 'Google Places API');
            }
            const places = data.results.map(this.transformPlaceResult);
            return {
                places,
                nextPageToken: data.next_page_token,
                totalResults: places.length,
                searchParams: {
                    query,
                    location,
                    radius,
                },
            };
        }
        catch (error) {
            if (error instanceof Errors_1.ApiError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new Errors_1.NetworkError(`Network error during text search: ${error.message}`, error);
            }
            throw new Errors_1.NetworkError(`Unknown error during text search: ${String(error)}`);
        }
    }
    /**
     * Gets detailed information about a place
     * @param placeId - The place ID
     * @param fields - Optional fields to retrieve
     * @returns Promise resolving to place details
     */
    async getPlaceDetails(placeId, fields) {
        (0, validation_1.validateRequired)(placeId, 'placeId');
        // Check cache first
        const cacheKey = `details_${placeId}`;
        const cached = this.cache.get(cacheKey);
        if (cached) {
            return cached;
        }
        const searchParams = new URLSearchParams({
            place_id: placeId,
            key: this.apiKey,
        });
        if (fields && fields.length > 0) {
            searchParams.append('fields', fields.join(','));
        }
        const url = `${this.baseUrl}/details/json?${searchParams.toString()}`;
        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                },
            });
            if (!response.ok) {
                throw new Errors_1.ApiError(`Place details request failed: ${response.statusText}`, response.status, 'Google Places API');
            }
            const data = await response.json();
            if (data.status !== 'OK') {
                throw new Errors_1.ApiError(data.error_message || `Place details error: ${data.status}`, response.status, 'Google Places API');
            }
            const place = this.transformPlaceResult(data.result);
            // Cache the result
            this.cache.set(cacheKey, place);
            return place;
        }
        catch (error) {
            if (error instanceof Errors_1.ApiError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new Errors_1.NetworkError(`Network error during place details: ${error.message}`, error);
            }
            throw new Errors_1.NetworkError(`Unknown error during place details: ${String(error)}`);
        }
    }
    /**
     * Gets the next page of search results
     * @param pageToken - The next page token from previous search
     * @returns Promise resolving to next page of results
     */
    async getNextPage(pageToken) {
        (0, validation_1.validateRequired)(pageToken, 'pageToken');
        const searchParams = new URLSearchParams({
            pagetoken: pageToken,
            key: this.apiKey,
        });
        const url = `${this.baseUrl}/nearbysearch/json?${searchParams.toString()}`;
        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                },
            });
            if (!response.ok) {
                throw new Errors_1.ApiError(`Next page request failed: ${response.statusText}`, response.status, 'Google Places API');
            }
            const data = await response.json();
            if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
                throw new Errors_1.ApiError(data.error_message || `Next page error: ${data.status}`, response.status, 'Google Places API');
            }
            const places = data.results.map(this.transformPlaceResult);
            return {
                places,
                nextPageToken: data.next_page_token,
                totalResults: places.length,
            };
        }
        catch (error) {
            if (error instanceof Errors_1.ApiError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new Errors_1.NetworkError(`Network error during next page: ${error.message}`, error);
            }
            throw new Errors_1.NetworkError(`Unknown error during next page: ${String(error)}`);
        }
    }
    /**
     * Transforms Google Places API result to our PlaceResult format
     * @param apiResult - Raw result from Google Places API
     * @returns Transformed PlaceResult
     */
    transformPlaceResult(apiResult) {
        return {
            place_id: apiResult.place_id,
            name: apiResult.name,
            formatted_address: apiResult.formatted_address || '',
            formatted_phone_number: apiResult.formatted_phone_number,
            website: apiResult.website,
            types: apiResult.types || [],
            rating: apiResult.rating,
            geometry: {
                location: {
                    lat: apiResult.geometry?.location?.lat || 0,
                    lng: apiResult.geometry?.location?.lng || 0,
                },
            },
        };
    }
    /**
     * Clears the places cache
     */
    clearCache() {
        this.cache.clear();
    }
    /**
     * Gets the current cache size
     * @returns Number of cached entries
     */
    getCacheSize() {
        return this.cache.size;
    }
}
exports.GooglePlacesService = GooglePlacesService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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