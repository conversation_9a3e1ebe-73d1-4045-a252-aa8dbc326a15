{"file": "/Users/<USER>/WebstormProjects/goo/src/services/googlePlaces.ts", "mappings": ";;;AACA,6CAA+F;AAC/F,oDAAsE;AACtE,4CAAyD;AAuCzD;;GAEG;AACH,MAAa,mBAAmB;IAK9B,YAAY,MAAe;QAJnB,UAAK,GAAG,IAAI,GAAG,EAAe,CAAC;QAKrC,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,EAAE,CAAC;QAChE,IAAI,CAAC,OAAO,GAAG,sBAAU,CAAC,sBAAsB,CAAC;QAEjD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,2BAAkB,CAAC,mCAAmC,EAAE,uBAAuB,CAAC,CAAC;QAC7F,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,YAAY,CAChB,WAAwB,EACxB,MAAc,EACd,IAAY,EACZ,SAAuB,EAAE;QAEzB,sBAAsB;QACtB,IAAA,6BAAgB,EAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC/B,IAAA,0BAAa,EAAC,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QAE1C,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAClC,IAAA,0BAAa,EAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;QACnD,CAAC;QACD,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAClC,IAAA,0BAAa,EAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,eAAe,CAAC;YACvC,QAAQ,EAAE,GAAG,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,SAAS,EAAE;YAC5D,MAAM,EAAE,MAAM,CAAC,QAAQ,EAAE;YACzB,IAAI,EAAE,IAAI;YACV,GAAG,EAAE,IAAI,CAAC,MAAM;SACjB,CAAC,CAAC;QAEH,0BAA0B;QAC1B,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAClC,YAAY,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC9D,CAAC;QACD,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAClC,YAAY,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC9D,CAAC;QACD,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YACjC,YAAY,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,YAAY,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,sBAAsB,YAAY,CAAC,QAAQ,EAAE,EAAE,CAAC;QAE3E,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;gBAChC,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE;oBACP,QAAQ,EAAE,kBAAkB;iBAC7B;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,iBAAQ,CAChB,8BAA8B,QAAQ,CAAC,UAAU,EAAE,EACnD,QAAQ,CAAC,MAAM,EACf,mBAAmB,CACpB,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,GAAsB,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEtD,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,cAAc,EAAE,CAAC;gBAC3D,MAAM,IAAI,iBAAQ,CAChB,IAAI,CAAC,aAAa,IAAI,qBAAqB,IAAI,CAAC,MAAM,EAAE,EACxD,QAAQ,CAAC,MAAM,EACf,mBAAmB,CACpB,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAE3D,OAAO;gBACL,MAAM;gBACN,aAAa,EAAE,IAAI,CAAC,eAAe;gBACnC,YAAY,EAAE,MAAM,CAAC,MAAM;gBAC3B,YAAY,EAAE;oBACZ,WAAW;oBACX,MAAM;oBACN,IAAI;oBACJ,GAAG,MAAM;iBACV;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,iBAAQ,EAAE,CAAC;gBAC9B,MAAM,KAAK,CAAC;YACd,CAAC;YAED,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,MAAM,IAAI,qBAAY,CAAC,uCAAuC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;YACxF,CAAC;YAED,MAAM,IAAI,qBAAY,CAAC,uCAAuC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACjF,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,YAAY,CAChB,KAAa,EACb,QAAsB,EACtB,MAAe;QAEf,IAAA,6BAAgB,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAEjC,MAAM,YAAY,GAAG,IAAI,eAAe,CAAC;YACvC,KAAK,EAAE,KAAK;YACZ,GAAG,EAAE,IAAI,CAAC,MAAM;SACjB,CAAC,CAAC;QAEH,IAAI,QAAQ,EAAE,CAAC;YACb,YAAY,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;QAChF,CAAC;QACD,IAAI,MAAM,EAAE,CAAC;YACX,YAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,oBAAoB,YAAY,CAAC,QAAQ,EAAE,EAAE,CAAC;QAEzE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;gBAChC,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE;oBACP,QAAQ,EAAE,kBAAkB;iBAC7B;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,iBAAQ,CAChB,8BAA8B,QAAQ,CAAC,UAAU,EAAE,EACnD,QAAQ,CAAC,MAAM,EACf,mBAAmB,CACpB,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,GAAsB,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEtD,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,cAAc,EAAE,CAAC;gBAC3D,MAAM,IAAI,iBAAQ,CAChB,IAAI,CAAC,aAAa,IAAI,6BAA6B,IAAI,CAAC,MAAM,EAAE,EAChE,QAAQ,CAAC,MAAM,EACf,mBAAmB,CACpB,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAE3D,OAAO;gBACL,MAAM;gBACN,aAAa,EAAE,IAAI,CAAC,eAAe;gBACnC,YAAY,EAAE,MAAM,CAAC,MAAM;gBAC3B,YAAY,EAAE;oBACZ,KAAK;oBACL,QAAQ;oBACR,MAAM;iBACP;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,iBAAQ,EAAE,CAAC;gBAC9B,MAAM,KAAK,CAAC;YACd,CAAC;YAED,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,MAAM,IAAI,qBAAY,CAAC,qCAAqC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;YACtF,CAAC;YAED,MAAM,IAAI,qBAAY,CAAC,qCAAqC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC/E,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,eAAe,CAAC,OAAe,EAAE,MAAiB;QACtD,IAAA,6BAAgB,EAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAErC,oBAAoB;QACpB,MAAM,QAAQ,GAAG,WAAW,OAAO,EAAE,CAAC;QACtC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,eAAe,CAAC;YACvC,QAAQ,EAAE,OAAO;YACjB,GAAG,EAAE,IAAI,CAAC,MAAM;SACjB,CAAC,CAAC;QAEH,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,YAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,iBAAiB,YAAY,CAAC,QAAQ,EAAE,EAAE,CAAC;QAEtE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;gBAChC,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE;oBACP,QAAQ,EAAE,kBAAkB;iBAC7B;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,iBAAQ,CAChB,iCAAiC,QAAQ,CAAC,UAAU,EAAE,EACtD,QAAQ,CAAC,MAAM,EACf,mBAAmB,CACpB,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,GAAyB,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEzD,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;gBACzB,MAAM,IAAI,iBAAQ,CAChB,IAAI,CAAC,aAAa,IAAI,wBAAwB,IAAI,CAAC,MAAM,EAAE,EAC3D,QAAQ,CAAC,MAAM,EACf,mBAAmB,CACpB,CAAC;YACJ,CAAC;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAErD,mBAAmB;YACnB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAEhC,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,iBAAQ,EAAE,CAAC;gBAC9B,MAAM,KAAK,CAAC;YACd,CAAC;YAED,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,MAAM,IAAI,qBAAY,CAAC,uCAAuC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;YACxF,CAAC;YAED,MAAM,IAAI,qBAAY,CAAC,uCAAuC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACjF,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,WAAW,CAAC,SAAiB;QACjC,IAAA,6BAAgB,EAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAEzC,MAAM,YAAY,GAAG,IAAI,eAAe,CAAC;YACvC,SAAS,EAAE,SAAS;YACpB,GAAG,EAAE,IAAI,CAAC,MAAM;SACjB,CAAC,CAAC;QAEH,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,sBAAsB,YAAY,CAAC,QAAQ,EAAE,EAAE,CAAC;QAE3E,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;gBAChC,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE;oBACP,QAAQ,EAAE,kBAAkB;iBAC7B;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,iBAAQ,CAChB,6BAA6B,QAAQ,CAAC,UAAU,EAAE,EAClD,QAAQ,CAAC,MAAM,EACf,mBAAmB,CACpB,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,GAAsB,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEtD,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,cAAc,EAAE,CAAC;gBAC3D,MAAM,IAAI,iBAAQ,CAChB,IAAI,CAAC,aAAa,IAAI,oBAAoB,IAAI,CAAC,MAAM,EAAE,EACvD,QAAQ,CAAC,MAAM,EACf,mBAAmB,CACpB,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAE3D,OAAO;gBACL,MAAM;gBACN,aAAa,EAAE,IAAI,CAAC,eAAe;gBACnC,YAAY,EAAE,MAAM,CAAC,MAAM;aAC5B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,iBAAQ,EAAE,CAAC;gBAC9B,MAAM,KAAK,CAAC;YACd,CAAC;YAED,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,MAAM,IAAI,qBAAY,CAAC,mCAAmC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;YACpF,CAAC;YAED,MAAM,IAAI,qBAAY,CAAC,mCAAmC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,oBAAoB,CAAC,SAAc;QACzC,OAAO;YACL,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,iBAAiB,EAAE,SAAS,CAAC,iBAAiB,IAAI,EAAE;YACpD,sBAAsB,EAAE,SAAS,CAAC,sBAAsB;YACxD,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,KAAK,EAAE,SAAS,CAAC,KAAK,IAAI,EAAE;YAC5B,MAAM,EAAE,SAAS,CAAC,MAAM;YACxB,QAAQ,EAAE;gBACR,QAAQ,EAAE;oBACR,GAAG,EAAE,SAAS,CAAC,QAAQ,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;oBAC3C,GAAG,EAAE,SAAS,CAAC,QAAQ,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;iBAC5C;aACF;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACrB,CAAC;IAED;;;OAGG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;CACF;AA3WD,kDA2WC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/src/services/googlePlaces.ts"], "sourcesContent": ["import { Coordinates, PlaceResult } from '../models/Business';\nimport { ApiError, NetworkError, ConfigurationError, ValidationError } from '../models/Errors';\nimport { validateRequired, validateRange } from '../utils/validation';\nimport { API_CONFIG, SEARCH_CONFIG } from '../constants';\n\n/**\n * Google Places API search parameters\n */\nexport interface SearchParams {\n  minprice?: number; // 0-4\n  maxprice?: number; // 0-4\n  opennow?: boolean;\n  type?: string;\n  keyword?: string;\n}\n\n/**\n * Search result from Google Places API\n */\nexport interface PlacesSearchResult {\n  places: PlaceResult[];\n  nextPageToken?: string;\n  totalResults: number;\n  searchParams?: any;\n}\n\n/**\n * Google Places API response interfaces\n */\ninterface PlacesApiResponse {\n  results: any[];\n  status: string;\n  error_message?: string;\n  next_page_token?: string;\n}\n\ninterface PlaceDetailsResponse {\n  result: any;\n  status: string;\n  error_message?: string;\n}\n\n/**\n * Service for interacting with Google Places API\n */\nexport class GooglePlacesService {\n  private cache = new Map<string, any>();\n  private readonly apiKey: string;\n  private readonly baseUrl: string;\n\n  constructor(apiKey?: string) {\n    this.apiKey = apiKey || process.env.GOOGLE_PLACES_API_KEY || '';\n    this.baseUrl = API_CONFIG.GOOGLE_PLACES_BASE_URL;\n\n    if (!this.apiKey) {\n      throw new ConfigurationError('Google Places API key is required', 'GOOGLE_PLACES_API_KEY');\n    }\n  }\n\n  /**\n   * Searches for places near a location\n   * @param coordinates - Center point for search\n   * @param radius - Search radius in meters\n   * @param type - Type of place to search for\n   * @param params - Additional search parameters\n   * @returns Promise resolving to search results\n   */\n  async searchNearby(\n    coordinates: Coordinates,\n    radius: number,\n    type: string,\n    params: SearchParams = {}\n  ): Promise<PlacesSearchResult> {\n    // Validate parameters\n    validateRequired(type, 'type');\n    validateRange(radius, 1, 50000, 'radius');\n\n    if (params.minprice !== undefined) {\n      validateRange(params.minprice, 0, 4, 'minprice');\n    }\n    if (params.maxprice !== undefined) {\n      validateRange(params.maxprice, 0, 4, 'maxprice');\n    }\n\n    const searchParams = new URLSearchParams({\n      location: `${coordinates.latitude},${coordinates.longitude}`,\n      radius: radius.toString(),\n      type: type,\n      key: this.apiKey,\n    });\n\n    // Add optional parameters\n    if (params.minprice !== undefined) {\n      searchParams.append('minprice', params.minprice.toString());\n    }\n    if (params.maxprice !== undefined) {\n      searchParams.append('maxprice', params.maxprice.toString());\n    }\n    if (params.opennow !== undefined) {\n      searchParams.append('opennow', params.opennow.toString());\n    }\n    if (params.keyword) {\n      searchParams.append('keyword', params.keyword);\n    }\n\n    const url = `${this.baseUrl}/nearbysearch/json?${searchParams.toString()}`;\n\n    try {\n      const response = await fetch(url, {\n        method: 'GET',\n        headers: {\n          'Accept': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new ApiError(\n          `Places API request failed: ${response.statusText}`,\n          response.status,\n          'Google Places API'\n        );\n      }\n\n      const data: PlacesApiResponse = await response.json();\n\n      if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {\n        throw new ApiError(\n          data.error_message || `Places API error: ${data.status}`,\n          response.status,\n          'Google Places API'\n        );\n      }\n\n      const places = data.results.map(this.transformPlaceResult);\n\n      return {\n        places,\n        nextPageToken: data.next_page_token,\n        totalResults: places.length,\n        searchParams: {\n          coordinates,\n          radius,\n          type,\n          ...params,\n        },\n      };\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n\n      if (error instanceof Error) {\n        throw new NetworkError(`Network error during places search: ${error.message}`, error);\n      }\n\n      throw new NetworkError(`Unknown error during places search: ${String(error)}`);\n    }\n  }\n\n  /**\n   * Searches for places using text query\n   * @param query - Text search query\n   * @param location - Optional location bias\n   * @param radius - Optional search radius in meters\n   * @returns Promise resolving to search results\n   */\n  async searchByText(\n    query: string,\n    location?: Coordinates,\n    radius?: number\n  ): Promise<PlacesSearchResult> {\n    validateRequired(query, 'query');\n\n    const searchParams = new URLSearchParams({\n      query: query,\n      key: this.apiKey,\n    });\n\n    if (location) {\n      searchParams.append('location', `${location.latitude},${location.longitude}`);\n    }\n    if (radius) {\n      searchParams.append('radius', radius.toString());\n    }\n\n    const url = `${this.baseUrl}/textsearch/json?${searchParams.toString()}`;\n\n    try {\n      const response = await fetch(url, {\n        method: 'GET',\n        headers: {\n          'Accept': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new ApiError(\n          `Places text search failed: ${response.statusText}`,\n          response.status,\n          'Google Places API'\n        );\n      }\n\n      const data: PlacesApiResponse = await response.json();\n\n      if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {\n        throw new ApiError(\n          data.error_message || `Places text search error: ${data.status}`,\n          response.status,\n          'Google Places API'\n        );\n      }\n\n      const places = data.results.map(this.transformPlaceResult);\n\n      return {\n        places,\n        nextPageToken: data.next_page_token,\n        totalResults: places.length,\n        searchParams: {\n          query,\n          location,\n          radius,\n        },\n      };\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n\n      if (error instanceof Error) {\n        throw new NetworkError(`Network error during text search: ${error.message}`, error);\n      }\n\n      throw new NetworkError(`Unknown error during text search: ${String(error)}`);\n    }\n  }\n\n  /**\n   * Gets detailed information about a place\n   * @param placeId - The place ID\n   * @param fields - Optional fields to retrieve\n   * @returns Promise resolving to place details\n   */\n  async getPlaceDetails(placeId: string, fields?: string[]): Promise<PlaceResult> {\n    validateRequired(placeId, 'placeId');\n\n    // Check cache first\n    const cacheKey = `details_${placeId}`;\n    const cached = this.cache.get(cacheKey);\n    if (cached) {\n      return cached;\n    }\n\n    const searchParams = new URLSearchParams({\n      place_id: placeId,\n      key: this.apiKey,\n    });\n\n    if (fields && fields.length > 0) {\n      searchParams.append('fields', fields.join(','));\n    }\n\n    const url = `${this.baseUrl}/details/json?${searchParams.toString()}`;\n\n    try {\n      const response = await fetch(url, {\n        method: 'GET',\n        headers: {\n          'Accept': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new ApiError(\n          `Place details request failed: ${response.statusText}`,\n          response.status,\n          'Google Places API'\n        );\n      }\n\n      const data: PlaceDetailsResponse = await response.json();\n\n      if (data.status !== 'OK') {\n        throw new ApiError(\n          data.error_message || `Place details error: ${data.status}`,\n          response.status,\n          'Google Places API'\n        );\n      }\n\n      const place = this.transformPlaceResult(data.result);\n\n      // Cache the result\n      this.cache.set(cacheKey, place);\n\n      return place;\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n\n      if (error instanceof Error) {\n        throw new NetworkError(`Network error during place details: ${error.message}`, error);\n      }\n\n      throw new NetworkError(`Unknown error during place details: ${String(error)}`);\n    }\n  }\n\n  /**\n   * Gets the next page of search results\n   * @param pageToken - The next page token from previous search\n   * @returns Promise resolving to next page of results\n   */\n  async getNextPage(pageToken: string): Promise<PlacesSearchResult> {\n    validateRequired(pageToken, 'pageToken');\n\n    const searchParams = new URLSearchParams({\n      pagetoken: pageToken,\n      key: this.apiKey,\n    });\n\n    const url = `${this.baseUrl}/nearbysearch/json?${searchParams.toString()}`;\n\n    try {\n      const response = await fetch(url, {\n        method: 'GET',\n        headers: {\n          'Accept': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new ApiError(\n          `Next page request failed: ${response.statusText}`,\n          response.status,\n          'Google Places API'\n        );\n      }\n\n      const data: PlacesApiResponse = await response.json();\n\n      if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {\n        throw new ApiError(\n          data.error_message || `Next page error: ${data.status}`,\n          response.status,\n          'Google Places API'\n        );\n      }\n\n      const places = data.results.map(this.transformPlaceResult);\n\n      return {\n        places,\n        nextPageToken: data.next_page_token,\n        totalResults: places.length,\n      };\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n\n      if (error instanceof Error) {\n        throw new NetworkError(`Network error during next page: ${error.message}`, error);\n      }\n\n      throw new NetworkError(`Unknown error during next page: ${String(error)}`);\n    }\n  }\n\n  /**\n   * Transforms Google Places API result to our PlaceResult format\n   * @param apiResult - Raw result from Google Places API\n   * @returns Transformed PlaceResult\n   */\n  private transformPlaceResult(apiResult: any): PlaceResult {\n    return {\n      place_id: apiResult.place_id,\n      name: apiResult.name,\n      formatted_address: apiResult.formatted_address || '',\n      formatted_phone_number: apiResult.formatted_phone_number,\n      website: apiResult.website,\n      types: apiResult.types || [],\n      rating: apiResult.rating,\n      geometry: {\n        location: {\n          lat: apiResult.geometry?.location?.lat || 0,\n          lng: apiResult.geometry?.location?.lng || 0,\n        },\n      },\n    };\n  }\n\n  /**\n   * Clears the places cache\n   */\n  clearCache(): void {\n    this.cache.clear();\n  }\n\n  /**\n   * Gets the current cache size\n   * @returns Number of cached entries\n   */\n  getCacheSize(): number {\n    return this.cache.size;\n  }\n}\n"], "version": 3}