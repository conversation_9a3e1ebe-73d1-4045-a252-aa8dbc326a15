{"file": "/Users/<USER>/WebstormProjects/goo/tests/utils/rateLimiter.test.ts", "mappings": ";;AAAA,6DAAmH;AACnH,oDAAyD;AAEzD,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACtC,UAAU,CAAC,GAAG,EAAE;QACd,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,WAAW,GAAG,IAAI,oCAAsB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACvD,MAAM,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,oCAAsB,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,oCAAsB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YAC1D,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,oCAAsB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YAC1D,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,oCAAsB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,WAAW,GAAG,IAAI,oCAAsB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAEvD,iDAAiD;YACjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5B,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,WAAW,GAAG,IAAI,oCAAsB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,qBAAqB;YACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,CAAC;YAED,kCAAkC;YAClC,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,WAAW,GAAG,IAAI,oCAAsB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,qBAAqB;YACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,WAAW,CAAC,UAAU,EAAE,CAAC;YAC3B,CAAC;YAED,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE7C,iEAAiE;YACjE,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YAE9B,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,WAAW,GAAG,IAAI,oCAAsB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,6BAA6B;YAC7B,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAEhC,8CAA8C;YAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,CAAC;YAED,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,MAAM,WAAW,GAAG,IAAI,oCAAsB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,MAAM,OAAO,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;YAC3C,IAAI,CAAC,YAAY,EAAE,CAAC;YAEpB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,WAAW,GAAG,IAAI,oCAAsB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,yBAAyB;YACzB,WAAW,CAAC,UAAU,EAAE,CAAC;YAEzB,MAAM,OAAO,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;YAE3C,6DAA6D;YAC7D,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YAC9B,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,WAAW,GAAG,IAAI,oCAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,mBAAmB;YAEzE,yBAAyB;YACzB,WAAW,CAAC,UAAU,EAAE,CAAC;YAEzB,MAAM,OAAO,GAAG,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,gBAAgB;YAE/D,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YAE9B,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,uBAAc,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,WAAW,GAAG,IAAI,oCAAsB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,MAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEjD,WAAW,CAAC,UAAU,EAAE,CAAC;YACzB,MAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEjD,WAAW,CAAC,UAAU,EAAE,CAAC;YACzB,WAAW,CAAC,UAAU,EAAE,CAAC;YACzB,MAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,WAAW,GAAG,IAAI,oCAAsB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,qBAAqB;YACrB,WAAW,CAAC,UAAU,EAAE,CAAC;YACzB,WAAW,CAAC,UAAU,EAAE,CAAC;YACzB,MAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEjD,iCAAiC;YACjC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YAC9B,MAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE;QACrB,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,WAAW,GAAG,IAAI,oCAAsB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,sBAAsB;YACtB,WAAW,CAAC,UAAU,EAAE,CAAC;YACzB,WAAW,CAAC,UAAU,EAAE,CAAC;YACzB,MAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEjD,WAAW,CAAC,KAAK,EAAE,CAAC;YACpB,MAAM,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,2DAA2D;YAC3D,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,oCAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,oDAAoD,CAAC,CAAC;QAC/G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,2DAA2D;YAC3D,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,oCAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,oDAAoD,CAAC,CAAC;QAC/G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,2DAA2D;YAC3D,wEAAwE;YACxE,MAAM,OAAO,GAAG,IAAI,oCAAsB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACrD,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,gCAAgC;YAC1E,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,OAAO,GAAG,IAAI,oCAAsB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE7D,kDAAkD;YAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC9B,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,OAAO,GAAG,IAAI,oCAAsB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,wBAAwB;YAE5E,qBAAqB;YACrB,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEzC,qCAAqC;YACrC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,OAAO,GAAG,IAAI,oCAAsB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAElD,qBAAqB;YACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,CAAC;YAED,qCAAqC;YACrC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;YAElD,mDAAmD;YACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,CAAC;YACD,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,OAAO,GAAG,IAAI,oCAAsB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAElD,oBAAoB;YACpB,MAAM,OAAO,GAAG,EAAE,CAAC;YACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5B,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;YACrC,CAAC;YAED,2CAA2C;YAC3C,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;YACpE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,OAAO,GAAG,IAAI,oCAAsB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,yBAAyB;YAE9E,yBAAyB;YACzB,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEzC,sCAAsC;YACtC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,UAAU,CAAC,GAAG,EAAE;QACd,IAAA,oCAAsB,GAAE,CAAC;IAC3B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;QAC7D,MAAM,QAAQ,GAAG,IAAA,kCAAoB,EAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAC9C,MAAM,QAAQ,GAAG,IAAA,kCAAoB,EAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAE9C,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;QAC7D,MAAM,QAAQ,GAAG,IAAA,kCAAoB,EAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAC9C,IAAA,oCAAsB,GAAE,CAAC,CAAC,0CAA0C;QACpE,MAAM,QAAQ,GAAG,IAAA,kCAAoB,EAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAE7C,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;QAC5C,MAAM,QAAQ,GAAG,IAAA,kCAAoB,EAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAC9C,IAAA,oCAAsB,GAAE,CAAC;QACzB,MAAM,QAAQ,GAAG,IAAA,kCAAoB,EAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,uBAAuB;QACtE,IAAA,oCAAsB,GAAE,CAAC;QACzB,MAAM,QAAQ,GAAG,IAAA,kCAAoB,EAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,iBAAiB;QAEhE,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;QACxD,MAAM,OAAO,GAAG,IAAA,kCAAoB,EAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5C,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,oCAAsB,CAAC,CAAC;IACzD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;IACrC,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;QACnD,MAAM,OAAO,GAAG,IAAI,oCAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjD,MAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;QACtE,MAAM,OAAO,GAAG,IAAI,oCAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,oCAAoC;QAEtF,4BAA4B;QAC5B,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAExC,0CAA0C;QAC1C,MAAM,aAAa,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;QACtD,MAAM,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACzC,MAAM,CAAC,aAAa,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,4BAA4B;IAC/E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;QACtD,MAAM,OAAO,GAAG,IAAI,oCAAsB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,mBAAmB;QAEzE,sCAAsC;QACtC,MAAM,aAAa,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;QACtD,MAAM,CAAC,aAAa,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;QAChE,MAAM,OAAO,GAAG,IAAI,oCAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,qCAAqC;QAEvF,oBAAoB;QACpB,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAExC,wEAAwE;QACxE,MAAM,aAAa,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;QACtD,MAAM,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACzC,MAAM,CAAC,aAAa,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/WebstormProjects/goo/tests/utils/rateLimiter.test.ts"], "sourcesContent": ["import { TokenBucketRateLimiter, getGlobalRateLimiter, resetGlobalRateLimiter } from '../../src/utils/rateLimiter';\nimport { RateLimitError } from '../../src/models/Errors';\n\ndescribe('TokenBucketRateLimiter', () => {\n  beforeEach(() => {\n    jest.useFakeTimers();\n  });\n\n  afterEach(() => {\n    jest.useRealTimers();\n  });\n\n  describe('constructor', () => {\n    it('should create rate limiter with correct configuration', () => {\n      const rateLimiter = new TokenBucketRateLimiter(10, 20);\n      expect(rateLimiter).toBeInstanceOf(TokenBucketRateLimiter);\n    });\n\n    it('should throw error for invalid configuration', () => {\n      expect(() => new TokenBucketRateLimiter(0, 10)).toThrow();\n      expect(() => new TokenBucketRateLimiter(10, 0)).toThrow();\n      expect(() => new TokenBucketRateLimiter(-1, 10)).toThrow();\n    });\n  });\n\n  describe('tryConsume', () => {\n    it('should allow requests within rate limit', () => {\n      const rateLimiter = new TokenBucketRateLimiter(10, 20);\n      \n      // Should allow initial requests up to burst size\n      for (let i = 0; i < 20; i++) {\n        expect(rateLimiter.tryConsume()).toBe(true);\n      }\n    });\n\n    it('should reject requests when bucket is empty', () => {\n      const rateLimiter = new TokenBucketRateLimiter(10, 5);\n      \n      // Consume all tokens\n      for (let i = 0; i < 5; i++) {\n        expect(rateLimiter.tryConsume()).toBe(true);\n      }\n      \n      // Next request should be rejected\n      expect(rateLimiter.tryConsume()).toBe(false);\n    });\n\n    it('should refill tokens over time', () => {\n      const rateLimiter = new TokenBucketRateLimiter(10, 5);\n      \n      // Consume all tokens\n      for (let i = 0; i < 5; i++) {\n        rateLimiter.tryConsume();\n      }\n      \n      expect(rateLimiter.tryConsume()).toBe(false);\n      \n      // Advance time by 100ms (should add 1 token at 10 tokens/second)\n      jest.advanceTimersByTime(100);\n      \n      expect(rateLimiter.tryConsume()).toBe(true);\n      expect(rateLimiter.tryConsume()).toBe(false);\n    });\n\n    it('should not exceed burst size when refilling', () => {\n      const rateLimiter = new TokenBucketRateLimiter(10, 5);\n      \n      // Advance time significantly\n      jest.advanceTimersByTime(10000);\n      \n      // Should still only allow burst size requests\n      for (let i = 0; i < 5; i++) {\n        expect(rateLimiter.tryConsume()).toBe(true);\n      }\n      \n      expect(rateLimiter.tryConsume()).toBe(false);\n    });\n  });\n\n  describe('waitForToken', () => {\n    it('should resolve immediately when tokens are available', async () => {\n      const rateLimiter = new TokenBucketRateLimiter(10, 5);\n      \n      const promise = rateLimiter.waitForToken();\n      jest.runAllTimers();\n      \n      await expect(promise).resolves.toBeUndefined();\n    });\n\n    it('should wait for token refill when bucket is empty', async () => {\n      const rateLimiter = new TokenBucketRateLimiter(10, 1);\n\n      // Consume the only token\n      rateLimiter.tryConsume();\n\n      const promise = rateLimiter.waitForToken();\n\n      // Should resolve after 100ms (time for 1 token at 10/second)\n      jest.advanceTimersByTime(100);\n      await expect(promise).resolves.toBeUndefined();\n    });\n\n    it('should timeout if waiting too long', async () => {\n      const rateLimiter = new TokenBucketRateLimiter(1, 1); // Very slow refill\n      \n      // Consume the only token\n      rateLimiter.tryConsume();\n      \n      const promise = rateLimiter.waitForToken(500); // 500ms timeout\n      \n      jest.advanceTimersByTime(600);\n      \n      await expect(promise).rejects.toThrow(RateLimitError);\n    });\n  });\n\n  describe('getAvailableTokens', () => {\n    it('should return correct number of available tokens', () => {\n      const rateLimiter = new TokenBucketRateLimiter(10, 5);\n      \n      expect(rateLimiter.getAvailableTokens()).toBe(5);\n      \n      rateLimiter.tryConsume();\n      expect(rateLimiter.getAvailableTokens()).toBe(4);\n      \n      rateLimiter.tryConsume();\n      rateLimiter.tryConsume();\n      expect(rateLimiter.getAvailableTokens()).toBe(2);\n    });\n\n    it('should update available tokens after refill', () => {\n      const rateLimiter = new TokenBucketRateLimiter(10, 2);\n      \n      // Consume all tokens\n      rateLimiter.tryConsume();\n      rateLimiter.tryConsume();\n      expect(rateLimiter.getAvailableTokens()).toBe(0);\n      \n      // Advance time to refill 1 token\n      jest.advanceTimersByTime(100);\n      expect(rateLimiter.getAvailableTokens()).toBe(1);\n    });\n  });\n\n  describe('reset', () => {\n    it('should reset bucket to full capacity', () => {\n      const rateLimiter = new TokenBucketRateLimiter(10, 5);\n      \n      // Consume some tokens\n      rateLimiter.tryConsume();\n      rateLimiter.tryConsume();\n      expect(rateLimiter.getAvailableTokens()).toBe(3);\n      \n      rateLimiter.reset();\n      expect(rateLimiter.getAvailableTokens()).toBe(5);\n    });\n  });\n\n  describe('edge cases and error handling', () => {\n    it('should handle zero capacity', () => {\n      // Constructor parameters are (tokensPerSecond, bucketSize)\n      expect(() => new TokenBucketRateLimiter(1, 0)).toThrow('Tokens per second and bucket size must be positive');\n    });\n\n    it('should handle zero refill rate', () => {\n      // Constructor parameters are (tokensPerSecond, bucketSize)\n      expect(() => new TokenBucketRateLimiter(0, 1)).toThrow('Tokens per second and bucket size must be positive');\n    });\n\n    it('should handle very small values', () => {\n      // Constructor parameters are (tokensPerSecond, bucketSize)\n      // With bucket size 0.1, we get 0 tokens initially (Math.floor(0.1) = 0)\n      const limiter = new TokenBucketRateLimiter(0.1, 0.1);\n      expect(limiter.tryConsume()).toBe(false); // No tokens available initially\n      expect(limiter.tryConsume()).toBe(false);\n    });\n\n    it('should handle very large capacity', () => {\n      const limiter = new TokenBucketRateLimiter(1000000, 1000000);\n\n      // Should be able to consume many tokens initially\n      for (let i = 0; i < 1000; i++) {\n        expect(limiter.tryConsume()).toBe(true);\n      }\n    });\n\n    it('should handle fractional refill rates', () => {\n      const limiter = new TokenBucketRateLimiter(0.5, 2); // 0.5 tokens per second\n\n      // Consume all tokens\n      expect(limiter.tryConsume()).toBe(true);\n      expect(limiter.tryConsume()).toBe(true);\n      expect(limiter.tryConsume()).toBe(false);\n\n      // Should get 1 token after 2 seconds\n      jest.advanceTimersByTime(2000);\n      expect(limiter.tryConsume()).toBe(true);\n      expect(limiter.tryConsume()).toBe(false);\n    });\n\n    it('should handle very long time intervals', () => {\n      const limiter = new TokenBucketRateLimiter(10, 5);\n\n      // Consume all tokens\n      for (let i = 0; i < 5; i++) {\n        expect(limiter.tryConsume()).toBe(true);\n      }\n\n      // Advance time by a very long period\n      jest.advanceTimersByTime(1000000); // 1000 seconds\n\n      // Should be fully refilled but not exceed capacity\n      for (let i = 0; i < 5; i++) {\n        expect(limiter.tryConsume()).toBe(true);\n      }\n      expect(limiter.tryConsume()).toBe(false);\n    });\n\n    it('should handle rapid successive calls', () => {\n      const limiter = new TokenBucketRateLimiter(10, 5);\n\n      // Rapid consumption\n      const results = [];\n      for (let i = 0; i < 10; i++) {\n        results.push(limiter.tryConsume());\n      }\n\n      // First 5 should succeed, rest should fail\n      expect(results.slice(0, 5)).toEqual([true, true, true, true, true]);\n      expect(results.slice(5)).toEqual([false, false, false, false, false]);\n    });\n\n    it('should maintain precision with small time intervals', () => {\n      const limiter = new TokenBucketRateLimiter(1000, 1); // 1000 tokens per second\n\n      // Consume the only token\n      expect(limiter.tryConsume()).toBe(true);\n      expect(limiter.tryConsume()).toBe(false);\n\n      // Advance by 1ms (should add 1 token)\n      jest.advanceTimersByTime(1);\n      expect(limiter.tryConsume()).toBe(true);\n      expect(limiter.tryConsume()).toBe(false);\n    });\n  });\n});\n\ndescribe('getGlobalRateLimiter', () => {\n  beforeEach(() => {\n    resetGlobalRateLimiter();\n  });\n\n  it('should return the same instance for same parameters', () => {\n    const limiter1 = getGlobalRateLimiter(10, 20);\n    const limiter2 = getGlobalRateLimiter(10, 20);\n\n    expect(limiter1).toBe(limiter2);\n  });\n\n  it('should create new instance for different parameters', () => {\n    const limiter1 = getGlobalRateLimiter(10, 20);\n    resetGlobalRateLimiter(); // Reset between different parameter calls\n    const limiter2 = getGlobalRateLimiter(5, 10);\n\n    expect(limiter1).not.toBe(limiter2);\n  });\n\n  it('should handle parameter variations', () => {\n    const limiter1 = getGlobalRateLimiter(10, 20);\n    resetGlobalRateLimiter();\n    const limiter2 = getGlobalRateLimiter(10, 21); // Different burst size\n    resetGlobalRateLimiter();\n    const limiter3 = getGlobalRateLimiter(11, 20); // Different rate\n\n    expect(limiter1).not.toBe(limiter2);\n    expect(limiter1).not.toBe(limiter3);\n    expect(limiter2).not.toBe(limiter3);\n  });\n\n  it('should return TokenBucketRateLimiter instances', () => {\n    const limiter = getGlobalRateLimiter(5, 10);\n    expect(limiter).toBeInstanceOf(TokenBucketRateLimiter);\n  });\n});\n\ndescribe('getTimeUntilNextToken', () => {\n  it('should return 0 when tokens are available', () => {\n    const limiter = new TokenBucketRateLimiter(1, 5);\n    expect(limiter.getTimeUntilNextToken()).toBe(0);\n  });\n\n  it('should return time until next token when no tokens available', () => {\n    const limiter = new TokenBucketRateLimiter(1, 1); // 1 token per second, bucket size 1\n\n    // Consume the initial token\n    expect(limiter.tryConsume()).toBe(true);\n\n    // Now should return time until next token\n    const timeUntilNext = limiter.getTimeUntilNextToken();\n    expect(timeUntilNext).toBeGreaterThan(0);\n    expect(timeUntilNext).toBeLessThanOrEqual(1000); // Should be within 1 second\n  });\n\n  it('should handle edge case when bucket is empty', () => {\n    const limiter = new TokenBucketRateLimiter(0.1, 0.1); // Very slow refill\n\n    // Should return time until next token\n    const timeUntilNext = limiter.getTimeUntilNextToken();\n    expect(timeUntilNext).toBeGreaterThanOrEqual(0);\n  });\n\n  it('should calculate correct time based on refill interval', () => {\n    const limiter = new TokenBucketRateLimiter(2, 1); // 2 tokens per second, bucket size 1\n\n    // Consume the token\n    expect(limiter.tryConsume()).toBe(true);\n\n    // Time until next should be around 500ms (1000ms / 2 tokens per second)\n    const timeUntilNext = limiter.getTimeUntilNextToken();\n    expect(timeUntilNext).toBeGreaterThan(0);\n    expect(timeUntilNext).toBeLessThanOrEqual(500);\n  });\n});\n"], "version": 3}