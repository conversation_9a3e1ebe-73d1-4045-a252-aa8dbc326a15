module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: [
    '**/__tests__/**/*.+(ts|tsx|js)',
    '**/*.(test|spec).+(ts|tsx|js)'
  ],
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest'
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/index.ts'
  ],
  coverageThreshold: {
    global: {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95
    }
  },
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  // Performance optimizations
  maxWorkers: '50%', // Use 50% of CPU cores for parallel execution
  testTimeout: 5000, // Reduce timeout from default 5000ms to 5000ms (keep same for now)
  bail: false, // Don't bail on first failure - run all tests
  verbose: false, // Reduce verbosity for faster output
  silent: false, // Keep some output for debugging
  // Cache optimizations
  cache: true,
  cacheDirectory: '<rootDir>/.jest-cache',
  // Coverage optimizations (only collect when needed)
  collectCoverage: false, // Only collect coverage when explicitly requested
  coverageReporters: ['text', 'lcov'],
  // Module resolution optimizations
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  // Transform optimizations
  transformIgnorePatterns: [
    'node_modules/(?!(.*\\.mjs$))'
  ],
  // Test discovery optimizations
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/coverage/'
  ],
  // Environment optimizations
  testEnvironmentOptions: {
    url: 'http://localhost'
  }
};
