
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">45.66% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>321/703</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">33.69% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>123/365</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">48.96% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>71/145</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">46.13% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>304/659</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="src"><a href="src/index.html">src</a></td>
	<td data-value="70.89" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 70%"></div><div class="cover-empty" style="width: 30%"></div></div>
	</td>
	<td data-value="70.89" class="pct medium">70.89%</td>
	<td data-value="134" class="abs medium">95/134</td>
	<td data-value="64" class="pct medium">64%</td>
	<td data-value="50" class="abs medium">32/50</td>
	<td data-value="60" class="pct medium">60%</td>
	<td data-value="30" class="abs medium">18/30</td>
	<td data-value="76.61" class="pct medium">76.61%</td>
	<td data-value="124" class="abs medium">95/124</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/components"><a href="src/components/index.html">src/components</a></td>
	<td data-value="73.68" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 73%"></div><div class="cover-empty" style="width: 27%"></div></div>
	</td>
	<td data-value="73.68" class="pct medium">73.68%</td>
	<td data-value="76" class="abs medium">56/76</td>
	<td data-value="79.41" class="pct medium">79.41%</td>
	<td data-value="34" class="abs medium">27/34</td>
	<td data-value="51.21" class="pct medium">51.21%</td>
	<td data-value="41" class="abs medium">21/41</td>
	<td data-value="83.6" class="pct high">83.6%</td>
	<td data-value="61" class="abs high">51/61</td>
	</tr>

<tr>
	<td class="file high" data-value="src/mocks"><a href="src/mocks/index.html">src/mocks</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="34" class="abs high">34/34</td>
	<td data-value="80" class="pct high">80%</td>
	<td data-value="15" class="abs high">12/15</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="31" class="abs high">31/31</td>
	</tr>

<tr>
	<td class="file low" data-value="src/models"><a href="src/models/index.html">src/models</a></td>
	<td data-value="46.42" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 46%"></div><div class="cover-empty" style="width: 54%"></div></div>
	</td>
	<td data-value="46.42" class="pct low">46.42%</td>
	<td data-value="28" class="abs low">13/28</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="4" class="abs low">1/4</td>
	<td data-value="44.44" class="pct low">44.44%</td>
	<td data-value="9" class="abs low">4/9</td>
	<td data-value="46.42" class="pct low">46.42%</td>
	<td data-value="28" class="abs low">13/28</td>
	</tr>

<tr>
	<td class="file low" data-value="src/services"><a href="src/services/index.html">src/services</a></td>
	<td data-value="28.53" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 28%"></div><div class="cover-empty" style="width: 72%"></div></div>
	</td>
	<td data-value="28.53" class="pct low">28.53%</td>
	<td data-value="431" class="abs low">123/431</td>
	<td data-value="19.46" class="pct low">19.46%</td>
	<td data-value="262" class="abs low">51/262</td>
	<td data-value="35.08" class="pct low">35.08%</td>
	<td data-value="57" class="abs low">20/57</td>
	<td data-value="27.46" class="pct low">27.46%</td>
	<td data-value="415" class="abs low">114/415</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-16T22:01:32.216Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    