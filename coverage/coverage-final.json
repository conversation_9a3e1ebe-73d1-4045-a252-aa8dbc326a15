{"/Users/<USER>/WebstormProjects/goo/src/App.tsx": {"path": "/Users/<USER>/WebstormProjects/goo/src/App.tsx", "statementMap": {"0": {"start": {"line": 15, "column": 26}, "end": {"line": 17, "column": 1}}, "1": {"start": {"line": 24, "column": 44}, "end": {"line": 24, "column": 79}}, "2": {"start": {"line": 25, "column": 32}, "end": {"line": 25, "column": 47}}, "3": {"start": {"line": 26, "column": 28}, "end": {"line": 26, "column": 60}}, "4": {"start": {"line": 27, "column": 34}, "end": {"line": 27, "column": 45}}, "5": {"start": {"line": 28, "column": 42}, "end": {"line": 28, "column": 70}}, "6": {"start": {"line": 29, "column": 42}, "end": {"line": 29, "column": 88}}, "7": {"start": {"line": 30, "column": 54}, "end": {"line": 30, "column": 66}}, "8": {"start": {"line": 31, "column": 40}, "end": {"line": 31, "column": 52}}, "9": {"start": {"line": 34, "column": 24}, "end": {"line": 34, "column": 47}}, "10": {"start": {"line": 37, "column": 2}, "end": {"line": 47, "column": 3}}, "11": {"start": {"line": 38, "column": 34}, "end": {"line": 38, "column": 73}}, "12": {"start": {"line": 39, "column": 4}, "end": {"line": 42, "column": 13}}, "13": {"start": {"line": 43, "column": 4}, "end": {"line": 46, "column": 13}}, "14": {"start": {"line": 49, "column": 23}, "end": {"line": 112, "column": 3}}, "15": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 21}}, "16": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 19}}, "17": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 27}}, "18": {"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 19}}, "19": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 28}}, "20": {"start": {"line": 56, "column": 4}, "end": {"line": 111, "column": 5}}, "21": {"start": {"line": 57, "column": 6}, "end": {"line": 102, "column": 7}}, "22": {"start": {"line": 58, "column": 26}, "end": {"line": 58, "column": 71}}, "23": {"start": {"line": 59, "column": 42}, "end": {"line": 59, "column": 44}}, "24": {"start": {"line": 61, "column": 8}, "end": {"line": 95, "column": 9}}, "25": {"start": {"line": 62, "column": 44}, "end": {"line": 76, "column": 13}}, "26": {"start": {"line": 62, "column": 75}, "end": {"line": 76, "column": 11}}, "27": {"start": {"line": 78, "column": 10}, "end": {"line": 78, "column": 47}}, "28": {"start": {"line": 80, "column": 31}, "end": {"line": 80, "column": 67}}, "29": {"start": {"line": 80, "column": 57}, "end": {"line": 80, "column": 66}}, "30": {"start": {"line": 81, "column": 34}, "end": {"line": 81, "column": 71}}, "31": {"start": {"line": 81, "column": 60}, "end": {"line": 81, "column": 70}}, "32": {"start": {"line": 83, "column": 10}, "end": {"line": 93, "column": 13}}, "33": {"start": {"line": 94, "column": 10}, "end": {"line": 94, "column": 38}}, "34": {"start": {"line": 97, "column": 24}, "end": {"line": 97, "column": 72}}, "35": {"start": {"line": 98, "column": 8}, "end": {"line": 98, "column": 34}}, "36": {"start": {"line": 99, "column": 8}, "end": {"line": 101, "column": 9}}, "37": {"start": {"line": 100, "column": 10}, "end": {"line": 100, "column": 43}}, "38": {"start": {"line": 104, "column": 6}, "end": {"line": 108, "column": 7}}, "39": {"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 65}}, "40": {"start": {"line": 107, "column": 8}, "end": {"line": 107, "column": 60}}, "41": {"start": {"line": 110, "column": 6}, "end": {"line": 110, "column": 24}}, "42": {"start": {"line": 115, "column": 22}, "end": {"line": 115, "column": 54}}, "43": {"start": {"line": 115, "column": 28}, "end": {"line": 115, "column": 54}}, "44": {"start": {"line": 116, "column": 25}, "end": {"line": 116, "column": 64}}, "45": {"start": {"line": 116, "column": 31}, "end": {"line": 116, "column": 64}}, "46": {"start": {"line": 117, "column": 25}, "end": {"line": 117, "column": 97}}, "47": {"start": {"line": 117, "column": 31}, "end": {"line": 117, "column": 97}}, "48": {"start": {"line": 118, "column": 24}, "end": {"line": 118, "column": 147}}, "49": {"start": {"line": 118, "column": 30}, "end": {"line": 118, "column": 147}}, "50": {"start": {"line": 119, "column": 27}, "end": {"line": 119, "column": 84}}, "51": {"start": {"line": 119, "column": 33}, "end": {"line": 119, "column": 84}}, "52": {"start": {"line": 122, "column": 29}, "end": {"line": 122, "column": 73}}, "53": {"start": {"line": 122, "column": 48}, "end": {"line": 122, "column": 73}}, "54": {"start": {"line": 123, "column": 24}, "end": {"line": 133, "column": 3}}, "55": {"start": {"line": 124, "column": 6}, "end": {"line": 132, "column": 7}}, "56": {"start": {"line": 125, "column": 10}, "end": {"line": 125, "column": 40}}, "57": {"start": {"line": 126, "column": 13}, "end": {"line": 132, "column": 7}}, "58": {"start": {"line": 127, "column": 10}, "end": {"line": 127, "column": 72}}, "59": {"start": {"line": 128, "column": 13}, "end": {"line": 132, "column": 7}}, "60": {"start": {"line": 129, "column": 10}, "end": {"line": 129, "column": 73}}, "61": {"start": {"line": 131, "column": 10}, "end": {"line": 131, "column": 25}}, "62": {"start": {"line": 134, "column": 23}, "end": {"line": 138, "column": 3}}, "63": {"start": {"line": 135, "column": 4}, "end": {"line": 135, "column": 27}}, "64": {"start": {"line": 136, "column": 4}, "end": {"line": 136, "column": 19}}, "65": {"start": {"line": 137, "column": 4}, "end": {"line": 137, "column": 41}}, "66": {"start": {"line": 139, "column": 23}, "end": {"line": 139, "column": 64}}, "67": {"start": {"line": 139, "column": 29}, "end": {"line": 139, "column": 64}}, "68": {"start": {"line": 141, "column": 2}, "end": {"line": 168, "column": 4}}}, "fnMap": {"0": {"name": "App", "decl": {"start": {"line": 23, "column": 9}, "end": {"line": 23, "column": 12}}, "loc": {"start": {"line": 23, "column": 46}, "end": {"line": 169, "column": 1}}, "line": 23}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 49, "column": 23}, "end": {"line": 49, "column": 24}}, "loc": {"start": {"line": 49, "column": 46}, "end": {"line": 112, "column": 3}}, "line": 49}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 62, "column": 62}, "end": {"line": 62, "column": 63}}, "loc": {"start": {"line": 62, "column": 75}, "end": {"line": 76, "column": 11}}, "line": 62}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 80, "column": 52}, "end": {"line": 80, "column": 53}}, "loc": {"start": {"line": 80, "column": 57}, "end": {"line": 80, "column": 66}}, "line": 80}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 81, "column": 55}, "end": {"line": 81, "column": 56}}, "loc": {"start": {"line": 81, "column": 60}, "end": {"line": 81, "column": 70}}, "line": 81}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 115, "column": 22}, "end": {"line": 115, "column": 23}}, "loc": {"start": {"line": 115, "column": 28}, "end": {"line": 115, "column": 54}}, "line": 115}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 116, "column": 25}, "end": {"line": 116, "column": 26}}, "loc": {"start": {"line": 116, "column": 31}, "end": {"line": 116, "column": 64}}, "line": 116}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 117, "column": 25}, "end": {"line": 117, "column": 26}}, "loc": {"start": {"line": 117, "column": 31}, "end": {"line": 117, "column": 97}}, "line": 117}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 118, "column": 24}, "end": {"line": 118, "column": 25}}, "loc": {"start": {"line": 118, "column": 30}, "end": {"line": 118, "column": 147}}, "line": 118}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 119, "column": 27}, "end": {"line": 119, "column": 28}}, "loc": {"start": {"line": 119, "column": 33}, "end": {"line": 119, "column": 84}}, "line": 119}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 122, "column": 29}, "end": {"line": 122, "column": 30}}, "loc": {"start": {"line": 122, "column": 48}, "end": {"line": 122, "column": 73}}, "line": 122}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 123, "column": 24}, "end": {"line": 123, "column": 25}}, "loc": {"start": {"line": 123, "column": 47}, "end": {"line": 133, "column": 3}}, "line": 123}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 134, "column": 23}, "end": {"line": 134, "column": 24}}, "loc": {"start": {"line": 134, "column": 29}, "end": {"line": 138, "column": 3}}, "line": 134}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 139, "column": 23}, "end": {"line": 139, "column": 24}}, "loc": {"start": {"line": 139, "column": 29}, "end": {"line": 139, "column": 64}}, "line": 139}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 10}, "end": {"line": 16, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 16, "column": 10}, "end": {"line": 16, "column": 46}}, {"start": {"line": 16, "column": 50}, "end": {"line": 16, "column": 60}}], "line": 16}, "1": {"loc": {"start": {"line": 37, "column": 2}, "end": {"line": 47, "column": 3}}, "type": "if", "locations": [{"start": {"line": 37, "column": 2}, "end": {"line": 47, "column": 3}}, {"start": {}, "end": {}}], "line": 37}, "2": {"loc": {"start": {"line": 57, "column": 6}, "end": {"line": 102, "column": 7}}, "type": "if", "locations": [{"start": {"line": 57, "column": 6}, "end": {"line": 102, "column": 7}}, {"start": {"line": 96, "column": 13}, "end": {"line": 102, "column": 7}}], "line": 57}, "3": {"loc": {"start": {"line": 67, "column": 27}, "end": {"line": 67, "column": 58}}, "type": "cond-expr", "locations": [{"start": {"line": 67, "column": 39}, "end": {"line": 67, "column": 49}}, {"start": {"line": 67, "column": 52}, "end": {"line": 67, "column": 58}}], "line": 67}, "4": {"loc": {"start": {"line": 90, "column": 35}, "end": {"line": 90, "column": 108}}, "type": "cond-expr", "locations": [{"start": {"line": 90, "column": 62}, "end": {"line": 90, "column": 104}}, {"start": {"line": 90, "column": 107}, "end": {"line": 90, "column": 108}}], "line": 90}, "5": {"loc": {"start": {"line": 99, "column": 8}, "end": {"line": 101, "column": 9}}, "type": "if", "locations": [{"start": {"line": 99, "column": 8}, "end": {"line": 101, "column": 9}}, {"start": {}, "end": {}}], "line": 99}, "6": {"loc": {"start": {"line": 99, "column": 12}, "end": {"line": 99, "column": 101}}, "type": "binary-expr", "locations": [{"start": {"line": 99, "column": 12}, "end": {"line": 99, "column": 53}}, {"start": {"line": 99, "column": 57}, "end": {"line": 99, "column": 101}}], "line": 99}, "7": {"loc": {"start": {"line": 104, "column": 6}, "end": {"line": 108, "column": 7}}, "type": "if", "locations": [{"start": {"line": 104, "column": 6}, "end": {"line": 108, "column": 7}}, {"start": {"line": 106, "column": 13}, "end": {"line": 108, "column": 7}}], "line": 104}, "8": {"loc": {"start": {"line": 124, "column": 6}, "end": {"line": 132, "column": 7}}, "type": "if", "locations": [{"start": {"line": 124, "column": 6}, "end": {"line": 132, "column": 7}}, {"start": {"line": 126, "column": 13}, "end": {"line": 132, "column": 7}}], "line": 124}, "9": {"loc": {"start": {"line": 126, "column": 13}, "end": {"line": 132, "column": 7}}, "type": "if", "locations": [{"start": {"line": 126, "column": 13}, "end": {"line": 132, "column": 7}}, {"start": {"line": 128, "column": 13}, "end": {"line": 132, "column": 7}}], "line": 126}, "10": {"loc": {"start": {"line": 128, "column": 13}, "end": {"line": 132, "column": 7}}, "type": "if", "locations": [{"start": {"line": 128, "column": 13}, "end": {"line": 132, "column": 7}}, {"start": {"line": 130, "column": 13}, "end": {"line": 132, "column": 7}}], "line": 128}, "11": {"loc": {"start": {"line": 144, "column": 7}, "end": {"line": 144, "column": 81}}, "type": "binary-expr", "locations": [{"start": {"line": 144, "column": 7}, "end": {"line": 144, "column": 14}}, {"start": {"line": 144, "column": 18}, "end": {"line": 144, "column": 81}}], "line": 144}, "12": {"loc": {"start": {"line": 144, "column": 35}, "end": {"line": 144, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 144, "column": 35}, "end": {"line": 144, "column": 47}}, {"start": {"line": 144, "column": 51}, "end": {"line": 144, "column": 76}}], "line": 144}, "13": {"loc": {"start": {"line": 145, "column": 7}, "end": {"line": 154, "column": 18}}, "type": "binary-expr", "locations": [{"start": {"line": 145, "column": 7}, "end": {"line": 145, "column": 12}}, {"start": {"line": 145, "column": 16}, "end": {"line": 154, "column": 18}}], "line": 145}, "14": {"loc": {"start": {"line": 155, "column": 7}, "end": {"line": 155, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 155, "column": 7}, "end": {"line": 155, "column": 20}}, {"start": {"line": 155, "column": 24}, "end": {"line": 155, "column": 30}}, {"start": {"line": 155, "column": 34}, "end": {"line": 155, "column": 75}}], "line": 155}}, "s": {"0": 1, "1": 7, "2": 7, "3": 7, "4": 7, "5": 7, "6": 7, "7": 7, "8": 7, "9": 7, "10": 7, "11": 0, "12": 0, "13": 0, "14": 7, "15": 2, "16": 2, "17": 2, "18": 2, "19": 2, "20": 2, "21": 2, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 2, "35": 1, "36": 1, "37": 0, "38": 1, "39": 0, "40": 1, "41": 2, "42": 7, "43": 0, "44": 7, "45": 0, "46": 7, "47": 0, "48": 7, "49": 0, "50": 7, "51": 0, "52": 7, "53": 0, "54": 7, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 7, "63": 0, "64": 0, "65": 0, "66": 7, "67": 0, "68": 7}, "f": {"0": 7, "1": 2, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "b": {"0": [1, 1], "1": [0, 7], "2": [0, 2], "3": [0, 0], "4": [0, 0], "5": [0, 1], "6": [1, 0], "7": [0, 1], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [7, 2], "12": [2, 0], "13": [7, 1], "14": [7, 1, 1]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "311ba785ec045826c157990ab6e4c1f935597209"}, "/Users/<USER>/WebstormProjects/goo/src/BusinessSearchApp.ts": {"path": "/Users/<USER>/WebstormProjects/goo/src/BusinessSearchApp.ts", "statementMap": {"0": {"start": {"line": 33, "column": 19}, "end": {"line": 33, "column": 69}}, "1": {"start": {"line": 34, "column": 4}, "end": {"line": 39, "column": 5}}, "2": {"start": {"line": 35, "column": 6}, "end": {"line": 38, "column": 8}}, "3": {"start": {"line": 42, "column": 4}, "end": {"line": 49, "column": 6}}, "4": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 30}}, "5": {"start": {"line": 62, "column": 37}, "end": {"line": 62, "column": 71}}, "6": {"start": {"line": 63, "column": 4}, "end": {"line": 67, "column": 7}}, "7": {"start": {"line": 69, "column": 19}, "end": {"line": 69, "column": 77}}, "8": {"start": {"line": 72, "column": 4}, "end": {"line": 77, "column": 5}}, "9": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 48}}, "10": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": 59}}, "11": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 18}}, "12": {"start": {"line": 88, "column": 26}, "end": {"line": 88, "column": 71}}, "13": {"start": {"line": 89, "column": 30}, "end": {"line": 89, "column": 68}}, "14": {"start": {"line": 92, "column": 23}, "end": {"line": 92, "column": 63}}, "15": {"start": {"line": 93, "column": 26}, "end": {"line": 93, "column": 67}}, "16": {"start": {"line": 96, "column": 4}, "end": {"line": 98, "column": 6}}, "17": {"start": {"line": 97, "column": 6}, "end": {"line": 97, "column": 97}}, "18": {"start": {"line": 105, "column": 4}, "end": {"line": 105, "column": 52}}, "19": {"start": {"line": 106, "column": 4}, "end": {"line": 106, "column": 36}}, "20": {"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 60}}, "21": {"start": {"line": 122, "column": 4}, "end": {"line": 122, "column": 41}}, "22": {"start": {"line": 131, "column": 4}, "end": {"line": 131, "column": 49}}, "23": {"start": {"line": 139, "column": 4}, "end": {"line": 139, "column": 45}}, "24": {"start": {"line": 146, "column": 4}, "end": {"line": 146, "column": 39}}, "25": {"start": {"line": 147, "column": 4}, "end": {"line": 147, "column": 36}}, "26": {"start": {"line": 150, "column": 29}, "end": {"line": 150, "column": 69}}, "27": {"start": {"line": 151, "column": 32}, "end": {"line": 151, "column": 75}}, "28": {"start": {"line": 152, "column": 39}, "end": {"line": 152, "column": 71}}, "29": {"start": {"line": 154, "column": 4}, "end": {"line": 154, "column": 34}}, "30": {"start": {"line": 155, "column": 4}, "end": {"line": 155, "column": 37}}, "31": {"start": {"line": 156, "column": 4}, "end": {"line": 156, "column": 44}}, "32": {"start": {"line": 164, "column": 4}, "end": {"line": 164, "column": 49}}, "33": {"start": {"line": 172, "column": 4}, "end": {"line": 172, "column": 30}}, "34": {"start": {"line": 185, "column": 19}, "end": {"line": 190, "column": 5}}, "35": {"start": {"line": 192, "column": 4}, "end": {"line": 199, "column": 5}}, "36": {"start": {"line": 194, "column": 31}, "end": {"line": 194, "column": 71}}, "37": {"start": {"line": 195, "column": 6}, "end": {"line": 195, "column": 59}}, "38": {"start": {"line": 196, "column": 6}, "end": {"line": 196, "column": 30}}, "39": {"start": {"line": 198, "column": 6}, "end": {"line": 198, "column": 68}}, "40": {"start": {"line": 201, "column": 4}, "end": {"line": 223, "column": 5}}, "41": {"start": {"line": 203, "column": 22}, "end": {"line": 218, "column": 8}}, "42": {"start": {"line": 219, "column": 6}, "end": {"line": 219, "column": 51}}, "43": {"start": {"line": 220, "column": 6}, "end": {"line": 220, "column": 28}}, "44": {"start": {"line": 222, "column": 6}, "end": {"line": 222, "column": 58}}, "45": {"start": {"line": 226, "column": 4}, "end": {"line": 226, "column": 38}}, "46": {"start": {"line": 229, "column": 4}, "end": {"line": 229, "column": 37}}, "47": {"start": {"line": 231, "column": 4}, "end": {"line": 231, "column": 18}}, "48": {"start": {"line": 239, "column": 4}, "end": {"line": 242, "column": 6}}, "49": {"start": {"line": 245, "column": 29}, "end": {"line": 245, "column": 69}}, "50": {"start": {"line": 246, "column": 32}, "end": {"line": 246, "column": 75}}, "51": {"start": {"line": 247, "column": 39}, "end": {"line": 247, "column": 71}}, "52": {"start": {"line": 250, "column": 4}, "end": {"line": 254, "column": 6}}, "53": {"start": {"line": 257, "column": 4}, "end": {"line": 257, "column": 41}}, "54": {"start": {"line": 266, "column": 17}, "end": {"line": 266, "column": 34}}, "55": {"start": {"line": 267, "column": 4}, "end": {"line": 274, "column": 7}}, "56": {"start": {"line": 268, "column": 18}, "end": {"line": 268, "column": 159}}, "57": {"start": {"line": 269, "column": 6}, "end": {"line": 271, "column": 7}}, "58": {"start": {"line": 270, "column": 8}, "end": {"line": 270, "column": 21}}, "59": {"start": {"line": 272, "column": 6}, "end": {"line": 272, "column": 20}}, "60": {"start": {"line": 273, "column": 6}, "end": {"line": 273, "column": 18}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": 3}}, "loc": {"start": {"line": 31, "column": 38}, "end": {"line": 53, "column": 3}}, "line": 31}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": 3}}, "loc": {"start": {"line": 60, "column": 72}, "end": {"line": 80, "column": 3}}, "line": 60}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 86, "column": 2}, "end": {"line": 86, "column": 3}}, "loc": {"start": {"line": 86, "column": 37}, "end": {"line": 99, "column": 3}}, "line": 86}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 96, "column": 30}, "end": {"line": 96, "column": 31}}, "loc": {"start": {"line": 97, "column": 6}, "end": {"line": 97, "column": 97}}, "line": 97}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 104, "column": 2}, "end": {"line": 104, "column": 3}}, "loc": {"start": {"line": 104, "column": 29}, "end": {"line": 107, "column": 3}}, "line": 104}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 3}}, "loc": {"start": {"line": 113, "column": 42}, "end": {"line": 115, "column": 3}}, "line": 113}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 121, "column": 2}, "end": {"line": 121, "column": 3}}, "loc": {"start": {"line": 121, "column": 23}, "end": {"line": 123, "column": 3}}, "line": 121}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 130, "column": 2}, "end": {"line": 130, "column": 3}}, "loc": {"start": {"line": 130, "column": 39}, "end": {"line": 132, "column": 3}}, "line": 130}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 138, "column": 2}, "end": {"line": 138, "column": 3}}, "loc": {"start": {"line": 138, "column": 32}, "end": {"line": 140, "column": 3}}, "line": 138}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 145, "column": 2}, "end": {"line": 145, "column": 3}}, "loc": {"start": {"line": 145, "column": 23}, "end": {"line": 157, "column": 3}}, "line": 145}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 163, "column": 2}, "end": {"line": 163, "column": 3}}, "loc": {"start": {"line": 163, "column": 31}, "end": {"line": 165, "column": 3}}, "line": 163}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 171, "column": 2}, "end": {"line": 171, "column": 3}}, "loc": {"start": {"line": 171, "column": 45}, "end": {"line": 173, "column": 3}}, "line": 171}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 179, "column": 2}, "end": {"line": 179, "column": 3}}, "loc": {"start": {"line": 184, "column": 5}, "end": {"line": 232, "column": 3}}, "line": 184}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 237, "column": 2}, "end": {"line": 237, "column": 3}}, "loc": {"start": {"line": 237, "column": 37}, "end": {"line": 258, "column": 3}}, "line": 237}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 265, "column": 2}, "end": {"line": 265, "column": 3}}, "loc": {"start": {"line": 265, "column": 76}, "end": {"line": 275, "column": 3}}, "line": 265}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 267, "column": 26}, "end": {"line": 267, "column": 27}}, "loc": {"start": {"line": 267, "column": 36}, "end": {"line": 274, "column": 5}}, "line": 267}}, "branchMap": {"0": {"loc": {"start": {"line": 31, "column": 14}, "end": {"line": 31, "column": 36}}, "type": "default-arg", "locations": [{"start": {"line": 31, "column": 34}, "end": {"line": 31, "column": 36}}], "line": 31}, "1": {"loc": {"start": {"line": 33, "column": 19}, "end": {"line": 33, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 33, "column": 19}, "end": {"line": 33, "column": 32}}, {"start": {"line": 33, "column": 36}, "end": {"line": 33, "column": 69}}], "line": 33}, "2": {"loc": {"start": {"line": 34, "column": 4}, "end": {"line": 39, "column": 5}}, "type": "if", "locations": [{"start": {"line": 34, "column": 4}, "end": {"line": 39, "column": 5}}, {"start": {}, "end": {}}], "line": 34}, "3": {"loc": {"start": {"line": 44, "column": 28}, "end": {"line": 44, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 44, "column": 28}, "end": {"line": 44, "column": 55}}, {"start": {"line": 44, "column": 59}, "end": {"line": 44, "column": 61}}], "line": 44}, "4": {"loc": {"start": {"line": 45, "column": 29}, "end": {"line": 45, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 45, "column": 29}, "end": {"line": 45, "column": 57}}, {"start": {"line": 45, "column": 61}, "end": {"line": 45, "column": 62}}], "line": 45}, "5": {"loc": {"start": {"line": 46, "column": 24}, "end": {"line": 46, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 46, "column": 24}, "end": {"line": 46, "column": 47}}, {"start": {"line": 46, "column": 51}, "end": {"line": 46, "column": 55}}], "line": 46}, "6": {"loc": {"start": {"line": 47, "column": 34}, "end": {"line": 47, "column": 108}}, "type": "binary-expr", "locations": [{"start": {"line": 47, "column": 34}, "end": {"line": 47, "column": 67}}, {"start": {"line": 47, "column": 71}, "end": {"line": 47, "column": 108}}], "line": 47}, "7": {"loc": {"start": {"line": 48, "column": 26}, "end": {"line": 48, "column": 83}}, "type": "binary-expr", "locations": [{"start": {"line": 48, "column": 26}, "end": {"line": 48, "column": 51}}, {"start": {"line": 48, "column": 55}, "end": {"line": 48, "column": 83}}], "line": 48}, "8": {"loc": {"start": {"line": 269, "column": 6}, "end": {"line": 271, "column": 7}}, "type": "if", "locations": [{"start": {"line": 269, "column": 6}, "end": {"line": 271, "column": 7}}, {"start": {}, "end": {}}], "line": 269}}, "s": {"0": 39, "1": 39, "2": 3, "3": 36, "4": 36, "5": 7, "6": 7, "7": 4, "8": 4, "9": 4, "10": 1, "11": 4, "12": 9, "13": 9, "14": 9, "15": 9, "16": 9, "17": 2, "18": 1, "19": 1, "20": 2, "21": 1, "22": 2, "23": 1, "24": 2, "25": 2, "26": 2, "27": 2, "28": 2, "29": 2, "30": 2, "31": 2, "32": 1, "33": 3, "34": 4, "35": 4, "36": 4, "37": 4, "38": 4, "39": 0, "40": 4, "41": 4, "42": 3, "43": 3, "44": 1, "45": 4, "46": 4, "47": 4, "48": 36, "49": 36, "50": 36, "51": 36, "52": 36, "53": 36, "54": 9, "55": 9, "56": 7, "57": 7, "58": 2, "59": 5, "60": 5}, "f": {"0": 39, "1": 7, "2": 9, "3": 2, "4": 1, "5": 2, "6": 1, "7": 2, "8": 1, "9": 2, "10": 1, "11": 3, "12": 4, "13": 36, "14": 9, "15": 7}, "b": {"0": [2], "1": [39, 3], "2": [3, 36], "3": [36, 35], "4": [36, 35], "5": [36, 35], "6": [36, 35], "7": [36, 35], "8": [2, 5]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "a60fcfb97d1cea86989cbd73c42d0ce3f4b8afd4"}, "/Users/<USER>/WebstormProjects/goo/src/index.tsx": {"path": "/Users/<USER>/WebstormProjects/goo/src/index.tsx", "statementMap": {"0": {"start": {"line": 6, "column": 26}, "end": {"line": 8, "column": 1}}, "1": {"start": {"line": 10, "column": 26}, "end": {"line": 10, "column": 55}}, "2": {"start": {"line": 12, "column": 13}, "end": {"line": 14, "column": 1}}, "3": {"start": {"line": 15, "column": 0}, "end": {"line": 19, "column": 2}}}, "fnMap": {}, "branchMap": {"0": {"loc": {"start": {"line": 7, "column": 10}, "end": {"line": 7, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 7, "column": 10}, "end": {"line": 7, "column": 46}}, {"start": {"line": 7, "column": 50}, "end": {"line": 7, "column": 60}}], "line": 7}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {}, "b": {"0": [0, 0]}}, "/Users/<USER>/WebstormProjects/goo/src/components/DataManagement.tsx": {"path": "/Users/<USER>/WebstormProjects/goo/src/components/DataManagement.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 23}, "end": {"line": 32, "column": 1}}, "1": {"start": {"line": 4, "column": 21}, "end": {"line": 4, "column": 63}}, "2": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 63}}, "3": {"start": {"line": 5, "column": 31}, "end": {"line": 5, "column": 80}}, "4": {"start": {"line": 5, "column": 37}, "end": {"line": 5, "column": 80}}, "5": {"start": {"line": 6, "column": 23}, "end": {"line": 8, "column": 3}}, "6": {"start": {"line": 7, "column": 4}, "end": {"line": 7, "column": 57}}, "7": {"start": {"line": 9, "column": 21}, "end": {"line": 9, "column": 59}}, "8": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 59}}, "9": {"start": {"line": 12, "column": 22}, "end": {"line": 12, "column": 26}}, "10": {"start": {"line": 13, "column": 23}, "end": {"line": 13, "column": 28}}, "11": {"start": {"line": 15, "column": 2}, "end": {"line": 31, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 23}, "end": {"line": 3, "column": 24}}, "loc": {"start": {"line": 3, "column": 29}, "end": {"line": 32, "column": 1}}, "line": 3}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 4, "column": 21}, "end": {"line": 4, "column": 22}}, "loc": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": 63}}, "line": 4}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 5, "column": 31}, "end": {"line": 5, "column": 32}}, "loc": {"start": {"line": 5, "column": 37}, "end": {"line": 5, "column": 80}}, "line": 5}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 6, "column": 23}, "end": {"line": 6, "column": 24}}, "loc": {"start": {"line": 6, "column": 71}, "end": {"line": 8, "column": 3}}, "line": 6}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 9, "column": 21}, "end": {"line": 9, "column": 22}}, "loc": {"start": {"line": 9, "column": 27}, "end": {"line": 9, "column": 59}}, "line": 9}}, "branchMap": {}, "s": {"0": 1, "1": 2, "2": 1, "3": 2, "4": 1, "5": 2, "6": 0, "7": 2, "8": 1, "9": 2, "10": 2, "11": 2}, "f": {"0": 2, "1": 1, "2": 1, "3": 0, "4": 1}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "a7ca3e85595c48e4529ee7e87874015647dc316b"}, "/Users/<USER>/WebstormProjects/goo/src/components/DebugPanel.tsx": {"path": "/Users/<USER>/WebstormProjects/goo/src/components/DebugPanel.tsx", "statementMap": {"0": {"start": {"line": 16, "column": 46}, "end": {"line": 71, "column": 1}}, "1": {"start": {"line": 25, "column": 4}, "end": {"line": 70, "column": 6}}, "2": {"start": {"line": 35, "column": 45}, "end": {"line": 35, "column": 121}}, "3": {"start": {"line": 45, "column": 45}, "end": {"line": 45, "column": 121}}, "4": {"start": {"line": 51, "column": 49}, "end": {"line": 51, "column": 78}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 16, "column": 46}, "end": {"line": 16, "column": 47}}, "loc": {"start": {"line": 24, "column": 6}, "end": {"line": 71, "column": 1}}, "line": 24}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 35, "column": 38}, "end": {"line": 35, "column": 39}}, "loc": {"start": {"line": 35, "column": 45}, "end": {"line": 35, "column": 121}}, "line": 35}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 45, "column": 38}, "end": {"line": 45, "column": 39}}, "loc": {"start": {"line": 45, "column": 45}, "end": {"line": 45, "column": 121}}, "line": 45}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 51, "column": 42}, "end": {"line": 51, "column": 43}}, "loc": {"start": {"line": 51, "column": 49}, "end": {"line": 51, "column": 78}}, "line": 51}}, "branchMap": {}, "s": {"0": 1, "1": 2, "2": 1, "3": 1, "4": 1}, "f": {"0": 2, "1": 1, "2": 1, "3": 1}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "af67491c7f15732f185ac1c2b7a139e044a4a74d"}, "/Users/<USER>/WebstormProjects/goo/src/components/ErrorStates.tsx": {"path": "/Users/<USER>/WebstormProjects/goo/src/components/ErrorStates.tsx", "statementMap": {"0": {"start": {"line": 21, "column": 48}, "end": {"line": 70, "column": 1}}, "1": {"start": {"line": 25, "column": 4}, "end": {"line": 69, "column": 6}}, "2": {"start": {"line": 42, "column": 63}, "end": {"line": 42, "column": 92}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 21, "column": 48}, "end": {"line": 21, "column": 49}}, "loc": {"start": {"line": 24, "column": 6}, "end": {"line": 70, "column": 1}}, "line": 24}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 42, "column": 44}, "end": {"line": 42, "column": 45}}, "loc": {"start": {"line": 42, "column": 63}, "end": {"line": 42, "column": 92}}, "line": 42}}, "branchMap": {"0": {"loc": {"start": {"line": 28, "column": 13}, "end": {"line": 35, "column": 13}}, "type": "binary-expr", "locations": [{"start": {"line": 28, "column": 13}, "end": {"line": 28, "column": 37}}, {"start": {"line": 29, "column": 16}, "end": {"line": 34, "column": 22}}], "line": 28}, "1": {"loc": {"start": {"line": 38, "column": 13}, "end": {"line": 45, "column": 13}}, "type": "binary-expr", "locations": [{"start": {"line": 38, "column": 13}, "end": {"line": 38, "column": 40}}, {"start": {"line": 39, "column": 16}, "end": {"line": 44, "column": 22}}], "line": 38}, "2": {"loc": {"start": {"line": 48, "column": 13}, "end": {"line": 58, "column": 13}}, "type": "binary-expr", "locations": [{"start": {"line": 48, "column": 13}, "end": {"line": 48, "column": 40}}, {"start": {"line": 49, "column": 16}, "end": {"line": 57, "column": 22}}], "line": 48}, "3": {"loc": {"start": {"line": 61, "column": 13}, "end": {"line": 67, "column": 13}}, "type": "binary-expr", "locations": [{"start": {"line": 61, "column": 13}, "end": {"line": 61, "column": 37}}, {"start": {"line": 62, "column": 17}, "end": {"line": 66, "column": 22}}], "line": 61}, "4": {"loc": {"start": {"line": 64, "column": 24}, "end": {"line": 64, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 64, "column": 24}, "end": {"line": 64, "column": 37}}, {"start": {"line": 64, "column": 41}, "end": {"line": 64, "column": 72}}], "line": 64}}, "s": {"0": 2, "1": 5, "2": 1}, "f": {"0": 5, "1": 1}, "b": {"0": [5, 1], "1": [5, 1], "2": [5, 1], "3": [5, 2], "4": [2, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "3785b9aaf69248d22ff6e167e6e5501fee474915"}, "/Users/<USER>/WebstormProjects/goo/src/components/ResultsTables.tsx": {"path": "/Users/<USER>/WebstormProjects/goo/src/components/ResultsTables.tsx", "statementMap": {"0": {"start": {"line": 4, "column": 22}, "end": {"line": 146, "column": 1}}, "1": {"start": {"line": 5, "column": 44}, "end": {"line": 5, "column": 59}}, "2": {"start": {"line": 6, "column": 85}, "end": {"line": 6, "column": 103}}, "3": {"start": {"line": 8, "column": 62}, "end": {"line": 8, "column": 84}}, "4": {"start": {"line": 9, "column": 68}, "end": {"line": 9, "column": 93}}, "5": {"start": {"line": 12, "column": 20}, "end": {"line": 15, "column": 3}}, "6": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 46}}, "7": {"start": {"line": 17, "column": 32}, "end": {"line": 17, "column": 93}}, "8": {"start": {"line": 17, "column": 38}, "end": {"line": 17, "column": 93}}, "9": {"start": {"line": 18, "column": 36}, "end": {"line": 18, "column": 89}}, "10": {"start": {"line": 18, "column": 42}, "end": {"line": 18, "column": 89}}, "11": {"start": {"line": 19, "column": 32}, "end": {"line": 19, "column": 82}}, "12": {"start": {"line": 19, "column": 38}, "end": {"line": 19, "column": 82}}, "13": {"start": {"line": 20, "column": 32}, "end": {"line": 20, "column": 83}}, "14": {"start": {"line": 20, "column": 38}, "end": {"line": 20, "column": 83}}, "15": {"start": {"line": 21, "column": 25}, "end": {"line": 21, "column": 67}}, "16": {"start": {"line": 21, "column": 31}, "end": {"line": 21, "column": 67}}, "17": {"start": {"line": 22, "column": 23}, "end": {"line": 22, "column": 69}}, "18": {"start": {"line": 22, "column": 29}, "end": {"line": 22, "column": 69}}, "19": {"start": {"line": 23, "column": 19}, "end": {"line": 23, "column": 61}}, "20": {"start": {"line": 23, "column": 25}, "end": {"line": 23, "column": 61}}, "21": {"start": {"line": 24, "column": 35}, "end": {"line": 24, "column": 89}}, "22": {"start": {"line": 24, "column": 41}, "end": {"line": 24, "column": 89}}, "23": {"start": {"line": 25, "column": 26}, "end": {"line": 25, "column": 73}}, "24": {"start": {"line": 25, "column": 32}, "end": {"line": 25, "column": 73}}, "25": {"start": {"line": 26, "column": 25}, "end": {"line": 26, "column": 75}}, "26": {"start": {"line": 26, "column": 31}, "end": {"line": 26, "column": 75}}, "27": {"start": {"line": 27, "column": 36}, "end": {"line": 27, "column": 99}}, "28": {"start": {"line": 27, "column": 60}, "end": {"line": 27, "column": 99}}, "29": {"start": {"line": 28, "column": 31}, "end": {"line": 28, "column": 83}}, "30": {"start": {"line": 28, "column": 55}, "end": {"line": 28, "column": 83}}, "31": {"start": {"line": 30, "column": 2}, "end": {"line": 145, "column": 4}}, "32": {"start": {"line": 52, "column": 33}, "end": {"line": 52, "column": 66}}, "33": {"start": {"line": 53, "column": 33}, "end": {"line": 53, "column": 68}}, "34": {"start": {"line": 54, "column": 33}, "end": {"line": 54, "column": 70}}, "35": {"start": {"line": 55, "column": 33}, "end": {"line": 55, "column": 67}}, "36": {"start": {"line": 74, "column": 14}, "end": {"line": 85, "column": 19}}, "37": {"start": {"line": 100, "column": 33}, "end": {"line": 100, "column": 71}}, "38": {"start": {"line": 101, "column": 33}, "end": {"line": 101, "column": 72}}, "39": {"start": {"line": 102, "column": 33}, "end": {"line": 102, "column": 73}}, "40": {"start": {"line": 103, "column": 33}, "end": {"line": 103, "column": 76}}, "41": {"start": {"line": 123, "column": 14}, "end": {"line": 135, "column": 19}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 22}, "end": {"line": 4, "column": 23}}, "loc": {"start": {"line": 4, "column": 66}, "end": {"line": 146, "column": 1}}, "line": 4}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 12, "column": 20}, "end": {"line": 12, "column": 21}}, "loc": {"start": {"line": 12, "column": 80}, "end": {"line": 15, "column": 3}}, "line": 12}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 17, "column": 32}, "end": {"line": 17, "column": 33}}, "loc": {"start": {"line": 17, "column": 38}, "end": {"line": 17, "column": 93}}, "line": 17}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 18, "column": 36}, "end": {"line": 18, "column": 37}}, "loc": {"start": {"line": 18, "column": 42}, "end": {"line": 18, "column": 89}}, "line": 18}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 19, "column": 32}, "end": {"line": 19, "column": 33}}, "loc": {"start": {"line": 19, "column": 38}, "end": {"line": 19, "column": 82}}, "line": 19}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 20, "column": 32}, "end": {"line": 20, "column": 33}}, "loc": {"start": {"line": 20, "column": 38}, "end": {"line": 20, "column": 83}}, "line": 20}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 21, "column": 25}, "end": {"line": 21, "column": 26}}, "loc": {"start": {"line": 21, "column": 31}, "end": {"line": 21, "column": 67}}, "line": 21}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 22, "column": 23}, "end": {"line": 22, "column": 24}}, "loc": {"start": {"line": 22, "column": 29}, "end": {"line": 22, "column": 69}}, "line": 22}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 23, "column": 19}, "end": {"line": 23, "column": 20}}, "loc": {"start": {"line": 23, "column": 25}, "end": {"line": 23, "column": 61}}, "line": 23}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 24, "column": 35}, "end": {"line": 24, "column": 36}}, "loc": {"start": {"line": 24, "column": 41}, "end": {"line": 24, "column": 89}}, "line": 24}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 25, "column": 26}, "end": {"line": 25, "column": 27}}, "loc": {"start": {"line": 25, "column": 32}, "end": {"line": 25, "column": 73}}, "line": 25}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 26, "column": 25}, "end": {"line": 26, "column": 26}}, "loc": {"start": {"line": 26, "column": 31}, "end": {"line": 26, "column": 75}}, "line": 26}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 27, "column": 36}, "end": {"line": 27, "column": 37}}, "loc": {"start": {"line": 27, "column": 60}, "end": {"line": 27, "column": 99}}, "line": 27}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 28, "column": 31}, "end": {"line": 28, "column": 32}}, "loc": {"start": {"line": 28, "column": 55}, "end": {"line": 28, "column": 83}}, "line": 28}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 52, "column": 27}, "end": {"line": 52, "column": 28}}, "loc": {"start": {"line": 52, "column": 33}, "end": {"line": 52, "column": 66}}, "line": 52}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 53, "column": 27}, "end": {"line": 53, "column": 28}}, "loc": {"start": {"line": 53, "column": 33}, "end": {"line": 53, "column": 68}}, "line": 53}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 54, "column": 27}, "end": {"line": 54, "column": 28}}, "loc": {"start": {"line": 54, "column": 33}, "end": {"line": 54, "column": 70}}, "line": 54}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 55, "column": 27}, "end": {"line": 55, "column": 28}}, "loc": {"start": {"line": 55, "column": 33}, "end": {"line": 55, "column": 67}}, "line": 55}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 73, "column": 40}, "end": {"line": 73, "column": 41}}, "loc": {"start": {"line": 74, "column": 14}, "end": {"line": 85, "column": 19}}, "line": 74}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 100, "column": 27}, "end": {"line": 100, "column": 28}}, "loc": {"start": {"line": 100, "column": 33}, "end": {"line": 100, "column": 71}}, "line": 100}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 101, "column": 27}, "end": {"line": 101, "column": 28}}, "loc": {"start": {"line": 101, "column": 33}, "end": {"line": 101, "column": 72}}, "line": 101}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 102, "column": 27}, "end": {"line": 102, "column": 28}}, "loc": {"start": {"line": 102, "column": 33}, "end": {"line": 102, "column": 73}}, "line": 102}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 103, "column": 27}, "end": {"line": 103, "column": 28}}, "loc": {"start": {"line": 103, "column": 33}, "end": {"line": 103, "column": 76}}, "line": 103}, "23": {"name": "(anonymous_23)", "decl": {"start": {"line": 122, "column": 43}, "end": {"line": 122, "column": 44}}, "loc": {"start": {"line": 123, "column": 14}, "end": {"line": 135, "column": 19}}, "line": 123}}, "branchMap": {"0": {"loc": {"start": {"line": 27, "column": 72}, "end": {"line": 27, "column": 92}}, "type": "binary-expr", "locations": [{"start": {"line": 27, "column": 72}, "end": {"line": 27, "column": 87}}, {"start": {"line": 27, "column": 91}, "end": {"line": 27, "column": 92}}], "line": 27}, "1": {"loc": {"start": {"line": 28, "column": 56}, "end": {"line": 28, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 28, "column": 56}, "end": {"line": 28, "column": 71}}, {"start": {"line": 28, "column": 75}, "end": {"line": 28, "column": 76}}], "line": 28}, "2": {"loc": {"start": {"line": 78, "column": 21}, "end": {"line": 78, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 78, "column": 21}, "end": {"line": 78, "column": 35}}, {"start": {"line": 78, "column": 39}, "end": {"line": 78, "column": 44}}], "line": 78}, "3": {"loc": {"start": {"line": 82, "column": 32}, "end": {"line": 82, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 82, "column": 32}, "end": {"line": 82, "column": 60}}, {"start": {"line": 82, "column": 64}, "end": {"line": 82, "column": 65}}], "line": 82}, "4": {"loc": {"start": {"line": 83, "column": 21}, "end": {"line": 83, "column": 84}}, "type": "cond-expr", "locations": [{"start": {"line": 83, "column": 61}, "end": {"line": 83, "column": 70}}, {"start": {"line": 83, "column": 73}, "end": {"line": 83, "column": 84}}], "line": 83}, "5": {"loc": {"start": {"line": 84, "column": 21}, "end": {"line": 84, "column": 71}}, "type": "cond-expr", "locations": [{"start": {"line": 84, "column": 61}, "end": {"line": 84, "column": 64}}, {"start": {"line": 84, "column": 67}, "end": {"line": 84, "column": 71}}], "line": 84}, "6": {"loc": {"start": {"line": 123, "column": 43}, "end": {"line": 123, "column": 119}}, "type": "cond-expr", "locations": [{"start": {"line": 123, "column": 84}, "end": {"line": 123, "column": 114}}, {"start": {"line": 123, "column": 117}, "end": {"line": 123, "column": 119}}], "line": 123}, "7": {"loc": {"start": {"line": 123, "column": 43}, "end": {"line": 123, "column": 81}}, "type": "binary-expr", "locations": [{"start": {"line": 123, "column": 43}, "end": {"line": 123, "column": 58}}, {"start": {"line": 123, "column": 62}, "end": {"line": 123, "column": 81}}], "line": 123}, "8": {"loc": {"start": {"line": 127, "column": 21}, "end": {"line": 127, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 127, "column": 21}, "end": {"line": 127, "column": 35}}, {"start": {"line": 127, "column": 39}, "end": {"line": 127, "column": 44}}], "line": 127}, "9": {"loc": {"start": {"line": 131, "column": 32}, "end": {"line": 131, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 131, "column": 32}, "end": {"line": 131, "column": 60}}, {"start": {"line": 131, "column": 64}, "end": {"line": 131, "column": 65}}], "line": 131}, "10": {"loc": {"start": {"line": 132, "column": 21}, "end": {"line": 132, "column": 72}}, "type": "cond-expr", "locations": [{"start": {"line": 132, "column": 61}, "end": {"line": 132, "column": 65}}, {"start": {"line": 132, "column": 68}, "end": {"line": 132, "column": 72}}], "line": 132}}, "s": {"0": 2, "1": 5, "2": 5, "3": 5, "4": 5, "5": 5, "6": 0, "7": 5, "8": 0, "9": 5, "10": 0, "11": 5, "12": 0, "13": 5, "14": 0, "15": 5, "16": 0, "17": 5, "18": 0, "19": 5, "20": 0, "21": 5, "22": 0, "23": 5, "24": 0, "25": 5, "26": 0, "27": 5, "28": 4, "29": 5, "30": 4, "31": 5, "32": 0, "33": 0, "34": 0, "35": 0, "36": 5, "37": 0, "38": 0, "39": 0, "40": 0, "41": 4}, "f": {"0": 5, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 4, "13": 4, "14": 0, "15": 0, "16": 0, "17": 0, "18": 5, "19": 0, "20": 0, "21": 0, "22": 0, "23": 4}, "b": {"0": [4, 0], "1": [4, 0], "2": [5, 5], "3": [5, 1], "4": [4, 1], "5": [4, 1], "6": [4, 0], "7": [4, 4], "8": [4, 4], "9": [4, 0], "10": [0, 4]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "a5448273d7c5032b0fb933e3ed4802f245da1a2f"}, "/Users/<USER>/WebstormProjects/goo/src/components/SearchForm.tsx": {"path": "/Users/<USER>/WebstormProjects/goo/src/components/SearchForm.tsx", "statementMap": {"0": {"start": {"line": 3, "column": 19}, "end": {"line": 58, "column": 1}}, "1": {"start": {"line": 4, "column": 23}, "end": {"line": 16, "column": 3}}, "2": {"start": {"line": 5, "column": 4}, "end": {"line": 5, "column": 27}}, "3": {"start": {"line": 6, "column": 21}, "end": {"line": 6, "column": 54}}, "4": {"start": {"line": 7, "column": 19}, "end": {"line": 14, "column": 5}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 21}}, "6": {"start": {"line": 18, "column": 2}, "end": {"line": 57, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 19}, "end": {"line": 3, "column": 20}}, "loc": {"start": {"line": 3, "column": 74}, "end": {"line": 58, "column": 1}}, "line": 3}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 4, "column": 23}, "end": {"line": 4, "column": 24}}, "loc": {"start": {"line": 4, "column": 68}, "end": {"line": 16, "column": 3}}, "line": 4}}, "branchMap": {}, "s": {"0": 2, "1": 10, "2": 3, "3": 3, "4": 3, "5": 3, "6": 10}, "f": {"0": 10, "1": 3}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "661bfb9fd999434c669d36285ce510c8c9245f02"}, "/Users/<USER>/WebstormProjects/goo/src/components/SearchHistory.tsx": {"path": "/Users/<USER>/WebstormProjects/goo/src/components/SearchHistory.tsx", "statementMap": {"0": {"start": {"line": 4, "column": 22}, "end": {"line": 56, "column": 1}}, "1": {"start": {"line": 6, "column": 69}, "end": {"line": 21, "column": 5}}, "2": {"start": {"line": 23, "column": 24}, "end": {"line": 25, "column": 5}}, "3": {"start": {"line": 24, "column": 8}, "end": {"line": 24, "column": 54}}, "4": {"start": {"line": 27, "column": 4}, "end": {"line": 55, "column": 6}}, "5": {"start": {"line": 43, "column": 24}, "end": {"line": 50, "column": 29}}, "6": {"start": {"line": 49, "column": 55}, "end": {"line": 49, "column": 74}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 22}, "end": {"line": 4, "column": 23}}, "loc": {"start": {"line": 4, "column": 28}, "end": {"line": 56, "column": 1}}, "line": 4}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 23, "column": 24}, "end": {"line": 23, "column": 25}}, "loc": {"start": {"line": 23, "column": 50}, "end": {"line": 25, "column": 5}}, "line": 23}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 42, "column": 39}, "end": {"line": 42, "column": 40}}, "loc": {"start": {"line": 43, "column": 24}, "end": {"line": 50, "column": 29}}, "line": 43}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 49, "column": 49}, "end": {"line": 49, "column": 50}}, "loc": {"start": {"line": 49, "column": 55}, "end": {"line": 49, "column": 74}}, "line": 49}}, "branchMap": {"0": {"loc": {"start": {"line": 47, "column": 33}, "end": {"line": 47, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 47, "column": 33}, "end": {"line": 47, "column": 52}}, {"start": {"line": 47, "column": 56}, "end": {"line": 47, "column": 61}}], "line": 47}}, "s": {"0": 1, "1": 2, "2": 2, "3": 1, "4": 2, "5": 4, "6": 1}, "f": {"0": 2, "1": 1, "2": 4, "3": 1}, "b": {"0": [4, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "645db975fb8ba3d3587d936369471d00b3e8c2dc"}, "/Users/<USER>/WebstormProjects/goo/src/mocks/MockDataGenerator.ts": {"path": "/Users/<USER>/WebstormProjects/goo/src/mocks/MockDataGenerator.ts", "statementMap": {"0": {"start": {"line": 2, "column": 26}, "end": {"line": 7, "column": 3}}, "1": {"start": {"line": 9, "column": 26}, "end": {"line": 12, "column": 3}}, "2": {"start": {"line": 15, "column": 23}, "end": {"line": 15, "column": 25}}, "3": {"start": {"line": 16, "column": 33}, "end": {"line": 16, "column": 66}}, "4": {"start": {"line": 17, "column": 24}, "end": {"line": 17, "column": 45}}, "5": {"start": {"line": 19, "column": 4}, "end": {"line": 42, "column": 5}}, "6": {"start": {"line": 19, "column": 17}, "end": {"line": 19, "column": 18}}, "7": {"start": {"line": 20, "column": 25}, "end": {"line": 20, "column": 61}}, "8": {"start": {"line": 21, "column": 29}, "end": {"line": 21, "column": 58}}, "9": {"start": {"line": 22, "column": 23}, "end": {"line": 40, "column": 7}}, "10": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 32}}, "11": {"start": {"line": 44, "column": 4}, "end": {"line": 47, "column": 6}}, "12": {"start": {"line": 52, "column": 49}, "end": {"line": 58, "column": 5}}, "13": {"start": {"line": 60, "column": 4}, "end": {"line": 68, "column": 6}}, "14": {"start": {"line": 73, "column": 72}, "end": {"line": 79, "column": 5}}, "15": {"start": {"line": 81, "column": 21}, "end": {"line": 81, "column": 71}}, "16": {"start": {"line": 84, "column": 4}, "end": {"line": 86, "column": 5}}, "17": {"start": {"line": 85, "column": 8}, "end": {"line": 85, "column": 78}}, "18": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 74}}, "19": {"start": {"line": 93, "column": 42}, "end": {"line": 100, "column": 5}}, "20": {"start": {"line": 101, "column": 4}, "end": {"line": 101, "column": 39}}, "21": {"start": {"line": 106, "column": 19}, "end": {"line": 106, "column": 40}}, "22": {"start": {"line": 107, "column": 24}, "end": {"line": 107, "column": 85}}, "23": {"start": {"line": 108, "column": 23}, "end": {"line": 108, "column": 46}}, "24": {"start": {"line": 110, "column": 4}, "end": {"line": 110, "column": 47}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 3}}, "loc": {"start": {"line": 14, "column": 93}, "end": {"line": 48, "column": 3}}, "line": 14}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": 3}}, "loc": {"start": {"line": 50, "column": 48}, "end": {"line": 69, "column": 3}}, "line": 50}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 3}}, "loc": {"start": {"line": 72, "column": 82}, "end": {"line": 89, "column": 3}}, "line": 72}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 92, "column": 2}, "end": {"line": 92, "column": 3}}, "loc": {"start": {"line": 92, "column": 55}, "end": {"line": 102, "column": 3}}, "line": 92}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 105, "column": 2}, "end": {"line": 105, "column": 3}}, "loc": {"start": {"line": 105, "column": 32}, "end": {"line": 111, "column": 3}}, "line": 105}}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 68}, "end": {"line": 14, "column": 86}}, "type": "default-arg", "locations": [{"start": {"line": 14, "column": 84}, "end": {"line": 14, "column": 86}}], "line": 14}, "1": {"loc": {"start": {"line": 37, "column": 12}, "end": {"line": 39, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 37, "column": 12}, "end": {"line": 37, "column": 22}}, {"start": {"line": 37, "column": 26}, "end": {"line": 39, "column": 9}}], "line": 37}, "2": {"loc": {"start": {"line": 63, "column": 20}, "end": {"line": 63, "column": 79}}, "type": "binary-expr", "locations": [{"start": {"line": 63, "column": 20}, "end": {"line": 63, "column": 44}}, {"start": {"line": 63, "column": 48}, "end": {"line": 63, "column": 79}}], "line": 63}, "3": {"loc": {"start": {"line": 81, "column": 21}, "end": {"line": 81, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 81, "column": 21}, "end": {"line": 81, "column": 43}}, {"start": {"line": 81, "column": 47}, "end": {"line": 81, "column": 71}}], "line": 81}, "4": {"loc": {"start": {"line": 84, "column": 4}, "end": {"line": 86, "column": 5}}, "type": "if", "locations": [{"start": {"line": 84, "column": 4}, "end": {"line": 86, "column": 5}}, {"start": {}, "end": {}}], "line": 84}, "5": {"loc": {"start": {"line": 85, "column": 15}, "end": {"line": 85, "column": 77}}, "type": "binary-expr", "locations": [{"start": {"line": 85, "column": 15}, "end": {"line": 85, "column": 44}}, {"start": {"line": 85, "column": 48}, "end": {"line": 85, "column": 77}}], "line": 85}, "6": {"loc": {"start": {"line": 88, "column": 11}, "end": {"line": 88, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 88, "column": 11}, "end": {"line": 88, "column": 40}}, {"start": {"line": 88, "column": 44}, "end": {"line": 88, "column": 73}}], "line": 88}, "7": {"loc": {"start": {"line": 101, "column": 11}, "end": {"line": 101, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 101, "column": 11}, "end": {"line": 101, "column": 30}}, {"start": {"line": 101, "column": 34}, "end": {"line": 101, "column": 38}}], "line": 101}}, "s": {"0": 8, "1": 8, "2": 4, "3": 4, "4": 4, "5": 4, "6": 4, "7": 2011, "8": 2011, "9": 2011, "10": 2011, "11": 4, "12": 2, "13": 2, "14": 2014, "15": 2014, "16": 2014, "17": 1, "18": 2013, "19": 4, "20": 4, "21": 2012, "22": 2012, "23": 2012, "24": 2012}, "f": {"0": 4, "1": 2, "2": 2014, "3": 4, "4": 2012}, "b": {"0": [0], "1": [2011, 1407], "2": [2, 1], "3": [2014, 0], "4": [1, 2013], "5": [1, 1], "6": [2013, 2012], "7": [4, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "401806eddcc47fc4fec4970b8fe0cc4acd90d508"}, "/Users/<USER>/WebstormProjects/goo/src/mocks/ProgressiveSearchSimulator.ts": {"path": "/Users/<USER>/WebstormProjects/goo/src/mocks/ProgressiveSearchSimulator.ts", "statementMap": {"0": {"start": {"line": 7, "column": 25}, "end": {"line": 7, "column": 27}}, "1": {"start": {"line": 8, "column": 22}, "end": {"line": 8, "column": 23}}, "2": {"start": {"line": 9, "column": 18}, "end": {"line": 9, "column": 21}}, "3": {"start": {"line": 11, "column": 4}, "end": {"line": 27, "column": 5}}, "4": {"start": {"line": 11, "column": 17}, "end": {"line": 11, "column": 18}}, "5": {"start": {"line": 12, "column": 6}, "end": {"line": 12, "column": 63}}, "6": {"start": {"line": 12, "column": 35}, "end": {"line": 12, "column": 61}}, "7": {"start": {"line": 15, "column": 20}, "end": {"line": 20, "column": 7}}, "8": {"start": {"line": 22, "column": 6}, "end": {"line": 26, "column": 8}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": 3}}, "loc": {"start": {"line": 4, "column": 56}, "end": {"line": 4, "column": 58}}, "line": 4}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": 3}}, "loc": {"start": {"line": 6, "column": 63}, "end": {"line": 28, "column": 3}}, "line": 6}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 12, "column": 24}, "end": {"line": 12, "column": 25}}, "loc": {"start": {"line": 12, "column": 35}, "end": {"line": 12, "column": 61}}, "line": 12}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 4, "6": 4, "7": 4, "8": 4}, "f": {"0": 1, "1": 1, "2": 4}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "8a15f5742da00364f8d574ec1a74995aabd4810c"}, "/Users/<USER>/WebstormProjects/goo/src/models/Business.ts": {"path": "/Users/<USER>/WebstormProjects/goo/src/models/Business.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "/Users/<USER>/WebstormProjects/goo/src/models/Errors.ts": {"path": "/Users/<USER>/WebstormProjects/goo/src/models/Errors.ts", "statementMap": {"0": {"start": {"line": 9, "column": 4}, "end": {"line": 9, "column": 19}}, "1": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 38}}, "2": {"start": {"line": 13, "column": 4}, "end": {"line": 15, "column": 5}}, "3": {"start": {"line": 14, "column": 6}, "end": {"line": 14, "column": 54}}, "4": {"start": {"line": 23, "column": 18}, "end": {"line": 23, "column": 36}}, "5": {"start": {"line": 24, "column": 24}, "end": {"line": 24, "column": 27}}, "6": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 19}}, "7": {"start": {"line": 35, "column": 18}, "end": {"line": 35, "column": 29}}, "8": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 26}}, "9": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 33}}, "10": {"start": {"line": 53, "column": 18}, "end": {"line": 53, "column": 36}}, "11": {"start": {"line": 54, "column": 24}, "end": {"line": 54, "column": 27}}, "12": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 19}}, "13": {"start": {"line": 68, "column": 18}, "end": {"line": 68, "column": 33}}, "14": {"start": {"line": 69, "column": 24}, "end": {"line": 69, "column": 27}}, "15": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 26}}, "16": {"start": {"line": 80, "column": 18}, "end": {"line": 80, "column": 35}}, "17": {"start": {"line": 81, "column": 24}, "end": {"line": 81, "column": 27}}, "18": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 19}}, "19": {"start": {"line": 92, "column": 18}, "end": {"line": 92, "column": 46}}, "20": {"start": {"line": 93, "column": 24}, "end": {"line": 93, "column": 27}}, "21": {"start": {"line": 96, "column": 4}, "end": {"line": 96, "column": 26}}, "22": {"start": {"line": 104, "column": 18}, "end": {"line": 104, "column": 31}}, "23": {"start": {"line": 105, "column": 24}, "end": {"line": 105, "column": 27}}, "24": {"start": {"line": 108, "column": 4}, "end": {"line": 108, "column": 26}}, "25": {"start": {"line": 116, "column": 18}, "end": {"line": 116, "column": 39}}, "26": {"start": {"line": 117, "column": 24}, "end": {"line": 117, "column": 27}}, "27": {"start": {"line": 120, "column": 4}, "end": {"line": 120, "column": 19}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": 3}}, "loc": {"start": {"line": 8, "column": 62}, "end": {"line": 16, "column": 3}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 3}}, "loc": {"start": {"line": 26, "column": 63}, "end": {"line": 28, "column": 3}}, "line": 26}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": 3}}, "loc": {"start": {"line": 43, "column": 4}, "end": {"line": 46, "column": 3}}, "line": 43}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 56, "column": 2}, "end": {"line": 56, "column": 3}}, "loc": {"start": {"line": 59, "column": 4}, "end": {"line": 61, "column": 3}}, "line": 59}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 71, "column": 2}, "end": {"line": 71, "column": 3}}, "loc": {"start": {"line": 71, "column": 46}, "end": {"line": 73, "column": 3}}, "line": 71}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 83, "column": 2}, "end": {"line": 83, "column": 3}}, "loc": {"start": {"line": 83, "column": 65}, "end": {"line": 85, "column": 3}}, "line": 83}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 95, "column": 2}, "end": {"line": 95, "column": 3}}, "loc": {"start": {"line": 95, "column": 76}, "end": {"line": 97, "column": 3}}, "line": 95}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 107, "column": 2}, "end": {"line": 107, "column": 3}}, "loc": {"start": {"line": 107, "column": 46}, "end": {"line": 109, "column": 3}}, "line": 107}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 119, "column": 2}, "end": {"line": 119, "column": 3}}, "loc": {"start": {"line": 119, "column": 67}, "end": {"line": 121, "column": 3}}, "line": 119}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 4}, "end": {"line": 15, "column": 5}}, "type": "if", "locations": [{"start": {"line": 13, "column": 4}, "end": {"line": 15, "column": 5}}, {"start": {}, "end": {}}], "line": 13}, "1": {"loc": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 28}}, "type": "default-arg", "locations": [{"start": {"line": 40, "column": 25}, "end": {"line": 40, "column": 28}}], "line": 40}, "2": {"loc": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 43}}, "type": "default-arg", "locations": [{"start": {"line": 57, "column": 22}, "end": {"line": 57, "column": 43}}], "line": 57}}, "s": {"0": 35, "1": 35, "2": 35, "3": 35, "4": 3, "5": 3, "6": 3, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 7, "23": 7, "24": 7, "25": 25, "26": 25, "27": 25}, "f": {"0": 35, "1": 3, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 7, "8": 25}, "b": {"0": [35, 0], "1": [0], "2": [0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "d7f64ee398394ab4ddcae4da5fb5ab500708f3ef"}, "/Users/<USER>/WebstormProjects/goo/src/services/businessSearch.ts": {"path": "/Users/<USER>/WebstormProjects/goo/src/services/businessSearch.ts", "statementMap": {"0": {"start": {"line": 37, "column": 42}, "end": {"line": 37, "column": 44}}, "1": {"start": {"line": 38, "column": 24}, "end": {"line": 43, "column": 3}}, "2": {"start": {"line": 57, "column": 22}, "end": {"line": 57, "column": 32}}, "3": {"start": {"line": 59, "column": 4}, "end": {"line": 182, "column": 5}}, "4": {"start": {"line": 61, "column": 6}, "end": {"line": 65, "column": 9}}, "5": {"start": {"line": 68, "column": 26}, "end": {"line": 68, "column": 91}}, "6": {"start": {"line": 71, "column": 29}, "end": {"line": 71, "column": 53}}, "7": {"start": {"line": 72, "column": 27}, "end": {"line": 81, "column": 7}}, "8": {"start": {"line": 84, "column": 25}, "end": {"line": 87, "column": 7}}, "9": {"start": {"line": 90, "column": 33}, "end": {"line": 92, "column": 20}}, "10": {"start": {"line": 91, "column": 33}, "end": {"line": 91, "column": 84}}, "11": {"start": {"line": 95, "column": 6}, "end": {"line": 95, "column": 65}}, "12": {"start": {"line": 95, "column": 40}, "end": {"line": 95, "column": 63}}, "13": {"start": {"line": 98, "column": 31}, "end": {"line": 100, "column": 43}}, "14": {"start": {"line": 99, "column": 28}, "end": {"line": 99, "column": 44}}, "15": {"start": {"line": 100, "column": 25}, "end": {"line": 100, "column": 42}}, "16": {"start": {"line": 103, "column": 39}, "end": {"line": 103, "column": 41}}, "17": {"start": {"line": 104, "column": 6}, "end": {"line": 114, "column": 7}}, "18": {"start": {"line": 105, "column": 8}, "end": {"line": 110, "column": 9}}, "19": {"start": {"line": 106, "column": 10}, "end": {"line": 109, "column": 12}}, "20": {"start": {"line": 112, "column": 8}, "end": {"line": 112, "column": 60}}, "21": {"start": {"line": 117, "column": 30}, "end": {"line": 119, "column": 7}}, "22": {"start": {"line": 118, "column": 42}, "end": {"line": 118, "column": 62}}, "23": {"start": {"line": 122, "column": 39}, "end": {"line": 122, "column": 41}}, "24": {"start": {"line": 123, "column": 42}, "end": {"line": 123, "column": 44}}, "25": {"start": {"line": 125, "column": 6}, "end": {"line": 140, "column": 7}}, "26": {"start": {"line": 126, "column": 8}, "end": {"line": 139, "column": 9}}, "27": {"start": {"line": 127, "column": 31}, "end": {"line": 127, "column": 68}}, "28": {"start": {"line": 128, "column": 10}, "end": {"line": 135, "column": 11}}, "29": {"start": {"line": 129, "column": 12}, "end": {"line": 129, "column": 48}}, "30": {"start": {"line": 130, "column": 12}, "end": {"line": 130, "column": 67}}, "31": {"start": {"line": 131, "column": 12}, "end": {"line": 131, "column": 40}}, "32": {"start": {"line": 133, "column": 12}, "end": {"line": 133, "column": 50}}, "33": {"start": {"line": 134, "column": 12}, "end": {"line": 134, "column": 43}}, "34": {"start": {"line": 137, "column": 10}, "end": {"line": 137, "column": 42}}, "35": {"start": {"line": 138, "column": 10}, "end": {"line": 138, "column": 41}}, "36": {"start": {"line": 143, "column": 29}, "end": {"line": 143, "column": 51}}, "37": {"start": {"line": 144, "column": 25}, "end": {"line": 144, "column": 50}}, "38": {"start": {"line": 145, "column": 31}, "end": {"line": 145, "column": 50}}, "39": {"start": {"line": 146, "column": 34}, "end": {"line": 146, "column": 56}}, "40": {"start": {"line": 147, "column": 34}, "end": {"line": 147, "column": 84}}, "41": {"start": {"line": 150, "column": 41}, "end": {"line": 168, "column": 7}}, "42": {"start": {"line": 171, "column": 6}, "end": {"line": 171, "column": 45}}, "43": {"start": {"line": 172, "column": 6}, "end": {"line": 172, "column": 48}}, "44": {"start": {"line": 174, "column": 6}, "end": {"line": 174, "column": 26}}, "45": {"start": {"line": 177, "column": 29}, "end": {"line": 177, "column": 51}}, "46": {"start": {"line": 178, "column": 6}, "end": {"line": 178, "column": 39}}, "47": {"start": {"line": 179, "column": 6}, "end": {"line": 179, "column": 61}}, "48": {"start": {"line": 181, "column": 6}, "end": {"line": 181, "column": 18}}, "49": {"start": {"line": 195, "column": 4}, "end": {"line": 221, "column": 7}}, "50": {"start": {"line": 196, "column": 47}, "end": {"line": 199, "column": 7}}, "51": {"start": {"line": 201, "column": 23}, "end": {"line": 201, "column": 80}}, "52": {"start": {"line": 203, "column": 33}, "end": {"line": 218, "column": 7}}, "53": {"start": {"line": 220, "column": 6}, "end": {"line": 220, "column": 22}}, "54": {"start": {"line": 229, "column": 4}, "end": {"line": 229, "column": 45}}, "55": {"start": {"line": 232, "column": 4}, "end": {"line": 234, "column": 5}}, "56": {"start": {"line": 233, "column": 6}, "end": {"line": 233, "column": 59}}, "57": {"start": {"line": 242, "column": 4}, "end": {"line": 242, "column": 37}}, "58": {"start": {"line": 243, "column": 4}, "end": {"line": 243, "column": 72}}, "59": {"start": {"line": 244, "column": 4}, "end": {"line": 244, "column": 83}}, "60": {"start": {"line": 245, "column": 4}, "end": {"line": 245, "column": 83}}, "61": {"start": {"line": 253, "column": 4}, "end": {"line": 253, "column": 35}}, "62": {"start": {"line": 260, "column": 4}, "end": {"line": 260, "column": 28}}, "63": {"start": {"line": 268, "column": 4}, "end": {"line": 279, "column": 6}}, "64": {"start": {"line": 286, "column": 4}, "end": {"line": 286, "column": 28}}, "65": {"start": {"line": 287, "column": 4}, "end": {"line": 292, "column": 6}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 3}}, "loc": {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 6}}, "line": 49}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 56, "column": 2}, "end": {"line": 56, "column": 3}}, "loc": {"start": {"line": 56, "column": 72}, "end": {"line": 183, "column": 3}}, "line": 56}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 91, "column": 28}, "end": {"line": 91, "column": 29}}, "loc": {"start": {"line": 91, "column": 33}, "end": {"line": 91, "column": 84}}, "line": 91}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 95, "column": 30}, "end": {"line": 95, "column": 31}}, "loc": {"start": {"line": 95, "column": 40}, "end": {"line": 95, "column": 63}}, "line": 95}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 99, "column": 16}, "end": {"line": 99, "column": 17}}, "loc": {"start": {"line": 99, "column": 28}, "end": {"line": 99, "column": 44}}, "line": 99}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 100, "column": 13}, "end": {"line": 100, "column": 14}}, "loc": {"start": {"line": 100, "column": 25}, "end": {"line": 100, "column": 42}}, "line": 100}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 118, "column": 32}, "end": {"line": 118, "column": 33}}, "loc": {"start": {"line": 118, "column": 42}, "end": {"line": 118, "column": 62}}, "line": 118}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 191, "column": 2}, "end": {"line": 191, "column": 3}}, "loc": {"start": {"line": 194, "column": 25}, "end": {"line": 222, "column": 3}}, "line": 194}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 195, "column": 22}, "end": {"line": 195, "column": 23}}, "loc": {"start": {"line": 195, "column": 31}, "end": {"line": 221, "column": 5}}, "line": 195}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 228, "column": 2}, "end": {"line": 228, "column": 3}}, "loc": {"start": {"line": 228, "column": 64}, "end": {"line": 235, "column": 3}}, "line": 228}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 241, "column": 2}, "end": {"line": 241, "column": 3}}, "loc": {"start": {"line": 241, "column": 67}, "end": {"line": 246, "column": 3}}, "line": 241}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 252, "column": 2}, "end": {"line": 252, "column": 3}}, "loc": {"start": {"line": 252, "column": 37}, "end": {"line": 254, "column": 3}}, "line": 252}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 259, "column": 2}, "end": {"line": 259, "column": 3}}, "loc": {"start": {"line": 259, "column": 29}, "end": {"line": 261, "column": 3}}, "line": 259}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 267, "column": 2}, "end": {"line": 267, "column": 3}}, "loc": {"start": {"line": 267, "column": 42}, "end": {"line": 280, "column": 3}}, "line": 267}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 285, "column": 2}, "end": {"line": 285, "column": 3}}, "loc": {"start": {"line": 285, "column": 16}, "end": {"line": 293, "column": 3}}, "line": 285}}, "branchMap": {"0": {"loc": {"start": {"line": 77, "column": 20}, "end": {"line": 77, "column": 61}}, "type": "cond-expr", "locations": [{"start": {"line": 77, "column": 48}, "end": {"line": 77, "column": 49}}, {"start": {"line": 77, "column": 52}, "end": {"line": 77, "column": 61}}], "line": 77}, "1": {"loc": {"start": {"line": 90, "column": 33}, "end": {"line": 92, "column": 20}}, "type": "cond-expr", "locations": [{"start": {"line": 91, "column": 10}, "end": {"line": 91, "column": 85}}, {"start": {"line": 92, "column": 10}, "end": {"line": 92, "column": 20}}], "line": 90}, "2": {"loc": {"start": {"line": 91, "column": 33}, "end": {"line": 91, "column": 84}}, "type": "binary-expr", "locations": [{"start": {"line": 91, "column": 33}, "end": {"line": 91, "column": 41}}, {"start": {"line": 91, "column": 45}, "end": {"line": 91, "column": 84}}], "line": 91}, "3": {"loc": {"start": {"line": 105, "column": 8}, "end": {"line": 110, "column": 9}}, "type": "if", "locations": [{"start": {"line": 105, "column": 8}, "end": {"line": 110, "column": 9}}, {"start": {}, "end": {}}], "line": 105}, "4": {"loc": {"start": {"line": 126, "column": 8}, "end": {"line": 139, "column": 9}}, "type": "if", "locations": [{"start": {"line": 126, "column": 8}, "end": {"line": 139, "column": 9}}, {"start": {"line": 136, "column": 15}, "end": {"line": 139, "column": 9}}], "line": 126}, "5": {"loc": {"start": {"line": 128, "column": 10}, "end": {"line": 135, "column": 11}}, "type": "if", "locations": [{"start": {"line": 128, "column": 10}, "end": {"line": 135, "column": 11}}, {"start": {"line": 132, "column": 17}, "end": {"line": 135, "column": 11}}], "line": 128}, "6": {"loc": {"start": {"line": 128, "column": 14}, "end": {"line": 128, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 128, "column": 14}, "end": {"line": 128, "column": 26}}, {"start": {"line": 128, "column": 30}, "end": {"line": 128, "column": 53}}], "line": 128}, "7": {"loc": {"start": {"line": 147, "column": 34}, "end": {"line": 147, "column": 84}}, "type": "cond-expr", "locations": [{"start": {"line": 147, "column": 51}, "end": {"line": 147, "column": 80}}, {"start": {"line": 147, "column": 83}, "end": {"line": 147, "column": 84}}], "line": 147}, "8": {"loc": {"start": {"line": 232, "column": 4}, "end": {"line": 234, "column": 5}}, "type": "if", "locations": [{"start": {"line": 232, "column": 4}, "end": {"line": 234, "column": 5}}, {"start": {}, "end": {}}], "line": 232}, "9": {"loc": {"start": {"line": 270, "column": 31}, "end": {"line": 272, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 271, "column": 10}, "end": {"line": 271, "column": 72}}, {"start": {"line": 272, "column": 10}, "end": {"line": 272, "column": 11}}], "line": 270}, "10": {"loc": {"start": {"line": 273, "column": 34}, "end": {"line": 275, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 274, "column": 10}, "end": {"line": 274, "column": 76}}, {"start": {"line": 275, "column": 10}, "end": {"line": 275, "column": 11}}], "line": 273}, "11": {"loc": {"start": {"line": 276, "column": 29}, "end": {"line": 278, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 277, "column": 10}, "end": {"line": 277, "column": 79}}, {"start": {"line": 278, "column": 10}, "end": {"line": 278, "column": 11}}], "line": 276}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "e53f74385af8bf1618664dbf3f97afdcd4061ef4"}, "/Users/<USER>/WebstormProjects/goo/src/services/dataManager.ts": {"path": "/Users/<USER>/WebstormProjects/goo/src/services/dataManager.ts", "statementMap": {"0": {"start": {"line": 37, "column": 31}, "end": {"line": 37, "column": 54}}, "1": {"start": {"line": 38, "column": 34}, "end": {"line": 38, "column": 80}}, "2": {"start": {"line": 46, "column": 4}, "end": {"line": 60, "column": 5}}, "3": {"start": {"line": 47, "column": 18}, "end": {"line": 47, "column": 48}}, "4": {"start": {"line": 48, "column": 18}, "end": {"line": 48, "column": 28}}, "5": {"start": {"line": 50, "column": 37}, "end": {"line": 54, "column": 7}}, "6": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 60}}, "7": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 17}}, "8": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 118}}, "9": {"start": {"line": 69, "column": 4}, "end": {"line": 88, "column": 5}}, "10": {"start": {"line": 70, "column": 21}, "end": {"line": 70, "column": 46}}, "11": {"start": {"line": 71, "column": 6}, "end": {"line": 73, "column": 7}}, "12": {"start": {"line": 72, "column": 8}, "end": {"line": 72, "column": 20}}, "13": {"start": {"line": 75, "column": 37}, "end": {"line": 75, "column": 55}}, "14": {"start": {"line": 78, "column": 6}, "end": {"line": 81, "column": 7}}, "15": {"start": {"line": 79, "column": 8}, "end": {"line": 79, "column": 37}}, "16": {"start": {"line": 80, "column": 8}, "end": {"line": 80, "column": 20}}, "17": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 59}}, "18": {"start": {"line": 86, "column": 6}, "end": {"line": 86, "column": 35}}, "19": {"start": {"line": 87, "column": 6}, "end": {"line": 87, "column": 18}}, "20": {"start": {"line": 96, "column": 36}, "end": {"line": 96, "column": 38}}, "21": {"start": {"line": 97, "column": 35}, "end": {"line": 97, "column": 37}}, "22": {"start": {"line": 99, "column": 4}, "end": {"line": 122, "column": 5}}, "23": {"start": {"line": 99, "column": 17}, "end": {"line": 99, "column": 18}}, "24": {"start": {"line": 100, "column": 18}, "end": {"line": 100, "column": 37}}, "25": {"start": {"line": 101, "column": 6}, "end": {"line": 103, "column": 7}}, "26": {"start": {"line": 102, "column": 8}, "end": {"line": 102, "column": 17}}, "27": {"start": {"line": 105, "column": 6}, "end": {"line": 121, "column": 7}}, "28": {"start": {"line": 106, "column": 23}, "end": {"line": 106, "column": 48}}, "29": {"start": {"line": 107, "column": 8}, "end": {"line": 107, "column": 30}}, "30": {"start": {"line": 107, "column": 21}, "end": {"line": 107, "column": 30}}, "31": {"start": {"line": 109, "column": 39}, "end": {"line": 109, "column": 57}}, "32": {"start": {"line": 112, "column": 8}, "end": {"line": 115, "column": 9}}, "33": {"start": {"line": 113, "column": 10}, "end": {"line": 113, "column": 33}}, "34": {"start": {"line": 114, "column": 10}, "end": {"line": 114, "column": 19}}, "35": {"start": {"line": 117, "column": 8}, "end": {"line": 117, "column": 68}}, "36": {"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": 31}}, "37": {"start": {"line": 125, "column": 4}, "end": {"line": 125, "column": 62}}, "38": {"start": {"line": 125, "column": 32}, "end": {"line": 125, "column": 60}}, "39": {"start": {"line": 128, "column": 4}, "end": {"line": 130, "column": 6}}, "40": {"start": {"line": 129, "column": 6}, "end": {"line": 129, "column": 97}}, "41": {"start": {"line": 138, "column": 4}, "end": {"line": 138, "column": 33}}, "42": {"start": {"line": 145, "column": 35}, "end": {"line": 145, "column": 37}}, "43": {"start": {"line": 147, "column": 4}, "end": {"line": 152, "column": 5}}, "44": {"start": {"line": 147, "column": 17}, "end": {"line": 147, "column": 18}}, "45": {"start": {"line": 148, "column": 18}, "end": {"line": 148, "column": 37}}, "46": {"start": {"line": 149, "column": 6}, "end": {"line": 151, "column": 7}}, "47": {"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 31}}, "48": {"start": {"line": 154, "column": 4}, "end": {"line": 154, "column": 62}}, "49": {"start": {"line": 154, "column": 32}, "end": {"line": 154, "column": 60}}, "50": {"start": {"line": 162, "column": 23}, "end": {"line": 162, "column": 24}}, "51": {"start": {"line": 163, "column": 25}, "end": {"line": 163, "column": 26}}, "52": {"start": {"line": 164, "column": 41}, "end": {"line": 164, "column": 45}}, "53": {"start": {"line": 165, "column": 41}, "end": {"line": 165, "column": 45}}, "54": {"start": {"line": 167, "column": 4}, "end": {"line": 197, "column": 5}}, "55": {"start": {"line": 167, "column": 17}, "end": {"line": 167, "column": 18}}, "56": {"start": {"line": 168, "column": 18}, "end": {"line": 168, "column": 37}}, "57": {"start": {"line": 169, "column": 6}, "end": {"line": 171, "column": 7}}, "58": {"start": {"line": 170, "column": 8}, "end": {"line": 170, "column": 17}}, "59": {"start": {"line": 173, "column": 6}, "end": {"line": 196, "column": 7}}, "60": {"start": {"line": 174, "column": 23}, "end": {"line": 174, "column": 48}}, "61": {"start": {"line": 175, "column": 8}, "end": {"line": 175, "column": 30}}, "62": {"start": {"line": 175, "column": 21}, "end": {"line": 175, "column": 30}}, "63": {"start": {"line": 177, "column": 39}, "end": {"line": 177, "column": 57}}, "64": {"start": {"line": 180, "column": 8}, "end": {"line": 182, "column": 9}}, "65": {"start": {"line": 181, "column": 10}, "end": {"line": 181, "column": 19}}, "66": {"start": {"line": 184, "column": 8}, "end": {"line": 184, "column": 23}}, "67": {"start": {"line": 185, "column": 8}, "end": {"line": 185, "column": 40}}, "68": {"start": {"line": 187, "column": 8}, "end": {"line": 189, "column": 9}}, "69": {"start": {"line": 188, "column": 10}, "end": {"line": 188, "column": 49}}, "70": {"start": {"line": 190, "column": 8}, "end": {"line": 192, "column": 9}}, "71": {"start": {"line": 191, "column": 10}, "end": {"line": 191, "column": 49}}, "72": {"start": {"line": 195, "column": 8}, "end": {"line": 195, "column": 17}}, "73": {"start": {"line": 199, "column": 4}, "end": {"line": 204, "column": 6}}, "74": {"start": {"line": 212, "column": 35}, "end": {"line": 212, "column": 37}}, "75": {"start": {"line": 213, "column": 16}, "end": {"line": 213, "column": 26}}, "76": {"start": {"line": 215, "column": 4}, "end": {"line": 234, "column": 5}}, "77": {"start": {"line": 215, "column": 17}, "end": {"line": 215, "column": 18}}, "78": {"start": {"line": 216, "column": 18}, "end": {"line": 216, "column": 37}}, "79": {"start": {"line": 217, "column": 6}, "end": {"line": 219, "column": 7}}, "80": {"start": {"line": 218, "column": 8}, "end": {"line": 218, "column": 17}}, "81": {"start": {"line": 221, "column": 6}, "end": {"line": 233, "column": 7}}, "82": {"start": {"line": 222, "column": 23}, "end": {"line": 222, "column": 48}}, "83": {"start": {"line": 223, "column": 8}, "end": {"line": 223, "column": 30}}, "84": {"start": {"line": 223, "column": 21}, "end": {"line": 223, "column": 30}}, "85": {"start": {"line": 225, "column": 39}, "end": {"line": 225, "column": 57}}, "86": {"start": {"line": 227, "column": 8}, "end": {"line": 229, "column": 9}}, "87": {"start": {"line": 228, "column": 10}, "end": {"line": 228, "column": 33}}, "88": {"start": {"line": 232, "column": 8}, "end": {"line": 232, "column": 31}}, "89": {"start": {"line": 236, "column": 4}, "end": {"line": 236, "column": 62}}, "90": {"start": {"line": 236, "column": 32}, "end": {"line": 236, "column": 60}}, "91": {"start": {"line": 237, "column": 4}, "end": {"line": 237, "column": 31}}, "92": {"start": {"line": 245, "column": 26}, "end": {"line": 245, "column": 52}}, "93": {"start": {"line": 247, "column": 35}, "end": {"line": 251, "column": 5}}, "94": {"start": {"line": 253, "column": 4}, "end": {"line": 253, "column": 47}}, "95": {"start": {"line": 262, "column": 4}, "end": {"line": 287, "column": 5}}, "96": {"start": {"line": 263, "column": 37}, "end": {"line": 263, "column": 57}}, "97": {"start": {"line": 266, "column": 6}, "end": {"line": 268, "column": 7}}, "98": {"start": {"line": 267, "column": 8}, "end": {"line": 267, "column": 57}}, "99": {"start": {"line": 270, "column": 26}, "end": {"line": 270, "column": 27}}, "100": {"start": {"line": 272, "column": 6}, "end": {"line": 282, "column": 7}}, "101": {"start": {"line": 273, "column": 8}, "end": {"line": 281, "column": 9}}, "102": {"start": {"line": 275, "column": 37}, "end": {"line": 275, "column": 79}}, "103": {"start": {"line": 276, "column": 10}, "end": {"line": 276, "column": 52}}, "104": {"start": {"line": 277, "column": 10}, "end": {"line": 277, "column": 26}}, "105": {"start": {"line": 279, "column": 10}, "end": {"line": 279, "column": 65}}, "106": {"start": {"line": 284, "column": 6}, "end": {"line": 284, "column": 27}}, "107": {"start": {"line": 286, "column": 6}, "end": {"line": 286, "column": 111}}, "108": {"start": {"line": 296, "column": 19}, "end": {"line": 296, "column": 44}}, "109": {"start": {"line": 297, "column": 17}, "end": {"line": 297, "column": 123}}, "110": {"start": {"line": 298, "column": 4}, "end": {"line": 298, "column": 38}}, "111": {"start": {"line": 307, "column": 15}, "end": {"line": 307, "column": 16}}, "112": {"start": {"line": 308, "column": 4}, "end": {"line": 312, "column": 5}}, "113": {"start": {"line": 308, "column": 17}, "end": {"line": 308, "column": 18}}, "114": {"start": {"line": 309, "column": 19}, "end": {"line": 309, "column": 36}}, "115": {"start": {"line": 310, "column": 6}, "end": {"line": 310, "column": 41}}, "116": {"start": {"line": 311, "column": 6}, "end": {"line": 311, "column": 25}}, "117": {"start": {"line": 313, "column": 4}, "end": {"line": 313, "column": 39}}, "118": {"start": {"line": 321, "column": 16}, "end": {"line": 321, "column": 17}}, "119": {"start": {"line": 322, "column": 4}, "end": {"line": 327, "column": 5}}, "120": {"start": {"line": 322, "column": 17}, "end": {"line": 322, "column": 18}}, "121": {"start": {"line": 323, "column": 18}, "end": {"line": 323, "column": 37}}, "122": {"start": {"line": 324, "column": 6}, "end": {"line": 326, "column": 7}}, "123": {"start": {"line": 325, "column": 8}, "end": {"line": 325, "column": 16}}, "124": {"start": {"line": 328, "column": 4}, "end": {"line": 328, "column": 17}}, "125": {"start": {"line": 337, "column": 4}, "end": {"line": 359, "column": 6}}, "126": {"start": {"line": 344, "column": 72}, "end": {"line": 350, "column": 9}}, "127": {"start": {"line": 351, "column": 78}, "end": {"line": 357, "column": 9}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 3}}, "loc": {"start": {"line": 45, "column": 55}, "end": {"line": 61, "column": 3}}, "line": 45}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 68, "column": 2}, "end": {"line": 68, "column": 3}}, "loc": {"start": {"line": 68, "column": 52}, "end": {"line": 89, "column": 3}}, "line": 68}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 95, "column": 2}, "end": {"line": 95, "column": 3}}, "loc": {"start": {"line": 95, "column": 40}, "end": {"line": 131, "column": 3}}, "line": 95}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 125, "column": 25}, "end": {"line": 125, "column": 26}}, "loc": {"start": {"line": 125, "column": 32}, "end": {"line": 125, "column": 60}}, "line": 125}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 128, "column": 24}, "end": {"line": 128, "column": 25}}, "loc": {"start": {"line": 129, "column": 6}, "end": {"line": 129, "column": 97}}, "line": 129}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 137, "column": 2}, "end": {"line": 137, "column": 3}}, "loc": {"start": {"line": 137, "column": 40}, "end": {"line": 139, "column": 3}}, "line": 137}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 144, "column": 2}, "end": {"line": 144, "column": 3}}, "loc": {"start": {"line": 144, "column": 23}, "end": {"line": 155, "column": 3}}, "line": 144}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 154, "column": 25}, "end": {"line": 154, "column": 26}}, "loc": {"start": {"line": 154, "column": 32}, "end": {"line": 154, "column": 60}}, "line": 154}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 161, "column": 2}, "end": {"line": 161, "column": 3}}, "loc": {"start": {"line": 161, "column": 32}, "end": {"line": 205, "column": 3}}, "line": 161}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 211, "column": 2}, "end": {"line": 211, "column": 3}}, "loc": {"start": {"line": 211, "column": 31}, "end": {"line": 238, "column": 3}}, "line": 211}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 236, "column": 25}, "end": {"line": 236, "column": 26}}, "loc": {"start": {"line": 236, "column": 32}, "end": {"line": 236, "column": 60}}, "line": 236}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 244, "column": 2}, "end": {"line": 244, "column": 3}}, "loc": {"start": {"line": 244, "column": 23}, "end": {"line": 254, "column": 3}}, "line": 244}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 261, "column": 2}, "end": {"line": 261, "column": 3}}, "loc": {"start": {"line": 261, "column": 39}, "end": {"line": 288, "column": 3}}, "line": 261}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 295, "column": 2}, "end": {"line": 295, "column": 3}}, "loc": {"start": {"line": 295, "column": 58}, "end": {"line": 299, "column": 3}}, "line": 295}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 306, "column": 2}, "end": {"line": 306, "column": 3}}, "loc": {"start": {"line": 306, "column": 42}, "end": {"line": 314, "column": 3}}, "line": 306}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 320, "column": 2}, "end": {"line": 320, "column": 3}}, "loc": {"start": {"line": 320, "column": 25}, "end": {"line": 329, "column": 3}}, "line": 320}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 336, "column": 2}, "end": {"line": 336, "column": 3}}, "loc": {"start": {"line": 336, "column": 59}, "end": {"line": 360, "column": 3}}, "line": 336}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 344, "column": 52}, "end": {"line": 344, "column": 53}}, "loc": {"start": {"line": 344, "column": 72}, "end": {"line": 350, "column": 9}}, "line": 344}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 351, "column": 58}, "end": {"line": 351, "column": 59}}, "loc": {"start": {"line": 351, "column": 78}, "end": {"line": 357, "column": 9}}, "line": 351}}, "branchMap": {"0": {"loc": {"start": {"line": 59, "column": 60}, "end": {"line": 59, "column": 114}}, "type": "cond-expr", "locations": [{"start": {"line": 59, "column": 85}, "end": {"line": 59, "column": 98}}, {"start": {"line": 59, "column": 101}, "end": {"line": 59, "column": 114}}], "line": 59}, "1": {"loc": {"start": {"line": 71, "column": 6}, "end": {"line": 73, "column": 7}}, "type": "if", "locations": [{"start": {"line": 71, "column": 6}, "end": {"line": 73, "column": 7}}, {"start": {}, "end": {}}], "line": 71}, "2": {"loc": {"start": {"line": 78, "column": 6}, "end": {"line": 81, "column": 7}}, "type": "if", "locations": [{"start": {"line": 78, "column": 6}, "end": {"line": 81, "column": 7}}, {"start": {}, "end": {}}], "line": 78}, "3": {"loc": {"start": {"line": 101, "column": 6}, "end": {"line": 103, "column": 7}}, "type": "if", "locations": [{"start": {"line": 101, "column": 6}, "end": {"line": 103, "column": 7}}, {"start": {}, "end": {}}], "line": 101}, "4": {"loc": {"start": {"line": 101, "column": 10}, "end": {"line": 101, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 101, "column": 10}, "end": {"line": 101, "column": 14}}, {"start": {"line": 101, "column": 18}, "end": {"line": 101, "column": 49}}], "line": 101}, "5": {"loc": {"start": {"line": 107, "column": 8}, "end": {"line": 107, "column": 30}}, "type": "if", "locations": [{"start": {"line": 107, "column": 8}, "end": {"line": 107, "column": 30}}, {"start": {}, "end": {}}], "line": 107}, "6": {"loc": {"start": {"line": 112, "column": 8}, "end": {"line": 115, "column": 9}}, "type": "if", "locations": [{"start": {"line": 112, "column": 8}, "end": {"line": 115, "column": 9}}, {"start": {}, "end": {}}], "line": 112}, "7": {"loc": {"start": {"line": 149, "column": 6}, "end": {"line": 151, "column": 7}}, "type": "if", "locations": [{"start": {"line": 149, "column": 6}, "end": {"line": 151, "column": 7}}, {"start": {}, "end": {}}], "line": 149}, "8": {"loc": {"start": {"line": 149, "column": 10}, "end": {"line": 149, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 149, "column": 10}, "end": {"line": 149, "column": 13}}, {"start": {"line": 149, "column": 17}, "end": {"line": 149, "column": 47}}], "line": 149}, "9": {"loc": {"start": {"line": 169, "column": 6}, "end": {"line": 171, "column": 7}}, "type": "if", "locations": [{"start": {"line": 169, "column": 6}, "end": {"line": 171, "column": 7}}, {"start": {}, "end": {}}], "line": 169}, "10": {"loc": {"start": {"line": 169, "column": 10}, "end": {"line": 169, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 169, "column": 10}, "end": {"line": 169, "column": 14}}, {"start": {"line": 169, "column": 18}, "end": {"line": 169, "column": 49}}], "line": 169}, "11": {"loc": {"start": {"line": 175, "column": 8}, "end": {"line": 175, "column": 30}}, "type": "if", "locations": [{"start": {"line": 175, "column": 8}, "end": {"line": 175, "column": 30}}, {"start": {}, "end": {}}], "line": 175}, "12": {"loc": {"start": {"line": 180, "column": 8}, "end": {"line": 182, "column": 9}}, "type": "if", "locations": [{"start": {"line": 180, "column": 8}, "end": {"line": 182, "column": 9}}, {"start": {}, "end": {}}], "line": 180}, "13": {"loc": {"start": {"line": 187, "column": 8}, "end": {"line": 189, "column": 9}}, "type": "if", "locations": [{"start": {"line": 187, "column": 8}, "end": {"line": 189, "column": 9}}, {"start": {}, "end": {}}], "line": 187}, "14": {"loc": {"start": {"line": 187, "column": 12}, "end": {"line": 187, "column": 78}}, "type": "binary-expr", "locations": [{"start": {"line": 187, "column": 12}, "end": {"line": 187, "column": 36}}, {"start": {"line": 187, "column": 40}, "end": {"line": 187, "column": 78}}], "line": 187}, "15": {"loc": {"start": {"line": 190, "column": 8}, "end": {"line": 192, "column": 9}}, "type": "if", "locations": [{"start": {"line": 190, "column": 8}, "end": {"line": 192, "column": 9}}, {"start": {}, "end": {}}], "line": 190}, "16": {"loc": {"start": {"line": 190, "column": 12}, "end": {"line": 190, "column": 78}}, "type": "binary-expr", "locations": [{"start": {"line": 190, "column": 12}, "end": {"line": 190, "column": 36}}, {"start": {"line": 190, "column": 40}, "end": {"line": 190, "column": 78}}], "line": 190}, "17": {"loc": {"start": {"line": 202, "column": 19}, "end": {"line": 202, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 202, "column": 37}, "end": {"line": 202, "column": 62}}, {"start": {"line": 202, "column": 65}, "end": {"line": 202, "column": 69}}], "line": 202}, "18": {"loc": {"start": {"line": 203, "column": 19}, "end": {"line": 203, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 203, "column": 37}, "end": {"line": 203, "column": 62}}, {"start": {"line": 203, "column": 65}, "end": {"line": 203, "column": 69}}], "line": 203}, "19": {"loc": {"start": {"line": 217, "column": 6}, "end": {"line": 219, "column": 7}}, "type": "if", "locations": [{"start": {"line": 217, "column": 6}, "end": {"line": 219, "column": 7}}, {"start": {}, "end": {}}], "line": 217}, "20": {"loc": {"start": {"line": 217, "column": 10}, "end": {"line": 217, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 217, "column": 10}, "end": {"line": 217, "column": 14}}, {"start": {"line": 217, "column": 18}, "end": {"line": 217, "column": 49}}], "line": 217}, "21": {"loc": {"start": {"line": 223, "column": 8}, "end": {"line": 223, "column": 30}}, "type": "if", "locations": [{"start": {"line": 223, "column": 8}, "end": {"line": 223, "column": 30}}, {"start": {}, "end": {}}], "line": 223}, "22": {"loc": {"start": {"line": 227, "column": 8}, "end": {"line": 229, "column": 9}}, "type": "if", "locations": [{"start": {"line": 227, "column": 8}, "end": {"line": 229, "column": 9}}, {"start": {}, "end": {}}], "line": 227}, "23": {"loc": {"start": {"line": 266, "column": 6}, "end": {"line": 268, "column": 7}}, "type": "if", "locations": [{"start": {"line": 266, "column": 6}, "end": {"line": 268, "column": 7}}, {"start": {}, "end": {}}], "line": 266}, "24": {"loc": {"start": {"line": 266, "column": 10}, "end": {"line": 266, "column": 79}}, "type": "binary-expr", "locations": [{"start": {"line": 266, "column": 10}, "end": {"line": 266, "column": 35}}, {"start": {"line": 266, "column": 39}, "end": {"line": 266, "column": 79}}], "line": 266}, "25": {"loc": {"start": {"line": 286, "column": 53}, "end": {"line": 286, "column": 107}}, "type": "cond-expr", "locations": [{"start": {"line": 286, "column": 78}, "end": {"line": 286, "column": 91}}, {"start": {"line": 286, "column": 94}, "end": {"line": 286, "column": 107}}], "line": 286}, "26": {"loc": {"start": {"line": 324, "column": 6}, "end": {"line": 326, "column": 7}}, "type": "if", "locations": [{"start": {"line": 324, "column": 6}, "end": {"line": 326, "column": 7}}, {"start": {}, "end": {}}], "line": 324}, "27": {"loc": {"start": {"line": 324, "column": 10}, "end": {"line": 324, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 324, "column": 10}, "end": {"line": 324, "column": 13}}, {"start": {"line": 324, "column": 17}, "end": {"line": 324, "column": 47}}], "line": 324}}, "s": {"0": 41, "1": 41, "2": 34, "3": 34, "4": 34, "5": 34, "6": 34, "7": 32, "8": 2, "9": 10, "10": 10, "11": 10, "12": 5, "13": 5, "14": 3, "15": 2, "16": 2, "17": 1, "18": 2, "19": 2, "20": 12, "21": 12, "22": 12, "23": 12, "24": 10, "25": 10, "26": 3, "27": 7, "28": 7, "29": 7, "30": 0, "31": 7, "32": 7, "33": 1, "34": 1, "35": 6, "36": 0, "37": 12, "38": 1, "39": 12, "40": 3, "41": 4, "42": 4, "43": 4, "44": 4, "45": 5, "46": 5, "47": 3, "48": 4, "49": 3, "50": 3, "51": 3, "52": 3, "53": 3, "54": 3, "55": 3, "56": 2, "57": 2, "58": 0, "59": 2, "60": 2, "61": 2, "62": 0, "63": 2, "64": 2, "65": 0, "66": 2, "67": 2, "68": 2, "69": 2, "70": 2, "71": 1, "72": 0, "73": 3, "74": 3, "75": 3, "76": 3, "77": 3, "78": 4, "79": 4, "80": 1, "81": 3, "82": 3, "83": 3, "84": 0, "85": 3, "86": 3, "87": 1, "88": 0, "89": 3, "90": 1, "91": 3, "92": 3, "93": 3, "94": 3, "95": 6, "96": 6, "97": 4, "98": 2, "99": 2, "100": 2, "101": 2, "102": 2, "103": 2, "104": 2, "105": 0, "106": 2, "107": 4, "108": 34, "109": 34, "110": 34, "111": 34, "112": 34, "113": 34, "114": 1122, "115": 1122, "116": 1122, "117": 34, "118": 1, "119": 1, "120": 1, "121": 1, "122": 1, "123": 0, "124": 1, "125": 9, "126": 9, "127": 9}, "f": {"0": 34, "1": 10, "2": 12, "3": 1, "4": 3, "5": 4, "6": 4, "7": 3, "8": 3, "9": 3, "10": 1, "11": 3, "12": 6, "13": 34, "14": 34, "15": 1, "16": 9, "17": 9, "18": 9}, "b": {"0": [2, 0], "1": [5, 5], "2": [2, 1], "3": [3, 7], "4": [10, 8], "5": [0, 7], "6": [1, 6], "7": [3, 2], "8": [5, 4], "9": [0, 2], "10": [2, 2], "11": [0, 2], "12": [0, 2], "13": [2, 0], "14": [2, 1], "15": [1, 1], "16": [2, 1], "17": [1, 2], "18": [1, 2], "19": [1, 3], "20": [4, 3], "21": [0, 3], "22": [1, 2], "23": [2, 2], "24": [4, 2], "25": [4, 0], "26": [0, 1], "27": [1, 1]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "e3715ec9ae743a4d0ea758899f19929db75407f7"}, "/Users/<USER>/WebstormProjects/goo/src/services/geocoding.ts": {"path": "/Users/<USER>/WebstormProjects/goo/src/services/geocoding.ts", "statementMap": {"0": {"start": {"line": 36, "column": 18}, "end": {"line": 36, "column": 57}}, "1": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 68}}, "2": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 49}}, "3": {"start": {"line": 44, "column": 4}, "end": {"line": 46, "column": 5}}, "4": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 97}}, "5": {"start": {"line": 57, "column": 4}, "end": {"line": 61, "column": 5}}, "6": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 31}}, "7": {"start": {"line": 60, "column": 6}, "end": {"line": 60, "column": 79}}, "8": {"start": {"line": 64, "column": 21}, "end": {"line": 64, "column": 37}}, "9": {"start": {"line": 65, "column": 19}, "end": {"line": 65, "column": 43}}, "10": {"start": {"line": 66, "column": 4}, "end": {"line": 68, "column": 5}}, "11": {"start": {"line": 67, "column": 6}, "end": {"line": 67, "column": 35}}, "12": {"start": {"line": 70, "column": 4}, "end": {"line": 131, "column": 5}}, "13": {"start": {"line": 71, "column": 18}, "end": {"line": 71, "column": 98}}, "14": {"start": {"line": 73, "column": 23}, "end": {"line": 78, "column": 8}}, "15": {"start": {"line": 80, "column": 6}, "end": {"line": 86, "column": 7}}, "16": {"start": {"line": 81, "column": 8}, "end": {"line": 85, "column": 10}}, "17": {"start": {"line": 88, "column": 36}, "end": {"line": 88, "column": 57}}, "18": {"start": {"line": 90, "column": 6}, "end": {"line": 98, "column": 7}}, "19": {"start": {"line": 91, "column": 8}, "end": {"line": 93, "column": 9}}, "20": {"start": {"line": 92, "column": 10}, "end": {"line": 92, "column": 89}}, "21": {"start": {"line": 94, "column": 8}, "end": {"line": 97, "column": 10}}, "22": {"start": {"line": 100, "column": 6}, "end": {"line": 102, "column": 7}}, "23": {"start": {"line": 101, "column": 8}, "end": {"line": 101, "column": 87}}, "24": {"start": {"line": 104, "column": 21}, "end": {"line": 104, "column": 36}}, "25": {"start": {"line": 105, "column": 6}, "end": {"line": 107, "column": 7}}, "26": {"start": {"line": 106, "column": 8}, "end": {"line": 106, "column": 94}}, "27": {"start": {"line": 109, "column": 39}, "end": {"line": 112, "column": 7}}, "28": {"start": {"line": 115, "column": 6}, "end": {"line": 115, "column": 44}}, "29": {"start": {"line": 117, "column": 6}, "end": {"line": 117, "column": 25}}, "30": {"start": {"line": 119, "column": 6}, "end": {"line": 121, "column": 7}}, "31": {"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": 20}}, "32": {"start": {"line": 123, "column": 6}, "end": {"line": 128, "column": 7}}, "33": {"start": {"line": 124, "column": 8}, "end": {"line": 126, "column": 9}}, "34": {"start": {"line": 125, "column": 10}, "end": {"line": 125, "column": 71}}, "35": {"start": {"line": 127, "column": 8}, "end": {"line": 127, "column": 90}}, "36": {"start": {"line": 130, "column": 6}, "end": {"line": 130, "column": 92}}, "37": {"start": {"line": 141, "column": 21}, "end": {"line": 141, "column": 78}}, "38": {"start": {"line": 142, "column": 19}, "end": {"line": 142, "column": 43}}, "39": {"start": {"line": 143, "column": 4}, "end": {"line": 145, "column": 5}}, "40": {"start": {"line": 144, "column": 6}, "end": {"line": 144, "column": 20}}, "41": {"start": {"line": 147, "column": 4}, "end": {"line": 204, "column": 5}}, "42": {"start": {"line": 148, "column": 18}, "end": {"line": 148, "column": 115}}, "43": {"start": {"line": 150, "column": 23}, "end": {"line": 155, "column": 8}}, "44": {"start": {"line": 157, "column": 6}, "end": {"line": 163, "column": 7}}, "45": {"start": {"line": 158, "column": 8}, "end": {"line": 162, "column": 10}}, "46": {"start": {"line": 165, "column": 36}, "end": {"line": 165, "column": 57}}, "47": {"start": {"line": 167, "column": 6}, "end": {"line": 171, "column": 7}}, "48": {"start": {"line": 168, "column": 8}, "end": {"line": 170, "column": 10}}, "49": {"start": {"line": 173, "column": 6}, "end": {"line": 175, "column": 7}}, "50": {"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 69}}, "51": {"start": {"line": 178, "column": 6}, "end": {"line": 188, "column": 7}}, "52": {"start": {"line": 179, "column": 8}, "end": {"line": 187, "column": 9}}, "53": {"start": {"line": 180, "column": 10}, "end": {"line": 186, "column": 11}}, "54": {"start": {"line": 181, "column": 12}, "end": {"line": 185, "column": 13}}, "55": {"start": {"line": 182, "column": 30}, "end": {"line": 182, "column": 49}}, "56": {"start": {"line": 183, "column": 14}, "end": {"line": 183, "column": 48}}, "57": {"start": {"line": 184, "column": 14}, "end": {"line": 184, "column": 29}}, "58": {"start": {"line": 190, "column": 6}, "end": {"line": 190, "column": 71}}, "59": {"start": {"line": 192, "column": 6}, "end": {"line": 194, "column": 7}}, "60": {"start": {"line": 193, "column": 8}, "end": {"line": 193, "column": 20}}, "61": {"start": {"line": 196, "column": 6}, "end": {"line": 201, "column": 7}}, "62": {"start": {"line": 197, "column": 8}, "end": {"line": 199, "column": 9}}, "63": {"start": {"line": 198, "column": 10}, "end": {"line": 198, "column": 79}}, "64": {"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 98}}, "65": {"start": {"line": 203, "column": 6}, "end": {"line": 203, "column": 91}}, "66": {"start": {"line": 211, "column": 4}, "end": {"line": 211, "column": 23}}, "67": {"start": {"line": 219, "column": 4}, "end": {"line": 219, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": 3}}, "loc": {"start": {"line": 40, "column": 31}, "end": {"line": 47, "column": 3}}, "line": 40}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": 3}}, "loc": {"start": {"line": 55, "column": 68}, "end": {"line": 132, "column": 3}}, "line": 55}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 140, "column": 2}, "end": {"line": 140, "column": 3}}, "loc": {"start": {"line": 140, "column": 72}, "end": {"line": 205, "column": 3}}, "line": 140}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 210, "column": 2}, "end": {"line": 210, "column": 3}}, "loc": {"start": {"line": 210, "column": 21}, "end": {"line": 212, "column": 3}}, "line": 210}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 218, "column": 2}, "end": {"line": 218, "column": 3}}, "loc": {"start": {"line": 218, "column": 25}, "end": {"line": 220, "column": 3}}, "line": 218}}, "branchMap": {"0": {"loc": {"start": {"line": 41, "column": 18}, "end": {"line": 41, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 18}, "end": {"line": 41, "column": 24}}, {"start": {"line": 41, "column": 28}, "end": {"line": 41, "column": 61}}, {"start": {"line": 41, "column": 65}, "end": {"line": 41, "column": 67}}], "line": 41}, "1": {"loc": {"start": {"line": 44, "column": 4}, "end": {"line": 46, "column": 5}}, "type": "if", "locations": [{"start": {"line": 44, "column": 4}, "end": {"line": 46, "column": 5}}, {"start": {}, "end": {}}], "line": 44}, "2": {"loc": {"start": {"line": 66, "column": 4}, "end": {"line": 68, "column": 5}}, "type": "if", "locations": [{"start": {"line": 66, "column": 4}, "end": {"line": 68, "column": 5}}, {"start": {}, "end": {}}], "line": 66}, "3": {"loc": {"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 14}}, {"start": {"line": 66, "column": 18}, "end": {"line": 66, "column": 44}}], "line": 66}, "4": {"loc": {"start": {"line": 80, "column": 6}, "end": {"line": 86, "column": 7}}, "type": "if", "locations": [{"start": {"line": 80, "column": 6}, "end": {"line": 86, "column": 7}}, {"start": {}, "end": {}}], "line": 80}, "5": {"loc": {"start": {"line": 90, "column": 6}, "end": {"line": 98, "column": 7}}, "type": "if", "locations": [{"start": {"line": 90, "column": 6}, "end": {"line": 98, "column": 7}}, {"start": {}, "end": {}}], "line": 90}, "6": {"loc": {"start": {"line": 91, "column": 8}, "end": {"line": 93, "column": 9}}, "type": "if", "locations": [{"start": {"line": 91, "column": 8}, "end": {"line": 93, "column": 9}}, {"start": {}, "end": {}}], "line": 91}, "7": {"loc": {"start": {"line": 95, "column": 10}, "end": {"line": 95, "column": 78}}, "type": "binary-expr", "locations": [{"start": {"line": 95, "column": 10}, "end": {"line": 95, "column": 28}}, {"start": {"line": 95, "column": 32}, "end": {"line": 95, "column": 78}}], "line": 95}, "8": {"loc": {"start": {"line": 100, "column": 6}, "end": {"line": 102, "column": 7}}, "type": "if", "locations": [{"start": {"line": 100, "column": 6}, "end": {"line": 102, "column": 7}}, {"start": {}, "end": {}}], "line": 100}, "9": {"loc": {"start": {"line": 100, "column": 10}, "end": {"line": 100, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 100, "column": 10}, "end": {"line": 100, "column": 23}}, {"start": {"line": 100, "column": 27}, "end": {"line": 100, "column": 52}}], "line": 100}, "10": {"loc": {"start": {"line": 105, "column": 6}, "end": {"line": 107, "column": 7}}, "type": "if", "locations": [{"start": {"line": 105, "column": 6}, "end": {"line": 107, "column": 7}}, {"start": {}, "end": {}}], "line": 105}, "11": {"loc": {"start": {"line": 105, "column": 10}, "end": {"line": 105, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 105, "column": 10}, "end": {"line": 105, "column": 26}}, {"start": {"line": 105, "column": 30}, "end": {"line": 105, "column": 55}}], "line": 105}, "12": {"loc": {"start": {"line": 119, "column": 6}, "end": {"line": 121, "column": 7}}, "type": "if", "locations": [{"start": {"line": 119, "column": 6}, "end": {"line": 121, "column": 7}}, {"start": {}, "end": {}}], "line": 119}, "13": {"loc": {"start": {"line": 119, "column": 10}, "end": {"line": 119, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 119, "column": 10}, "end": {"line": 119, "column": 41}}, {"start": {"line": 119, "column": 45}, "end": {"line": 119, "column": 70}}], "line": 119}, "14": {"loc": {"start": {"line": 123, "column": 6}, "end": {"line": 128, "column": 7}}, "type": "if", "locations": [{"start": {"line": 123, "column": 6}, "end": {"line": 128, "column": 7}}, {"start": {}, "end": {}}], "line": 123}, "15": {"loc": {"start": {"line": 124, "column": 8}, "end": {"line": 126, "column": 9}}, "type": "if", "locations": [{"start": {"line": 124, "column": 8}, "end": {"line": 126, "column": 9}}, {"start": {}, "end": {}}], "line": 124}, "16": {"loc": {"start": {"line": 143, "column": 4}, "end": {"line": 145, "column": 5}}, "type": "if", "locations": [{"start": {"line": 143, "column": 4}, "end": {"line": 145, "column": 5}}, {"start": {}, "end": {}}], "line": 143}, "17": {"loc": {"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 14}}, {"start": {"line": 143, "column": 18}, "end": {"line": 143, "column": 44}}], "line": 143}, "18": {"loc": {"start": {"line": 157, "column": 6}, "end": {"line": 163, "column": 7}}, "type": "if", "locations": [{"start": {"line": 157, "column": 6}, "end": {"line": 163, "column": 7}}, {"start": {}, "end": {}}], "line": 157}, "19": {"loc": {"start": {"line": 167, "column": 6}, "end": {"line": 171, "column": 7}}, "type": "if", "locations": [{"start": {"line": 167, "column": 6}, "end": {"line": 171, "column": 7}}, {"start": {}, "end": {}}], "line": 167}, "20": {"loc": {"start": {"line": 169, "column": 10}, "end": {"line": 169, "column": 86}}, "type": "binary-expr", "locations": [{"start": {"line": 169, "column": 10}, "end": {"line": 169, "column": 28}}, {"start": {"line": 169, "column": 32}, "end": {"line": 169, "column": 86}}], "line": 169}, "21": {"loc": {"start": {"line": 173, "column": 6}, "end": {"line": 175, "column": 7}}, "type": "if", "locations": [{"start": {"line": 173, "column": 6}, "end": {"line": 175, "column": 7}}, {"start": {}, "end": {}}], "line": 173}, "22": {"loc": {"start": {"line": 173, "column": 10}, "end": {"line": 173, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 173, "column": 10}, "end": {"line": 173, "column": 23}}, {"start": {"line": 173, "column": 27}, "end": {"line": 173, "column": 52}}], "line": 173}, "23": {"loc": {"start": {"line": 179, "column": 8}, "end": {"line": 187, "column": 9}}, "type": "if", "locations": [{"start": {"line": 179, "column": 8}, "end": {"line": 187, "column": 9}}, {"start": {}, "end": {}}], "line": 179}, "24": {"loc": {"start": {"line": 181, "column": 12}, "end": {"line": 185, "column": 13}}, "type": "if", "locations": [{"start": {"line": 181, "column": 12}, "end": {"line": 185, "column": 13}}, {"start": {}, "end": {}}], "line": 181}, "25": {"loc": {"start": {"line": 192, "column": 6}, "end": {"line": 194, "column": 7}}, "type": "if", "locations": [{"start": {"line": 192, "column": 6}, "end": {"line": 194, "column": 7}}, {"start": {}, "end": {}}], "line": 192}, "26": {"loc": {"start": {"line": 192, "column": 10}, "end": {"line": 192, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 192, "column": 10}, "end": {"line": 192, "column": 41}}, {"start": {"line": 192, "column": 45}, "end": {"line": 192, "column": 70}}], "line": 192}, "27": {"loc": {"start": {"line": 196, "column": 6}, "end": {"line": 201, "column": 7}}, "type": "if", "locations": [{"start": {"line": 196, "column": 6}, "end": {"line": 201, "column": 7}}, {"start": {}, "end": {}}], "line": 196}, "28": {"loc": {"start": {"line": 197, "column": 8}, "end": {"line": 199, "column": 9}}, "type": "if", "locations": [{"start": {"line": 197, "column": 8}, "end": {"line": 199, "column": 9}}, {"start": {}, "end": {}}], "line": 197}}, "s": {"0": 22, "1": 22, "2": 22, "3": 22, "4": 22, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0}, "f": {"0": 22, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [22, 22, 22], "1": [22, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "9336ac93cc909381e5c65a46b49f78286c36ab70"}, "/Users/<USER>/WebstormProjects/goo/src/services/googlePlaces.ts": {"path": "/Users/<USER>/WebstormProjects/goo/src/services/googlePlaces.ts", "statementMap": {"0": {"start": {"line": 47, "column": 18}, "end": {"line": 47, "column": 40}}, "1": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 68}}, "2": {"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 53}}, "3": {"start": {"line": 55, "column": 4}, "end": {"line": 57, "column": 5}}, "4": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 97}}, "5": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": 35}}, "6": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": 46}}, "7": {"start": {"line": 78, "column": 4}, "end": {"line": 80, "column": 5}}, "8": {"start": {"line": 79, "column": 6}, "end": {"line": 79, "column": 55}}, "9": {"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 5}}, "10": {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 55}}, "11": {"start": {"line": 85, "column": 25}, "end": {"line": 90, "column": 6}}, "12": {"start": {"line": 93, "column": 4}, "end": {"line": 95, "column": 5}}, "13": {"start": {"line": 94, "column": 6}, "end": {"line": 94, "column": 66}}, "14": {"start": {"line": 96, "column": 4}, "end": {"line": 98, "column": 5}}, "15": {"start": {"line": 97, "column": 6}, "end": {"line": 97, "column": 66}}, "16": {"start": {"line": 99, "column": 4}, "end": {"line": 101, "column": 5}}, "17": {"start": {"line": 100, "column": 6}, "end": {"line": 100, "column": 64}}, "18": {"start": {"line": 102, "column": 4}, "end": {"line": 104, "column": 5}}, "19": {"start": {"line": 103, "column": 6}, "end": {"line": 103, "column": 53}}, "20": {"start": {"line": 106, "column": 16}, "end": {"line": 106, "column": 78}}, "21": {"start": {"line": 108, "column": 4}, "end": {"line": 157, "column": 5}}, "22": {"start": {"line": 109, "column": 23}, "end": {"line": 114, "column": 8}}, "23": {"start": {"line": 116, "column": 6}, "end": {"line": 122, "column": 7}}, "24": {"start": {"line": 117, "column": 8}, "end": {"line": 121, "column": 10}}, "25": {"start": {"line": 124, "column": 38}, "end": {"line": 124, "column": 59}}, "26": {"start": {"line": 126, "column": 6}, "end": {"line": 132, "column": 7}}, "27": {"start": {"line": 127, "column": 8}, "end": {"line": 131, "column": 10}}, "28": {"start": {"line": 134, "column": 21}, "end": {"line": 134, "column": 64}}, "29": {"start": {"line": 136, "column": 6}, "end": {"line": 146, "column": 8}}, "30": {"start": {"line": 148, "column": 6}, "end": {"line": 150, "column": 7}}, "31": {"start": {"line": 149, "column": 8}, "end": {"line": 149, "column": 20}}, "32": {"start": {"line": 152, "column": 6}, "end": {"line": 154, "column": 7}}, "33": {"start": {"line": 153, "column": 8}, "end": {"line": 153, "column": 94}}, "34": {"start": {"line": 156, "column": 6}, "end": {"line": 156, "column": 85}}, "35": {"start": {"line": 172, "column": 4}, "end": {"line": 172, "column": 37}}, "36": {"start": {"line": 174, "column": 25}, "end": {"line": 177, "column": 6}}, "37": {"start": {"line": 179, "column": 4}, "end": {"line": 181, "column": 5}}, "38": {"start": {"line": 180, "column": 6}, "end": {"line": 180, "column": 84}}, "39": {"start": {"line": 182, "column": 4}, "end": {"line": 184, "column": 5}}, "40": {"start": {"line": 183, "column": 6}, "end": {"line": 183, "column": 55}}, "41": {"start": {"line": 186, "column": 16}, "end": {"line": 186, "column": 76}}, "42": {"start": {"line": 188, "column": 4}, "end": {"line": 236, "column": 5}}, "43": {"start": {"line": 189, "column": 23}, "end": {"line": 194, "column": 8}}, "44": {"start": {"line": 196, "column": 6}, "end": {"line": 202, "column": 7}}, "45": {"start": {"line": 197, "column": 8}, "end": {"line": 201, "column": 10}}, "46": {"start": {"line": 204, "column": 38}, "end": {"line": 204, "column": 59}}, "47": {"start": {"line": 206, "column": 6}, "end": {"line": 212, "column": 7}}, "48": {"start": {"line": 207, "column": 8}, "end": {"line": 211, "column": 10}}, "49": {"start": {"line": 214, "column": 21}, "end": {"line": 214, "column": 64}}, "50": {"start": {"line": 216, "column": 6}, "end": {"line": 225, "column": 8}}, "51": {"start": {"line": 227, "column": 6}, "end": {"line": 229, "column": 7}}, "52": {"start": {"line": 228, "column": 8}, "end": {"line": 228, "column": 20}}, "53": {"start": {"line": 231, "column": 6}, "end": {"line": 233, "column": 7}}, "54": {"start": {"line": 232, "column": 8}, "end": {"line": 232, "column": 92}}, "55": {"start": {"line": 235, "column": 6}, "end": {"line": 235, "column": 83}}, "56": {"start": {"line": 246, "column": 4}, "end": {"line": 246, "column": 41}}, "57": {"start": {"line": 249, "column": 21}, "end": {"line": 249, "column": 41}}, "58": {"start": {"line": 250, "column": 19}, "end": {"line": 250, "column": 43}}, "59": {"start": {"line": 251, "column": 4}, "end": {"line": 253, "column": 5}}, "60": {"start": {"line": 252, "column": 6}, "end": {"line": 252, "column": 20}}, "61": {"start": {"line": 255, "column": 25}, "end": {"line": 258, "column": 6}}, "62": {"start": {"line": 260, "column": 4}, "end": {"line": 262, "column": 5}}, "63": {"start": {"line": 261, "column": 6}, "end": {"line": 261, "column": 54}}, "64": {"start": {"line": 264, "column": 16}, "end": {"line": 264, "column": 73}}, "65": {"start": {"line": 266, "column": 4}, "end": {"line": 308, "column": 5}}, "66": {"start": {"line": 267, "column": 23}, "end": {"line": 272, "column": 8}}, "67": {"start": {"line": 274, "column": 6}, "end": {"line": 280, "column": 7}}, "68": {"start": {"line": 275, "column": 8}, "end": {"line": 279, "column": 10}}, "69": {"start": {"line": 282, "column": 41}, "end": {"line": 282, "column": 62}}, "70": {"start": {"line": 284, "column": 6}, "end": {"line": 290, "column": 7}}, "71": {"start": {"line": 285, "column": 8}, "end": {"line": 289, "column": 10}}, "72": {"start": {"line": 292, "column": 20}, "end": {"line": 292, "column": 58}}, "73": {"start": {"line": 295, "column": 6}, "end": {"line": 295, "column": 38}}, "74": {"start": {"line": 297, "column": 6}, "end": {"line": 297, "column": 19}}, "75": {"start": {"line": 299, "column": 6}, "end": {"line": 301, "column": 7}}, "76": {"start": {"line": 300, "column": 8}, "end": {"line": 300, "column": 20}}, "77": {"start": {"line": 303, "column": 6}, "end": {"line": 305, "column": 7}}, "78": {"start": {"line": 304, "column": 8}, "end": {"line": 304, "column": 94}}, "79": {"start": {"line": 307, "column": 6}, "end": {"line": 307, "column": 85}}, "80": {"start": {"line": 317, "column": 4}, "end": {"line": 317, "column": 45}}, "81": {"start": {"line": 319, "column": 25}, "end": {"line": 322, "column": 6}}, "82": {"start": {"line": 324, "column": 16}, "end": {"line": 324, "column": 78}}, "83": {"start": {"line": 326, "column": 4}, "end": {"line": 369, "column": 5}}, "84": {"start": {"line": 327, "column": 23}, "end": {"line": 332, "column": 8}}, "85": {"start": {"line": 334, "column": 6}, "end": {"line": 340, "column": 7}}, "86": {"start": {"line": 335, "column": 8}, "end": {"line": 339, "column": 10}}, "87": {"start": {"line": 342, "column": 38}, "end": {"line": 342, "column": 59}}, "88": {"start": {"line": 344, "column": 6}, "end": {"line": 350, "column": 7}}, "89": {"start": {"line": 345, "column": 8}, "end": {"line": 349, "column": 10}}, "90": {"start": {"line": 352, "column": 21}, "end": {"line": 352, "column": 64}}, "91": {"start": {"line": 354, "column": 6}, "end": {"line": 358, "column": 8}}, "92": {"start": {"line": 360, "column": 6}, "end": {"line": 362, "column": 7}}, "93": {"start": {"line": 361, "column": 8}, "end": {"line": 361, "column": 20}}, "94": {"start": {"line": 364, "column": 6}, "end": {"line": 366, "column": 7}}, "95": {"start": {"line": 365, "column": 8}, "end": {"line": 365, "column": 90}}, "96": {"start": {"line": 368, "column": 6}, "end": {"line": 368, "column": 81}}, "97": {"start": {"line": 378, "column": 4}, "end": {"line": 392, "column": 6}}, "98": {"start": {"line": 399, "column": 4}, "end": {"line": 399, "column": 23}}, "99": {"start": {"line": 407, "column": 4}, "end": {"line": 407, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": 3}}, "loc": {"start": {"line": 51, "column": 31}, "end": {"line": 58, "column": 3}}, "line": 51}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 68, "column": 2}, "end": {"line": 68, "column": 3}}, "loc": {"start": {"line": 73, "column": 33}, "end": {"line": 158, "column": 3}}, "line": 73}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 167, "column": 2}, "end": {"line": 167, "column": 3}}, "loc": {"start": {"line": 171, "column": 33}, "end": {"line": 237, "column": 3}}, "line": 171}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 245, "column": 2}, "end": {"line": 245, "column": 3}}, "loc": {"start": {"line": 245, "column": 82}, "end": {"line": 309, "column": 3}}, "line": 245}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 316, "column": 2}, "end": {"line": 316, "column": 3}}, "loc": {"start": {"line": 316, "column": 68}, "end": {"line": 370, "column": 3}}, "line": 316}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 377, "column": 2}, "end": {"line": 377, "column": 3}}, "loc": {"start": {"line": 377, "column": 60}, "end": {"line": 393, "column": 3}}, "line": 377}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 398, "column": 2}, "end": {"line": 398, "column": 3}}, "loc": {"start": {"line": 398, "column": 21}, "end": {"line": 400, "column": 3}}, "line": 398}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 406, "column": 2}, "end": {"line": 406, "column": 3}}, "loc": {"start": {"line": 406, "column": 25}, "end": {"line": 408, "column": 3}}, "line": 406}}, "branchMap": {"0": {"loc": {"start": {"line": 52, "column": 18}, "end": {"line": 52, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 52, "column": 18}, "end": {"line": 52, "column": 24}}, {"start": {"line": 52, "column": 28}, "end": {"line": 52, "column": 61}}, {"start": {"line": 52, "column": 65}, "end": {"line": 52, "column": 67}}], "line": 52}, "1": {"loc": {"start": {"line": 55, "column": 4}, "end": {"line": 57, "column": 5}}, "type": "if", "locations": [{"start": {"line": 55, "column": 4}, "end": {"line": 57, "column": 5}}, {"start": {}, "end": {}}], "line": 55}, "2": {"loc": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 29}}, "type": "default-arg", "locations": [{"start": {"line": 72, "column": 27}, "end": {"line": 72, "column": 29}}], "line": 72}, "3": {"loc": {"start": {"line": 78, "column": 4}, "end": {"line": 80, "column": 5}}, "type": "if", "locations": [{"start": {"line": 78, "column": 4}, "end": {"line": 80, "column": 5}}, {"start": {}, "end": {}}], "line": 78}, "4": {"loc": {"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 5}}, "type": "if", "locations": [{"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 5}}, {"start": {}, "end": {}}], "line": 81}, "5": {"loc": {"start": {"line": 93, "column": 4}, "end": {"line": 95, "column": 5}}, "type": "if", "locations": [{"start": {"line": 93, "column": 4}, "end": {"line": 95, "column": 5}}, {"start": {}, "end": {}}], "line": 93}, "6": {"loc": {"start": {"line": 96, "column": 4}, "end": {"line": 98, "column": 5}}, "type": "if", "locations": [{"start": {"line": 96, "column": 4}, "end": {"line": 98, "column": 5}}, {"start": {}, "end": {}}], "line": 96}, "7": {"loc": {"start": {"line": 99, "column": 4}, "end": {"line": 101, "column": 5}}, "type": "if", "locations": [{"start": {"line": 99, "column": 4}, "end": {"line": 101, "column": 5}}, {"start": {}, "end": {}}], "line": 99}, "8": {"loc": {"start": {"line": 102, "column": 4}, "end": {"line": 104, "column": 5}}, "type": "if", "locations": [{"start": {"line": 102, "column": 4}, "end": {"line": 104, "column": 5}}, {"start": {}, "end": {}}], "line": 102}, "9": {"loc": {"start": {"line": 116, "column": 6}, "end": {"line": 122, "column": 7}}, "type": "if", "locations": [{"start": {"line": 116, "column": 6}, "end": {"line": 122, "column": 7}}, {"start": {}, "end": {}}], "line": 116}, "10": {"loc": {"start": {"line": 126, "column": 6}, "end": {"line": 132, "column": 7}}, "type": "if", "locations": [{"start": {"line": 126, "column": 6}, "end": {"line": 132, "column": 7}}, {"start": {}, "end": {}}], "line": 126}, "11": {"loc": {"start": {"line": 126, "column": 10}, "end": {"line": 126, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 126, "column": 10}, "end": {"line": 126, "column": 30}}, {"start": {"line": 126, "column": 34}, "end": {"line": 126, "column": 64}}], "line": 126}, "12": {"loc": {"start": {"line": 128, "column": 10}, "end": {"line": 128, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 128, "column": 10}, "end": {"line": 128, "column": 28}}, {"start": {"line": 128, "column": 32}, "end": {"line": 128, "column": 66}}], "line": 128}, "13": {"loc": {"start": {"line": 148, "column": 6}, "end": {"line": 150, "column": 7}}, "type": "if", "locations": [{"start": {"line": 148, "column": 6}, "end": {"line": 150, "column": 7}}, {"start": {}, "end": {}}], "line": 148}, "14": {"loc": {"start": {"line": 152, "column": 6}, "end": {"line": 154, "column": 7}}, "type": "if", "locations": [{"start": {"line": 152, "column": 6}, "end": {"line": 154, "column": 7}}, {"start": {}, "end": {}}], "line": 152}, "15": {"loc": {"start": {"line": 179, "column": 4}, "end": {"line": 181, "column": 5}}, "type": "if", "locations": [{"start": {"line": 179, "column": 4}, "end": {"line": 181, "column": 5}}, {"start": {}, "end": {}}], "line": 179}, "16": {"loc": {"start": {"line": 182, "column": 4}, "end": {"line": 184, "column": 5}}, "type": "if", "locations": [{"start": {"line": 182, "column": 4}, "end": {"line": 184, "column": 5}}, {"start": {}, "end": {}}], "line": 182}, "17": {"loc": {"start": {"line": 196, "column": 6}, "end": {"line": 202, "column": 7}}, "type": "if", "locations": [{"start": {"line": 196, "column": 6}, "end": {"line": 202, "column": 7}}, {"start": {}, "end": {}}], "line": 196}, "18": {"loc": {"start": {"line": 206, "column": 6}, "end": {"line": 212, "column": 7}}, "type": "if", "locations": [{"start": {"line": 206, "column": 6}, "end": {"line": 212, "column": 7}}, {"start": {}, "end": {}}], "line": 206}, "19": {"loc": {"start": {"line": 206, "column": 10}, "end": {"line": 206, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 206, "column": 10}, "end": {"line": 206, "column": 30}}, {"start": {"line": 206, "column": 34}, "end": {"line": 206, "column": 64}}], "line": 206}, "20": {"loc": {"start": {"line": 208, "column": 10}, "end": {"line": 208, "column": 74}}, "type": "binary-expr", "locations": [{"start": {"line": 208, "column": 10}, "end": {"line": 208, "column": 28}}, {"start": {"line": 208, "column": 32}, "end": {"line": 208, "column": 74}}], "line": 208}, "21": {"loc": {"start": {"line": 227, "column": 6}, "end": {"line": 229, "column": 7}}, "type": "if", "locations": [{"start": {"line": 227, "column": 6}, "end": {"line": 229, "column": 7}}, {"start": {}, "end": {}}], "line": 227}, "22": {"loc": {"start": {"line": 231, "column": 6}, "end": {"line": 233, "column": 7}}, "type": "if", "locations": [{"start": {"line": 231, "column": 6}, "end": {"line": 233, "column": 7}}, {"start": {}, "end": {}}], "line": 231}, "23": {"loc": {"start": {"line": 251, "column": 4}, "end": {"line": 253, "column": 5}}, "type": "if", "locations": [{"start": {"line": 251, "column": 4}, "end": {"line": 253, "column": 5}}, {"start": {}, "end": {}}], "line": 251}, "24": {"loc": {"start": {"line": 260, "column": 4}, "end": {"line": 262, "column": 5}}, "type": "if", "locations": [{"start": {"line": 260, "column": 4}, "end": {"line": 262, "column": 5}}, {"start": {}, "end": {}}], "line": 260}, "25": {"loc": {"start": {"line": 260, "column": 8}, "end": {"line": 260, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 260, "column": 8}, "end": {"line": 260, "column": 14}}, {"start": {"line": 260, "column": 18}, "end": {"line": 260, "column": 35}}], "line": 260}, "26": {"loc": {"start": {"line": 274, "column": 6}, "end": {"line": 280, "column": 7}}, "type": "if", "locations": [{"start": {"line": 274, "column": 6}, "end": {"line": 280, "column": 7}}, {"start": {}, "end": {}}], "line": 274}, "27": {"loc": {"start": {"line": 284, "column": 6}, "end": {"line": 290, "column": 7}}, "type": "if", "locations": [{"start": {"line": 284, "column": 6}, "end": {"line": 290, "column": 7}}, {"start": {}, "end": {}}], "line": 284}, "28": {"loc": {"start": {"line": 286, "column": 10}, "end": {"line": 286, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 286, "column": 10}, "end": {"line": 286, "column": 28}}, {"start": {"line": 286, "column": 32}, "end": {"line": 286, "column": 69}}], "line": 286}, "29": {"loc": {"start": {"line": 299, "column": 6}, "end": {"line": 301, "column": 7}}, "type": "if", "locations": [{"start": {"line": 299, "column": 6}, "end": {"line": 301, "column": 7}}, {"start": {}, "end": {}}], "line": 299}, "30": {"loc": {"start": {"line": 303, "column": 6}, "end": {"line": 305, "column": 7}}, "type": "if", "locations": [{"start": {"line": 303, "column": 6}, "end": {"line": 305, "column": 7}}, {"start": {}, "end": {}}], "line": 303}, "31": {"loc": {"start": {"line": 334, "column": 6}, "end": {"line": 340, "column": 7}}, "type": "if", "locations": [{"start": {"line": 334, "column": 6}, "end": {"line": 340, "column": 7}}, {"start": {}, "end": {}}], "line": 334}, "32": {"loc": {"start": {"line": 344, "column": 6}, "end": {"line": 350, "column": 7}}, "type": "if", "locations": [{"start": {"line": 344, "column": 6}, "end": {"line": 350, "column": 7}}, {"start": {}, "end": {}}], "line": 344}, "33": {"loc": {"start": {"line": 344, "column": 10}, "end": {"line": 344, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 344, "column": 10}, "end": {"line": 344, "column": 30}}, {"start": {"line": 344, "column": 34}, "end": {"line": 344, "column": 64}}], "line": 344}, "34": {"loc": {"start": {"line": 346, "column": 10}, "end": {"line": 346, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 346, "column": 10}, "end": {"line": 346, "column": 28}}, {"start": {"line": 346, "column": 32}, "end": {"line": 346, "column": 65}}], "line": 346}, "35": {"loc": {"start": {"line": 360, "column": 6}, "end": {"line": 362, "column": 7}}, "type": "if", "locations": [{"start": {"line": 360, "column": 6}, "end": {"line": 362, "column": 7}}, {"start": {}, "end": {}}], "line": 360}, "36": {"loc": {"start": {"line": 364, "column": 6}, "end": {"line": 366, "column": 7}}, "type": "if", "locations": [{"start": {"line": 364, "column": 6}, "end": {"line": 366, "column": 7}}, {"start": {}, "end": {}}], "line": 364}, "37": {"loc": {"start": {"line": 381, "column": 25}, "end": {"line": 381, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 381, "column": 25}, "end": {"line": 381, "column": 52}}, {"start": {"line": 381, "column": 56}, "end": {"line": 381, "column": 58}}], "line": 381}, "38": {"loc": {"start": {"line": 384, "column": 13}, "end": {"line": 384, "column": 34}}, "type": "binary-expr", "locations": [{"start": {"line": 384, "column": 13}, "end": {"line": 384, "column": 28}}, {"start": {"line": 384, "column": 32}, "end": {"line": 384, "column": 34}}], "line": 384}, "39": {"loc": {"start": {"line": 388, "column": 15}, "end": {"line": 388, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 388, "column": 15}, "end": {"line": 388, "column": 48}}, {"start": {"line": 388, "column": 52}, "end": {"line": 388, "column": 53}}], "line": 388}, "40": {"loc": {"start": {"line": 389, "column": 15}, "end": {"line": 389, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 389, "column": 15}, "end": {"line": 389, "column": 48}}, {"start": {"line": 389, "column": 52}, "end": {"line": 389, "column": 53}}], "line": 389}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "b": {"0": [0, 0, 0], "1": [0, 0], "2": [0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0], "30": [0, 0], "31": [0, 0], "32": [0, 0], "33": [0, 0], "34": [0, 0], "35": [0, 0], "36": [0, 0], "37": [0, 0], "38": [0, 0], "39": [0, 0], "40": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "480949af034ff6c94f964f57fec9996852505890"}, "/Users/<USER>/WebstormProjects/goo/src/services/websiteVerification.ts": {"path": "/Users/<USER>/WebstormProjects/goo/src/services/websiteVerification.ts", "statementMap": {"0": {"start": {"line": 37, "column": 20}, "end": {"line": 37, "column": 57}}, "1": {"start": {"line": 38, "column": 20}, "end": {"line": 43, "column": 5}}, "2": {"start": {"line": 52, "column": 8}, "end": {"line": 54, "column": 9}}, "3": {"start": {"line": 53, "column": 12}, "end": {"line": 53, "column": 82}}, "4": {"start": {"line": 56, "column": 30}, "end": {"line": 56, "column": 47}}, "5": {"start": {"line": 59, "column": 23}, "end": {"line": 59, "column": 52}}, "6": {"start": {"line": 60, "column": 8}, "end": {"line": 62, "column": 9}}, "7": {"start": {"line": 61, "column": 12}, "end": {"line": 61, "column": 26}}, "8": {"start": {"line": 64, "column": 26}, "end": {"line": 64, "column": 36}}, "9": {"start": {"line": 67, "column": 8}, "end": {"line": 130, "column": 9}}, "10": {"start": {"line": 68, "column": 31}, "end": {"line": 68, "column": 52}}, "11": {"start": {"line": 69, "column": 30}, "end": {"line": 69, "column": 90}}, "12": {"start": {"line": 69, "column": 47}, "end": {"line": 69, "column": 65}}, "13": {"start": {"line": 71, "column": 29}, "end": {"line": 78, "column": 14}}, "14": {"start": {"line": 80, "column": 12}, "end": {"line": 80, "column": 36}}, "15": {"start": {"line": 81, "column": 33}, "end": {"line": 81, "column": 55}}, "16": {"start": {"line": 82, "column": 32}, "end": {"line": 82, "column": 74}}, "17": {"start": {"line": 85, "column": 33}, "end": {"line": 85, "column": 92}}, "18": {"start": {"line": 86, "column": 31}, "end": {"line": 86, "column": 99}}, "19": {"start": {"line": 88, "column": 12}, "end": {"line": 97, "column": 14}}, "20": {"start": {"line": 99, "column": 12}, "end": {"line": 103, "column": 13}}, "21": {"start": {"line": 100, "column": 16}, "end": {"line": 100, "column": 53}}, "22": {"start": {"line": 102, "column": 16}, "end": {"line": 102, "column": 49}}, "23": {"start": {"line": 104, "column": 12}, "end": {"line": 104, "column": 57}}, "24": {"start": {"line": 107, "column": 33}, "end": {"line": 107, "column": 55}}, "25": {"start": {"line": 108, "column": 31}, "end": {"line": 108, "column": 46}}, "26": {"start": {"line": 110, "column": 12}, "end": {"line": 116, "column": 13}}, "27": {"start": {"line": 111, "column": 16}, "end": {"line": 115, "column": 17}}, "28": {"start": {"line": 112, "column": 20}, "end": {"line": 112, "column": 53}}, "29": {"start": {"line": 114, "column": 20}, "end": {"line": 114, "column": 49}}, "30": {"start": {"line": 118, "column": 12}, "end": {"line": 126, "column": 14}}, "31": {"start": {"line": 128, "column": 12}, "end": {"line": 128, "column": 45}}, "32": {"start": {"line": 129, "column": 12}, "end": {"line": 129, "column": 57}}, "33": {"start": {"line": 132, "column": 8}, "end": {"line": 132, "column": 35}}, "34": {"start": {"line": 135, "column": 8}, "end": {"line": 135, "column": 46}}, "35": {"start": {"line": 137, "column": 8}, "end": {"line": 137, "column": 22}}, "36": {"start": {"line": 150, "column": 46}, "end": {"line": 150, "column": 48}}, "37": {"start": {"line": 153, "column": 8}, "end": {"line": 168, "column": 9}}, "38": {"start": {"line": 153, "column": 21}, "end": {"line": 153, "column": 22}}, "39": {"start": {"line": 154, "column": 26}, "end": {"line": 154, "column": 56}}, "40": {"start": {"line": 155, "column": 34}, "end": {"line": 164, "column": 13}}, "41": {"start": {"line": 156, "column": 16}, "end": {"line": 163, "column": 19}}, "42": {"start": {"line": 156, "column": 56}, "end": {"line": 163, "column": 17}}, "43": {"start": {"line": 166, "column": 33}, "end": {"line": 166, "column": 65}}, "44": {"start": {"line": 167, "column": 12}, "end": {"line": 167, "column": 42}}, "45": {"start": {"line": 170, "column": 8}, "end": {"line": 170, "column": 23}}, "46": {"start": {"line": 181, "column": 25}, "end": {"line": 181, "column": 26}}, "47": {"start": {"line": 184, "column": 8}, "end": {"line": 192, "column": 9}}, "48": {"start": {"line": 185, "column": 12}, "end": {"line": 185, "column": 30}}, "49": {"start": {"line": 186, "column": 15}, "end": {"line": 192, "column": 9}}, "50": {"start": {"line": 187, "column": 12}, "end": {"line": 187, "column": 30}}, "51": {"start": {"line": 188, "column": 15}, "end": {"line": 192, "column": 9}}, "52": {"start": {"line": 189, "column": 12}, "end": {"line": 189, "column": 31}}, "53": {"start": {"line": 191, "column": 12}, "end": {"line": 191, "column": 31}}, "54": {"start": {"line": 195, "column": 8}, "end": {"line": 199, "column": 9}}, "55": {"start": {"line": 196, "column": 12}, "end": {"line": 196, "column": 30}}, "56": {"start": {"line": 197, "column": 15}, "end": {"line": 199, "column": 9}}, "57": {"start": {"line": 198, "column": 12}, "end": {"line": 198, "column": 30}}, "58": {"start": {"line": 202, "column": 8}, "end": {"line": 208, "column": 9}}, "59": {"start": {"line": 203, "column": 12}, "end": {"line": 203, "column": 30}}, "60": {"start": {"line": 204, "column": 15}, "end": {"line": 208, "column": 9}}, "61": {"start": {"line": 205, "column": 12}, "end": {"line": 205, "column": 30}}, "62": {"start": {"line": 206, "column": 15}, "end": {"line": 208, "column": 9}}, "63": {"start": {"line": 207, "column": 12}, "end": {"line": 207, "column": 30}}, "64": {"start": {"line": 210, "column": 8}, "end": {"line": 210, "column": 52}}, "65": {"start": {"line": 218, "column": 8}, "end": {"line": 228, "column": 10}}, "66": {"start": {"line": 235, "column": 8}, "end": {"line": 235, "column": 27}}, "67": {"start": {"line": 243, "column": 8}, "end": {"line": 243, "column": 31}}, "68": {"start": {"line": 250, "column": 8}, "end": {"line": 255, "column": 10}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 5}}, "loc": {"start": {"line": 50, "column": 66}, "end": {"line": 138, "column": 5}}, "line": 50}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 69, "column": 41}, "end": {"line": 69, "column": 42}}, "loc": {"start": {"line": 69, "column": 47}, "end": {"line": 69, "column": 65}}, "line": 69}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 146, "column": 4}, "end": {"line": 146, "column": 5}}, "loc": {"start": {"line": 149, "column": 37}, "end": {"line": 171, "column": 5}}, "line": 149}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 155, "column": 44}, "end": {"line": 155, "column": 45}}, "loc": {"start": {"line": 156, "column": 16}, "end": {"line": 163, "column": 19}}, "line": 156}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 156, "column": 46}, "end": {"line": 156, "column": 47}}, "loc": {"start": {"line": 156, "column": 56}, "end": {"line": 163, "column": 17}}, "line": 156}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 180, "column": 4}, "end": {"line": 180, "column": 5}}, "loc": {"start": {"line": 180, "column": 95}, "end": {"line": 211, "column": 5}}, "line": 180}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 217, "column": 4}, "end": {"line": 217, "column": 5}}, "loc": {"start": {"line": 217, "column": 46}, "end": {"line": 229, "column": 5}}, "line": 217}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 234, "column": 4}, "end": {"line": 234, "column": 5}}, "loc": {"start": {"line": 234, "column": 23}, "end": {"line": 236, "column": 5}}, "line": 234}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 242, "column": 4}, "end": {"line": 242, "column": 5}}, "loc": {"start": {"line": 242, "column": 27}, "end": {"line": 244, "column": 5}}, "line": 242}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 249, "column": 4}, "end": {"line": 249, "column": 5}}, "loc": {"start": {"line": 249, "column": 23}, "end": {"line": 256, "column": 5}}, "line": 249}}, "branchMap": {"0": {"loc": {"start": {"line": 52, "column": 8}, "end": {"line": 54, "column": 9}}, "type": "if", "locations": [{"start": {"line": 52, "column": 8}, "end": {"line": 54, "column": 9}}, {"start": {}, "end": {}}], "line": 52}, "1": {"loc": {"start": {"line": 60, "column": 8}, "end": {"line": 62, "column": 9}}, "type": "if", "locations": [{"start": {"line": 60, "column": 8}, "end": {"line": 62, "column": 9}}, {"start": {}, "end": {}}], "line": 60}, "2": {"loc": {"start": {"line": 82, "column": 32}, "end": {"line": 82, "column": 74}}, "type": "binary-expr", "locations": [{"start": {"line": 82, "column": 32}, "end": {"line": 82, "column": 68}}, {"start": {"line": 82, "column": 72}, "end": {"line": 82, "column": 74}}], "line": 82}, "3": {"loc": {"start": {"line": 90, "column": 24}, "end": {"line": 90, "column": 64}}, "type": "cond-expr", "locations": [{"start": {"line": 90, "column": 39}, "end": {"line": 90, "column": 49}}, {"start": {"line": 90, "column": 52}, "end": {"line": 90, "column": 64}}], "line": 90}, "4": {"loc": {"start": {"line": 99, "column": 12}, "end": {"line": 103, "column": 13}}, "type": "if", "locations": [{"start": {"line": 99, "column": 12}, "end": {"line": 103, "column": 13}}, {"start": {"line": 101, "column": 19}, "end": {"line": 103, "column": 13}}], "line": 99}, "5": {"loc": {"start": {"line": 110, "column": 12}, "end": {"line": 116, "column": 13}}, "type": "if", "locations": [{"start": {"line": 110, "column": 12}, "end": {"line": 116, "column": 13}}, {"start": {}, "end": {}}], "line": 110}, "6": {"loc": {"start": {"line": 111, "column": 16}, "end": {"line": 115, "column": 17}}, "type": "if", "locations": [{"start": {"line": 111, "column": 16}, "end": {"line": 115, "column": 17}}, {"start": {"line": 113, "column": 23}, "end": {"line": 115, "column": 17}}], "line": 111}, "7": {"loc": {"start": {"line": 148, "column": 8}, "end": {"line": 148, "column": 31}}, "type": "default-arg", "locations": [{"start": {"line": 148, "column": 30}, "end": {"line": 148, "column": 31}}], "line": 148}, "8": {"loc": {"start": {"line": 184, "column": 8}, "end": {"line": 192, "column": 9}}, "type": "if", "locations": [{"start": {"line": 184, "column": 8}, "end": {"line": 192, "column": 9}}, {"start": {"line": 186, "column": 15}, "end": {"line": 192, "column": 9}}], "line": 184}, "9": {"loc": {"start": {"line": 184, "column": 12}, "end": {"line": 184, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 184, "column": 12}, "end": {"line": 184, "column": 29}}, {"start": {"line": 184, "column": 33}, "end": {"line": 184, "column": 49}}], "line": 184}, "10": {"loc": {"start": {"line": 186, "column": 15}, "end": {"line": 192, "column": 9}}, "type": "if", "locations": [{"start": {"line": 186, "column": 15}, "end": {"line": 192, "column": 9}}, {"start": {"line": 188, "column": 15}, "end": {"line": 192, "column": 9}}], "line": 186}, "11": {"loc": {"start": {"line": 186, "column": 19}, "end": {"line": 186, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 186, "column": 19}, "end": {"line": 186, "column": 36}}, {"start": {"line": 186, "column": 40}, "end": {"line": 186, "column": 56}}], "line": 186}, "12": {"loc": {"start": {"line": 188, "column": 15}, "end": {"line": 192, "column": 9}}, "type": "if", "locations": [{"start": {"line": 188, "column": 15}, "end": {"line": 192, "column": 9}}, {"start": {"line": 190, "column": 15}, "end": {"line": 192, "column": 9}}], "line": 188}, "13": {"loc": {"start": {"line": 188, "column": 19}, "end": {"line": 188, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 188, "column": 19}, "end": {"line": 188, "column": 36}}, {"start": {"line": 188, "column": 40}, "end": {"line": 188, "column": 56}}], "line": 188}, "14": {"loc": {"start": {"line": 195, "column": 8}, "end": {"line": 199, "column": 9}}, "type": "if", "locations": [{"start": {"line": 195, "column": 8}, "end": {"line": 199, "column": 9}}, {"start": {"line": 197, "column": 15}, "end": {"line": 199, "column": 9}}], "line": 195}, "15": {"loc": {"start": {"line": 197, "column": 15}, "end": {"line": 199, "column": 9}}, "type": "if", "locations": [{"start": {"line": 197, "column": 15}, "end": {"line": 199, "column": 9}}, {"start": {}, "end": {}}], "line": 197}, "16": {"loc": {"start": {"line": 202, "column": 8}, "end": {"line": 208, "column": 9}}, "type": "if", "locations": [{"start": {"line": 202, "column": 8}, "end": {"line": 208, "column": 9}}, {"start": {"line": 204, "column": 15}, "end": {"line": 208, "column": 9}}], "line": 202}, "17": {"loc": {"start": {"line": 204, "column": 15}, "end": {"line": 208, "column": 9}}, "type": "if", "locations": [{"start": {"line": 204, "column": 15}, "end": {"line": 208, "column": 9}}, {"start": {"line": 206, "column": 15}, "end": {"line": 208, "column": 9}}], "line": 204}, "18": {"loc": {"start": {"line": 206, "column": 15}, "end": {"line": 208, "column": 9}}, "type": "if", "locations": [{"start": {"line": 206, "column": 15}, "end": {"line": 208, "column": 9}}, {"start": {}, "end": {}}], "line": 206}, "19": {"loc": {"start": {"line": 222, "column": 25}, "end": {"line": 224, "column": 19}}, "type": "cond-expr", "locations": [{"start": {"line": 223, "column": 18}, "end": {"line": 223, "column": 79}}, {"start": {"line": 224, "column": 18}, "end": {"line": 224, "column": 19}}], "line": 222}, "20": {"loc": {"start": {"line": 225, "column": 33}, "end": {"line": 227, "column": 19}}, "type": "cond-expr", "locations": [{"start": {"line": 226, "column": 18}, "end": {"line": 226, "column": 73}}, {"start": {"line": 227, "column": 18}, "end": {"line": 227, "column": 19}}], "line": 225}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "92c225cecccd99c8cf60fe6d9a0c92b262516709"}}