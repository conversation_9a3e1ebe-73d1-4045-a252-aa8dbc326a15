<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1750111292237" clover="3.2.0">
  <project timestamp="1750111292237" name="All files">
    <metrics statements="659" coveredstatements="304" conditionals="365" coveredconditionals="123" methods="145" coveredmethods="71" elements="1169" coveredelements="498" complexity="0" loc="659" ncloc="659" packages="5" files="18" classes="18"/>
    <package name="src">
      <metrics statements="124" coveredstatements="95" conditionals="50" coveredconditionals="32" methods="30" coveredmethods="18"/>
      <file name="App.tsx" path="/Users/<USER>/WebstormProjects/goo/src/App.tsx">
        <metrics statements="59" coveredstatements="35" conditionals="31" coveredconditionals="15" methods="14" coveredmethods="2"/>
        <line num="15" count="1" type="stmt"/>
        <line num="24" count="7" type="stmt"/>
        <line num="25" count="7" type="stmt"/>
        <line num="26" count="7" type="stmt"/>
        <line num="27" count="7" type="stmt"/>
        <line num="28" count="7" type="stmt"/>
        <line num="29" count="7" type="stmt"/>
        <line num="30" count="7" type="stmt"/>
        <line num="31" count="7" type="stmt"/>
        <line num="34" count="7" type="stmt"/>
        <line num="37" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="49" count="7" type="stmt"/>
        <line num="50" count="2" type="stmt"/>
        <line num="51" count="2" type="stmt"/>
        <line num="52" count="2" type="stmt"/>
        <line num="53" count="2" type="stmt"/>
        <line num="54" count="2" type="stmt"/>
        <line num="56" count="2" type="stmt"/>
        <line num="57" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="97" count="2" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="cond" truecount="2" falsecount="2"/>
        <line num="100" count="0" type="stmt"/>
        <line num="104" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="110" count="2" type="stmt"/>
        <line num="115" count="7" type="stmt"/>
        <line num="116" count="7" type="stmt"/>
        <line num="117" count="7" type="stmt"/>
        <line num="118" count="7" type="stmt"/>
        <line num="119" count="7" type="stmt"/>
        <line num="122" count="7" type="stmt"/>
        <line num="123" count="7" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="7" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="7" type="stmt"/>
        <line num="141" count="7" type="stmt"/>
      </file>
      <file name="BusinessSearchApp.ts" path="/Users/<USER>/WebstormProjects/goo/src/BusinessSearchApp.ts">
        <metrics statements="61" coveredstatements="60" conditionals="17" coveredconditionals="17" methods="16" coveredmethods="16"/>
        <line num="33" count="39" type="cond" truecount="2" falsecount="0"/>
        <line num="34" count="39" type="cond" truecount="2" falsecount="0"/>
        <line num="35" count="3" type="stmt"/>
        <line num="42" count="36" type="stmt"/>
        <line num="52" count="36" type="stmt"/>
        <line num="62" count="7" type="stmt"/>
        <line num="63" count="7" type="stmt"/>
        <line num="69" count="4" type="stmt"/>
        <line num="72" count="4" type="stmt"/>
        <line num="73" count="4" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="79" count="4" type="stmt"/>
        <line num="88" count="9" type="stmt"/>
        <line num="89" count="9" type="stmt"/>
        <line num="92" count="9" type="stmt"/>
        <line num="93" count="9" type="stmt"/>
        <line num="96" count="9" type="stmt"/>
        <line num="97" count="2" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="114" count="2" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="131" count="2" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="146" count="2" type="stmt"/>
        <line num="147" count="2" type="stmt"/>
        <line num="150" count="2" type="stmt"/>
        <line num="151" count="2" type="stmt"/>
        <line num="152" count="2" type="stmt"/>
        <line num="154" count="2" type="stmt"/>
        <line num="155" count="2" type="stmt"/>
        <line num="156" count="2" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
        <line num="172" count="3" type="stmt"/>
        <line num="185" count="4" type="stmt"/>
        <line num="192" count="4" type="stmt"/>
        <line num="194" count="4" type="stmt"/>
        <line num="195" count="4" type="stmt"/>
        <line num="196" count="4" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="201" count="4" type="stmt"/>
        <line num="203" count="4" type="stmt"/>
        <line num="219" count="3" type="stmt"/>
        <line num="220" count="3" type="stmt"/>
        <line num="222" count="1" type="stmt"/>
        <line num="226" count="4" type="stmt"/>
        <line num="229" count="4" type="stmt"/>
        <line num="231" count="4" type="stmt"/>
        <line num="239" count="36" type="stmt"/>
        <line num="245" count="36" type="stmt"/>
        <line num="246" count="36" type="stmt"/>
        <line num="247" count="36" type="stmt"/>
        <line num="250" count="36" type="stmt"/>
        <line num="257" count="36" type="stmt"/>
        <line num="266" count="9" type="stmt"/>
        <line num="267" count="9" type="stmt"/>
        <line num="268" count="7" type="stmt"/>
        <line num="269" count="7" type="cond" truecount="2" falsecount="0"/>
        <line num="270" count="2" type="stmt"/>
        <line num="272" count="5" type="stmt"/>
        <line num="273" count="5" type="stmt"/>
      </file>
      <file name="index.tsx" path="/Users/<USER>/WebstormProjects/goo/src/index.tsx">
        <metrics statements="4" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components">
      <metrics statements="61" coveredstatements="51" conditionals="34" coveredconditionals="27" methods="41" coveredmethods="21"/>
      <file name="DataManagement.tsx" path="/Users/<USER>/WebstormProjects/goo/src/components/DataManagement.tsx">
        <metrics statements="9" coveredstatements="8" conditionals="0" coveredconditionals="0" methods="5" coveredmethods="4"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="2" type="stmt"/>
        <line num="5" count="2" type="stmt"/>
        <line num="6" count="2" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="2" type="stmt"/>
        <line num="12" count="2" type="stmt"/>
        <line num="13" count="2" type="stmt"/>
        <line num="15" count="2" type="stmt"/>
      </file>
      <file name="DebugPanel.tsx" path="/Users/<USER>/WebstormProjects/goo/src/components/DebugPanel.tsx">
        <metrics statements="5" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="4"/>
        <line num="16" count="1" type="stmt"/>
        <line num="25" count="2" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
      </file>
      <file name="ErrorStates.tsx" path="/Users/<USER>/WebstormProjects/goo/src/components/ErrorStates.tsx">
        <metrics statements="3" coveredstatements="3" conditionals="10" coveredconditionals="9" methods="2" coveredmethods="2"/>
        <line num="21" count="2" type="stmt"/>
        <line num="25" count="5" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
      </file>
      <file name="ResultsTables.tsx" path="/Users/<USER>/WebstormProjects/goo/src/components/ResultsTables.tsx">
        <metrics statements="30" coveredstatements="21" conditionals="22" coveredconditionals="17" methods="24" coveredmethods="5"/>
        <line num="4" count="2" type="stmt"/>
        <line num="5" count="5" type="stmt"/>
        <line num="6" count="5" type="stmt"/>
        <line num="8" count="5" type="stmt"/>
        <line num="9" count="5" type="stmt"/>
        <line num="12" count="5" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="17" count="5" type="stmt"/>
        <line num="18" count="5" type="stmt"/>
        <line num="19" count="5" type="stmt"/>
        <line num="20" count="5" type="stmt"/>
        <line num="21" count="5" type="stmt"/>
        <line num="22" count="5" type="stmt"/>
        <line num="23" count="5" type="stmt"/>
        <line num="24" count="5" type="stmt"/>
        <line num="25" count="5" type="stmt"/>
        <line num="26" count="5" type="stmt"/>
        <line num="27" count="5" type="cond" truecount="1" falsecount="1"/>
        <line num="28" count="5" type="cond" truecount="1" falsecount="1"/>
        <line num="30" count="5" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="74" count="5" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="123" count="4" type="cond" truecount="3" falsecount="1"/>
      </file>
      <file name="SearchForm.tsx" path="/Users/<USER>/WebstormProjects/goo/src/components/SearchForm.tsx">
        <metrics statements="7" coveredstatements="7" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="3" count="2" type="stmt"/>
        <line num="4" count="10" type="stmt"/>
        <line num="5" count="3" type="stmt"/>
        <line num="6" count="3" type="stmt"/>
        <line num="7" count="3" type="stmt"/>
        <line num="15" count="3" type="stmt"/>
        <line num="18" count="10" type="stmt"/>
      </file>
      <file name="SearchHistory.tsx" path="/Users/<USER>/WebstormProjects/goo/src/components/SearchHistory.tsx">
        <metrics statements="7" coveredstatements="7" conditionals="2" coveredconditionals="1" methods="4" coveredmethods="4"/>
        <line num="4" count="1" type="stmt"/>
        <line num="6" count="2" type="stmt"/>
        <line num="23" count="2" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="27" count="2" type="stmt"/>
        <line num="43" count="4" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.mocks">
      <metrics statements="31" coveredstatements="31" conditionals="15" coveredconditionals="12" methods="8" coveredmethods="8"/>
      <file name="MockDataGenerator.ts" path="/Users/<USER>/WebstormProjects/goo/src/mocks/MockDataGenerator.ts">
        <metrics statements="24" coveredstatements="24" conditionals="15" coveredconditionals="12" methods="5" coveredmethods="5"/>
        <line num="2" count="8" type="stmt"/>
        <line num="9" count="8" type="stmt"/>
        <line num="15" count="4" type="stmt"/>
        <line num="16" count="4" type="stmt"/>
        <line num="17" count="4" type="stmt"/>
        <line num="19" count="4" type="stmt"/>
        <line num="20" count="2011" type="stmt"/>
        <line num="21" count="2011" type="stmt"/>
        <line num="22" count="2011" type="stmt"/>
        <line num="41" count="2011" type="stmt"/>
        <line num="44" count="4" type="stmt"/>
        <line num="52" count="2" type="stmt"/>
        <line num="60" count="2" type="stmt"/>
        <line num="73" count="2014" type="stmt"/>
        <line num="81" count="2014" type="cond" truecount="1" falsecount="1"/>
        <line num="84" count="2014" type="cond" truecount="2" falsecount="0"/>
        <line num="85" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="88" count="2013" type="cond" truecount="2" falsecount="0"/>
        <line num="93" count="4" type="stmt"/>
        <line num="101" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="106" count="2012" type="stmt"/>
        <line num="107" count="2012" type="stmt"/>
        <line num="108" count="2012" type="stmt"/>
        <line num="110" count="2012" type="stmt"/>
      </file>
      <file name="ProgressiveSearchSimulator.ts" path="/Users/<USER>/WebstormProjects/goo/src/mocks/ProgressiveSearchSimulator.ts">
        <metrics statements="7" coveredstatements="7" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="3"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="4" type="stmt"/>
        <line num="15" count="4" type="stmt"/>
        <line num="22" count="4" type="stmt"/>
      </file>
    </package>
    <package name="src.models">
      <metrics statements="28" coveredstatements="13" conditionals="4" coveredconditionals="1" methods="9" coveredmethods="4"/>
      <file name="Business.ts" path="/Users/<USER>/WebstormProjects/goo/src/models/Business.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="Errors.ts" path="/Users/<USER>/WebstormProjects/goo/src/models/Errors.ts">
        <metrics statements="28" coveredstatements="13" conditionals="4" coveredconditionals="1" methods="9" coveredmethods="4"/>
        <line num="9" count="35" type="stmt"/>
        <line num="10" count="35" type="stmt"/>
        <line num="13" count="35" type="cond" truecount="1" falsecount="1"/>
        <line num="14" count="35" type="stmt"/>
        <line num="23" count="3" type="stmt"/>
        <line num="24" count="3" type="stmt"/>
        <line num="27" count="3" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="104" count="7" type="stmt"/>
        <line num="105" count="7" type="stmt"/>
        <line num="108" count="7" type="stmt"/>
        <line num="116" count="25" type="stmt"/>
        <line num="117" count="25" type="stmt"/>
        <line num="120" count="25" type="stmt"/>
      </file>
    </package>
    <package name="src.services">
      <metrics statements="415" coveredstatements="114" conditionals="262" coveredconditionals="51" methods="57" coveredmethods="20"/>
      <file name="businessSearch.ts" path="/Users/<USER>/WebstormProjects/goo/src/services/businessSearch.ts">
        <metrics statements="65" coveredstatements="0" conditionals="24" coveredconditionals="0" methods="15" coveredmethods="0"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="232" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="233" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
      </file>
      <file name="dataManager.ts" path="/Users/<USER>/WebstormProjects/goo/src/services/dataManager.ts">
        <metrics statements="116" coveredstatements="109" conditionals="56" coveredconditionals="47" methods="19" coveredmethods="19"/>
        <line num="37" count="41" type="stmt"/>
        <line num="38" count="41" type="stmt"/>
        <line num="46" count="34" type="stmt"/>
        <line num="47" count="34" type="stmt"/>
        <line num="48" count="34" type="stmt"/>
        <line num="50" count="34" type="stmt"/>
        <line num="56" count="34" type="stmt"/>
        <line num="57" count="32" type="stmt"/>
        <line num="59" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="69" count="10" type="stmt"/>
        <line num="70" count="10" type="stmt"/>
        <line num="71" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="72" count="5" type="stmt"/>
        <line num="75" count="5" type="stmt"/>
        <line num="78" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="79" count="2" type="stmt"/>
        <line num="80" count="2" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="86" count="2" type="stmt"/>
        <line num="87" count="2" type="stmt"/>
        <line num="96" count="12" type="stmt"/>
        <line num="97" count="12" type="stmt"/>
        <line num="99" count="12" type="stmt"/>
        <line num="100" count="10" type="stmt"/>
        <line num="101" count="10" type="cond" truecount="4" falsecount="0"/>
        <line num="102" count="3" type="stmt"/>
        <line num="105" count="7" type="stmt"/>
        <line num="106" count="7" type="stmt"/>
        <line num="107" count="7" type="cond" truecount="1" falsecount="1"/>
        <line num="109" count="7" type="stmt"/>
        <line num="112" count="7" type="cond" truecount="2" falsecount="0"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="117" count="6" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="125" count="12" type="stmt"/>
        <line num="128" count="12" type="stmt"/>
        <line num="129" count="3" type="stmt"/>
        <line num="138" count="4" type="stmt"/>
        <line num="145" count="4" type="stmt"/>
        <line num="147" count="4" type="stmt"/>
        <line num="148" count="5" type="stmt"/>
        <line num="149" count="5" type="cond" truecount="4" falsecount="0"/>
        <line num="150" count="3" type="stmt"/>
        <line num="154" count="4" type="stmt"/>
        <line num="162" count="3" type="stmt"/>
        <line num="163" count="3" type="stmt"/>
        <line num="164" count="3" type="stmt"/>
        <line num="165" count="3" type="stmt"/>
        <line num="167" count="3" type="stmt"/>
        <line num="168" count="2" type="stmt"/>
        <line num="169" count="2" type="cond" truecount="3" falsecount="1"/>
        <line num="170" count="0" type="stmt"/>
        <line num="173" count="2" type="stmt"/>
        <line num="174" count="2" type="stmt"/>
        <line num="175" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="177" count="2" type="stmt"/>
        <line num="180" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="181" count="0" type="stmt"/>
        <line num="184" count="2" type="stmt"/>
        <line num="185" count="2" type="stmt"/>
        <line num="187" count="2" type="cond" truecount="3" falsecount="1"/>
        <line num="188" count="2" type="stmt"/>
        <line num="190" count="2" type="cond" truecount="4" falsecount="0"/>
        <line num="191" count="1" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="199" count="3" type="stmt"/>
        <line num="212" count="3" type="stmt"/>
        <line num="213" count="3" type="stmt"/>
        <line num="215" count="3" type="stmt"/>
        <line num="216" count="4" type="stmt"/>
        <line num="217" count="4" type="cond" truecount="4" falsecount="0"/>
        <line num="218" count="1" type="stmt"/>
        <line num="221" count="3" type="stmt"/>
        <line num="222" count="3" type="stmt"/>
        <line num="223" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="225" count="3" type="stmt"/>
        <line num="227" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="228" count="1" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="236" count="3" type="stmt"/>
        <line num="237" count="3" type="stmt"/>
        <line num="245" count="3" type="stmt"/>
        <line num="247" count="3" type="stmt"/>
        <line num="253" count="3" type="stmt"/>
        <line num="262" count="6" type="stmt"/>
        <line num="263" count="6" type="stmt"/>
        <line num="266" count="4" type="cond" truecount="4" falsecount="0"/>
        <line num="267" count="2" type="stmt"/>
        <line num="270" count="2" type="stmt"/>
        <line num="272" count="2" type="stmt"/>
        <line num="273" count="2" type="stmt"/>
        <line num="275" count="2" type="stmt"/>
        <line num="276" count="2" type="stmt"/>
        <line num="277" count="2" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="284" count="2" type="stmt"/>
        <line num="286" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="296" count="34" type="stmt"/>
        <line num="297" count="34" type="stmt"/>
        <line num="298" count="34" type="stmt"/>
        <line num="307" count="34" type="stmt"/>
        <line num="308" count="34" type="stmt"/>
        <line num="309" count="1122" type="stmt"/>
        <line num="310" count="1122" type="stmt"/>
        <line num="311" count="1122" type="stmt"/>
        <line num="313" count="34" type="stmt"/>
        <line num="321" count="1" type="stmt"/>
        <line num="322" count="1" type="stmt"/>
        <line num="323" count="1" type="stmt"/>
        <line num="324" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="325" count="0" type="stmt"/>
        <line num="328" count="1" type="stmt"/>
        <line num="337" count="9" type="stmt"/>
        <line num="344" count="9" type="stmt"/>
        <line num="351" count="9" type="stmt"/>
      </file>
      <file name="geocoding.ts" path="/Users/<USER>/WebstormProjects/goo/src/services/geocoding.ts">
        <metrics statements="68" coveredstatements="5" conditionals="59" coveredconditionals="4" methods="5" coveredmethods="1"/>
        <line num="36" count="22" type="stmt"/>
        <line num="41" count="22" type="cond" truecount="3" falsecount="0"/>
        <line num="42" count="22" type="stmt"/>
        <line num="44" count="22" type="cond" truecount="1" falsecount="1"/>
        <line num="45" count="22" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="120" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="144" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="168" count="0" type="stmt"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="174" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="193" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
      </file>
      <file name="googlePlaces.ts" path="/Users/<USER>/WebstormProjects/goo/src/services/googlePlaces.ts">
        <metrics statements="100" coveredstatements="0" conditionals="82" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="47" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="127" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="180" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="183" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="197" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="207" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="228" count="0" type="stmt"/>
        <line num="231" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="232" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="252" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="260" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="261" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="275" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="284" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="285" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="299" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="300" count="0" type="stmt"/>
        <line num="303" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="304" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="317" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="334" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="335" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="344" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="345" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="360" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="361" count="0" type="stmt"/>
        <line num="364" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="365" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="407" count="0" type="stmt"/>
      </file>
      <file name="websiteVerification.ts" path="/Users/<USER>/WebstormProjects/goo/src/services/websiteVerification.ts">
        <metrics statements="66" coveredstatements="0" conditionals="41" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="189" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="195" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
