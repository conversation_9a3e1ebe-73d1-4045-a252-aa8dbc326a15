{"name": "goo", "version": "1.5.0", "description": "React Business Search & Website Detection Application", "main": "dist/index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"@types/jest": "^29.5.12", "axios": "^1.6.8", "dotenv": "^16.3.1", "libphonenumber-js": "^1.12.9", "react": "^18.3.1", "react-dom": "^18.3.1", "react-scripts": "^5.0.1", "validator": "^13.15.15", "zipcodes": "^8.0.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.0", "@types/node": "^20.0.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/validator": "^13.15.2", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.5.3"}, "private": true, "author": "", "license": "ISC", "jest": {"collectCoverageFrom": ["src/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/index.tsx", "!src/reportWebVitals.ts"], "coverageThreshold": {"global": {"branches": 90, "functions": 90, "lines": 90, "statements": 90}}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}