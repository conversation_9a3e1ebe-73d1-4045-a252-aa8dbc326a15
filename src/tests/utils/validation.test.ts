import { ValidationError } from '../../src/models/Errors';
import {
  validateZipCode,
  validateRadius,
  validateBusinessType,
  validateUrl,
  validateSearchParams,
  validateRequired,
  validateRange,
  validatePhone,
  validateEmail
} from '../../src/utils/validation';
import { BUSINESS_TYPES } from '../../src/constants';

describe('Validation Utils', () => {
  describe('validateZipCode', () => {
    it('should accept valid 5-digit zip codes', () => {
      expect(() => validateZipCode('90210')).not.toThrow();
      expect(() => validateZipCode('10001')).not.toThrow();
    });

    it('should accept valid 9-digit zip codes', () => {
      expect(() => validateZipCode('90210-1234')).not.toThrow();
    });

    it('should reject invalid zip code formats', () => {
      expect(() => validateZipCode('1234')).toThrow(ValidationError);
      expect(() => validateZipCode('123456')).toThrow(ValidationError);
      expect(() => validateZipCode('abcde')).toThrow(ValidationError);
      expect(() => validateZipCode('')).toThrow(ValidationError);
      expect(() => validateZipCode('99999')).toThrow(ValidationError);
    });

    it('should throw ValidationError with correct message and field', () => {
      try {
        validateZipCode('invalid');
      } catch (error) {
        const e = error as ValidationError;
        expect(e).toBeInstanceOf(ValidationError);
        expect(e.field).toBe('zipCode');
        expect(e.message).toContain('Invalid zip code');
      }
    });
  });

  describe('validateRadius', () => {
    it('should accept valid radius values', () => {
      expect(() => validateRadius(1)).not.toThrow();
      expect(() => validateRadius(50)).not.toThrow();
    });

    it('should reject radius values outside valid range', () => {
      expect(() => validateRadius(0)).toThrow(ValidationError);
      expect(() => validateRadius(51)).toThrow(ValidationError);
    });

    it('should reject non-integer radius values', () => {
      expect(() => validateRadius(1.5)).toThrow(ValidationError);
    });

    it('should throw ValidationError with correct field', () => {
      try {
        validateRadius(0);
      } catch (error) {
        const e = error as ValidationError;
        expect(e).toBeInstanceOf(ValidationError);
        expect(e.field).toBe('radius');
      }
    });
  });

  describe('validateBusinessType', () => {
    it('should accept valid business types', () => {
      expect(() => validateBusinessType('restaurant')).not.toThrow();
    });

    it('should reject invalid business types', () => {
      expect(() => validateBusinessType('invalid_type')).toThrow(ValidationError);
      expect(() => validateBusinessType('')).toThrow(ValidationError);
      expect(() => validateBusinessType('RESTAURANT')).toThrow(ValidationError);
    });
  });

  describe('validateUrl', () => {
    it('should accept valid URLs', () => {
      expect(() => validateUrl('https://example.com')).not.toThrow();
    });

    it('should reject invalid URLs', () => {
      expect(() => validateUrl('example.com')).toThrow(ValidationError);
      expect(() => validateUrl('ftp://example.com')).toThrow(ValidationError);
      expect(() => validateUrl('not-a-url')).toThrow(ValidationError);
    });
  });

  describe('validateSearchParams', () => {
    const validParams = {
      zipCode: '90210',
      radius: 10,
      businessType: 'restaurant'
    };

    it('should accept valid search parameters', () => {
      expect(() => validateSearchParams(validParams)).not.toThrow();
    });

    it('should reject invalid zip code in search params', () => {
      const invalidParams = { ...validParams, zipCode: 'invalid' };
      expect(() => validateSearchParams(invalidParams)).toThrow(ValidationError);
    });
  });

  describe('validateRequired', () => {
    it('should pass for non-empty values', () => {
      expect(() => validateRequired('value', 'field')).not.toThrow();
    });

    it('should throw for empty or null values', () => {
      expect(() => validateRequired('', 'field')).toThrow(ValidationError);
      expect(() => validateRequired(null, 'field')).toThrow(ValidationError);
      expect(() => validateRequired(undefined, 'field')).toThrow(ValidationError);
    });
  });

  describe('validateRange', () => {
    it('should pass for values within range', () => {
      expect(() => validateRange(5, 1, 10, 'value')).not.toThrow();
    });

    it('should throw for values outside range', () => {
      expect(() => validateRange(0, 1, 10, 'value')).toThrow(ValidationError);
      expect(() => validateRange(11, 1, 10, 'value')).toThrow(ValidationError);
    });
  });

  describe('validatePhone', () => {
    it('should pass for valid US phone numbers', () => {
      expect(() => validatePhone('(*************')).not.toThrow();
      expect(() => validatePhone('************')).not.toThrow();
      expect(() => validatePhone('+15551234567')).not.toThrow();
    });

    it('should pass for valid international phone numbers', () => {
      expect(() => validatePhone('+44 20 7946 0958')).not.toThrow(); // UK
      expect(() => validatePhone('+33 1 42 86 83 26')).not.toThrow(); // France
    });

    it('should pass for empty phone number', () => {
      expect(() => validatePhone('')).not.toThrow();
    });

    it('should throw for invalid phone numbers', () => {
      expect(() => validatePhone('123')).toThrow('Invalid phone number format');
      expect(() => validatePhone('555-555-555')).toThrow('Invalid phone number format'); // Too short
    });
  });

  describe('validateEmail', () => {
    it('should pass for valid email addresses', () => {
      expect(() => validateEmail('<EMAIL>')).not.toThrow();
    });

    it('should pass for empty email', () => {
      expect(() => validateEmail('')).not.toThrow();
    });

    it('should throw for invalid email addresses', () => {
      expect(() => validateEmail('invalid-email')).toThrow('Invalid email format');
      expect(() => validateEmail('test@.com')).toThrow('Invalid email format');
      expect(() => validateEmail('<EMAIL>')).toThrow('Invalid email format'); // Double dots
    });
  });
});
