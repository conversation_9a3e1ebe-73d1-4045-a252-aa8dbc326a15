import {
  calculateDistance,
  calculateDistanceKm,
  findClosest,
  isWithinRadius,
  sortByDistance,
} from '../../src/utils/distance';
import { Coordinates } from '../../src/models/Business';

describe('Distance Util', () => {
  const losAngeles: Coordinates = { latitude: 34.0522, longitude: -118.2437 };
  const lasVegas: Coordinates = { latitude: 36.1699, longitude: -115.1398 };
  const newYork: Coordinates = { latitude: 40.7128, longitude: -74.0060 };
  const london: Coordinates = { latitude: 51.5074, longitude: -0.1278 };

  describe('calculateDistance', () => {
    it('should calculate the distance between two points correctly', () => {
      const distance = calculateDistance(losAngeles, lasVegas);
      expect(distance).toBeCloseTo(229.8, 0);
    });
  });

  describe('calculateDistanceKm', () => {
    it('should calculate the distance in kilometers correctly', () => {
      const distance = calculateDistanceKm(losAngeles, lasVegas);
      expect(distance).toBeCloseTo(369.83, 1);
    });
  });

  describe('findClosest', () => {
    it('should find the closest coordinate', () => {
      const locations = [newYork, lasVegas, london];
      const closest = findClosest(losAngeles, locations);
      expect(closest?.coordinate).toEqual(lasVegas);
    });

    it('should return null for an empty array', () => {
      const closest = findClosest(losAngeles, []);
      expect(closest).toBeNull();
    });
  });

  describe('isWithinRadius', () => {
    it('should return true if a point is within the radius', () => {
      expect(isWithinRadius(losAngeles, lasVegas, 230)).toBe(true);
    });

    it('should return false if a point is outside the radius', () => {
      expect(isWithinRadius(losAngeles, newYork, 2000)).toBe(false);
    });
  });

  describe('sortByDistance', () => {
    it('should sort coordinates by distance', () => {
      const locations = [newYork, lasVegas, london];
      const sorted = sortByDistance(losAngeles, locations);
      expect(sorted[0].coordinate).toEqual(lasVegas);
      expect(sorted[1].coordinate).toEqual(newYork);
      expect(sorted[2].coordinate).toEqual(london);
    });
  });
});
