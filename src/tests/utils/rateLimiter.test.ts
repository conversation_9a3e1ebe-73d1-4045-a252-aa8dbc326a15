import { TokenBucketRateLimiter, getGlobalRateLimiter, resetGlobalRateLimiter } from '../../src/utils/rateLimiter';
import { RateLimitError } from '../../src/models/Errors';

describe('TokenBucketRateLimiter', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('constructor', () => {
    it('should create rate limiter with correct configuration', () => {
      const rateLimiter = new TokenBucketRateLimiter(10, 20);
      expect(rateLimiter).toBeInstanceOf(TokenBucketRateLimiter);
    });

    it('should throw error for invalid configuration', () => {
      expect(() => new TokenBucketRateLimiter(0, 10)).toThrow();
      expect(() => new TokenBucketRateLimiter(10, 0)).toThrow();
      expect(() => new TokenBucketRateLimiter(-1, 10)).toThrow();
    });
  });

  describe('tryConsume', () => {
    it('should allow requests within rate limit', () => {
      const rateLimiter = new TokenBucketRateLimiter(10, 20);
      
      // Should allow initial requests up to burst size
      for (let i = 0; i < 20; i++) {
        expect(rateLimiter.tryConsume()).toBe(true);
      }
    });

    it('should reject requests when bucket is empty', () => {
      const rateLimiter = new TokenBucketRateLimiter(10, 5);
      
      // Consume all tokens
      for (let i = 0; i < 5; i++) {
        expect(rateLimiter.tryConsume()).toBe(true);
      }
      
      // Next request should be rejected
      expect(rateLimiter.tryConsume()).toBe(false);
    });

    it('should refill tokens over time', () => {
      const rateLimiter = new TokenBucketRateLimiter(10, 5);
      
      // Consume all tokens
      for (let i = 0; i < 5; i++) {
        rateLimiter.tryConsume();
      }
      
      expect(rateLimiter.tryConsume()).toBe(false);
      
      // Advance time by 100ms (should add 1 token at 10 tokens/second)
      jest.advanceTimersByTime(100);
      
      expect(rateLimiter.tryConsume()).toBe(true);
      expect(rateLimiter.tryConsume()).toBe(false);
    });

    it('should not exceed burst size when refilling', () => {
      const rateLimiter = new TokenBucketRateLimiter(10, 5);
      
      // Advance time significantly
      jest.advanceTimersByTime(10000);
      
      // Should still only allow burst size requests
      for (let i = 0; i < 5; i++) {
        expect(rateLimiter.tryConsume()).toBe(true);
      }
      
      expect(rateLimiter.tryConsume()).toBe(false);
    });
  });

  describe('waitForToken', () => {
    it('should resolve immediately when tokens are available', async () => {
      const rateLimiter = new TokenBucketRateLimiter(10, 5);
      
      const promise = rateLimiter.waitForToken();
      jest.runAllTimers();
      
      await expect(promise).resolves.toBeUndefined();
    });

    it('should wait for token refill when bucket is empty', async () => {
      const rateLimiter = new TokenBucketRateLimiter(10, 1);

      // Consume the only token
      rateLimiter.tryConsume();

      const promise = rateLimiter.waitForToken();

      // Should resolve after 100ms (time for 1 token at 10/second)
      jest.advanceTimersByTime(100);
      await expect(promise).resolves.toBeUndefined();
    });

    it('should timeout if waiting too long', async () => {
      const rateLimiter = new TokenBucketRateLimiter(1, 1); // Very slow refill
      
      // Consume the only token
      rateLimiter.tryConsume();
      
      const promise = rateLimiter.waitForToken(500); // 500ms timeout
      
      jest.advanceTimersByTime(600);
      
      await expect(promise).rejects.toThrow(RateLimitError);
    });
  });

  describe('getAvailableTokens', () => {
    it('should return correct number of available tokens', () => {
      const rateLimiter = new TokenBucketRateLimiter(10, 5);
      
      expect(rateLimiter.getAvailableTokens()).toBe(5);
      
      rateLimiter.tryConsume();
      expect(rateLimiter.getAvailableTokens()).toBe(4);
      
      rateLimiter.tryConsume();
      rateLimiter.tryConsume();
      expect(rateLimiter.getAvailableTokens()).toBe(2);
    });

    it('should update available tokens after refill', () => {
      const rateLimiter = new TokenBucketRateLimiter(10, 2);
      
      // Consume all tokens
      rateLimiter.tryConsume();
      rateLimiter.tryConsume();
      expect(rateLimiter.getAvailableTokens()).toBe(0);
      
      // Advance time to refill 1 token
      jest.advanceTimersByTime(100);
      expect(rateLimiter.getAvailableTokens()).toBe(1);
    });
  });

  describe('reset', () => {
    it('should reset bucket to full capacity', () => {
      const rateLimiter = new TokenBucketRateLimiter(10, 5);
      
      // Consume some tokens
      rateLimiter.tryConsume();
      rateLimiter.tryConsume();
      expect(rateLimiter.getAvailableTokens()).toBe(3);
      
      rateLimiter.reset();
      expect(rateLimiter.getAvailableTokens()).toBe(5);
    });
  });

  describe('edge cases and error handling', () => {
    it('should handle zero capacity', () => {
      // Constructor parameters are (tokensPerSecond, bucketSize)
      expect(() => new TokenBucketRateLimiter(1, 0)).toThrow('Tokens per second and bucket size must be positive');
    });

    it('should handle zero refill rate', () => {
      // Constructor parameters are (tokensPerSecond, bucketSize)
      expect(() => new TokenBucketRateLimiter(0, 1)).toThrow('Tokens per second and bucket size must be positive');
    });

    it('should handle very small values', () => {
      // Constructor parameters are (tokensPerSecond, bucketSize)
      // With bucket size 0.1, we get 0 tokens initially (Math.floor(0.1) = 0)
      const limiter = new TokenBucketRateLimiter(0.1, 0.1);
      expect(limiter.tryConsume()).toBe(false); // No tokens available initially
      expect(limiter.tryConsume()).toBe(false);
    });

    it('should handle very large capacity', () => {
      const limiter = new TokenBucketRateLimiter(1000000, 1000000);

      // Should be able to consume many tokens initially
      for (let i = 0; i < 1000; i++) {
        expect(limiter.tryConsume()).toBe(true);
      }
    });

    it('should handle fractional refill rates', () => {
      const limiter = new TokenBucketRateLimiter(0.5, 2); // 0.5 tokens per second

      // Consume all tokens
      expect(limiter.tryConsume()).toBe(true);
      expect(limiter.tryConsume()).toBe(true);
      expect(limiter.tryConsume()).toBe(false);

      // Should get 1 token after 2 seconds
      jest.advanceTimersByTime(2000);
      expect(limiter.tryConsume()).toBe(true);
      expect(limiter.tryConsume()).toBe(false);
    });

    it('should handle very long time intervals', () => {
      const limiter = new TokenBucketRateLimiter(10, 5);

      // Consume all tokens
      for (let i = 0; i < 5; i++) {
        expect(limiter.tryConsume()).toBe(true);
      }

      // Advance time by a very long period
      jest.advanceTimersByTime(1000000); // 1000 seconds

      // Should be fully refilled but not exceed capacity
      for (let i = 0; i < 5; i++) {
        expect(limiter.tryConsume()).toBe(true);
      }
      expect(limiter.tryConsume()).toBe(false);
    });

    it('should handle rapid successive calls', () => {
      const limiter = new TokenBucketRateLimiter(10, 5);

      // Rapid consumption
      const results = [];
      for (let i = 0; i < 10; i++) {
        results.push(limiter.tryConsume());
      }

      // First 5 should succeed, rest should fail
      expect(results.slice(0, 5)).toEqual([true, true, true, true, true]);
      expect(results.slice(5)).toEqual([false, false, false, false, false]);
    });

    it('should maintain precision with small time intervals', () => {
      const limiter = new TokenBucketRateLimiter(1000, 1); // 1000 tokens per second

      // Consume the only token
      expect(limiter.tryConsume()).toBe(true);
      expect(limiter.tryConsume()).toBe(false);

      // Advance by 1ms (should add 1 token)
      jest.advanceTimersByTime(1);
      expect(limiter.tryConsume()).toBe(true);
      expect(limiter.tryConsume()).toBe(false);
    });
  });
});

describe('getGlobalRateLimiter', () => {
  beforeEach(() => {
    resetGlobalRateLimiter();
  });

  it('should return the same instance for same parameters', () => {
    const limiter1 = getGlobalRateLimiter(10, 20);
    const limiter2 = getGlobalRateLimiter(10, 20);

    expect(limiter1).toBe(limiter2);
  });

  it('should create new instance for different parameters', () => {
    const limiter1 = getGlobalRateLimiter(10, 20);
    resetGlobalRateLimiter(); // Reset between different parameter calls
    const limiter2 = getGlobalRateLimiter(5, 10);

    expect(limiter1).not.toBe(limiter2);
  });

  it('should handle parameter variations', () => {
    const limiter1 = getGlobalRateLimiter(10, 20);
    resetGlobalRateLimiter();
    const limiter2 = getGlobalRateLimiter(10, 21); // Different burst size
    resetGlobalRateLimiter();
    const limiter3 = getGlobalRateLimiter(11, 20); // Different rate

    expect(limiter1).not.toBe(limiter2);
    expect(limiter1).not.toBe(limiter3);
    expect(limiter2).not.toBe(limiter3);
  });

  it('should return TokenBucketRateLimiter instances', () => {
    const limiter = getGlobalRateLimiter(5, 10);
    expect(limiter).toBeInstanceOf(TokenBucketRateLimiter);
  });
});

describe('getTimeUntilNextToken', () => {
  it('should return 0 when tokens are available', () => {
    const limiter = new TokenBucketRateLimiter(1, 5);
    expect(limiter.getTimeUntilNextToken()).toBe(0);
  });

  it('should return time until next token when no tokens available', () => {
    const limiter = new TokenBucketRateLimiter(1, 1); // 1 token per second, bucket size 1

    // Consume the initial token
    expect(limiter.tryConsume()).toBe(true);

    // Now should return time until next token
    const timeUntilNext = limiter.getTimeUntilNextToken();
    expect(timeUntilNext).toBeGreaterThan(0);
    expect(timeUntilNext).toBeLessThanOrEqual(1000); // Should be within 1 second
  });

  it('should handle edge case when bucket is empty', () => {
    const limiter = new TokenBucketRateLimiter(0.1, 0.1); // Very slow refill

    // Should return time until next token
    const timeUntilNext = limiter.getTimeUntilNextToken();
    expect(timeUntilNext).toBeGreaterThanOrEqual(0);
  });

  it('should calculate correct time based on refill interval', () => {
    const limiter = new TokenBucketRateLimiter(2, 1); // 2 tokens per second, bucket size 1

    // Consume the token
    expect(limiter.tryConsume()).toBe(true);

    // Time until next should be around 500ms (1000ms / 2 tokens per second)
    const timeUntilNext = limiter.getTimeUntilNextToken();
    expect(timeUntilNext).toBeGreaterThan(0);
    expect(timeUntilNext).toBeLessThanOrEqual(500);
  });
});
