import { BusinessSearchApp, AppConfig } from '../BusinessSearchApp';
import { BusinessSearchService, SearchRequest } from '../services/businessSearch';
import { SearchResult } from '../models/Business';
import { ValidationError, CacheError } from '../models/Errors';
import { GeocodingService } from '../services/geocoding';
import { GooglePlacesService } from '../services/googlePlaces';
import { WebsiteVerificationService } from '../services/websiteVerification';
import { DataManager } from '../services/dataManager';

// Mock the services
jest.mock('../services/geocoding');
jest.mock('../services/businessSearch');
jest.mock('../services/googlePlaces');
jest.mock('../services/websiteVerification');
jest.mock('../services/dataManager');

describe('BusinessSearchApp', () => {
  let app: BusinessSearchApp;
  let mockBusinessSearchService: jest.Mocked<BusinessSearchService>;
  let mockDataManager: jest.Mocked<DataManager>;
  let mockSearchResult: SearchResult;
  let mockSearchRequest: any;
  let businessSearchService: jest.Mocked<BusinessSearchService>;
  const config: AppConfig = { apiKey: 'test-key' };

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Create mock search result
    mockSearchResult = {
      searchParams: {
        zipCode: '10001',
        radius: 5,
        businessType: 'restaurant',
        timestamp: new Date('2023-06-01')
      },
      results: {
        withWebsites: [],
        withoutWebsites: []
      },
      statistics: {
        totalFound: 0,
        withWebsiteCount: 0,
        withoutWebsiteCount: 0,
        websiteAdoptionRate: 0,
        searchDuration: 1000
      }
    };

    mockSearchRequest = {
      zipCode: '10001',
      radius: 5,
      businessType: 'restaurant'
    };

    // Setup mock business search service
    mockBusinessSearchService = {
      searchBusinesses: jest.fn().mockResolvedValue(mockSearchResult),
      getSearchHistory: jest.fn().mockReturnValue([]),
      clearSearchHistory: jest.fn(),
      getSearchStatistics: jest.fn().mockReturnValue({
        totalSearches: 0,
        averageResultsPerSearch: 0,
        averageWebsiteAdoptionRate: 0,
        averageSearchDuration: 0
      }),
      reset: jest.fn()
    } as any;

    // Setup mock data manager
    mockDataManager = {
      saveSearchResult: jest.fn().mockReturnValue('test-key'),
      getSearchResult: jest.fn().mockReturnValue(null),
      getAllSearchResults: jest.fn().mockReturnValue([]),
      deleteSearchResult: jest.fn(),
      clearAllData: jest.fn(),
      getStorageInfo: jest.fn().mockReturnValue({
        totalEntries: 0,
        totalSizeBytes: 0,
        oldestEntry: null,
        newestEntry: null
      }),
      cleanupExpiredData: jest.fn().mockReturnValue(0),
      exportData: jest.fn().mockReturnValue('{"version":"1.0","exportDate":"2023-06-01","searchResults":[]}'),
      importData: jest.fn().mockReturnValue(0),
      getCacheSize: jest.fn().mockReturnValue(0)
    } as any;

    // Mock the constructor to inject our mocks
    (BusinessSearchService as jest.MockedClass<typeof BusinessSearchService>).mockImplementation(() => mockBusinessSearchService);
    (DataManager as jest.MockedClass<typeof DataManager>).mockImplementation(() => mockDataManager);

    businessSearchService = new BusinessSearchService(undefined as any, undefined as any, undefined as any) as jest.Mocked<BusinessSearchService>;
    businessSearchService.searchBusinesses = jest.fn();
    app = new BusinessSearchApp(config);
    app.businessSearchService = businessSearchService;
  });

  describe('searchBusinesses', () => {
    it('should perform a complete business search', async () => {
      const searchRequest = {
        zipCode: '10001',
        radius: 10,
        businessType: 'restaurant',
      };

      // With mocked services, this should succeed and return a result
      const result = await app.searchBusinesses(searchRequest);
      expect(result).toHaveProperty('searchParams');
      expect(result).toHaveProperty('results');
      expect(result).toHaveProperty('statistics');
    });

    it('should validate search parameters', async () => {
      const invalidRequest = {
        zipCode: 'invalid',
        radius: 10,
        businessType: 'restaurant',
      };

      await expect(app.searchBusinesses(invalidRequest)).rejects.toThrow(ValidationError);
    });

    it('should validate radius range', async () => {
      const invalidRequest = {
        zipCode: '10001',
        radius: 0,
        businessType: 'restaurant',
      };

      await expect(app.searchBusinesses(invalidRequest)).rejects.toThrow(ValidationError);
    });

    it('should validate business type', async () => {
      const invalidRequest = {
        zipCode: '10001',
        radius: 10,
        businessType: 'invalid_type',
      };

      await expect(app.searchBusinesses(invalidRequest)).rejects.toThrow(ValidationError);
    });

    it('should call the business search service with the correct parameters', async () => {
      const searchParams: SearchRequest = {
        zipCode: '90210',
        radius: 10,
        businessType: 'restaurant',
      };
      await app.searchBusinesses(searchParams);
      expect(businessSearchService.searchBusinesses).toHaveBeenCalledWith(searchParams);
    });
  });

  describe('getSearchHistory', () => {
    it('should return search history', () => {
      const history = app.getSearchHistory();
      expect(Array.isArray(history)).toBe(true);
    });
  });

  describe('clearSearchHistory', () => {
    it('should clear search history', () => {
      app.clearSearchHistory();
      const history = app.getSearchHistory();
      expect(history).toHaveLength(0);
    });
  });

  describe('getSearchStatistics', () => {
    it('should return search statistics', () => {
      const stats = app.getSearchStatistics();
      expect(stats).toHaveProperty('totalSearches');
      expect(stats).toHaveProperty('averageResultsPerSearch');
      expect(stats).toHaveProperty('averageWebsiteAdoptionRate');
      expect(stats).toHaveProperty('averageSearchDuration');
    });
  });

  describe('exportData', () => {
    it('should export search data', () => {
      const exported = app.exportData();
      expect(typeof exported).toBe('string');
      
      const parsed = JSON.parse(exported);
      expect(parsed).toHaveProperty('version');
      expect(parsed).toHaveProperty('exportDate');
      expect(parsed).toHaveProperty('searchResults');
    });
  });

  describe('importData', () => {
    it('should import search data', () => {
      const exportData = {
        version: '1.0',
        exportDate: new Date().toISOString(),
        searchResults: [],
      };

      const importedCount = app.importData(JSON.stringify(exportData));
      expect(importedCount).toBe(0);
    });

    it('should handle invalid import data', () => {
      // Override the mock to throw for invalid JSON
      mockDataManager.importData.mockImplementation((jsonData: string) => {
        if (jsonData === 'invalid-json') {
          throw new CacheError('Failed to import data: Unexpected token i in JSON at position 0');
        }
        return 0;
      });

      expect(() => app.importData('invalid-json')).toThrow(CacheError);
    });
  });

  describe('getStorageInfo', () => {
    it('should return storage information', () => {
      const info = app.getStorageInfo();
      expect(info).toHaveProperty('totalEntries');
      expect(info).toHaveProperty('totalSizeBytes');
      expect(info).toHaveProperty('oldestEntry');
      expect(info).toHaveProperty('newestEntry');
    });
  });

  describe('clearAllData', () => {
    it('should clear all application data', () => {
      app.clearAllData();
      const history = app.getSearchHistory();
      const stats = app.getSearchStatistics();
      
      expect(history).toHaveLength(0);
      expect(stats.totalSearches).toBe(0);
    });
  });

  describe('configuration', () => {
    it('should allow custom configuration', () => {
      const customApp = new BusinessSearchApp({
        cacheExpirationHours: 48,
        maxConcurrentRequests: 10,
        requestTimeoutMs: 10000,
      });

      expect(customApp).toBeInstanceOf(BusinessSearchApp);
    });

    it('should use default configuration when none provided', () => {
      const defaultApp = new BusinessSearchApp();
      expect(defaultApp).toBeInstanceOf(BusinessSearchApp);
    });
  });

  describe('error handling', () => {
    it('should handle missing API key gracefully', () => {
      // Remove API key from environment
      const originalKey = process.env.GOOGLE_PLACES_API_KEY;
      delete process.env.GOOGLE_PLACES_API_KEY;

      expect(() => new BusinessSearchApp()).toThrow();

      // Restore API key
      if (originalKey) {
        process.env.GOOGLE_PLACES_API_KEY = originalKey;
      }
    });

    it('should handle API key from config', () => {
      // Remove API key from environment
      const originalKey = process.env.GOOGLE_PLACES_API_KEY;
      delete process.env.GOOGLE_PLACES_API_KEY;

      expect(() => new BusinessSearchApp({ apiKey: 'test-key' })).not.toThrow();

      // Restore API key
      if (originalKey) {
        process.env.GOOGLE_PLACES_API_KEY = originalKey;
      }
    });
  });

  describe('service integration', () => {
    it('should initialize all required services', () => {
      expect(app).toHaveProperty('searchBusinesses');
      expect(app).toHaveProperty('getSearchHistory');
      expect(app).toHaveProperty('getSearchStatistics');
      expect(app).toHaveProperty('exportData');
      expect(app).toHaveProperty('importData');
    });
  });

  describe('cleanupExpiredData', () => {
    it('should cleanup expired data', () => {
      const removedCount = app.cleanupExpiredData();
      expect(typeof removedCount).toBe('number');
      expect(removedCount).toBeGreaterThanOrEqual(0);
    });
  });

  describe('getConfig', () => {
    it('should return readonly configuration', () => {
      const config = app.getConfig();
      expect(config).toHaveProperty('apiKey');
      expect(config).toHaveProperty('cacheExpirationHours');
      expect(config).toHaveProperty('maxConcurrentRequests');
      expect(config).toHaveProperty('requestTimeoutMs');
      expect(config).toHaveProperty('rateLimitRequestsPerSecond');
      expect(config).toHaveProperty('rateLimitBurstSize');
    });

    it('should return default values when not specified', () => {
      const config = app.getConfig();
      expect(config.cacheExpirationHours).toBe(24);
      expect(config.maxConcurrentRequests).toBe(5);
      expect(config.requestTimeoutMs).toBe(5000);
    });
  });

  describe('getHealthStatus', () => {
    it('should return health status for all services', async () => {
      const health = await app.getHealthStatus();

      expect(health).toHaveProperty('geocoding');
      expect(health).toHaveProperty('places');
      expect(health).toHaveProperty('websiteVerification');
      expect(health).toHaveProperty('storage');

      expect(typeof health.geocoding).toBe('boolean');
      expect(typeof health.places).toBe('boolean');
      expect(typeof health.websiteVerification).toBe('boolean');
      expect(typeof health.storage).toBe('boolean');
    });

    it('should handle service failures gracefully', async () => {
      // This test will likely fail due to missing API key, but should not throw
      const health = await app.getHealthStatus();
      expect(health.websiteVerification).toBe(true); // This should always be true
    });
  });

  describe('search history deduplication', () => {
    it('should deduplicate search results', () => {
      // This tests the private deduplicateSearchResults method indirectly
      const history1 = app.getSearchHistory();
      const history2 = app.getSearchHistory();

      // Should return the same results
      expect(history1).toEqual(history2);
    });
  });

  describe('advanced configuration', () => {
    it('should accept all configuration options', () => {
      const customApp = new BusinessSearchApp({
        apiKey: 'test-key',
        cacheExpirationHours: 48,
        maxConcurrentRequests: 10,
        requestTimeoutMs: 10000,
        rateLimitRequestsPerSecond: 5,
        rateLimitBurstSize: 15,
      });

      const config = customApp.getConfig();
      expect(config.cacheExpirationHours).toBe(48);
      expect(config.maxConcurrentRequests).toBe(10);
      expect(config.requestTimeoutMs).toBe(10000);
      expect(config.rateLimitRequestsPerSecond).toBe(5);
      expect(config.rateLimitBurstSize).toBe(15);
    });
  });

  describe('data persistence integration', () => {
    it('should save search results automatically', async () => {
      // Mock a successful search that would save data
      const searchRequest = {
        zipCode: '10001',
        radius: 5,
        businessType: 'restaurant',
      };

      // This will fail due to missing API key, but tests the flow
      try {
        await app.searchBusinesses(searchRequest);
      } catch (error) {
        // Expected to fail without API key
        expect(error).toBeDefined();
      }
    });

    it('should integrate search history from multiple sources', () => {
      const history = app.getSearchHistory();
      expect(Array.isArray(history)).toBe(true);

      // Should combine memory and persistent storage
      // Even if empty, should not throw
    });
  });

  describe('cache management', () => {
    it('should clear all caches when clearing data', () => {
      // This tests that clearAllData calls all service cache clears
      expect(() => app.clearAllData()).not.toThrow();
    });
  });

  describe('error handling and edge cases', () => {
    it('should handle dataManager save errors in searchBusinesses', async () => {
      // Mock the business search service to return a result
      const mockBusinessSearchService = {
        searchBusinesses: jest.fn().mockResolvedValue(mockSearchResult)
      };

      // Replace the service in the app
      (app as any).businessSearchService = mockBusinessSearchService;

      // Mock dataManager to throw error on save
      const originalSave = app['dataManager'].saveSearchResult;
      app['dataManager'].saveSearchResult = jest.fn().mockImplementation(() => {
        throw new Error('Storage full');
      });

      // Should still return result even if save fails
      const result = await app.searchBusinesses(mockSearchRequest);
      expect(result).toEqual(mockSearchResult);

      // Restore original method
      app['dataManager'].saveSearchResult = originalSave;
    });

    it('should handle deduplication in getSearchHistory', () => {
      const duplicateResult = { ...mockSearchResult };

      // Mock the business search service
      const mockBusinessSearchService = {
        getSearchHistory: jest.fn().mockReturnValue([mockSearchResult])
      };
      (app as any).businessSearchService = mockBusinessSearchService;

      // Mock dataManager
      app['dataManager'].getAllSearchResults = jest.fn().mockReturnValue([duplicateResult]);

      const history = app.getSearchHistory();

      // Should only return one result (deduplicated)
      expect(history).toHaveLength(1);
      expect(history[0]).toEqual(mockSearchResult);
    });

    it('should handle successful geocoding health check', async () => {
      // Mock the geocoding service directly on the app instance
      const mockGeocodingService = {
        zipCodeToCoordinates: jest.fn().mockResolvedValue({ lat: 40.7128, lng: -74.0060 })
      };
      (app as any).geocodingService = mockGeocodingService;

      const status = await app.getHealthStatus();

      expect(status.geocoding).toBe(true);
    });

    it('should handle storage health check errors', async () => {
      // Mock dataManager to throw error on save
      const originalSave = app['dataManager'].saveSearchResult;
      app['dataManager'].saveSearchResult = jest.fn().mockImplementation(() => {
        throw new Error('Storage error');
      });

      const status = await app.getHealthStatus();

      expect(status.storage).toBe(false);

      // Restore original method
      app['dataManager'].saveSearchResult = originalSave;
    });

    it('should handle search history sorting with multiple results', () => {
      const olderResult = {
        ...mockSearchResult,
        searchParams: {
          ...mockSearchResult.searchParams,
          timestamp: new Date('2023-01-01')
        }
      };

      const newerResult = {
        ...mockSearchResult,
        searchParams: {
          ...mockSearchResult.searchParams,
          timestamp: new Date('2023-12-31')
        }
      };

      // Mock the business search service
      const mockBusinessSearchService = {
        getSearchHistory: jest.fn().mockReturnValue([olderResult])
      };
      (app as any).businessSearchService = mockBusinessSearchService;

      app['dataManager'].getAllSearchResults = jest.fn().mockReturnValue([newerResult]);

      const history = app.getSearchHistory();

      // Should be sorted newest first
      expect(history[0].searchParams.timestamp.getTime()).toBeGreaterThan(
        history[1].searchParams.timestamp.getTime()
      );
    });

    it('should handle deduplication edge cases', () => {
      const result1 = { ...mockSearchResult };
      const result2 = {
        ...mockSearchResult,
        searchParams: {
          ...mockSearchResult.searchParams,
          zipCode: '90210' // Different zip code
        }
      };
      const result3 = { ...mockSearchResult }; // Exact duplicate of result1

      // Mock the business search service
      const mockBusinessSearchService = {
        getSearchHistory: jest.fn().mockReturnValue([result1, result2])
      };
      (app as any).businessSearchService = mockBusinessSearchService;

      app['dataManager'].getAllSearchResults = jest.fn().mockReturnValue([result3]);

      const history = app.getSearchHistory();

      // Should have 2 unique results (result1 and result2, with result3 deduplicated)
      expect(history).toHaveLength(2);
    });
  });
});
