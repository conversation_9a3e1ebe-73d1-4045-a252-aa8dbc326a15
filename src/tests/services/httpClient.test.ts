import { HttpClient } from '../../src/services/httpClient';
import { ApiError, NetworkError, RateLimitError } from '../../src/models/Errors';

// Mock fetch globally
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

// Helper function to create complete Response mocks
function createMockResponse(options: {
  ok: boolean;
  status: number;
  statusText?: string;
  headers?: Headers;
  json?: () => Promise<any>;
  text?: () => Promise<string>;
}): Response {
  return {
    ok: options.ok,
    status: options.status,
    statusText: options.statusText || 'OK',
    headers: options.headers || new Headers(),
    json: options.json || (async () => ({})),
    text: options.text || (async () => ''),
    redirected: false,
    type: 'default',
    url: 'https://api.example.com/test',
    body: null,
    bodyUsed: false,
    bytes: jest.fn(),
    clone: jest.fn(),
    arrayBuffer: jest.fn(),
    blob: jest.fn(),
    formData: jest.fn(),
  } as unknown as Response;
}

describe('HttpClient', () => {
  let httpClient: HttpClient;

  beforeEach(() => {
    // Use a shorter timeout for testing to avoid Jest timeout issues
    httpClient = new HttpClient(1000, 3);
    mockFetch.mockClear();
  });

  describe('get', () => {
    it('should make successful GET request', async () => {
      const mockResponse = { data: 'test' };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => mockResponse,
        redirected: false,
        type: 'default',
        url: 'https://api.example.com/test',
        body: null,
        bodyUsed: false,
        bytes: jest.fn(),
        clone: jest.fn(),
        arrayBuffer: jest.fn(),
        blob: jest.fn(),
        formData: jest.fn(),
        text: jest.fn(),
      } as unknown as Response);

      const result = await httpClient.get('https://api.example.com/test');

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.example.com/test',
        expect.objectContaining({
          method: 'GET',
          headers: expect.any(Headers),
        })
      );
    });

    it('should handle API errors', async () => {
      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        text: async () => 'Error details',
      }));

      await expect(httpClient.get('https://api.example.com/test'))
        .rejects.toThrow(ApiError);
    });

    it('should handle network errors', async () => {
      // Mock the sleep method to make tests faster
      const sleepSpy = jest.spyOn(httpClient as any, 'sleep').mockResolvedValue(undefined);
      
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(httpClient.get('https://api.example.com/test'))
        .rejects.toThrow(NetworkError);
        
      sleepSpy.mockRestore();
    });

    it('should handle rate limiting', async () => {
      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests',
        headers: new Headers({ 'Retry-After': '60' }),
      }));

      await expect(httpClient.get('https://api.example.com/test'))
        .rejects.toThrow(RateLimitError);
    });

    it('should retry on transient errors', async () => {
      // Mock the sleep method to make tests faster
      const sleepSpy = jest.spyOn(httpClient as any, 'sleep').mockResolvedValue(undefined);
      
      // First call fails with 500 error (retryable)
      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        text: async () => 'Server error',
      }));

      // Second call succeeds
      const mockResponse = { data: 'test' };
      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => mockResponse,
      }));

      // Execute the request
      const result = await httpClient.get('https://api.example.com/test');

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledTimes(2);
      
      sleepSpy.mockRestore();
    });

    it('should handle timeout errors', async () => {
      // Mock the sleep method to make tests faster
      const sleepSpy = jest.spyOn(httpClient as any, 'sleep').mockResolvedValue(undefined);
      
      const abortError = new Error('The operation was aborted');
      abortError.name = 'AbortError';
      mockFetch.mockRejectedValueOnce(abortError);

      await expect(httpClient.get('https://api.example.com/test'))
        .rejects.toThrow(NetworkError);
        
      sleepSpy.mockRestore();
    });

    it('should handle different content types', async () => {
      // Test JSON response
      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ message: 'success' }),
      }));

      const jsonResult = await httpClient.get('https://api.example.com/json');
      expect(jsonResult).toEqual({ message: 'success' });

      // Test text response
      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'text/plain' }),
        text: async () => 'plain text',
      }));

      const textResult = await httpClient.get('https://api.example.com/text');
      expect(textResult).toBe('plain text');
    });

    it('should add custom headers', async () => {
      const mockResponse = { data: 'test' };
      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => mockResponse,
      }));

      await httpClient.get('https://api.example.com/test', {
        headers: { 'Custom-Header': 'value' }
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.example.com/test',
        expect.objectContaining({
          method: 'GET',
          headers: expect.any(Headers),
        })
      );
      
      // Check that the headers were properly set
      const callArgs = mockFetch.mock.calls[0];
      if (callArgs && callArgs[1]) {
        const headers = callArgs[1].headers as Headers;
        expect(headers.get('User-Agent')).toBe('BusinessSearchApp/1.0');
        expect(headers.get('Custom-Header')).toBe('value');
      }
    });
  });

  describe('post', () => {
    it('should make successful POST request with data', async () => {
      const mockResponse = { success: true };
      const postData = { name: 'test' };
      
      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: true,
        status: 201,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => mockResponse,
      }));

      const result = await httpClient.post('https://api.example.com/test', postData);

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.example.com/test',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(postData),
          headers: expect.any(Headers),
        })
      );
      
      // Check that the headers were properly set
      const callArgs = mockFetch.mock.calls[0];
      if (callArgs && callArgs[1]) {
        const headers = callArgs[1].headers as Headers;
        expect(headers.get('User-Agent')).toBe('BusinessSearchApp/1.0');
        expect(headers.get('Content-Type')).toBe('application/json');
      }
    });
  });

  describe('head', () => {
    it('should make successful HEAD request', async () => {
      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: true,
        status: 200,
        headers: new Headers({ 'Content-Type': 'text/html' }),
      }));

      const result = await httpClient.head('https://example.com');

      expect(result.ok).toBe(true);
      expect(result.status).toBe(200);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://example.com',
        expect.objectContaining({
          method: 'HEAD'
        })
      );
    });

    it('should handle redirects in HEAD requests', async () => {
      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: true,
        status: 301,
        headers: new Headers({ 'Location': 'https://example.com/new' }),
      }));

      const result = await httpClient.head('https://example.com');

      expect(result.status).toBe(301);
    });
  });

  describe('retry logic', () => {
    it('should not retry on non-retryable errors', async () => {
      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        text: async () => 'Bad request',
      }));

      await expect(httpClient.get('https://api.example.com/test'))
        .rejects.toThrow(ApiError);

      expect(mockFetch).toHaveBeenCalledTimes(1);
    });

    it('should exhaust all retries before failing', async () => {
      // Mock the sleep method to make tests faster
      const sleepSpy = jest.spyOn(httpClient as any, 'sleep').mockResolvedValue(undefined);
      
      // All calls fail with retryable errors
      mockFetch.mockResolvedValue(createMockResponse({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        text: async () => 'Server error',
      }));

      await expect(httpClient.get('https://api.example.com/test'))
        .rejects.toThrow(ApiError);

      // Should try initial request + 3 retries = 4 total
      expect(mockFetch).toHaveBeenCalledTimes(4);
      
      // Should have called sleep 3 times (for retries)
      expect(sleepSpy).toHaveBeenCalledTimes(3);
      
      sleepSpy.mockRestore();
    });

    it('should use exponential backoff for retries', async () => {
      // Mock the sleep method to track delays
      const sleepSpy = jest.spyOn(httpClient as any, 'sleep').mockResolvedValue(undefined);

      mockFetch.mockResolvedValue(createMockResponse({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        text: async () => 'Server error',
      }));

      await expect(httpClient.get('https://api.example.com/test'))
        .rejects.toThrow(ApiError);

      // Should have made 4 attempts (initial + 3 retries)
      expect(mockFetch).toHaveBeenCalledTimes(4);

      // Should have called sleep 3 times (for retries)
      expect(sleepSpy).toHaveBeenCalledTimes(3);

      sleepSpy.mockRestore();
    });
  });

  describe('request interceptors', () => {
    it('should apply request interceptors', async () => {
      const interceptor = jest.fn((config) => ({
        ...config,
        headers: { ...config.headers, 'X-Custom': 'intercepted' }
      }));

      httpClient.addRequestInterceptor(interceptor);

      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({}),
      }));

      await httpClient.get('https://api.example.com/test');

      expect(interceptor).toHaveBeenCalled();
      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.example.com/test',
        expect.objectContaining({
          method: 'GET',
          headers: expect.any(Headers),
        })
      );
      
      // Check that the interceptor was applied
      const callArgs = mockFetch.mock.calls[0];
      if (callArgs && callArgs[1]) {
        const headers = callArgs[1].headers as Headers;
        expect(headers.get('User-Agent')).toBe('BusinessSearchApp/1.0');
        expect(headers.get('X-Custom')).toBe('intercepted');
      }
    });
  });

  describe('response interceptors', () => {
    it('should apply response interceptors', async () => {
      const interceptor = jest.fn((response) => ({
        ...response,
        intercepted: true
      }));

      httpClient.addResponseInterceptor(interceptor);

      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ data: 'test' }),
      }));

      const result = await httpClient.get('https://api.example.com/test');

      expect(interceptor).toHaveBeenCalled();
      expect(result).toEqual({
        data: 'test',
        intercepted: true
      });
    });
  });

  describe('constructor options', () => {
    it('should accept custom timeout and retries', () => {
      const customClient = new HttpClient(10000, 5);
      expect(customClient).toBeInstanceOf(HttpClient);
    });

    it('should use default values when not provided', () => {
      const defaultClient = new HttpClient();
      expect(defaultClient).toBeInstanceOf(HttpClient);
    });
  });

  describe('error classification', () => {
    it('should classify 4xx errors as non-retryable', async () => {
      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        text: async () => 'Bad request',
      }));

      await expect(httpClient.get('https://api.example.com/test'))
        .rejects.toThrow(ApiError);

      // Should not retry 4xx errors
      expect(mockFetch).toHaveBeenCalledTimes(1);
    });

    it('should classify 5xx errors as retryable', async () => {
      // Mock the sleep method to make tests faster
      const sleepSpy = jest.spyOn(httpClient as any, 'sleep').mockResolvedValue(undefined);
      
      mockFetch.mockResolvedValue(createMockResponse({
        ok: false,
        status: 503,
        statusText: 'Service Unavailable',
        text: async () => 'Service unavailable',
      }));

      await expect(httpClient.get('https://api.example.com/test'))
        .rejects.toThrow(ApiError);

      // Should retry 5xx errors
      expect(mockFetch).toHaveBeenCalledTimes(4);
      
      // Should have called sleep 3 times (for retries)
      expect(sleepSpy).toHaveBeenCalledTimes(3);
      
      sleepSpy.mockRestore();
    });
  });

  describe('request configuration', () => {
    it('should handle custom headers in requests', async () => {
      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ success: true }),
      }));

      await httpClient.get('https://api.example.com/test', {
        headers: { 'Authorization': 'Bearer token' }
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.example.com/test',
        expect.objectContaining({
          method: 'GET',
          headers: expect.any(Headers),
        })
      );
      
      // Check that the headers were properly set
      const callArgs = mockFetch.mock.calls[0];
      if (callArgs && callArgs[1]) {
        const headers = callArgs[1].headers as Headers;
        expect(headers.get('User-Agent')).toBe('BusinessSearchApp/1.0');
        expect(headers.get('Authorization')).toBe('Bearer token');
      }
    });

    it('should handle custom timeout', async () => {
      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ success: true }),
      }));

      await httpClient.get('https://api.example.com/test', {
        timeout: 15000
      });

      expect(mockFetch).toHaveBeenCalled();
    });

    it('should handle custom retry count', async () => {
      // Mock the sleep method to make tests faster
      const sleepSpy = jest.spyOn(httpClient as any, 'sleep').mockResolvedValue(undefined);
      
      mockFetch.mockResolvedValue(createMockResponse({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        text: async () => 'Server error',
      }));

      await expect(httpClient.get('https://api.example.com/test', {
        retries: 1
      })).rejects.toThrow(ApiError);

      // Should try initial request + 1 retry = 2 total
      expect(mockFetch).toHaveBeenCalledTimes(2);
      
      sleepSpy.mockRestore();
    });
  });

  describe('edge cases', () => {
    it('should handle fetch throwing non-Error objects', async () => {
      // Mock the sleep method to make tests faster
      const sleepSpy = jest.spyOn(httpClient as any, 'sleep').mockResolvedValue(undefined);
      
      mockFetch.mockRejectedValue('string error');

      await expect(httpClient.get('https://api.example.com/test'))
        .rejects.toThrow(NetworkError);
        
      sleepSpy.mockRestore();
    });

    it('should handle response.text() throwing error', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        headers: new Headers(),
        text: async () => { throw new Error('Text parsing failed'); },
        redirected: false,
        type: 'default',
        url: 'https://api.example.com/test',
        body: null,
        bodyUsed: false,
        bytes: jest.fn(),
        clone: jest.fn(),
        arrayBuffer: jest.fn(),
        blob: jest.fn(),
        formData: jest.fn(),
        json: jest.fn(),
      } as unknown as Response);

      await expect(httpClient.get('https://api.example.com/test'))
        .rejects.toThrow(ApiError);
    });

    it('should handle response.json() throwing error', async () => {
      // Mock the sleep method to make tests faster
      const sleepSpy = jest.spyOn(httpClient as any, 'sleep').mockResolvedValue(undefined);
      
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => { throw new Error('JSON parsing failed'); },
        redirected: false,
        type: 'default',
        url: 'https://api.example.com/test',
        body: null,
        bodyUsed: false,
        bytes: jest.fn(),
        clone: jest.fn(),
        arrayBuffer: jest.fn(),
        blob: jest.fn(),
        formData: jest.fn(),
        text: jest.fn(),
      } as unknown as Response);

      await expect(httpClient.get('https://api.example.com/test'))
        .rejects.toThrow(NetworkError);
        
      sleepSpy.mockRestore();
    });
  });
});
