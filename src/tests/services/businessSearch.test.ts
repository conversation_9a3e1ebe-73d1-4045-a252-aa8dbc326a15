import { BusinessSearchService, SearchRequest } from '../../services/businessSearch';
import { GeocodingService } from '../../services/geocoding';
import { GooglePlacesService } from '../../services/googlePlaces';
import { WebsiteVerificationService } from '../../services/websiteVerification';
import { Business, SearchResult, Coordinates } from '../../models/Business';
import { ValidationError } from '../../models/Errors';

// Mock the services
jest.mock('../../services/geocoding');
jest.mock('../../services/googlePlaces');
jest.mock('../../services/websiteVerification');

const MockedGeocodingService = GeocodingService as jest.MockedClass<typeof GeocodingService>;
const MockedGooglePlacesService = GooglePlacesService as jest.MockedClass<typeof GooglePlacesService>;
const MockedWebsiteVerificationService = WebsiteVerificationService as jest.MockedClass<typeof WebsiteVerificationService>;

describe('BusinessSearchService', () => {
  let businessSearchService: BusinessSearchService;
  let geocodingService: jest.Mocked<GeocodingService>;
  let googlePlacesService: jest.Mocked<GooglePlacesService>;
  let websiteVerificationService: jest.Mocked<WebsiteVerificationService>;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();

    // Create mock instances
    geocodingService = new GeocodingService('dummy_key') as jest.Mocked<GeocodingService>;
    geocodingService = {
      getCoordinates: jest.fn(),
    } as any;

    googlePlacesService = {
      findNearby: jest.fn(),
    } as any;

    mockWebsiteVerificationService = new MockedWebsiteVerificationService() as jest.Mocked<WebsiteVerificationService>;

    businessSearchService = new BusinessSearchService(geocodingService, googlePlacesService, mockWebsiteVerificationService);

    geocodingService.zipCodeToCoordinates = jest.fn();
    googlePlacesService.searchNearby = jest.fn();
    mockWebsiteVerificationService.verifyMultipleWebsites = jest.fn();
  });

  describe('searchBusinesses', () => {
    const mockCoordinates: Coordinates = {
      latitude: 40.7128,
      longitude: -74.0060
    };

    const mockPlacesResult = {
      places: [
        {
          place_id: '1',
          name: 'Restaurant A',
          formatted_address: '123 Main St, New York, NY',
          website: 'https://restaurant-a.com',
          types: ['restaurant'],
          rating: 4.5,
          geometry: {
            location: { lat: 40.7128, lng: -74.0060 }
          }
        },
        {
          place_id: '2',
          name: 'Restaurant B',
          formatted_address: '456 Oak Ave, New York, NY',
          types: ['restaurant'],
          rating: 4.0,
          geometry: {
            location: { lat: 40.7130, lng: -74.0062 }
          }
        }
      ],
      totalResults: 2,
      nextPageToken: undefined
    };

    const mockVerificationResults = [
      {
        url: 'https://restaurant-a.com',
        status: 'verified' as const,
        accessible: true,
        confidence: 0.9,
        verifiedAt: new Date()
      }
    ];

    it('should search businesses successfully', async () => {
      // Setup mocks
      geocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);
      googlePlacesService.searchNearby.mockResolvedValue(mockPlacesResult);
      mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue(mockVerificationResults);

      const result = await businessSearchService.searchBusinesses({
        zipCode: '10001',
        radius: 10,
        businessType: 'restaurant'
      });

      expect(result.results.withWebsites).toHaveLength(1);
      expect(result.results.withoutWebsites).toHaveLength(1);
      expect(result.statistics.totalFound).toBe(2);
      expect(result.statistics.withWebsiteCount).toBe(1);
      expect(result.statistics.withoutWebsiteCount).toBe(1);
      expect(result.statistics.websiteAdoptionRate).toBe(0.5);

      // Verify service calls
      expect(geocodingService.zipCodeToCoordinates).toHaveBeenCalledWith('10001');
      expect(googlePlacesService.searchNearby).toHaveBeenCalledWith(
        mockCoordinates,
        16093.4, // 10 miles in meters
        'restaurant',
        {}
      );
      expect(mockWebsiteVerificationService.verifyMultipleWebsites).toHaveBeenCalledWith(
        ['https://restaurant-a.com'],
        5
      );
    });

    it('should handle businesses without websites', async () => {
      const placesWithoutWebsites = {
        places: [
          {
            place_id: '1',
            name: 'Restaurant A',
            formatted_address: '123 Main St, New York, NY',
            types: ['restaurant'],
            rating: 4.5,
            geometry: {
              location: { lat: 40.7128, lng: -74.0060 }
            }
          }
        ],
        totalResults: 1,
        nextPageToken: undefined
      };

      geocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);
      googlePlacesService.searchNearby.mockResolvedValue(placesWithoutWebsites);
      mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue([]);

      const result = await businessSearchService.searchBusinesses({
        zipCode: '10001',
        radius: 10,
        businessType: 'restaurant'
      });

      expect(result.results.withWebsites).toHaveLength(0);
      expect(result.results.withoutWebsites).toHaveLength(1);
      expect(result.statistics.websiteAdoptionRate).toBe(0);
    });

    it('should validate search parameters', async () => {
      await expect(businessSearchService.searchBusinesses({
        zipCode: 'invalid',
        radius: 10,
        businessType: 'restaurant'
      })).rejects.toThrow(ValidationError);

      await expect(businessSearchService.searchBusinesses({
        zipCode: '10001',
        radius: 0,
        businessType: 'restaurant'
      })).rejects.toThrow(ValidationError);

      await expect(businessSearchService.searchBusinesses({
        zipCode: '10001',
        radius: 10,
        businessType: 'invalid_type'
      })).rejects.toThrow(ValidationError);
    });

    it('should handle geocoding errors gracefully', async () => {
      geocodingService.zipCodeToCoordinates.mockRejectedValue(new Error('Geocoding failed'));

      await expect(businessSearchService.searchBusinesses({
        zipCode: '10001',
        radius: 10,
        businessType: 'restaurant'
      })).rejects.toThrow('Geocoding failed');
    });

    it('should calculate distances correctly', async () => {
      geocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);
      googlePlacesService.searchNearby.mockResolvedValue(mockPlacesResult);
      mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue([]);

      const result = await businessSearchService.searchBusinesses({
        zipCode: '10001',
        radius: 10,
        businessType: 'restaurant'
      });

      // Check that distances are calculated
      expect(result.results.withoutWebsites[0].distance).toBeGreaterThanOrEqual(0);
      expect(result.results.withoutWebsites[1].distance).toBeGreaterThanOrEqual(0);
    });

    it('should sort results by distance', async () => {
      const placesWithDifferentDistances = {
        places: [
          {
            place_id: '1',
            name: 'Far Restaurant',
            formatted_address: '123 Main St, New York, NY',
            types: ['restaurant'],
            geometry: {
              location: { lat: 40.8000, lng: -74.1000 } // Further away
            }
          },
          {
            place_id: '2',
            name: 'Near Restaurant',
            formatted_address: '456 Oak Ave, New York, NY',
            types: ['restaurant'],
            geometry: {
              location: { lat: 40.7129, lng: -74.0061 } // Closer
            }
          }
        ],
        totalResults: 2,
        nextPageToken: undefined
      };

      geocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);
      googlePlacesService.searchNearby.mockResolvedValue(placesWithDifferentDistances);
      mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue([]);

      const result = await businessSearchService.searchBusinesses({
        zipCode: '10001',
        radius: 10,
        businessType: 'restaurant'
      });

      // Results should be sorted by distance (closest first)
      expect(result.results.withoutWebsites[0].name).toBe('Near Restaurant');
      expect(result.results.withoutWebsites[1].name).toBe('Far Restaurant');
      expect(result.results.withoutWebsites[0].distance).toBeLessThan(result.results.withoutWebsites[1].distance);
    });

    it('should handle website verification failures gracefully', async () => {
      geocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);
      googlePlacesService.searchNearby.mockResolvedValue(mockPlacesResult);
      mockWebsiteVerificationService.verifyMultipleWebsites.mockRejectedValue(new Error('Verification failed'));

      // Should still return results even if website verification fails
      const result = await businessSearchService.searchBusinesses({
        zipCode: '10001',
        radius: 10,
        businessType: 'restaurant'
      });

      expect(result.results.withWebsites).toHaveLength(0);
      expect(result.results.withoutWebsites).toHaveLength(2);
      expect(result.statistics.totalFound).toBe(2);
    });
  });

  describe('getSearchHistory', () => {
    it('should return search history', () => {
      const history = businessSearchService.getSearchHistory();
      expect(Array.isArray(history)).toBe(true);
    });
  });

  describe('clearSearchHistory', () => {
    it('should clear search history', () => {
      businessSearchService.clearSearchHistory();
      const history = businessSearchService.getSearchHistory();
      expect(history).toHaveLength(0);
    });
  });

  describe('getSearchStatistics', () => {
    it('should return search statistics', () => {
      const stats = businessSearchService.getSearchStatistics();
      expect(stats).toHaveProperty('totalSearches');
      expect(stats).toHaveProperty('averageResultsPerSearch');
      expect(stats).toHaveProperty('averageWebsiteAdoptionRate');
    });
  });

  it('should perform a search and return classified results', async () => {
    geocodingService.zipCodeToCoordinates.mockResolvedValue({ latitude: 34.0522, longitude: -118.2437 });
    googlePlacesService.searchNearby.mockResolvedValue({
      places: [
        { place_id: '1', name: 'Biz with Web', website: 'http://biz.com', geometry: { location: { lat: 0, lng: 0 } } } as any,
        { place_id: '2', name: 'Biz without Web', geometry: { location: { lat: 0, lng: 0 } } } as any,
      ],
      next_page_token: undefined,
    });
    mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue([{url: 'http://biz.com', accessible: true, confidence: 1}]);

    const params: SearchRequest = { zipCode: '90210', radius: 5, businessType: 'restaurant' };
    const results: SearchResult = await businessSearchService.searchBusinesses(params);

    expect(results.results.withWebsites).toHaveLength(1);
    expect(results.results.withoutWebsites).toHaveLength(1);
    expect(results.statistics.totalFound).toBe(2);
  });

  it('should return search history', async () => {
    geocodingService.zipCodeToCoordinates.mockResolvedValue({ latitude: 34.0522, longitude: -118.2437 });
    googlePlacesService.searchNearby.mockResolvedValue({ places: [], next_page_token: undefined });
    mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue([]);
    
    const params: SearchRequest = { zipCode: '90210', radius: 5, businessType: 'restaurant' };
    await businessSearchService.searchBusinesses(params);
    
    const history = businessSearchService.getSearchHistory();
    expect(history).toHaveLength(1);
  });

  it('should clear search history', async () => {
    geocodingService.zipCodeToCoordinates.mockResolvedValue({ latitude: 34.0522, longitude: -118.2437 });
    googlePlacesService.searchNearby.mockResolvedValue({ places: [], next_page_token: undefined });
    mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue([]);

    const params: SearchRequest = { zipCode: '90210', radius: 5, businessType: 'restaurant' };
    await businessSearchService.searchBusinesses(params);

    businessSearchService.clearSearchHistory();
    const history = businessSearchService.getSearchHistory();
    expect(history).toHaveLength(0);
  });

  it('should return search statistics', async () => {
    geocodingService.zipCodeToCoordinates.mockResolvedValue({ latitude: 34.0522, longitude: -118.2437 });
    googlePlacesService.searchNearby.mockResolvedValue({ places: [], next_page_token: undefined });
    mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue([]);

    const params: SearchRequest = { zipCode: '90210', radius: 5, businessType: 'restaurant' };
    await businessSearchService.searchBusinesses(params);

    const stats = businessSearchService.getSearchStatistics();
    expect(stats.totalSearches).toBe(1);
  });
});
