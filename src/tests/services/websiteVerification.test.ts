import { WebsiteVerificationService } from '../../src/services/websiteVerification';
import { WebsiteVerificationError, NetworkError } from '../../src/models/Errors';
import { WebsiteStatus } from '../../src/models/Business';

// Mock fetch globally
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe('WebsiteVerificationService', () => {
  let websiteService: WebsiteVerificationService;

  beforeEach(() => {
    websiteService = new WebsiteVerificationService();
    mockFetch.mockClear();
  });

  describe('verifyWebsite', () => {
    it('should verify accessible website', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'text/html' }),
      } as Response);

      const result = await websiteService.verifyWebsite('https://example.com');

      expect(result.status).toBe('verified');
      expect(result.accessible).toBe(true);
      expect(result.statusCode).toBe(200);
      expect(result.confidence).toBeGreaterThan(0.8);
    });

    it('should handle redirects as verified', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 301,
        headers: new Headers({ 'location': 'https://example.com/new' }),
      } as Response);

      const result = await websiteService.verifyWebsite('https://example.com');

      expect(result.status).toBe('verified');
      expect(result.accessible).toBe(true);
      expect(result.statusCode).toBe(301);
    });

    it('should handle 404 as unverified', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
        headers: new Headers(),
      } as Response);

      const result = await websiteService.verifyWebsite('https://example.com');

      expect(result.status).toBe('unverified');
      expect(result.accessible).toBe(false);
      expect(result.statusCode).toBe(404);
      expect(result.confidence).toBeLessThan(0.5);
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      const result = await websiteService.verifyWebsite('https://example.com');

      expect(result.status).toBe('unverified');
      expect(result.accessible).toBe(false);
      expect(result.error).toContain('Network error');
    });

    it('should validate URL format', async () => {
      await expect(websiteService.verifyWebsite('invalid-url'))
        .rejects.toThrow(WebsiteVerificationError);

      expect(mockFetch).not.toHaveBeenCalled();
    });

    it('should handle timeout', async () => {
      const abortError = new Error('The operation was aborted');
      abortError.name = 'AbortError';
      mockFetch.mockRejectedValueOnce(abortError);

      const result = await websiteService.verifyWebsite('https://example.com');

      expect(result.status).toBe('unverified');
      expect(result.accessible).toBe(false);
      expect(result.error).toContain('timeout');
    });

    it('should cache verification results', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers(),
      } as Response);

      // First call
      const result1 = await websiteService.verifyWebsite('https://example.com');
      
      // Second call should use cache
      const result2 = await websiteService.verifyWebsite('https://example.com');

      expect(result1).toEqual(result2);
      expect(mockFetch).toHaveBeenCalledTimes(1);
    });
  });

  describe('verifyMultipleWebsites', () => {
    it('should verify multiple websites concurrently', async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Headers(),
        } as Response)
        .mockResolvedValueOnce({
          ok: false,
          status: 404,
          statusText: 'Not Found',
        } as Response);

      const urls = ['https://example1.com', 'https://example2.com'];
      const results = await websiteService.verifyMultipleWebsites(urls);

      expect(results).toHaveLength(2);
      expect(results[0].status).toBe('verified');
      expect(results[1].status).toBe('unverified');
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });

    it('should handle mixed results', async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Headers(),
        } as Response)
        .mockRejectedValueOnce(new Error('Network error'));

      const urls = ['https://good.com', 'https://bad.com'];
      const results = await websiteService.verifyMultipleWebsites(urls);

      expect(results).toHaveLength(2);
      expect(results[0].status).toBe('verified');
      expect(results[1].status).toBe('unverified');
    });

    it('should respect concurrency limit', async () => {
      const urls = Array.from({ length: 10 }, (_, i) => `https://example${i}.com`);
      
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Headers(),
      } as Response);

      const startTime = Date.now();
      await websiteService.verifyMultipleWebsites(urls, 3); // Limit to 3 concurrent
      const endTime = Date.now();

      // With concurrency limit, it should take longer than if all were parallel
      expect(mockFetch).toHaveBeenCalledTimes(10);
    });
  });

  describe('getVerificationStats', () => {
    it('should return verification statistics', async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Headers(),
        } as Response)
        .mockResolvedValueOnce({
          ok: false,
          status: 404,
          statusText: 'Not Found',
        } as Response);

      await websiteService.verifyWebsite('https://good.com');
      await websiteService.verifyWebsite('https://bad.com');

      const stats = websiteService.getVerificationStats();

      expect(stats.totalVerified).toBe(2);
      expect(stats.successfulVerifications).toBe(1);
      expect(stats.failedVerifications).toBe(1);
      expect(stats.successRate).toBe(0.5);
    });
  });

  describe('clearCache', () => {
    it('should clear verification cache', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers(),
      } as Response);

      await websiteService.verifyWebsite('https://example.com');
      expect(mockFetch).toHaveBeenCalledTimes(1);

      websiteService.clearCache();

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers(),
      } as Response);

      await websiteService.verifyWebsite('https://example.com');
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('calculateConfidence', () => {
    it('should calculate high confidence for successful responses', () => {
      const confidence = websiteService.calculateConfidence(200, 'text/html', 500);
      expect(confidence).toBeGreaterThan(0.8);
    });

    it('should calculate low confidence for error responses', () => {
      const confidence = websiteService.calculateConfidence(404, '', 0);
      expect(confidence).toBeLessThan(0.3);
    });

    it('should consider response time in confidence calculation', () => {
      const fastConfidence = websiteService.calculateConfidence(200, 'text/html', 100);
      const slowConfidence = websiteService.calculateConfidence(200, 'text/html', 5000);
      
      expect(fastConfidence).toBeGreaterThan(slowConfidence);
    });
  });
});
