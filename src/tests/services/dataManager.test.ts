import { DataManager } from '../../services/dataManager';
import { SearchResult, Business } from '../../models/Business';
import { CacheError } from '../../models/Errors';

// Create a proper localStorage mock
const localStorageData: { [key: string]: string } = {};
const localStorageMock = {
  getItem: jest.fn((key: string) => localStorageData[key] || null),
  setItem: jest.fn((key: string, value: string) => {
    localStorageData[key] = value;
  }),
  removeItem: jest.fn((key: string) => {
    delete localStorageData[key];
  }),
  clear: jest.fn(() => {
    Object.keys(localStorageData).forEach(key => delete localStorageData[key]);
  }),
  key: jest.fn((index: number) => {
    const keys = Object.keys(localStorageData);
    return keys[index] || null;
  }),
  get length() {
    return Object.keys(localStorageData).length;
  },
};

// Replace global localStorage
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
  writable: true,
});

describe('DataManager', () => {
  let dataManager: DataManager;

  beforeEach(() => {
    dataManager = new DataManager();
    // Clear the actual storage data
    Object.keys(localStorageData).forEach(key => delete localStorageData[key]);
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
    localStorageMock.clear.mockClear();
    localStorageMock.key.mockClear();
  });

  const mockSearchResult: SearchResult = {
    searchParams: {
      zipCode: '10001',
      radius: 10,
      businessType: 'restaurant',
      timestamp: new Date('2023-01-01T00:00:00Z'),
    },
    results: {
      withWebsites: [
        {
          id: '1',
          name: 'Restaurant A',
          address: '123 Main St',
          websiteStatus: 'verified',
          category: ['restaurant'],
          distance: 1.5,
          metadata: {
            lastUpdated: new Date('2023-01-01T00:00:00Z'),
            dataSource: 'google_places',
            confidence: 0.9,
          },
        } as Business,
      ],
      withoutWebsites: [
        {
          id: '2',
          name: 'Restaurant B',
          address: '456 Oak Ave',
          websiteStatus: 'none',
          category: ['restaurant'],
          distance: 2.1,
          metadata: {
            lastUpdated: new Date('2023-01-01T00:00:00Z'),
            dataSource: 'google_places',
            confidence: 0.8,
          },
        } as Business,
      ],
    },
    statistics: {
      totalFound: 2,
      withWebsiteCount: 1,
      withoutWebsiteCount: 1,
      websiteAdoptionRate: 0.5,
      searchDuration: 1500,
    },
  };

  describe('saveSearchResult', () => {
    it('should save search result to localStorage', () => {
      const key = dataManager.saveSearchResult(mockSearchResult);

      expect(key).toBeTruthy();
      expect(localStorageMock.setItem).toHaveBeenCalled();
    });

    it('should generate unique keys for different searches', () => {
      const key1 = dataManager.saveSearchResult(mockSearchResult);
      const key2 = dataManager.saveSearchResult({
        ...mockSearchResult,
        searchParams: {
          ...mockSearchResult.searchParams,
          zipCode: '10002',
        },
      });

      expect(key1).not.toBe(key2);
    });

    it('should handle localStorage errors gracefully', () => {
      // Mock localStorage.setItem to throw an error
      localStorageMock.setItem.mockImplementationOnce(() => {
        throw new Error('Storage quota exceeded');
      });

      expect(() => dataManager.saveSearchResult(mockSearchResult)).toThrow(CacheError);
    });
  });

  describe('getSearchResult', () => {
    it('should retrieve saved search result', () => {
      const key = dataManager.saveSearchResult(mockSearchResult);
      const retrieved = dataManager.getSearchResult(key);

      expect(retrieved).toBeTruthy();
      expect(retrieved!.searchParams.zipCode).toBe('10001');
      expect(retrieved!.results.withWebsites).toHaveLength(1);
      expect(retrieved!.results.withoutWebsites).toHaveLength(1);
    });

    it('should return null for non-existent key', () => {
      const result = dataManager.getSearchResult('non-existent-key');
      expect(result).toBeNull();
    });

    it('should return null for expired data', () => {
      // Mock an expired entry
      const expiredData = {
        data: mockSearchResult,
        timestamp: Date.now() - (25 * 60 * 60 * 1000), // 25 hours ago
        expiresAt: Date.now() - (1 * 60 * 60 * 1000), // 1 hour ago
      };

      (localStorage.getItem as jest.Mock).mockReturnValueOnce(JSON.stringify(expiredData));

      const result = dataManager.getSearchResult('expired-key');
      expect(result).toBeNull();
    });

    it('should handle corrupted data gracefully', () => {
      (localStorage.getItem as jest.Mock).mockReturnValueOnce('invalid-json');

      const result = dataManager.getSearchResult('corrupted-key');
      expect(result).toBeNull();
    });
  });

  describe('getAllSearchResults', () => {
    it('should return all saved search results', () => {
      const key1 = dataManager.saveSearchResult(mockSearchResult);
      const key2 = dataManager.saveSearchResult({
        ...mockSearchResult,
        searchParams: {
          ...mockSearchResult.searchParams,
          zipCode: '10002',
        },
      });

      const allResults = dataManager.getAllSearchResults();

      expect(allResults).toHaveLength(2);
      expect(allResults.some(r => r.searchParams.zipCode === '10001')).toBe(true);
      expect(allResults.some(r => r.searchParams.zipCode === '10002')).toBe(true);
    });

    it('should exclude expired results', () => {
      // Save a valid result
      dataManager.saveSearchResult(mockSearchResult);

      // Manually add an expired entry to the localStorage data
      const expiredKey = 'business_search_expired';
      const expiredData = {
        data: mockSearchResult,
        timestamp: Date.now() - (25 * 60 * 60 * 1000),
        expiresAt: Date.now() - (1 * 60 * 60 * 1000),
      };
      localStorageData[expiredKey] = JSON.stringify(expiredData);

      const allResults = dataManager.getAllSearchResults();

      expect(allResults).toHaveLength(1); // Only the valid result
    });
  });

  describe('deleteSearchResult', () => {
    it('should delete search result from localStorage', () => {
      const key = dataManager.saveSearchResult(mockSearchResult);
      
      dataManager.deleteSearchResult(key);

      expect(localStorage.removeItem).toHaveBeenCalledWith(key);
      expect(dataManager.getSearchResult(key)).toBeNull();
    });

    it('should handle deletion of non-existent key gracefully', () => {
      expect(() => dataManager.deleteSearchResult('non-existent')).not.toThrow();
    });
  });

  describe('clearAllData', () => {
    it('should clear all business search data', () => {
      dataManager.saveSearchResult(mockSearchResult);
      dataManager.saveSearchResult({
        ...mockSearchResult,
        searchParams: {
          ...mockSearchResult.searchParams,
          zipCode: '10002',
        },
      });

      dataManager.clearAllData();

      expect(dataManager.getAllSearchResults()).toHaveLength(0);
    });

    it('should only clear business search data, not other localStorage data', () => {
      // Add some non-business search data
      localStorageData['other_app_data'] = 'should_remain';
      
      dataManager.saveSearchResult(mockSearchResult);
      dataManager.clearAllData();

      expect(localStorageData['other_app_data']).toBe('should_remain');
      expect(dataManager.getAllSearchResults()).toHaveLength(0);
    });
  });

  describe('getStorageInfo', () => {
    it('should return storage information', () => {
      dataManager.saveSearchResult(mockSearchResult);
      
      const info = dataManager.getStorageInfo();

      expect(info).toHaveProperty('totalEntries');
      expect(info).toHaveProperty('totalSizeBytes');
      expect(info).toHaveProperty('oldestEntry');
      expect(info).toHaveProperty('newestEntry');
      expect(info.totalEntries).toBeGreaterThan(0);
    });

    it('should return zero values for empty storage', () => {
      const info = dataManager.getStorageInfo();

      expect(info.totalEntries).toBe(0);
      expect(info.totalSizeBytes).toBe(0);
      expect(info.oldestEntry).toBeNull();
      expect(info.newestEntry).toBeNull();
    });
  });

  describe('cleanupExpiredData', () => {
    it('should remove expired entries', () => {
      // Save a valid result
      const validKey = dataManager.saveSearchResult(mockSearchResult);

      // Manually add an expired entry
      const expiredKey = 'business_search_expired';
      const expiredData = {
        data: mockSearchResult,
        timestamp: Date.now() - (25 * 60 * 60 * 1000),
        expiresAt: Date.now() - (1 * 60 * 60 * 1000),
      };
      localStorageData[expiredKey] = JSON.stringify(expiredData);

      const removedCount = dataManager.cleanupExpiredData();

      expect(removedCount).toBe(1);
      expect(localStorageData[expiredKey]).toBeUndefined();
    });

    it('should return count of removed entries', () => {
      // All entries are valid by default
      dataManager.saveSearchResult(mockSearchResult);
      
      const removedCount = dataManager.cleanupExpiredData();
      
      expect(removedCount).toBe(0);
    });
  });

  describe('exportData', () => {
    it('should export all search results as JSON', () => {
      dataManager.saveSearchResult(mockSearchResult);
      
      const exported = dataManager.exportData();
      const parsed = JSON.parse(exported);

      expect(parsed).toHaveProperty('version');
      expect(parsed).toHaveProperty('exportDate');
      expect(parsed).toHaveProperty('searchResults');
      expect(parsed.searchResults).toHaveLength(1);
    });

    it('should include metadata in export', () => {
      dataManager.saveSearchResult(mockSearchResult);
      
      const exported = dataManager.exportData();
      const parsed = JSON.parse(exported);

      expect(parsed.version).toBe('1.0');
      expect(parsed.exportDate).toBeTruthy();
      expect(new Date(parsed.exportDate)).toBeInstanceOf(Date);
    });
  });

  describe('importData', () => {
    it('should import search results from JSON', () => {
      const exportData = {
        version: '1.0',
        exportDate: new Date().toISOString(),
        searchResults: [mockSearchResult],
      };

      const importedCount = dataManager.importData(JSON.stringify(exportData));

      expect(importedCount).toBe(1);
      expect(dataManager.getAllSearchResults()).toHaveLength(1);
    });

    it('should handle invalid JSON gracefully', () => {
      expect(() => dataManager.importData('invalid-json')).toThrow(CacheError);
    });

    it('should validate import data structure', () => {
      const invalidData = {
        version: '1.0',
        // Missing searchResults
      };

      expect(() => dataManager.importData(JSON.stringify(invalidData))).toThrow(CacheError);
    });
  });
});
