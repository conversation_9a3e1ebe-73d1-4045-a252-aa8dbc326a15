import { GooglePlacesService } from '../../src/services/googlePlaces';
import { ApiError, NetworkError, RateLimitError } from '../../src/models/Errors';
import { Coordinates, PlaceResult } from '../../src/models/Business';

// Mock fetch globally
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe('GooglePlacesService', () => {
  let placesService: GooglePlacesService;

  beforeEach(() => {
    placesService = new GooglePlacesService();
    mockFetch.mockClear();
  });

  describe('searchNearby', () => {
    const mockCoordinates: Coordinates = {
      latitude: 40.7128,
      longitude: -74.0060
    };

    const mockApiResponse = {
      results: [
        {
          place_id: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
          name: 'Test Restaurant',
          formatted_address: '123 Main St, New York, NY 10001, USA',
          formatted_phone_number: '******-123-4567',
          website: 'https://testrestaurant.com',
          types: ['restaurant', 'food', 'establishment'],
          rating: 4.5,
          geometry: {
            location: {
              lat: 40.7128,
              lng: -74.0060
            }
          }
        },
        {
          place_id: 'ChIJN1t_tDeuEmsRUsoyG83frY5',
          name: 'Another Restaurant',
          formatted_address: '456 Oak Ave, New York, NY 10002, USA',
          types: ['restaurant', 'food'],
          rating: 4.0,
          geometry: {
            location: {
              lat: 40.7130,
              lng: -74.0062
            }
          }
        }
      ],
      status: 'OK',
      next_page_token: 'next_token_123'
    };

    it('should search for nearby places successfully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockApiResponse
      } as Response);

      const result = await placesService.searchNearby(
        mockCoordinates,
        1000,
        'restaurant'
      );

      expect(result.places).toHaveLength(2);
      expect(result.places[0].name).toBe('Test Restaurant');
      expect(result.places[0].website).toBe('https://testrestaurant.com');
      expect(result.nextPageToken).toBe('next_token_123');
      expect(result.totalResults).toBe(2);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('nearbysearch/json'),
        expect.objectContaining({
          method: 'GET'
        })
      );
    });

    it('should handle API errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request'
      } as Response);

      await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant'))
        .rejects.toThrow(ApiError);
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant'))
        .rejects.toThrow(NetworkError);
    });

    it('should handle zero results', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          results: [],
          status: 'ZERO_RESULTS'
        })
      } as Response);

      const result = await placesService.searchNearby(mockCoordinates, 1000, 'restaurant');

      expect(result.places).toHaveLength(0);
      expect(result.totalResults).toBe(0);
      expect(result.nextPageToken).toBeUndefined();
    });

    it('should handle rate limiting', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests'
      } as Response);

      await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant'))
        .rejects.toThrow(ApiError);
    });

    it('should validate search parameters', async () => {
      await expect(placesService.searchNearby(mockCoordinates, 0, 'restaurant'))
        .rejects.toThrow();

      await expect(placesService.searchNearby(mockCoordinates, 1000, ''))
        .rejects.toThrow();
    });

    it('should include custom parameters in request', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockApiResponse
      } as Response);

      await placesService.searchNearby(
        mockCoordinates,
        1000,
        'restaurant',
        {
          minprice: 2,
          maxprice: 4,
          opennow: true
        }
      );

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('minprice=2'),
        expect.any(Object)
      );
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('maxprice=4'),
        expect.any(Object)
      );
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('opennow=true'),
        expect.any(Object)
      );
    });
  });

  describe('getPlaceDetails', () => {
    const mockPlaceId = 'ChIJN1t_tDeuEmsRUsoyG83frY4';
    const mockDetailsResponse = {
      result: {
        place_id: mockPlaceId,
        name: 'Test Restaurant',
        formatted_address: '123 Main St, New York, NY 10001, USA',
        formatted_phone_number: '******-123-4567',
        website: 'https://testrestaurant.com',
        types: ['restaurant', 'food', 'establishment'],
        rating: 4.5,
        geometry: {
          location: {
            lat: 40.7128,
            lng: -74.0060
          }
        },
        opening_hours: {
          open_now: true,
          weekday_text: [
            'Monday: 9:00 AM – 10:00 PM',
            'Tuesday: 9:00 AM – 10:00 PM'
          ]
        }
      },
      status: 'OK'
    };

    it('should get place details successfully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockDetailsResponse
      } as Response);

      const result = await placesService.getPlaceDetails(mockPlaceId);

      expect(result.place_id).toBe(mockPlaceId);
      expect(result.name).toBe('Test Restaurant');
      expect(result.website).toBe('https://testrestaurant.com');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('details/json'),
        expect.objectContaining({
          method: 'GET'
        })
      );
    });

    it('should handle place not found', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          status: 'NOT_FOUND'
        })
      } as Response);

      await expect(placesService.getPlaceDetails(mockPlaceId))
        .rejects.toThrow(ApiError);
    });

    it('should cache place details', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockDetailsResponse
      } as Response);

      // First call
      const result1 = await placesService.getPlaceDetails(mockPlaceId);
      
      // Second call should use cache
      const result2 = await placesService.getPlaceDetails(mockPlaceId);

      expect(result1).toEqual(result2);
      expect(mockFetch).toHaveBeenCalledTimes(1);
    });
  });

  describe('searchByText', () => {
    const mockCoordinates: Coordinates = {
      latitude: 40.7128,
      longitude: -74.0060
    };

    const mockTextResponse = {
      results: [
        {
          place_id: 'ChIJN1t_tDeuEmsRUsoyG83frY4',
          name: 'Pizza Palace',
          formatted_address: '789 Broadway, New York, NY 10003, USA',
          types: ['restaurant', 'meal_delivery'],
          rating: 4.2,
          geometry: {
            location: {
              lat: 40.7300,
              lng: -74.0000
            }
          }
        }
      ],
      status: 'OK'
    };

    it('should search by text query successfully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockTextResponse
      } as Response);

      const result = await placesService.searchByText(
        'pizza restaurants in New York',
        mockCoordinates,
        5000
      );

      expect(result.places).toHaveLength(1);
      expect(result.places[0].name).toBe('Pizza Palace');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('textsearch/json'),
        expect.objectContaining({
          method: 'GET'
        })
      );
    });

    it('should validate text query', async () => {
      await expect(placesService.searchByText('', mockCoordinates, 1000))
        .rejects.toThrow();
    });
  });

  describe('getNextPage', () => {
    it('should get next page of results', async () => {
      const nextPageResponse = {
        results: [
          {
            place_id: 'ChIJN1t_tDeuEmsRUsoyG83frY6',
            name: 'Next Page Restaurant',
            formatted_address: '999 Next St, New York, NY 10004, USA',
            types: ['restaurant'],
            geometry: {
              location: {
                lat: 40.7400,
                lng: -74.0100
              }
            }
          }
        ],
        status: 'OK'
      };

      // Clear any previous mocks
      mockFetch.mockClear();
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => nextPageResponse
      } as Response);

      const result = await placesService.getNextPage('next_token_123');

      expect(result.places).toHaveLength(1);
      expect(result.places[0].name).toBe('Next Page Restaurant');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('pagetoken=next_token_123'),
        expect.any(Object)
      );
    });

    it('should handle invalid page token', async () => {
      mockFetch.mockClear();
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          status: 'INVALID_REQUEST'
        })
      } as Response);

      await expect(placesService.getNextPage('invalid_token'))
        .rejects.toThrow(ApiError);
    });
  });

  describe('clearCache', () => {
    it('should clear the places cache', async () => {
      // Add something to cache first
      mockFetch.mockClear();
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          result: {
            place_id: 'test',
            name: 'Test Place',
            formatted_address: 'Test Address',
            geometry: {
              location: {
                lat: 40.7128,
                lng: -74.0060
              }
            }
          },
          status: 'OK'
        })
      } as Response);

      await placesService.getPlaceDetails('test');
      expect(mockFetch).toHaveBeenCalledTimes(1);

      // Clear cache
      placesService.clearCache();

      // Should make API call again
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          result: {
            place_id: 'test',
            name: 'Test Place',
            formatted_address: 'Test Address',
            geometry: {
              location: {
                lat: 40.7128,
                lng: -74.0060
              }
            }
          },
          status: 'OK'
        })
      } as Response);

      await placesService.getPlaceDetails('test');
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('constructor error handling', () => {
    it('should throw ConfigurationError when no API key provided', () => {
      const originalKey = process.env.GOOGLE_PLACES_API_KEY;
      delete process.env.GOOGLE_PLACES_API_KEY;

      expect(() => new GooglePlacesService()).toThrow('Google Places API key is required');

      // Restore API key
      if (originalKey) {
        process.env.GOOGLE_PLACES_API_KEY = originalKey;
      }
    });

    it('should accept API key from constructor parameter', () => {
      expect(() => new GooglePlacesService('test-api-key')).not.toThrow();
    });
  });

  describe('searchNearby parameter validation', () => {
    const mockCoordinates: Coordinates = {
      latitude: 40.7128,
      longitude: -74.0060
    };

    it('should validate radius range', async () => {
      await expect(placesService.searchNearby(mockCoordinates, 0, 'restaurant'))
        .rejects.toThrow();

      await expect(placesService.searchNearby(mockCoordinates, 60000, 'restaurant'))
        .rejects.toThrow();
    });

    it('should validate minprice range', async () => {
      await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant', {
        minprice: -1
      })).rejects.toThrow();

      await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant', {
        minprice: 5
      })).rejects.toThrow();
    });

    it('should validate maxprice range', async () => {
      await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant', {
        maxprice: -1
      })).rejects.toThrow();

      await expect(placesService.searchNearby(mockCoordinates, 1000, 'restaurant', {
        maxprice: 5
      })).rejects.toThrow();
    });

    it('should handle all optional parameters', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          results: [],
          status: 'ZERO_RESULTS'
        })
      } as Response);

      await placesService.searchNearby(mockCoordinates, 1000, 'restaurant', {
        minprice: 1,
        maxprice: 3,
        opennow: true,
        keyword: 'pizza'
      });

      const callUrl = mockFetch.mock.calls[0][0] as string;
      expect(callUrl).toContain('minprice=1');
      expect(callUrl).toContain('maxprice=3');
      expect(callUrl).toContain('opennow=true');
      expect(callUrl).toContain('keyword=pizza');
    });
  });

  describe('error handling edge cases', () => {
    it('should handle API status errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          status: 'REQUEST_DENIED',
          error_message: 'API key invalid'
        })
      } as Response);

      await expect(placesService.searchNearby({
        latitude: 40.7128,
        longitude: -74.0060
      }, 1000, 'restaurant')).rejects.toThrow('API key invalid');
    });

    it('should handle unknown errors', async () => {
      mockFetch.mockRejectedValueOnce('unknown error type');

      await expect(placesService.searchNearby({
        latitude: 40.7128,
        longitude: -74.0060
      }, 1000, 'restaurant')).rejects.toThrow('Unknown error during places search');
    });

    it('should handle text search errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(placesService.searchByText('pizza', {
        latitude: 40.7128,
        longitude: -74.0060
      }, 1000)).rejects.toThrow('Network error during text search');
    });

    it('should handle place details errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(placesService.getPlaceDetails('test-place-id'))
        .rejects.toThrow('Network error during place details');
    });

    it('should handle next page errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(placesService.getNextPage('test-token'))
        .rejects.toThrow('Network error during next page');
    });
  });

  describe('getPlaceDetails with fields', () => {
    it('should include fields parameter when provided', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          result: {
            place_id: 'test',
            name: 'Test Place',
            formatted_address: 'Test Address',
            geometry: {
              location: { lat: 40.7128, lng: -74.0060 }
            }
          },
          status: 'OK'
        })
      } as Response);

      await placesService.getPlaceDetails('test-place-id', ['name', 'geometry', 'formatted_address']);

      // Fields get URL encoded, so commas become %2C
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('fields=name%2Cgeometry%2Cformatted_address'),
        expect.any(Object)
      );
    });
  });

  describe('transformPlaceResult', () => {
    it('should handle missing optional fields', async () => {
      const minimalApiResponse = {
        results: [
          {
            place_id: 'test',
            name: 'Test Place',
            // Missing most optional fields
            geometry: {
              location: { lat: 40.7128, lng: -74.0060 }
            }
          }
        ],
        status: 'OK'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => minimalApiResponse
      } as Response);

      const result = await placesService.searchNearby({
        latitude: 40.7128,
        longitude: -74.0060
      }, 1000, 'restaurant');

      expect(result.places[0]).toEqual({
        place_id: 'test',
        name: 'Test Place',
        formatted_address: '',
        formatted_phone_number: undefined,
        website: undefined,
        types: [],
        rating: undefined,
        geometry: {
          location: { lat: 40.7128, lng: -74.0060 }
        }
      });
    });

    it('should handle missing geometry', async () => {
      const noGeometryResponse = {
        results: [
          {
            place_id: 'test',
            name: 'Test Place'
            // Missing geometry
          }
        ],
        status: 'OK'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => noGeometryResponse
      } as Response);

      const result = await placesService.searchNearby({
        latitude: 40.7128,
        longitude: -74.0060
      }, 1000, 'restaurant');

      expect(result.places[0].geometry.location).toEqual({
        lat: 0,
        lng: 0
      });
    });
  });

  describe('getCacheSize', () => {
    it('should return current cache size', async () => {
      const initialSize = placesService.getCacheSize();
      expect(initialSize).toBe(0);

      // Add something to cache
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          result: {
            place_id: 'test',
            name: 'Test Place',
            formatted_address: 'Test Address',
            geometry: {
              location: { lat: 40.7128, lng: -74.0060 }
            }
          },
          status: 'OK'
        })
      } as Response);

      await placesService.getPlaceDetails('test');

      const newSize = placesService.getCacheSize();
      expect(newSize).toBe(1);
    });
  });
});
