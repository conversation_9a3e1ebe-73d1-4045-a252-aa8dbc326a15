import '@testing-library/jest-dom';

// Mock environment variables for testing
process.env.GOOGLE_PLACES_API_KEY = 'test-api-key';
process.env.API_BASE_URL = 'https://maps.googleapis.com/maps/api';
process.env.REQUEST_TIMEOUT = '5000';
process.env.MAX_RETRIES = '3';
process.env.RATE_LIMIT_REQUESTS_PER_SECOND = '10';
process.env.RATE_LIMIT_BURST_SIZE = '20';
process.env.CACHE_EXPIRATION_HOURS = '24';
process.env.MAX_CACHE_SIZE_MB = '50';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  key: jest.fn(),
  length: 0,
};
global.localStorage = localStorageMock as any;

// Mock fetch for HTTP requests
global.fetch = jest.fn();

// Reset all mocks before each test
beforeEach(() => {
  jest.clearAllMocks();
  localStorageMock.getItem.mockClear();
  localStorageMock.setItem.mockClear();
  localStorageMock.removeItem.mockClear();
  localStorageMock.clear.mockClear();
  localStorageMock.key.mockClear();
  localStorageMock.length = 0;
  (global.fetch as jest.Mock).mockClear();
});
