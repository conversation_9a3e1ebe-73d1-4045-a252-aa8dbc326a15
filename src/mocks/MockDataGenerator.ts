export class MockDataGenerator {
  private businessNames = [
    "Joe's Coffee Shop", "Maria's Bakery", "Tech Solutions Inc",
    "Green Garden Restaurant", "Quick Fix Auto", "Wellness Spa Center",
    "Book Haven", "Pet Paradise", "Fashion Forward Boutique",
    "Home Improvement Pro", "Dental Care Plus", "Learning Tree Academy"
  ];
  
  private businessTypes = [
    ["restaurant", "food"], ["coffee_shop", "cafe"], ["retail", "store"],
    ["health", "medical"], ["automotive", "service"], ["education", "school"]
  ];
  
  generateBusinesses(zipCode: string, radius: number, type: string, count: number = 20): any {
    const businesses = [];
    const hasWebsitePercentage = this.getWebsiteAdoptionRate(type);
    const currentHour = new Date().getHours();
    
    for (let i = 0; i < count; i++) {
      const hasWebsite = Math.random() < hasWebsitePercentage;
      const correlatedData = this.generateCorrelatedData();
      const business = {
        place_id: `mock_place_${i}_${Date.now()}`,
        name: this.businessNames[i % this.businessNames.length] + ` ${zipCode}`,
        formatted_address: `${100 + i} Main St, City, ${zipCode}`,
        types: this.businessTypes[i % this.businessTypes.length],
        geometry: {
          location: {
            lat: 37.7749 + (Math.random() - 0.5) * 0.1,
            lng: -122.4194 + (Math.random() - 0.5) * 0.1
          }
        },
        rating: correlatedData.rating,
        user_ratings_total: correlatedData.reviewCount,
        price_level: correlatedData.priceLevel,
        opening_hours: { open_now: this.generateRealisticOpenStatus(type, currentHour) },
        ...(hasWebsite && { 
          website: `https://${this.businessNames[i % this.businessNames.length].toLowerCase().replace(/[^a-z0-9]/g, '')}.com` 
        })
      };
      businesses.push(business);
    }
    
    return {
      results: businesses,
      status: "OK"
    };
  }
  
  generateGeocodingResult(zipCode: string): any {
    // Mock coordinates for common US zip codes
    const mockCoordinates: Record<string, any> = {
      "94105": { lat: 37.7749, lng: -122.4194 }, // San Francisco
      "10001": { lat: 40.7128, lng: -74.0060 },  // New York
      "90210": { lat: 34.0901, lng: -118.4065 }, // Beverly Hills
      "60601": { lat: 41.8781, lng: -87.6298 },  // Chicago
      "33101": { lat: 25.7617, lng: -80.1918 }   // Miami
    };
    
    return {
      results: [{
        geometry: {
          location: mockCoordinates[zipCode] || { lat: 39.8283, lng: -98.5795 }
        },
        formatted_address: `${zipCode}, USA`
      }],
      status: "OK"
    };
  }

  // Add time-based patterns
  generateRealisticOpenStatus(businessType: string, currentHour: number): boolean {
    const patterns: Record<string, { opens: number; closes: number }> = {
      restaurant: { opens: 11, closes: 22 },
      coffee_shop: { opens: 6, closes: 20 },
      bar: { opens: 16, closes: 2 }, // Handle overnight logic
      retail: { opens: 9, closes: 21 },
      medical: { opens: 8, closes: 17 }
    };
    
    const schedule = patterns[businessType] || { opens: 9, closes: 18 };
    
    // Special handling for bars closing after midnight
    if (schedule.closes < schedule.opens) {
        return currentHour >= schedule.opens || currentHour < schedule.closes;
    }

    return currentHour >= schedule.opens && currentHour < schedule.closes;
  }
  
  // Industry-specific website adoption rates
  getWebsiteAdoptionRate(businessType: string): number {
    const rates: Record<string, number> = {
      restaurant: 0.55,      // 55% have websites
      auto_repair: 0.28,     // 28% have websites
      hair_salon: 0.42,      // 42% have websites
      medical: 0.85,         // 85% have websites
      retail: 0.70,          // 70% have websites
      coffee_shop: 0.65      // 65% have websites
    };
    return rates[businessType] || 0.50;
  }
  
  // Generate correlated data (rating affects review count)
  generateCorrelatedData(): any {
    const rating = 3 + Math.random() * 2;
    const reviewCount = Math.floor(Math.pow(rating, 2.5) * (10 + Math.random() * 20));
    const priceLevel = Math.ceil(rating / 1.5); // Better rated places tend to be pricier
    
    return { rating, reviewCount, priceLevel };
  }
} 