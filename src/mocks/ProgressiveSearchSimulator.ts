import { MockDataGenerator } from './MockDataGenerator';

export class ProgressiveSearchSimulator {
  constructor(private mockGenerator: MockDataGenerator) {}

  async * searchWithProgress(params: any): AsyncGenerator<any> {
    const totalResults = 20; // Simulate finding 20 results
    const batchSize = 5;
    const delay = 300; // ms

    for (let i = 0; i < totalResults; i += batchSize) {
      await new Promise(resolve => setTimeout(resolve, delay));

      // Each call to generateBusinesses returns a full batch
      const batch = this.mockGenerator.generateBusinesses(
        params.zipCode,
        params.radius,
        params.type,
        batchSize,
      );

      yield {
        results: batch.results,
        progress: ((i + batchSize) / totalResults) * 100,
        isComplete: i + batchSize >= totalResults
      };
    }
  }
} 