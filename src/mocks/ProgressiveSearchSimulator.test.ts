import { ProgressiveSearchSimulator } from './ProgressiveSearchSimulator';
import { MockDataGenerator } from './MockDataGenerator';

jest.mock('./MockDataGenerator');

describe('ProgressiveSearchSimulator', () => {
    it('yields results in batches with progress', async () => {
        const mockGenerator = new MockDataGenerator();
        (mockGenerator.generateBusinesses as jest.Mock).mockReturnValue({
            results: Array.from({ length: 5 }, (_, i) => ({ id: i.toString() })),
            status: 'OK',
        });
        
        const simulator = new ProgressiveSearchSimulator(mockGenerator);
        const params = { zipCode: '94105', radius: 5, type: 'restaurant' };

        const results = [];
        for await (const batch of simulator.searchWithProgress(params)) {
            results.push(batch);
        }

        expect(results).toHaveLength(4); // 20 results, 5 per batch
        
        expect(results[0].progress).toBe(25);
        expect(results[0].isComplete).toBe(false);
        expect(results[0].results).toHaveLength(5);

        expect(results[3].progress).toBe(100);
        expect(results[3].isComplete).toBe(true);
    });
}); 