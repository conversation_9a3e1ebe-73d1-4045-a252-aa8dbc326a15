import { RateLimitError } from '../models/Errors';

/**
 * Token bucket rate limiter implementation
 * Allows burst requests up to bucket size, then limits to refill rate
 */
export class TokenBucketRateLimiter {
  private tokens: number;
  private lastRefill: number;
  private readonly refillInterval: number; // milliseconds per token

  /**
   * Creates a new token bucket rate limiter
   * @param tokensPerSecond - Number of tokens to add per second
   * @param bucketSize - Maximum number of tokens in bucket
   */
  constructor(
    private readonly tokensPerSecond: number,
    private readonly bucketSize: number
  ) {
    if (tokensPerSecond <= 0 || bucketSize <= 0) {
      throw new Error('Tokens per second and bucket size must be positive');
    }

    this.tokens = bucketSize;
    this.lastRefill = Date.now();
    this.refillInterval = 1000 / tokensPerSecond; // ms per token
  }

  /**
   * Attempts to consume a token from the bucket
   * @returns True if token was consumed, false if bucket is empty
   */
  public tryConsume(): boolean {
    this.refillTokens();

    if (this.tokens >= 1) {
      this.tokens -= 1;
      return true;
    }

    return false;
  }

  /**
   * Waits for a token to become available
   * @param timeoutMs - Maximum time to wait in milliseconds
   * @returns Promise that resolves when token is available
   * @throws RateLimitError if timeout is reached
   */
  public async waitForToken(timeoutMs: number = 5000): Promise<void> {
    const startTime = Date.now();

    return new Promise((resolve, reject) => {
      const checkToken = () => {
        if (this.tryConsume()) {
          resolve();
          return;
        }

        if (Date.now() - startTime >= timeoutMs) {
          reject(new RateLimitError('Rate limit timeout exceeded'));
          return;
        }

        // Check again after a short delay
        setTimeout(checkToken, Math.min(this.refillInterval / 2, 50));
      };

      checkToken();
    });
  }

  /**
   * Gets the number of available tokens
   * @returns Number of available tokens
   */
  public getAvailableTokens(): number {
    this.refillTokens();
    return Math.floor(this.tokens);
  }

  /**
   * Resets the bucket to full capacity
   */
  public reset(): void {
    this.tokens = this.bucketSize;
    this.lastRefill = Date.now();
  }

  /**
   * Gets the time until next token is available
   * @returns Milliseconds until next token, or 0 if tokens are available
   */
  public getTimeUntilNextToken(): number {
    this.refillTokens();

    if (this.tokens >= 1) {
      return 0;
    }

    const timeSinceLastRefill = Date.now() - this.lastRefill;
    const timeForNextToken = this.refillInterval - (timeSinceLastRefill % this.refillInterval);
    
    return Math.max(0, timeForNextToken);
  }

  /**
   * Refills tokens based on elapsed time
   */
  private refillTokens(): void {
    const now = Date.now();
    const timePassed = now - this.lastRefill;

    if (timePassed >= this.refillInterval) {
      const tokensToAdd = Math.floor(timePassed / this.refillInterval);
      this.tokens = Math.min(this.bucketSize, this.tokens + tokensToAdd);
      this.lastRefill = now - (timePassed % this.refillInterval);
    }
  }
}

/**
 * Global rate limiter instance for API requests
 */
let globalRateLimiter: TokenBucketRateLimiter | null = null;

/**
 * Gets or creates the global rate limiter instance
 * @param tokensPerSecond - Tokens per second (default from config)
 * @param bucketSize - Bucket size (default from config)
 * @returns Global rate limiter instance
 */
export function getGlobalRateLimiter(
  tokensPerSecond: number = 10,
  bucketSize: number = 20
): TokenBucketRateLimiter {
  if (!globalRateLimiter) {
    globalRateLimiter = new TokenBucketRateLimiter(tokensPerSecond, bucketSize);
  }
  return globalRateLimiter;
}

/**
 * Resets the global rate limiter
 */
export function resetGlobalRateLimiter(): void {
  globalRateLimiter = null;
}
