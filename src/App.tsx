import React, { useState } from 'react';
import { BusinessSearchApp, AppConfig } from './BusinessSearchApp';
import { SearchResult, Business } from './models/Business';
import { MockDataGenerator } from './mocks/MockDataGenerator';
import { MockServiceWrapper } from './mocks/MockServiceWrapper';
import { ProgressiveSearchSimulator } from './mocks/ProgressiveSearchSimulator';
import SearchForm from './components/SearchForm';
import ResultsTables from './components/ResultsTables';
import SearchHistory from './components/SearchHistory';
import DataManagement from './components/DataManagement';
import ErrorStates, { ErrorInfo } from './components/ErrorStates';
import DebugPanel from './components/DebugPanel';

// This would come from a config file or environment variables
const config: AppConfig = {
  apiKey: process.env.REACT_APP_GOOGLE_API_KEY || 'mock_key',
};

interface AppProps {
  businessSearchApp: BusinessSearchApp;
}

function App({ businessSearchApp }: AppProps) {
  const [searchResults, setSearchResults] = useState<SearchResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<ErrorInfo | null>(null);
  const [progress, setProgress] = useState(0);
  const [searchParams, setSearchParams] = useState<any>({ radius: 5 });
  const [mockSettings, setMockSettings] = useState({ websiteRate: 65, resultCount: 20 });
  const [performanceMetrics, setPerformanceMetrics] = useState({});
  const [lastApiCall, setLastApiCall] = useState({});

  // Initialize services with mock wrapper
  const mockGenerator = new MockDataGenerator();

  // Wrap services for mock data
  if (process.env.REACT_APP_USE_MOCK_DATA === 'true') {
    const businessSearchService = businessSearchApp.businessSearchService;
    businessSearchService.geocodingService = new MockServiceWrapper(
      businessSearchService.geocodingService,
      mockGenerator
    ) as any;
    businessSearchService.googlePlacesService = new MockServiceWrapper(
      businessSearchService.googlePlacesService,
      mockGenerator
    ) as any;
  }

  const handleSearch = async (params: any) => {
    setLoading(true);
    setError(null);
    setSearchResults(null);
    setProgress(0);
    setSearchParams(params);

    try {
      if (process.env.REACT_APP_USE_MOCK_DATA === 'true') {
        const simulator = new ProgressiveSearchSimulator(mockGenerator);
        const allBusinesses: Business[] = [];

        for await (const batch of simulator.searchWithProgress(params)) {
          const newBusinesses: Business[] = batch.results.map((p: any) => ({
            id: p.place_id,
            name: p.name,
            address: p.formatted_address,
            website: p.website,
            websiteStatus: p.website ? 'verified' : 'none',
            category: p.types,
            rating: p.rating,
            distance: Math.random() * params.radius,
            metadata: {
              lastUpdated: new Date(),
              dataSource: 'google_places',
              confidence: 1,
            },
          }));

          allBusinesses.push(...newBusinesses);

          const withWebsites = allBusinesses.filter(b => b.website);
          const withoutWebsites = allBusinesses.filter(b => !b.website);

          setSearchResults({
            searchParams: { ...params, timestamp: new Date() },
            results: { withWebsites, withoutWebsites },
            statistics: {
              totalFound: allBusinesses.length,
              withWebsiteCount: withWebsites.length,
              withoutWebsiteCount: withoutWebsites.length,
              websiteAdoptionRate: allBusinesses.length > 0 ? withWebsites.length / allBusinesses.length : 0,
              searchDuration: 0, // Not applicable for progressive search
            },
          });
          setProgress(batch.progress);
        }
      } else {
        const results = await businessSearchApp.searchBusinesses(params);
        setSearchResults(results);
        if (results.results.withWebsites.length === 0 && results.results.withoutWebsites.length === 0) {
          setError({ type: 'no_results' });
        }
      }
    } catch (err: any) {
      if (err.name === 'ValidationError') {
        setError({ type: 'validation', details: [err.message] });
      } else {
        setError({ type: 'generic', message: err.message });
      }
    } finally {
      setLoading(false);
    }
  };

  // Placeholder functions for error actions
  const retrySearch = () => handleSearch(searchParams);
  const useOfflineMode = () => console.log("Using offline mode");
  const increaseRadius = () => handleSearch({ ...searchParams, radius: searchParams.radius + 5 });
  const removeFilters = () => handleSearch({ zipCode: searchParams.zipCode, radius: searchParams.radius, businessType: searchParams.businessType });
  const suggestNearbyZip = () => handleSearch({ ...searchParams, zipCode: '94107' }); // Mock nearby zip

  // Placeholder functions for debug panel
  const updateMockSettings = (settings: any) => setMockSettings(settings);
  const simulateError = (errorType: string) => {
      if (errorType === 'network') {
          setError({ type: 'network' });
      } else if (errorType === 'rateLimit') {
          setError({ type: 'generic', message: 'Rate limit exceeded' });
      } else if (errorType === 'invalid') {
          setError({ type: 'generic', message: 'Invalid API response' });
      } else {
          setError(null);
      }
  };
  const resetAllData = () => {
    setSearchResults(null);
    setError(null);
    console.log('Resetting all data...');
  };
  const seedTestData = () => console.log('Seeding test data...');

  return (
    <div>
      <SearchForm onSearch={handleSearch} />
      {loading && <p>Searching... {progress > 0 && `${progress.toFixed(0)}%`}</p>}
      {error && <ErrorStates 
                  error={error}
                  retry={retrySearch}
                  useOfflineMode={useOfflineMode}
                  increaseRadius={increaseRadius}
                  removeFilters={removeFilters}
                  suggestNearbyZip={suggestNearbyZip}
                  radius={searchParams.radius}
                  nearbyZip="94107" // Mock nearby zip
                />}
      {searchResults && !error && <ResultsTables results={searchResults} />}
      <SearchHistory />
      <DataManagement />
      <DebugPanel 
        mockSettings={mockSettings}
        updateMockSettings={updateMockSettings}
        simulateError={simulateError}
        performanceMetrics={performanceMetrics}
        lastApiCall={lastApiCall}
        resetAllData={resetAllData}
        seedTestData={seedTestData}
      />
    </div>
  );
}

export default App;
