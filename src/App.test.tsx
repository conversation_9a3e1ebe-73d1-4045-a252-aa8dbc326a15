import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import App from './App';
import { BusinessSearchApp } from './BusinessSearchApp';

// Mock child components that are not the focus of this test
jest.mock('./components/SearchHistory', () => () => <div>Search History</div>);
jest.mock('./components/DataManagement', () => () => <div>Data Management</div>);
jest.mock('./components/DebugPanel', () => () => <div>Debug Panel</div>);

describe('App', () => {
    let mockBusinessSearchApp: BusinessSearchApp;
    let mockSearchBusinesses: jest.Mock;

    beforeEach(() => {
        mockSearchBusinesses = jest.fn();
        mockBusinessSearchApp = {
            searchBusinesses: mockSearchBusinesses,
        } as any;
    });

    it('renders all main components', () => {
        render(<App businessSearchApp={mockBusinessSearchApp} />);
        expect(screen.getByText('Business Search')).toBeInTheDocument();
        expect(screen.getByText('Search History')).toBeInTheDocument();
        expect(screen.getByText('Data Management')).toBeInTheDocument();
        expect(screen.getByText('Debug Panel')).toBeInTheDocument();
    });

    it('performs a search and displays results', async () => {
        mockSearchBusinesses.mockImplementation(async () => {
            await new Promise(resolve => setTimeout(resolve, 100)); // Simulate network delay
            return {
                results: {
                    withWebsites: [{ id: '1', name: 'Test Biz', address: '123 Test St', distance: 1, metadata: {} }],
                    withoutWebsites: [],
                },
                statistics: {
                    totalFound: 1,
                    withWebsiteCount: 1,
                    withoutWebsiteCount: 0,
                }
            };
        });

        render(<App businessSearchApp={mockBusinessSearchApp} />);
        
        fireEvent.change(screen.getByLabelText(/zip code/i), { target: { value: '94105' } });
        fireEvent.click(screen.getByRole('button', { name: /search/i }));

        expect(await screen.findByText(/searching.../i)).toBeInTheDocument();

        await waitFor(() => {
            expect(screen.getByText('Test Biz')).toBeInTheDocument();
        });

        expect(screen.queryByText(/searching.../i)).not.toBeInTheDocument();
        expect(screen.getByText('Businesses WITH Websites (1)')).toBeInTheDocument();
    });

    it('displays an error message when the search fails', async () => {
        mockSearchBusinesses.mockRejectedValue(new Error('Network Error'));

        render(<App businessSearchApp={mockBusinessSearchApp} />);

        fireEvent.change(screen.getByLabelText(/zip code/i), { target: { value: '94105' } });
        fireEvent.click(screen.getByRole('button', { name: /search/i }));

        await waitFor(() => {
            expect(screen.getByText('❌ An Error Occurred')).toBeInTheDocument();
        });
    });
}); 