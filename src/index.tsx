import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { BusinessSearchApp, AppConfig } from './BusinessSearchApp';

const config: AppConfig = {
  apiKey: process.env.REACT_APP_GOOGLE_API_KEY || 'mock_key',
};

const businessSearchApp = new BusinessSearchApp(config);

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <App businessSearchApp={businessSearchApp} />
  </React.StrictMode>
); 