import React from 'react';
import { render, screen } from '@testing-library/react';
import ResultsTables from './ResultsTables';
import { SearchResult } from '../models/Business';

const mockResult: SearchResult = {
    searchParams: {
        zipCode: '94105',
        radius: 5,
        businessType: 'restaurant',
        timestamp: new Date(),
    },
    results: {
        withWebsites: [
            { id: '1', name: 'Website Biz', address: '123 Web St', website: 'http://web.com', websiteStatus: 'verified', category: ['restaurant'], distance: 1, metadata: { lastUpdated: new Date(), dataSource: 'google_places', confidence: 1 } },
        ],
        withoutWebsites: [
            { id: '2', name: 'No-Web Biz', address: '456 NoWeb Ave', websiteStatus: 'none', category: ['restaurant'], distance: 2, rating: 4.5, metadata: { lastUpdated: new Date(), dataSource: 'google_places', confidence: 1 } },
        ],
    },
    statistics: {
        totalFound: 2,
        withWebsiteCount: 1,
        withoutWebsiteCount: 1,
        websiteAdoptionRate: 0.5,
        searchDuration: 100,
    },
};

describe('ResultsTables', () => {
    it('renders the analytics dashboard with correct stats', () => {
        render(<ResultsTables results={mockResult} />);
        expect(screen.getByText(/total found: 2/i)).toBeInTheDocument();
        expect(screen.getByText(/with websites: 1/i)).toBeInTheDocument();
        expect(screen.getByText(/without websites: 1/i)).toBeInTheDocument();
    });

    it('renders the table for businesses with websites', () => {
        render(<ResultsTables results={mockResult} />);
        expect(screen.getByText('Businesses WITH Websites (1)')).toBeInTheDocument();
        expect(screen.getByText('Website Biz')).toBeInTheDocument();
        expect(screen.getByText('123 Web St')).toBeInTheDocument();
        expect(screen.getByRole('link', { name: /visit/i })).toHaveAttribute('href', 'http://web.com');
    });

    it('renders the table for businesses without websites', () => {
        render(<ResultsTables results={mockResult} />);
        expect(screen.getByText('🎯 Website Development Opportunities (1)')).toBeInTheDocument();
        expect(screen.getByText('No-Web Biz')).toBeInTheDocument();
        expect(screen.getByText('456 NoWeb Ave')).toBeInTheDocument();
        expect(screen.getByText(/4.5/)).toBeInTheDocument();
    });

    it('renders placeholder text for businesses with no phone number', () => {
        render(<ResultsTables results={mockResult} />);
        const cells = screen.getAllByText('N/A');
        expect(cells.length).toBeGreaterThan(0);
    });
}); 