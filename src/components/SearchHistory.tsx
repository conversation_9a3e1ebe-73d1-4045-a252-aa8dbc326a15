import React from 'react';
import { SearchParams } from '../models/Business';

const SearchHistory = () => {
    // Mock data for display purposes
    const searchHistory: ({ totalFound: number } & SearchParams)[] = [
        {
            timestamp: new Date(),
            zipCode: '94105',
            radius: 5,
            businessType: 'restaurant',
            totalFound: 15
        },
        {
            timestamp: new Date(Date.now() - 86400000),
            zipCode: '10001',
            radius: 2,
            businessType: 'coffee',
            totalFound: 8
        }
    ];

    const rerunSearch = (search: SearchParams) => {
        console.log('Re-running search for:', search);
    };

    return (
        <div>
            <h3>Search History</h3>
            <table border={1}>
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Zip Code</th>
                        <th>Radius</th>
                        <th>Type</th>
                        <th>Results</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    {searchHistory.map((search, index) => (
                        <tr key={index}>
                            <td>{new Date(search.timestamp).toLocaleString()}</td>
                            <td>{search.zipCode}</td>
                            <td>{search.radius} mi</td>
                            <td>{search.businessType || 'All'}</td>
                            <td>{search.totalFound}</td>
                            <td><button onClick={() => rerunSearch(search)}>Re-run</button></td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default SearchHistory;
