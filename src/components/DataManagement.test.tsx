import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import DataManagement from './DataManagement';

describe('DataManagement', () => {
    it('renders all data management controls', () => {
        render(<DataManagement />);
        
        expect(screen.getByRole('button', { name: /export all data/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /export current results/i })).toBeInTheDocument();
        expect(screen.getByLabelText(/import data/i)).toBeInTheDocument();
        expect(screen.getByText(/storage used/i)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /clear cache/i })).toBeInTheDocument();
    });

    it('calls console.log when buttons are clicked', () => {
        const consoleSpy = jest.spyOn(console, 'log');
        render(<DataManagement />);
        
        fireEvent.click(screen.getByRole('button', { name: /export all data/i }));
        expect(consoleSpy).toHaveBeenCalledWith('Exporting all data...');

        fireEvent.click(screen.getByRole('button', { name: /export current results/i }));
        expect(consoleSpy).toHaveBeenCalledWith('Exporting current results...');

        fireEvent.click(screen.getByRole('button', { name: /clear cache/i }));
        expect(consoleSpy).toHaveBeenCalledWith('Clearing cache...');

        consoleSpy.mockRestore();
    });
}); 