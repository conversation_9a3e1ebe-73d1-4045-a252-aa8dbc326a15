import React from 'react';

// Define a type for the error object for better type safety
interface ErrorInfo {
    type: 'network' | 'validation' | 'no_results' | 'generic';
    details?: string[];
    message?: string;
}

interface ErrorStatesProps {
    error: ErrorInfo;
    retry: () => void;
    useOfflineMode: () => void;
    increaseRadius: () => void;
    removeFilters: () => void;
    suggestNearbyZip: () => void;
    radius: number;
    nearbyZip: string;
}

const ErrorStates: React.FC<ErrorStatesProps> = ({ 
    error, retry, useOfflineMode, increaseRadius, 
    removeFilters, suggestNearbyZip, radius, nearbyZip 
}) => {
    return (
        <div id="errorContainer">
            {/* Network Error */}
            {error.type === 'network' && (
                <div style={{ border: '2px solid red', padding: '10px' }}>
                    <h3>⚠️ Network Error</h3>
                    <p>Unable to connect to search services.</p>
                    <button onClick={retry}>Retry Search</button>
                    <button onClick={useOfflineMode}>Use Offline Mode</button>
                </div>
            )}

            {/* Invalid Input */}
            {error.type === 'validation' && (
                <div style={{ border: '2px solid orange', padding: '10px' }}>
                    <h3>📝 Invalid Input</h3>
                    <ul>
                        {error.details?.map((detail, index) => <li key={index}>{detail}</li>)}
                    </ul>
                </div>
            )}

            {/* No Results */}
            {error.type === 'no_results' && (
                <div style={{ border: '2px solid blue', padding: '10px' }}>
                    <h3>🔍 No Results Found</h3>
                    <p>Try adjusting your search:</p>
                    <ul>
                        <li><button onClick={increaseRadius}>Increase radius to {radius + 5} miles</button></li>
                        <li><button onClick={removeFilters}>Remove filters</button></li>
                        <li><button onClick={suggestNearbyZip}>Try nearby zip: {nearbyZip}</button></li>
                    </ul>
                </div>
            )}

            {/* Generic Error */}
            {error.type === 'generic' && (
                 <div style={{ border: '2px solid red', padding: '10px' }}>
                    <h3>❌ An Error Occurred</h3>
                    <p>{error.message || 'An unexpected error occurred.'}</p>
                    <button onClick={retry}>Retry Search</button>
                </div>
            )}
        </div>
    );
};

export default ErrorStates;
export type { ErrorInfo }; 