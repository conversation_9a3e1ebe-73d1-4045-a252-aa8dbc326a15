import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import DebugPanel from './DebugPanel';

const mockProps = {
    mockSettings: {
        websiteRate: 50,
        resultCount: 10,
    },
    updateMockSettings: jest.fn(),
    simulateError: jest.fn(),
    performanceMetrics: { timeToFirstByte: 100 },
    lastApiCall: { url: '/test' },
    resetAllData: jest.fn(),
    seedTestData: jest.fn(),
};

describe('DebugPanel', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('renders the debug panel and its controls', () => {
        render(<DebugPanel {...mockProps} />);
        
        expect(screen.getByText('🔧 Developer Tools')).toBeInTheDocument();
        // The rest of the panel is inside <details> which may be closed.
        // We can click the summary to open it.
        fireEvent.click(screen.getByText('🔧 Developer Tools'));

        expect(screen.getByLabelText(/website adoption rate/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/result count/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/simulate errors/i)).toBeInTheDocument();
        expect(screen.getByText(/performance metrics/i)).toBeInTheDocument();
        expect(screen.getByText(/"timeToFirstByte": 100/)).toBeInTheDocument();
        expect(screen.getByText(/last api call/i)).toBeInTheDocument();
        expect(screen.getByText(/"url": "\/test"/)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /reset all data/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /seed test data/i })).toBeInTheDocument();
    });

    it('calls callbacks when controls are used', () => {
        render(<DebugPanel {...mockProps} />);
        fireEvent.click(screen.getByText('🔧 Developer Tools')); // Open details

        fireEvent.change(screen.getByLabelText(/website adoption rate/i), { target: { value: '75' } });
        expect(mockProps.updateMockSettings).toHaveBeenCalledWith({ ...mockProps.mockSettings, websiteRate: 75 });

        fireEvent.change(screen.getByLabelText(/result count/i), { target: { value: '50' } });
        expect(mockProps.updateMockSettings).toHaveBeenCalledWith({ ...mockProps.mockSettings, resultCount: 50 });

        fireEvent.change(screen.getByLabelText(/simulate errors/i), { target: { value: 'network' } });
        expect(mockProps.simulateError).toHaveBeenCalledWith('network');

        fireEvent.click(screen.getByRole('button', { name: /reset all data/i }));
        expect(mockProps.resetAllData).toHaveBeenCalledTimes(1);

        fireEvent.click(screen.getByRole('button', { name: /seed test data/i }));
        expect(mockProps.seedTestData).toHaveBeenCalledTimes(1);
    });
}); 