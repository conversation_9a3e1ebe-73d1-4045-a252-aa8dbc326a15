import React from 'react';

interface DebugPanelProps {
    mockSettings: {
        websiteRate: number;
        resultCount: number;
    };
    updateMockSettings: (settings: any) => void;
    simulateError: (errorType: string) => void;
    performanceMetrics: any;
    lastApiCall: any;
    resetAllData: () => void;
    seedTestData: () => void;
}

const DebugPanel: React.FC<DebugPanelProps> = ({
    mockSettings,
    updateMockSettings,
    simulateError,
    performanceMetrics,
    lastApiCall,
    resetAllData,
    seedTestData
}) => {
    return (
        <details>
            <summary>🔧 Developer Tools</summary>
            <div style={{ border: '1px solid gray', padding: '10px', fontFamily: 'monospace' }}>
                <h4>Mock Data Controls</h4>
                <div>
                    <label>Website Adoption Rate:
                        <input 
                            type="range" min="0" max="100" 
                            value={mockSettings.websiteRate}
                            onChange={(e) => updateMockSettings({ ...mockSettings, websiteRate: Number(e.target.value) })} 
                        />
                        {mockSettings.websiteRate}%
                    </label>
                </div>
                <div>
                    <label>Result Count:
                        <input 
                            type="number" 
                            value={mockSettings.resultCount} 
                            onChange={(e) => updateMockSettings({ ...mockSettings, resultCount: Number(e.target.value) })}
                        />
                    </label>
                </div>
                <div>
                    <label>Simulate Errors:
                        <select onChange={(e) => simulateError(e.target.value)}>
                            <option value="">No errors</option>
                            <option value="network">Network timeout</option>
                            <option value="rateLimit">Rate limit exceeded</option>
                            <option value="invalid">Invalid API response</option>
                        </select>
                    </label>
                </div>

                <h4>Performance Metrics</h4>
                <pre>{JSON.stringify(performanceMetrics, null, 2)}</pre>

                <h4>Last API Call</h4>
                <pre>{JSON.stringify(lastApiCall, null, 2)}</pre>

                <button onClick={resetAllData}>Reset All Data</button>
                <button onClick={seedTestData}>Seed Test Data</button>
            </div>
        </details>
    );
};

export default DebugPanel; 