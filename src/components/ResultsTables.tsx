import React, { useState } from 'react';
import { SearchResult, Business } from '../models/Business';

const ResultsTables = ({ results }: { results: SearchResult }) => {
  const { withWebsites, withoutWebsites } = results.results;
  const { totalFound, withWebsiteCount, withoutWebsiteCount, websiteAdoptionRate } = results.statistics;

  const [businessesWithWebsites, setBusinessesWithWebsites] = useState(withWebsites);
  const [businessesWithoutWebsites, setBusinessesWithoutWebsites] = useState(withoutWebsites);

  // Placeholder functions for sorting and actions
  const sortTable = (table: 'withWebsites' | 'withoutWebsites', key: string) => {
    console.log(`Sorting ${table} by ${key}`);
    // Implement sorting logic here
  };

  const exportWithoutWebsites = () => console.log('Exporting businesses without websites...');
  const generateOpportunityReport = () => console.log('Generating opportunity report...');
  const compareWithLastSearch = () => console.log('Comparing with last search...');
  const selectAllWithWebsites = () => console.log('Selecting all with websites...');
  const exportSelected = () => console.log('Exporting selected...');
  const previousPage = () => console.log('Going to previous page...');
  const nextPage = () => console.log('Going to next page...');
  const selectAllWithoutWebsites = () => console.log('Selecting all without websites...');
  const contactSelected = () => console.log('Generating contact list...');
  const createProposal = () => console.log('Creating proposal template...');
  const calculateOpportunityScore = (business: Business) => Math.round((business.rating || 0) * 20);
  const estimateWebsiteValue = (business: Business) => (business.rating || 0) * 500;

  return (
    <div>
      <h2>Search Results</h2>

      <div style={{ border: '1px solid black', padding: '10px', margin: '10px 0' }}>
        <h3>Analytics Dashboard</h3>
        <p>Total Found: {totalFound}</p>
        <p>With Websites: {withWebsiteCount} ({ (websiteAdoptionRate * 100).toFixed(2) }%)</p>
        <p>Without Websites: {withoutWebsiteCount}</p>
        {/* Add other analytics */}
      </div>

      <div>
        <button onClick={exportWithoutWebsites}>Export Businesses Without Websites</button>
        <button onClick={generateOpportunityReport}>Generate Opportunity Report</button>
        <button onClick={compareWithLastSearch}>Compare with Previous Search</button>
      </div>

      <div>
        <h3>Businesses WITH Websites ({withWebsiteCount})</h3>
        <div>
          Sort by:
          <button onClick={() => sortTable('withWebsites', 'name')}>Name</button>
          <button onClick={() => sortTable('withWebsites', 'rating')}>Rating</button>
          <button onClick={() => sortTable('withWebsites', 'distance')}>Distance</button>
          <button onClick={() => sortTable('withWebsites', 'price')}>Price</button>
        </div>
        <table border={1}>
          <thead>
            <tr>
              <th><input type="checkbox" onChange={selectAllWithWebsites} /></th>
              <th>Name ↕</th>
              <th>Address</th>
              <th>Phone</th>
              <th>Website</th>
              <th>Rating ↕</th>
              <th>Distance ↕</th>
              <th>Price ↕</th>
              <th>Status</th>
              <th>Website Health</th>
            </tr>
          </thead>
          <tbody>
            {businessesWithWebsites.map(business => (
              <tr key={business.id}>
                <td><input type="checkbox" value={business.id} /></td>
                <td>{business.name}</td>
                <td>{business.address}</td>
                <td>{business.phone || 'N/A'}</td>
                <td><a href={business.website} target="_blank" rel="noreferrer">Visit</a></td>
                <td>{business.rating} ⭐</td>
                <td>{business.distance.toFixed(2)}</td>
                <td>{'$'.repeat(business.metadata.confidence || 1)}</td>
                <td>{business.websiteStatus === 'verified' ? '🟢 Open' : '🔴 Closed'}</td>
                <td>{business.websiteStatus === 'verified' ? '✅' : '⚠️'}</td>
              </tr>
            ))}
          </tbody>
        </table>
        <div>
          <button onClick={exportSelected}>Export Selected</button>
          <button onClick={previousPage}>Previous</button>
          <button onClick={nextPage}>Next</button>
        </div>
      </div>

      <div>
        <h3>🎯 Website Development Opportunities ({withoutWebsiteCount})</h3>
        <div>
          Sort by:
          <button onClick={() => sortTable('withoutWebsites', 'rating')}>Best Rated First</button>
          <button onClick={() => sortTable('withoutWebsites', 'reviews')}>Most Reviews</button>
          <button onClick={() => sortTable('withoutWebsites', 'distance')}>Nearest First</button>
          <button onClick={() => sortTable('withoutWebsites', 'opportunity')}>Opportunity Score</button>
        </div>
        <table border={1}>
          <thead>
            <tr>
              <th><input type="checkbox" onChange={selectAllWithoutWebsites} /></th>
              <th>Business Name</th>
              <th>Address</th>
              <th>Phone</th>
              <th>Rating</th>
              <th>Reviews</th>
              <th>Distance</th>
              <th>Price</th>
              <th>Status</th>
              <th>Opportunity Score</th>
              <th>Est. Value</th>
            </tr>
          </thead>
          <tbody>
            {businessesWithoutWebsites.map(business => (
              <tr key={business.id} style={business.rating && business.rating > 4 ? { backgroundColor: '#ffffcc' } : {}}>
                <td><input type="checkbox" value={business.id} /></td>
                <td>{business.name}</td>
                <td>{business.address}</td>
                <td>{business.phone || 'N/A'}</td>
                <td>{business.rating} ⭐</td>
                <td>N/A</td>
                <td>{business.distance.toFixed(2)} mi</td>
                <td>{'$'.repeat(business.metadata.confidence || 1)}</td>
                <td>{business.websiteStatus === 'verified' ? '🟢' : '🔴'}</td>
                <td>{calculateOpportunityScore(business)}/100 🎯</td>
                <td>${estimateWebsiteValue(business)}</td>
              </tr>
            ))}
          </tbody>
        </table>
        <div>
          <button onClick={contactSelected}>Generate Contact List</button>
          <button onClick={createProposal}>Create Proposal Template</button>
        </div>
      </div>
    </div>
  );
};

export default ResultsTables;
