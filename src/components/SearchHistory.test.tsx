import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import SearchHistory from './SearchHistory';

describe('SearchHistory', () => {
    it('renders the search history table with mock data', () => {
        render(<SearchHistory />);
        
        expect(screen.getByText('94105')).toBeInTheDocument();
        expect(screen.getByText('restaurant')).toBeInTheDocument();
        expect(screen.getByText('15')).toBeInTheDocument();

        expect(screen.getByText('10001')).toBeInTheDocument();
        expect(screen.getByText('coffee')).toBeInTheDocument();
        expect(screen.getByText('8')).toBeInTheDocument();
    });

    it('calls the re-run function when a re-run button is clicked', () => {
        // Since the component doesn't take a callback, this test is limited.
        // We can spy on console.log as a proxy.
        const consoleSpy = jest.spyOn(console, 'log');
        render(<SearchHistory />);

        const rerunButtons = screen.getAllByRole('button', { name: /re-run/i });
        fireEvent.click(rerunButtons[0]);

        expect(consoleSpy).toHaveBeenCalledWith('Re-running search for:', expect.any(Object));
        consoleSpy.mockRestore();
    });
}); 