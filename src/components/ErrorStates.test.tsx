import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import ErrorStates, { ErrorInfo } from './ErrorStates';

const mockActions = {
    retry: jest.fn(),
    useOfflineMode: jest.fn(),
    increaseRadius: jest.fn(),
    removeFilters: jest.fn(),
    suggestNearbyZip: jest.fn(),
};

describe('ErrorStates', () => {
    beforeEach(() => {
        // Reset mocks before each test
        jest.clearAllMocks();
    });

    it('renders network error state correctly', () => {
        const error: ErrorInfo = { type: 'network' };
        render(<ErrorStates error={error} {...mockActions} radius={5} nearbyZip="94107" />);
        
        expect(screen.getByText('⚠️ Network Error')).toBeInTheDocument();
        fireEvent.click(screen.getByRole('button', { name: /retry search/i }));
        expect(mockActions.retry).toHaveBeenCalledTimes(1);
        fireEvent.click(screen.getByRole('button', { name: /use offline mode/i }));
        expect(mockActions.useOfflineMode).toHaveBeenCalledTimes(1);
    });

    it('renders validation error state correctly', () => {
        const error: ErrorInfo = { type: 'validation', details: ['Invalid zip code'] };
        render(<ErrorStates error={error} {...mockActions} radius={5} nearbyZip="94107" />);
        
        expect(screen.getByText('📝 Invalid Input')).toBeInTheDocument();
        expect(screen.getByText('Invalid zip code')).toBeInTheDocument();
    });

    it('renders no results error state correctly', () => {
        const error: ErrorInfo = { type: 'no_results' };
        render(<ErrorStates error={error} {...mockActions} radius={5} nearbyZip="94107" />);
        
        expect(screen.getByText('🔍 No Results Found')).toBeInTheDocument();
        fireEvent.click(screen.getByText(/increase radius/i));
        expect(mockActions.increaseRadius).toHaveBeenCalledTimes(1);
        fireEvent.click(screen.getByText(/remove filters/i));
        expect(mockActions.removeFilters).toHaveBeenCalledTimes(1);
        fireEvent.click(screen.getByText(/try nearby zip/i));
        expect(mockActions.suggestNearbyZip).toHaveBeenCalledTimes(1);
    });

    it('renders generic error state correctly', () => {
        const error: ErrorInfo = { type: 'generic', message: 'Something went wrong' };
        render(<ErrorStates error={error} {...mockActions} radius={5} nearbyZip="94107" />);

        expect(screen.getByText('❌ An Error Occurred')).toBeInTheDocument();
        expect(screen.getByText('Something went wrong')).toBeInTheDocument();
        fireEvent.click(screen.getByRole('button', { name: /retry search/i }));
        expect(mockActions.retry).toHaveBeenCalledTimes(1);
    });
}); 