/**
 * Application constants and configuration values
 */

// API Configuration
export const API_CONFIG = {
  GOOGLE_PLACES_BASE_URL: 'https://maps.googleapis.com/maps/api/place',
  GEOCODING_BASE_URL: 'https://maps.googleapis.com/maps/api/geocode',
  REQUEST_TIMEOUT: 5000,
  MAX_RETRIES: 3,
  RETRY_DELAY_BASE: 1000, // Base delay for exponential backoff
} as const;

// Search Configuration
export const SEARCH_CONFIG = {
  MIN_RADIUS: 1,
  MAX_RADIUS: 50,
  DEFAULT_RADIUS: 10,
  MAX_RESULTS_PER_PAGE: 20,
  MAX_TOTAL_RESULTS: 1000,
} as const;

// Rate Limiting Configuration
export const RATE_LIMIT_CONFIG = {
  REQUESTS_PER_SECOND: 10,
  BURST_SIZE: 20,
  TOKEN_REFILL_INTERVAL: 100, // milliseconds
} as const;

// Cache Configuration
export const CACHE_CONFIG = {
  EXPIRATION_HOURS: 24,
  MAX_SIZE_MB: 50,
  KEY_PREFIX: 'business_search_',
  CLEANUP_INTERVAL: 3600000, // 1 hour in milliseconds
} as const;

// Website Verification Configuration
export const WEBSITE_CONFIG = {
  TIMEOUT: 3000,
  MAX_REDIRECTS: 3,
  USER_AGENT: 'BusinessSearchBot/1.0',
  VALID_STATUS_CODES: [200, 201, 202, 203, 204, 301, 302, 303, 307, 308],
} as const;

// Validation Patterns
export const VALIDATION_PATTERNS = {
  ZIP_CODE: /^\d{5}(-\d{4})?$/,
  PHONE: /^\+?[\d\s\-\(\)\.]+$/,
  URL: /^https?:\/\/.+/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
} as const;

// Business Types (Google Places API types)
export const BUSINESS_TYPES = [
  'accounting',
  'airport',
  'amusement_park',
  'aquarium',
  'art_gallery',
  'atm',
  'bakery',
  'bank',
  'bar',
  'beauty_salon',
  'bicycle_store',
  'book_store',
  'bowling_alley',
  'bus_station',
  'cafe',
  'campground',
  'car_dealer',
  'car_rental',
  'car_repair',
  'car_wash',
  'casino',
  'cemetery',
  'church',
  'city_hall',
  'clothing_store',
  'convenience_store',
  'courthouse',
  'dentist',
  'department_store',
  'doctor',
  'drugstore',
  'electrician',
  'electronics_store',
  'embassy',
  'fire_station',
  'florist',
  'funeral_home',
  'furniture_store',
  'gas_station',
  'gym',
  'hair_care',
  'hardware_store',
  'hindu_temple',
  'home_goods_store',
  'hospital',
  'insurance_agency',
  'jewelry_store',
  'laundry',
  'lawyer',
  'library',
  'light_rail_station',
  'liquor_store',
  'local_government_office',
  'locksmith',
  'lodging',
  'meal_delivery',
  'meal_takeaway',
  'mosque',
  'movie_rental',
  'movie_theater',
  'moving_company',
  'museum',
  'night_club',
  'painter',
  'park',
  'parking',
  'pet_store',
  'pharmacy',
  'physiotherapist',
  'plumber',
  'police',
  'post_office',
  'primary_school',
  'real_estate_agency',
  'restaurant',
  'roofing_contractor',
  'rv_park',
  'school',
  'secondary_school',
  'shoe_store',
  'shopping_mall',
  'spa',
  'stadium',
  'storage',
  'store',
  'subway_station',
  'supermarket',
  'synagogue',
  'taxi_stand',
  'tourist_attraction',
  'train_station',
  'transit_station',
  'travel_agency',
  'university',
  'veterinary_care',
  'zoo'
] as const;

// Error Messages
export const ERROR_MESSAGES = {
  INVALID_ZIP_CODE: 'Invalid zip code format. Please use 5-digit or 9-digit format.',
  INVALID_RADIUS: `Radius must be between ${SEARCH_CONFIG.MIN_RADIUS} and ${SEARCH_CONFIG.MAX_RADIUS} miles.`,
  INVALID_BUSINESS_TYPE: 'Invalid business type. Please select from available options.',
  API_KEY_MISSING: 'Google Places API key is required.',
  NETWORK_ERROR: 'Network error occurred. Please check your connection.',
  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded. Please try again later.',
  GEOCODING_FAILED: 'Failed to convert zip code to coordinates.',
  WEBSITE_VERIFICATION_FAILED: 'Failed to verify website accessibility.',
  CACHE_ERROR: 'Cache operation failed.',
} as const;

export type BusinessType = typeof BUSINESS_TYPES[number];
