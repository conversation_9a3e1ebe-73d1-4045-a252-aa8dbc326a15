/**
 * Base error class for all application errors
 */
export abstract class AppError extends Error {
  abstract readonly code: string;
  abstract readonly statusCode: number;
  
  constructor(message: string, public readonly cause?: Error) {
    super(message);
    this.name = this.constructor.name;
    
    // Maintains proper stack trace for where our error was thrown
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

/**
 * Error thrown when validation fails
 */
export class ValidationError extends AppError {
  readonly code = 'VALIDATION_ERROR';
  readonly statusCode = 400;
  
  constructor(message: string, public readonly field?: string) {
    super(message);
  }
}

/**
 * Error thrown when API requests fail
 */
export class ApiError extends AppError {
  readonly code = 'API_ERROR';
  readonly statusCode: number;
  
  constructor(
    message: string,
    statusCode: number = 500,
    public readonly apiName?: string,
    cause?: Error
  ) {
    super(message, cause);
    this.statusCode = statusCode;
  }
}

/**
 * Error thrown when rate limits are exceeded
 */
export class RateLimitError extends AppError {
  readonly code = 'RATE_LIMIT_ERROR';
  readonly statusCode = 429;
  
  constructor(
    message: string = 'Rate limit exceeded',
    public readonly retryAfter?: number
  ) {
    super(message);
  }
}

/**
 * Error thrown when network requests fail
 */
export class NetworkError extends AppError {
  readonly code = 'NETWORK_ERROR';
  readonly statusCode = 503;
  
  constructor(message: string, cause?: Error) {
    super(message, cause);
  }
}

/**
 * Error thrown when geocoding fails
 */
export class GeocodingError extends AppError {
  readonly code = 'GEOCODING_ERROR';
  readonly statusCode = 400;
  
  constructor(message: string, public readonly zipCode?: string) {
    super(message);
  }
}

/**
 * Error thrown when website verification fails
 */
export class WebsiteVerificationError extends AppError {
  readonly code = 'WEBSITE_VERIFICATION_ERROR';
  readonly statusCode = 500;
  
  constructor(message: string, public readonly url?: string, cause?: Error) {
    super(message, cause);
  }
}

/**
 * Error thrown when cache operations fail
 */
export class CacheError extends AppError {
  readonly code = 'CACHE_ERROR';
  readonly statusCode = 500;
  
  constructor(message: string, cause?: Error) {
    super(message, cause);
  }
}

/**
 * Error thrown when configuration is invalid
 */
export class ConfigurationError extends AppError {
  readonly code = 'CONFIGURATION_ERROR';
  readonly statusCode = 500;
  
  constructor(message: string, public readonly configKey?: string) {
    super(message);
  }
}
