/**
 * Represents the website verification status of a business
 */
export type WebsiteStatus = 'verified' | 'unverified' | 'none';

/**
 * Represents the data source for business information
 */
export type DataSource = 'google_places' | 'cache' | 'fallback';

/**
 * Metadata associated with business data
 */
export interface BusinessMetadata {
  lastUpdated: Date;
  dataSource: DataSource;
  confidence: number; // 0-1 scale
}

/**
 * Core business entity with all required information
 */
export interface Business {
  id: string;
  name: string;
  address: string;
  phone?: string;
  website?: string;
  websiteStatus: WebsiteStatus;
  category: string[];
  rating?: number;
  distance: number; // in miles
  metadata: BusinessMetadata;
}

/**
 * Search parameters for business queries
 */
export interface SearchParams {
  zipCode: string;
  radius: number; // in miles (1-50)
  businessType: string;
  timestamp: Date;
}

/**
 * Statistics about search results
 */
export interface SearchStatistics {
  totalFound: number;
  withWebsiteCount: number;
  withoutWebsiteCount: number;
  websiteAdoptionRate: number; // percentage
  searchDuration: number; // in milliseconds
}

/**
 * Categorized search results
 */
export interface CategorizedResults {
  withWebsites: Business[];
  withoutWebsites: Business[];
}

/**
 * Complete search result structure
 */
export interface SearchResult {
  searchParams: SearchParams;
  results: CategorizedResults;
  statistics: SearchStatistics;
}

/**
 * Coordinates for geographic location
 */
export interface Coordinates {
  latitude: number;
  longitude: number;
}

/**
 * Google Places API place result
 */
export interface PlaceResult {
  place_id: string;
  name: string;
  formatted_address: string;
  formatted_phone_number?: string;
  website?: string;
  types: string[];
  rating?: number;
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
}
