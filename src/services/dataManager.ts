import { SearchResult } from '../models/Business';
import { CacheError } from '../models/Errors';
import { CACHE_CONFIG } from '../constants';

/**
 * Cached data structure
 */
interface CachedData {
  data: SearchResult;
  timestamp: number;
  expiresAt: number;
}

/**
 * Storage information
 */
export interface StorageInfo {
  totalEntries: number;
  totalSizeBytes: number;
  oldestEntry: Date | null;
  newestEntry: Date | null;
}

/**
 * Export data structure
 */
interface ExportData {
  version: string;
  exportDate: string;
  searchResults: SearchResult[];
}

/**
 * Service for managing data persistence using localStorage
 */
export class DataManager {
  private readonly keyPrefix = CACHE_CONFIG.KEY_PREFIX;
  private readonly expirationMs = CACHE_CONFIG.EXPIRATION_HOURS * 60 * 60 * 1000;

  /**
   * Saves a search result to localStorage
   * @param searchResult - The search result to save
   * @returns The storage key for the saved data
   */
  saveSearchResult(searchResult: SearchResult): string {
    try {
      const key = this.generateKey(searchResult);
      const now = Date.now();
      
      const cachedData: CachedData = {
        data: searchResult,
        timestamp: now,
        expiresAt: now + this.expirationMs,
      };

      localStorage.setItem(key, JSON.stringify(cachedData));
      return key;
    } catch (error) {
      throw new CacheError(`Failed to save search result: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Retrieves a search result from localStorage
   * @param key - The storage key
   * @returns The search result or null if not found/expired
   */
  getSearchResult(key: string): SearchResult | null {
    try {
      const stored = localStorage.getItem(key);
      if (!stored) {
        return null;
      }

      const cachedData: CachedData = JSON.parse(stored);
      
      // Check if data has expired
      if (Date.now() > cachedData.expiresAt) {
        localStorage.removeItem(key);
        return null;
      }

      return this.deserializeSearchResult(cachedData.data);
    } catch (error) {
      // Handle corrupted data
      localStorage.removeItem(key);
      return null;
    }
  }

  /**
   * Gets all stored search results (excluding expired ones)
   * @returns Array of all valid search results
   */
  getAllSearchResults(): SearchResult[] {
    const results: SearchResult[] = [];
    const keysToRemove: string[] = [];

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (!key || !key.startsWith(this.keyPrefix)) {
        continue;
      }

      try {
        const stored = localStorage.getItem(key);
        if (!stored) continue;

        const cachedData: CachedData = JSON.parse(stored);
        
        // Check if data has expired
        if (Date.now() > cachedData.expiresAt) {
          keysToRemove.push(key);
          continue;
        }

        results.push(this.deserializeSearchResult(cachedData.data));
      } catch (error) {
        // Handle corrupted data
        keysToRemove.push(key);
      }
    }

    // Clean up expired/corrupted entries
    keysToRemove.forEach(key => localStorage.removeItem(key));

    // Sort by timestamp (newest first)
    return results.sort((a, b) => 
      new Date(b.searchParams.timestamp).getTime() - new Date(a.searchParams.timestamp).getTime()
    );
  }

  /**
   * Deletes a specific search result
   * @param key - The storage key to delete
   */
  deleteSearchResult(key: string): void {
    localStorage.removeItem(key);
  }

  /**
   * Clears all business search data from localStorage
   */
  clearAllData(): void {
    const keysToRemove: string[] = [];

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(this.keyPrefix)) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key));
  }

  /**
   * Gets information about current storage usage
   * @returns Storage information
   */
  getStorageInfo(): StorageInfo {
    let totalEntries = 0;
    let totalSizeBytes = 0;
    let oldestTimestamp: number | null = null;
    let newestTimestamp: number | null = null;

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (!key || !key.startsWith(this.keyPrefix)) {
        continue;
      }

      try {
        const stored = localStorage.getItem(key);
        if (!stored) continue;

        const cachedData: CachedData = JSON.parse(stored);
        
        // Skip expired data
        if (Date.now() > cachedData.expiresAt) {
          continue;
        }

        totalEntries++;
        totalSizeBytes += stored.length;

        if (oldestTimestamp === null || cachedData.timestamp < oldestTimestamp) {
          oldestTimestamp = cachedData.timestamp;
        }
        if (newestTimestamp === null || cachedData.timestamp > newestTimestamp) {
          newestTimestamp = cachedData.timestamp;
        }
      } catch (error) {
        // Skip corrupted data
        continue;
      }
    }

    return {
      totalEntries,
      totalSizeBytes,
      oldestEntry: oldestTimestamp ? new Date(oldestTimestamp) : null,
      newestEntry: newestTimestamp ? new Date(newestTimestamp) : null,
    };
  }

  /**
   * Removes expired data from localStorage
   * @returns Number of entries removed
   */
  cleanupExpiredData(): number {
    const keysToRemove: string[] = [];
    const now = Date.now();

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (!key || !key.startsWith(this.keyPrefix)) {
        continue;
      }

      try {
        const stored = localStorage.getItem(key);
        if (!stored) continue;

        const cachedData: CachedData = JSON.parse(stored);
        
        if (now > cachedData.expiresAt) {
          keysToRemove.push(key);
        }
      } catch (error) {
        // Remove corrupted data
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key));
    return keysToRemove.length;
  }

  /**
   * Exports all search results as JSON
   * @returns JSON string containing all search results
   */
  exportData(): string {
    const searchResults = this.getAllSearchResults();
    
    const exportData: ExportData = {
      version: '1.0',
      exportDate: new Date().toISOString(),
      searchResults,
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Imports search results from JSON
   * @param jsonData - JSON string containing search results
   * @returns Number of imported results
   */
  importData(jsonData: string): number {
    try {
      const importData: ExportData = JSON.parse(jsonData);
      
      // Validate import data structure
      if (!importData.searchResults || !Array.isArray(importData.searchResults)) {
        throw new Error('Invalid import data structure');
      }

      let importedCount = 0;
      
      for (const searchResult of importData.searchResults) {
        try {
          // Deserialize the search result before saving
          const deserializedResult = this.deserializeSearchResult(searchResult);
          this.saveSearchResult(deserializedResult);
          importedCount++;
        } catch (error) {
          console.warn('Failed to import search result:', error);
          // Continue with other results
        }
      }

      return importedCount;
    } catch (error) {
      throw new CacheError(`Failed to import data: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Generates a unique key for a search result
   * @param searchResult - The search result
   * @returns Unique storage key
   */
  private generateKey(searchResult: SearchResult): string {
    const params = searchResult.searchParams;
    const hash = this.simpleHash(`${params.zipCode}_${params.radius}_${params.businessType}_${params.timestamp.getTime()}`);
    return `${this.keyPrefix}${hash}`;
  }

  /**
   * Simple hash function for generating keys
   * @param str - String to hash
   * @returns Hash string
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Gets the current cache size (number of entries)
   * @returns Number of cached entries
   */
  getCacheSize(): number {
    let count = 0;
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(this.keyPrefix)) {
        count++;
      }
    }
    return count;
  }

  /**
   * Deserializes a search result, converting date strings back to Date objects
   * @param data - Raw search result data
   * @returns Properly typed search result
   */
  private deserializeSearchResult(data: any): SearchResult {
    return {
      ...data,
      searchParams: {
        ...data.searchParams,
        timestamp: new Date(data.searchParams.timestamp),
      },
      results: {
        withWebsites: data.results.withWebsites.map((business: any) => ({
          ...business,
          metadata: {
            ...business.metadata,
            lastUpdated: new Date(business.metadata.lastUpdated),
          },
        })),
        withoutWebsites: data.results.withoutWebsites.map((business: any) => ({
          ...business,
          metadata: {
            ...business.metadata,
            lastUpdated: new Date(business.metadata.lastUpdated),
          },
        })),
      },
    };
  }
}
