import { Coordinates } from '../models/Business';
import { GeocodingError, ApiError, NetworkError, ConfigurationError } from '../models/Errors';
import { validateZipCode } from '../utils/validation';
import { API_CONFIG } from '../constants';

/**
 * Google Geocoding API response interfaces
 */
interface GeocodeResult {
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
  formatted_address: string;
  address_components?: AddressComponent[];
}

interface AddressComponent {
  long_name: string;
  short_name: string;
  types: string[];
}

interface GeocodeResponse {
  results: GeocodeResult[];
  status: string;
  error_message?: string;
}

/**
 * Service for converting between zip codes and coordinates using Google Geocoding API
 */
export class GeocodingService {
  private cache = new Map<string, Coordinates | string>();
  private readonly apiKey: string;
  private readonly baseUrl: string;

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.GOOGLE_PLACES_API_KEY || '';
    this.baseUrl = API_CONFIG.GEOCODING_BASE_URL;

    if (!this.apiKey) {
      throw new ConfigurationError('Google Places API key is required', 'GOOGLE_PLACES_API_KEY');
    }
  }

  /**
   * Converts a zip code to coordinates
   * @param zipCode - The zip code to convert
   * @returns Promise resolving to coordinates
   * @throws GeocodingError if conversion fails
   */
  async zipCodeToCoordinates(zipCode: string): Promise<Coordinates> {
    // Validate zip code format
    try {
      validateZipCode(zipCode);
    } catch (error) {
      throw new GeocodingError(`Invalid zip code format: ${zipCode}`, zipCode);
    }

    // Check cache first
    const cacheKey = `zip_${zipCode}`;
    const cached = this.cache.get(cacheKey);
    if (cached && typeof cached === 'object') {
      return cached as Coordinates;
    }

    try {
      const url = `${this.baseUrl}/json?address=${encodeURIComponent(zipCode)}&key=${this.apiKey}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new ApiError(
          `Geocoding API request failed: ${response.statusText}`,
          response.status,
          'Google Geocoding API'
        );
      }

      const data: GeocodeResponse = await response.json();

      if (data.status !== 'OK') {
        if (data.status === 'ZERO_RESULTS') {
          throw new GeocodingError(`No results found for zip code: ${zipCode}`, zipCode);
        }
        throw new GeocodingError(
          data.error_message || `Geocoding failed with status: ${data.status}`,
          zipCode
        );
      }

      if (!data.results || data.results.length === 0) {
        throw new GeocodingError(`No results found for zip code: ${zipCode}`, zipCode);
      }

      const result = data.results[0];
      if (!result.geometry || !result.geometry.location) {
        throw new GeocodingError(`Invalid response format for zip code: ${zipCode}`, zipCode);
      }

      const coordinates: Coordinates = {
        latitude: result.geometry.location.lat,
        longitude: result.geometry.location.lng,
      };

      // Cache the result
      this.cache.set(cacheKey, coordinates);

      return coordinates;
    } catch (error) {
      if (error instanceof GeocodingError || error instanceof ApiError) {
        throw error;
      }
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new NetworkError('Geocoding request timed out', error);
        }
        throw new NetworkError(`Network error during geocoding: ${error.message}`, error);
      }
      
      throw new GeocodingError(`Unknown error during geocoding: ${String(error)}`, zipCode);
    }
  }

  /**
   * Converts coordinates to zip code (reverse geocoding)
   * @param coordinates - The coordinates to convert
   * @returns Promise resolving to zip code
   * @throws GeocodingError if conversion fails
   */
  async coordinatesToZipCode(coordinates: Coordinates): Promise<string> {
    const cacheKey = `coords_${coordinates.latitude}_${coordinates.longitude}`;
    const cached = this.cache.get(cacheKey);
    if (cached && typeof cached === 'string') {
      return cached;
    }

    try {
      const url = `${this.baseUrl}/json?latlng=${coordinates.latitude},${coordinates.longitude}&key=${this.apiKey}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new ApiError(
          `Reverse geocoding API request failed: ${response.statusText}`,
          response.status,
          'Google Geocoding API'
        );
      }

      const data: GeocodeResponse = await response.json();

      if (data.status !== 'OK') {
        throw new GeocodingError(
          data.error_message || `Reverse geocoding failed with status: ${data.status}`
        );
      }

      if (!data.results || data.results.length === 0) {
        throw new GeocodingError('No results found for coordinates');
      }

      // Find postal code in address components
      for (const result of data.results) {
        if (result.address_components) {
          for (const component of result.address_components) {
            if (component.types.includes('postal_code')) {
              const zipCode = component.long_name;
              this.cache.set(cacheKey, zipCode);
              return zipCode;
            }
          }
        }
      }

      throw new GeocodingError('No postal code found for coordinates');
    } catch (error) {
      if (error instanceof GeocodingError || error instanceof ApiError) {
        throw error;
      }
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new NetworkError('Reverse geocoding request timed out', error);
        }
        throw new NetworkError(`Network error during reverse geocoding: ${error.message}`, error);
      }
      
      throw new GeocodingError(`Unknown error during reverse geocoding: ${String(error)}`);
    }
  }

  /**
   * Clears the geocoding cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Gets the current cache size
   * @returns Number of cached entries
   */
  getCacheSize(): number {
    return this.cache.size;
  }
}
