import { ApiError, NetworkError, RateLimitError } from '../models/Errors';
import { API_CONFIG } from '../constants';

/**
 * HTTP request configuration
 */
export interface RequestConfig {
  method?: string;
  headers?: Record<string, string>;
  body?: string;
  timeout?: number;
  retries?: number;
}

/**
 * HTTP response interface
 */
export interface HttpResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Headers;
  ok: boolean;
}

/**
 * Request interceptor function type
 */
export type RequestInterceptor = (config: RequestConfig) => RequestConfig;

/**
 * Response interceptor function type
 */
export type ResponseInterceptor = <T>(response: T) => T;

/**
 * HTTP client with retry logic, interceptors, and error handling
 */
export class HttpClient {
  private requestInterceptors: RequestInterceptor[] = [];
  private responseInterceptors: ResponseInterceptor[] = [];
  private readonly defaultTimeout: number;
  private readonly maxRetries: number;

  constructor(timeout: number = API_CONFIG.REQUEST_TIMEOUT, maxRetries: number = API_CONFIG.MAX_RETRIES) {
    this.defaultTimeout = timeout;
    this.maxRetries = maxRetries;
  }

  /**
   * Adds a request interceptor
   * @param interceptor - Function to modify request config
   */
  addRequestInterceptor(interceptor: RequestInterceptor): void {
    this.requestInterceptors.push(interceptor);
  }

  /**
   * Adds a response interceptor
   * @param interceptor - Function to modify response data
   */
  addResponseInterceptor(interceptor: ResponseInterceptor): void {
    this.responseInterceptors.push(interceptor);
  }

  /**
   * Makes a GET request
   * @param url - Request URL
   * @param config - Request configuration
   * @returns Promise resolving to response data
   */
  async get<T = any>(url: string, config: Partial<RequestConfig> = {}): Promise<T> {
    const response = await this.request<T>(url, {
      ...config,
      method: 'GET',
    });
    return response.data;
  }

  /**
   * Makes a POST request
   * @param url - Request URL
   * @param data - Request body data
   * @param config - Request configuration
   * @returns Promise resolving to response data
   */
  async post<T = any>(url: string, data?: any, config: Partial<RequestConfig> = {}): Promise<T> {
    const response = await this.request<T>(url, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
      headers: {
        'Content-Type': 'application/json',
        ...config.headers,
      },
    });
    return response.data;
  }

  /**
   * Makes a HEAD request
   * @param url - Request URL
   * @param config - Request configuration
   * @returns Promise resolving to response (without body)
   */
  async head(url: string, config: Partial<RequestConfig> = {}): Promise<Omit<HttpResponse, 'data'>> {
    const response = await this.request(url, {
      ...config,
      method: 'HEAD',
    });

    return {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      ok: response.ok,
    };
  }

  /**
   * Makes an HTTP request with retry logic
   * @param url - Request URL
   * @param config - Request configuration
   * @returns Promise resolving to HTTP response
   */
  private async request<T = any>(url: string, config: RequestConfig): Promise<HttpResponse<T>> {
    let finalConfig = { ...config };

    // Apply request interceptors
    for (const interceptor of this.requestInterceptors) {
      finalConfig = interceptor(finalConfig);
    }

    const timeout = finalConfig.timeout || this.defaultTimeout;
    const maxRetries = finalConfig.retries !== undefined ? finalConfig.retries : this.maxRetries;

    console.log(`[HttpClient] Starting request with maxRetries: ${maxRetries}, timeout: ${timeout}`);

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      console.log(`[HttpClient] Attempt ${attempt + 1}/${maxRetries + 1}`);
      
      try {
        const response = await this.makeRequest<T>(url, finalConfig, timeout);
        console.log(`[HttpClient] Request succeeded on attempt ${attempt + 1}`);

        // Apply response interceptors
        let responseData = response.data;
        for (const interceptor of this.responseInterceptors) {
          responseData = interceptor(responseData);
        }

        return {
          ...response,
          data: responseData,
        };
      } catch (error) {
        lastError = error as Error;
        console.log(`[HttpClient] Request failed on attempt ${attempt + 1}:`, error instanceof Error ? error.message : String(error));

        // Don't retry on certain errors
        if (error instanceof ApiError && !this.isRetryableError(error)) {
          console.log(`[HttpClient] Not retrying - non-retryable ApiError`);
          throw error;
        }

        if (error instanceof RateLimitError) {
          console.log(`[HttpClient] Not retrying - RateLimitError`);
          throw error;
        }

        // Don't retry on the last attempt
        if (attempt >= maxRetries) {
          console.log(`[HttpClient] No more retries - reached max attempts`);
          break;
        }

        // Wait before retrying (exponential backoff) - but only if we're going to retry
        if (attempt < maxRetries) {
          const delay = this.calculateRetryDelay(attempt);
          console.log(`[HttpClient] Waiting ${delay}ms before retry`);
          await this.sleep(delay);
        }
      }
    }

    // If we get here, all retries failed
    console.log(`[HttpClient] All retry attempts failed`);
    throw lastError || new NetworkError('All retry attempts failed');
  }

  /**
   * Makes the actual HTTP request
   * @param url - Request URL
   * @param config - Request configuration
   * @param timeout - Request timeout
   * @returns Promise resolving to HTTP response
   */
  private async makeRequest<T>(url: string, config: RequestConfig, timeout: number): Promise<HttpResponse<T>> {
    try {
      const headers = new Headers({
        'User-Agent': 'BusinessSearchApp/1.0',
        ...config.headers,
      });

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      try {
        const response = await fetch(url, {
          method: config.method || 'GET',
          headers,
          body: config.body,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);
        return await this.processResponse<T>(response, config);
      } catch (error) {
        clearTimeout(timeoutId);
        throw error;
      }
    } catch (error) {
      if (error instanceof ApiError || error instanceof RateLimitError) {
        throw error;
      }

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new NetworkError('Request timed out', error);
        }
        throw new NetworkError(`Network error: ${error.message}`, error);
      }

      throw new NetworkError(`Unknown network error: ${String(error)}`);
    }
  }

  /**
   * Processes the HTTP response
   * @param response - The fetch response
   * @param config - Request configuration
   * @returns Promise resolving to HTTP response
   */
  private async processResponse<T>(response: Response, config: RequestConfig): Promise<HttpResponse<T>> {
    // Handle rate limiting
    if (response.status === 429) {
      const retryAfter = response.headers.get('Retry-After');
      const retryAfterSeconds = retryAfter ? parseInt(retryAfter, 10) : undefined;
      throw new RateLimitError('Rate limit exceeded', retryAfterSeconds);
    }

    // Handle other HTTP errors
    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      throw new ApiError(
        `HTTP ${response.status}: ${response.statusText} - ${errorText}`,
        response.status
      );
    }

    // Parse response data
    let data: T;
    const contentType = response.headers.get('content-type');

    if (config.method === 'HEAD') {
      data = null as any;
    } else if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text() as any;
    }

    return {
      data,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      ok: response.ok,
    };
  }

  /**
   * Determines if an error is retryable
   * @param error - The error to check
   * @returns True if the error is retryable
   */
  private isRetryableError(error: ApiError): boolean {
    // Retry on server errors (5xx) but not client errors (4xx)
    return error.statusCode >= 500;
  }

  /**
   * Calculates retry delay using exponential backoff
   * @param attempt - The current attempt number (0-based)
   * @returns Delay in milliseconds
   */
  private calculateRetryDelay(attempt: number): number {
    const baseDelay = API_CONFIG.RETRY_DELAY_BASE;
    return baseDelay * Math.pow(2, attempt);
  }

  /**
   * Sleeps for the specified duration
   * @param ms - Duration in milliseconds
   * @returns Promise that resolves after the delay
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
